const { http } = require('../libs/http');

class StatisticsRepository {
    
    /**
     * 产品账户总览 + 股票账户 + 期货账户统计
     * @returns {{ errorCode, errorMsg, data: Object }}
     */
    qsummary(fund_id, future_product) {
        return new Promise((resolve, reject) => {
            http.get('/statistics/fund', { params: { fund_id, future_product } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
    
    /**
     * 股票交易中的账号概览
     * @returns {{ errorCode, errorMsg, data: { 资产占比: number, data: Array } }}
     */
    qaccounts(fund_id, account_id) {
        return new Promise((resolve, reject) => {
            http.get('/statistics/stock', { params: { fund_id, account_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
    
    /**
     * 股票交易中的策略概览
     * @returns {{ errorCode, errorMsg, data: Array }}
     */
    qstrategies(fund_id, account_id) {
        return new Promise((resolve, reject) => {
            http.get('/statistics/stock/strategy', { params: { fund_id, account_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
    
    /**
     * 期货交易中的对冲提示
     * @returns {{ errorCode, errorMsg, data: { 资产占比: number, data: Array } }}
     */
    qhedges(fund_id) {
        return new Promise((resolve, reject) => {
            http.get('/statistics/future', { params: { fund_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
    
    /**
     * 期货交易中的交易执行
     * @returns {{ errorCode, errorMsg, data: Array }}
     */
    qexecs(fund_id) {
        return new Promise((resolve, reject) => {
            http.get('/statistics/future/trade', { params: { fund_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
}

module.exports = { repoStatistics: new StatisticsRepository() };
