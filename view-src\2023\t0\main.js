const { IView } = require('../../../component/iview');
const { NumberMixin } = require('../../../mixin/number');
const { AlgorithmInfo, AlgorithmGroup } = require('../../../model/algorithm-info');
const { AccountDetail } = require('../../../model/account');
const { T0TaskPackage, T0TaskInfo, T0EntrustInfo, TaskEntrustStatus } = require('../../../model/t0');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repoAccount } = require('../../../repository/account');
const { repoAlgo, AlgorithmClasses } = require('../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '日内回转');
        this.status = TaskEntrustStatus;
        this.tasks = [new T0TaskInfo({})].splice(1);
    }

    /**
     * @param {T0TaskInfo} task 
     * @returns {T0TaskInfo}
     */
    asTask(task) {
        return task;
    }

    /**
     * @param {T0TaskInfo | undefined} task_info 
     */
    create(task_info) {

        if (this.dialog == undefined) {

            const CreationView = require('./creation');
            let dialog = this.dialog = new CreationView('@2023/t0/creation', false);
            dialog.registerEvent('save-success', this.handleEditing.bind(this));
            dialog.loadBuild(this.$dialog, null, () => {
                setTimeout(() => { this.deliever(task_info); }, 500);
            });
        }
        else {
            this.deliever(task_info);
        }
    }

    /**
     * @param {T0TaskInfo | undefined} task 
     */
    deliever(task) {
        this.dialog.transfer(task || new T0TaskInfo({}));
    }

    handleEditing() {

        console.log('to reload task list / cause = saving successful');
        this.requestTasks(true);
    }

    setupTop() {

        this.top = {

            stock_count: 0,
            strategy_count: 0,
            running_count: 0,
            stopped_count: 0,

            total_profit: 0,
            closed_profit: 0,
            float_profit: 0,
        };
        
        this.vtop = new Vue({

            el: this.$top,
            data: this.top,
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [
                this.create,
                this.formatSelectAccountName,
            ]),
        });
    }

    handleAccountChange() {
        this.requestAlgoes();
    }

    handleAlgoChange() {
        this.states.local.algoId = this.filter.algoId;
    }
    
    handleInstanceChange() {
        this.states.local.taskId = this.filter.taskId;
    }
    
    filterTasks() {
        this.requestTasks(true);
    }

    setupFilterRow() {

        this.accounts = [new AccountDetail({})].splice(1);
        this.algoGrps = [new AlgorithmGroup()].splice(1);
        this.algoes = [new AlgorithmInfo({})].splice(1);
        this.instances = [{ id: null, name: null }].splice(1);

        this.filter = {

            accounts: this.accounts,
            instances: this.instances,

            recordId: null,
            algoId: null,
            algoGrps: this.algoGrps,
            taskId: null,
        };

        this.vtoolbar = new Vue({

            el: this.$control,
            data: this.filter,
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleAccountChange,
                this.handleAlgoChange, 
                this.handleInstanceChange,
                this.filterTasks,
                this.formatSelectAccountName,
                this.getProperAccountId,
            ]),
        });
    }

    /**
     * @param {AccountDetail} account 
     */
    getProperAccountId(account) {
        return this.helperUi.getProperAccountId(account);
    }

    /**
     * @param {T0TaskInfo} data 
     */
    isTaskRunning(data) {
        return data.status == this.status.running;
    }

    /**
     * @param {T0TaskInfo} data 
     */
    isTaskStopped(data) {
        return data.status == this.status.completed || data.status == this.status.canceled;
    }

    /**
     * @param {T0TaskInfo} task 
     */
    meetLocal(task) {

        let { algoId, taskId } = this.states.local;
        let is_ok1 = this.helper.isNone(algoId) || algoId == task.xtrade.algo_id;
        let is_ok2 = this.helper.isNone(taskId) || taskId == task.xtrade.task_id;
        return is_ok1 && is_ok2;
    }

    /**
     * @param {T0TaskInfo} task 
     */
    isCurrentChoosed(task) {

        let ref = this.states.selected;
        return ref && ref.xtrade.task_id == task.xtrade.task_id;
    }

    /**
     * @param {T0TaskInfo} task 
     */
    handleTaskCheck(task) {
        this.setAsChoosed(task);
    }

    /**
     * @param {T0TaskInfo} task 
     */
    editm(task) {
        this.create(task);
    }

    /**
     * @param {T0TaskInfo} task 
     */
    async stopm(task) {

        let resp = await repoAlgo.cancelOrder([task.xtrade.task_id]);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess(`${task.task_name}，母单已撤单`);
            task.status = this.status.canceled;
            this.requestTasks(false);
        }
        else {
            this.interaction.showError(`任务撤单错误：${errorCode}/${errorMsg}`);
        }
    }

    setupTaskView() {

        this.states = {

            selected: this.asTask(null),
            /** 映射的本来筛选条件 */
            local: { algoId: null, taskId: null },
        };

        this.vtask = new Vue({

            el: this.$task,
            data: {
                tasks: this.tasks,
                states: this.states,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.isTaskRunning,
                this.isCurrentChoosed,
                this.meetLocal,
                this.editm,
                this.stopm,
                this.handleTaskCheck,
                this.formatSelectAccountName,
            ]),
        });
    }

    setupTitleView() {

        this.caption = { title: '任务委托列表' }
        this.vtask = new Vue({

            el: this.$title,
            data: this.caption,
        });
    }

    async requestAccounts() {

        var resp = await repoAccount.batchGetAccountCash();
        var { errorCode, errorMsg, data } = resp;
        
        if (errorCode == 0) {

            let accounts = data instanceof Array ? data.map(x => new AccountDetail(x)) : [];
            this.accounts.refill(accounts);
        }
        else {
            this.interaction.showError('账号获取异常：' + errorMsg);
        }
    }

    async requestAlgoes() {

        var account = this.accounts.find(x => this.getProperAccountId(x) == this.filter.recordId);
        if (!account) {

            this.algoGrps.clear();
            this.algoes.clear();
            return;
        }

        var resp = await repoAlgo.queryAlgoes(AlgorithmClasses.t0, account.accountId);
        var { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {
            return this.interaction.showError(`算法加载失败：${errorCode}/${errorMsg}`);
        }

        var algoes = Array.isArray(data) ? data : [];
        var map = algoes.groupBy(x => x.supplierName);
        var flattends = [];
        this.algoGrps.clear();
        
        for (let supplier_name in map) {

            let subset = map[supplier_name];
            let members = (Array.isArray(subset) ? subset : []).map(x => new AlgorithmInfo(x));
            this.algoGrps.push(new AlgorithmGroup(supplier_name, members));
            flattends.merge(members);
        }

        this.algoes.refill(flattends);
    }

    /**
     * @param {T0EntrustInfo} record 
     */
    identify(record) {
        return record.stock_code;
    }

    setupEntrustTable() {

        this.helper.extend(this, ColumnCommonFunc);
        let tentrust = new SmartTable(this.$table, this.identify, this, { tableName: 'smt-jwx1h', displayName: '算法单详情', headerHeight: 32, rowHeight: 32 });
        this.tentrust = tentrust;
    }

    /**
     * @param {T0EntrustInfo} record 
     */
    formatCodeName(record) {
        return `${record.stock_code} / ${record.stock_name}`;
    }

    /**
     * @param {T0EntrustInfo} record 
     */
    formatBuySell(record) {

        let method = NumberMixin.methods.thousandsInt;
        return `${method(record.buy_amount)} / ${method(record.sell_amount)}`;
    }

    /**
     * @param {T0EntrustInfo} record 
     */
    formatAvgPrice(record) {

        let method = NumberMixin.methods.fixed2;
        return `${method(record.buy_avg_price)} / ${method(record.sell_avg_price)}`;
    }

    /**
     * @param {T0EntrustInfo} data 
     */
    isEntrustStopped(data) {
        return data.status == this.status.completed || data.status == this.status.canceled;
    }

    /**
     * @param {T0EntrustInfo} record 
     */
    formatActions(record) {
        return this.isEntrustStopped(record) ? '<span>已停止</span>' : '<button class="danger" event.onclick="stopEntrust">停止</button>';
    }

    /**
     * @param {T0EntrustInfo} record 
     */
    async stopEntrust(record) {
        
        let resp = await repoAlgo.cancelOrder([record.id]);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.tentrust.updateRow({ stock_code: record.stock_code, status: this.status.canceled });
            this.interaction.showSuccess(`${record.stock_name}，委托已撤单`);
        }
        else {
            this.interaction.showError(`任务撤单错误：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {T0TaskPackage} datap 
     */
    summarize(datap) {

        const top_ref = this.top;

        top_ref.stock_count = datap.stock_count;
        top_ref.strategy_count = datap.tasks.length;
        top_ref.running_count = datap.tasks.filter(x => this.isTaskRunning(x)).length;
        top_ref.stopped_count = datap.tasks.filter(x => this.isTaskStopped(x)).length;

        top_ref.total_profit = datap.total_profit;
        top_ref.closed_profit = datap.closed_profit;
        top_ref.float_profit = datap.float_profit;
    }

    /**
     * @param {T0TaskInfo | undefined} task 
     */
    setAsChoosed(task) {

        this.states.selected = task;
        this.tentrust.refill(task ? task.entrusts : []);
        this.caption.title = task ? `${task.task_name} / 任务委托列表` : '任务委托列表';
    }

    /**
     * @param {Array<T0TaskInfo>} tasks 
     */
    updateTaks(tasks) {

        let sorteds = tasks.map(x => ({ id: x.xtrade.task_id, name: x.task_name })).sort((x, y) => this.helper.compare(x.name, y.name));
        this.instances.refill(sorteds);
        this.tasks.refill(tasks);
        let ref = this.states.selected;
        this.setAsChoosed(ref ? tasks.find(x => x.xtrade.task_id == ref.xtrade.task_id) : tasks[0]);
    }

    listen2Events() {
        this.registerEvent('on-view-visible', () => { this.tentrust.fitColumnWidth(); });
    }

    async requestTasks(show_loading) {
        
        let record_id = this.filter.recordId;
        let account = this.accounts.find(x => this.getProperAccountId(x) == record_id);
        let params = {

            algorithm_class: 2, 
            fund_id: account ? account.fundId : null,
            strategy_id: account ? account.strategyId : null,
            account_id: account ? account.accountId : null,
        };

        let $loading = show_loading ? this.interaction.showLoading({ text: `正在查询任务列表...` }) : null;

        try {

            let resp = await repoAlgo.qtasks(params);
            if (typeof resp == 'string') {
                // console.log(resp);
                resp = JSON.parse(resp.replace(/(\d{1,}):\{/g, '"$1":{'));
            }

            let { data, errorCode, errorMsg } = resp;
            let key = 'taskTableData';
            let map = data[key] || {};
            let members = [];
            for (let key in map) {

                map[key].taskId = key;
                members.push(map[key]);
            }
            data[key] = members;

            if (errorCode == 0) {

                let datap = new T0TaskPackage(data);
                this.summarize(datap);
                this.updateTaks(datap.tasks);
            }
            else {
                this.interaction.showError(`母单任务查询错误：${errorCode}/${errorMsg}`);
            }            
        }
        catch(ex) {
            //
        }
        finally {
            $loading && $loading.close();
        }
    }

    keepRefreshed() {

        this.refreshJob = setInterval(async () => {
            
            if (this._isJobRunning) {
                return;
            }

            this._isJobRunning = true;
            try { await this.requestTasks(false); } catch(ex) {}
            this._isJobRunning = false;

        }, 30 * 1000);
    }

    build($container) {

        super.build($container);
        this.$top = this.$container.querySelector('.top-box');
        this.$control = this.$container.querySelector('.control-box');
        this.$task = this.$container.querySelector('.morder-list');
        this.$title = this.$container.querySelector('.child-task-title');
        this.$table = this.$container.querySelector('.child-task-list');
        this.$dialog = this.$container.querySelector('.creation-dialog-external');

        this.setupTop();
        this.setupFilterRow();
        this.setupTaskView();
        this.setupTitleView();
        this.setupEntrustTable();
        this.listen2Events();

        this.requestAccounts();
        this.requestAlgoes();
        this.requestTasks(true);
        this.keepRefreshed();
    }
}

module.exports = View;
