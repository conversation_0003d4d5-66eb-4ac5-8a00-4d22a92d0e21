const { http } = require('../libs/http');
const { systemEnum } = require('../config/system-enum');
const { TradeDataQueryCondition } = require('../model/trading');
const { BinaryRepo } = require('./binary-repo');

class TradingRepository extends BinaryRepo {

    /**
     * 获取今日订单
     */
    getTodayOrders({ accountId, traderId, fundId, strategyId }) {

        return new Promise((resolve, reject) => {

            http.get('/order', {
                params: {
                    trader_id: traderId,
                    account_id: accountId,
                    fund_id: fundId,
                    strategy_id: strategyId,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /*
     * 获取交易员今日订单
     */
    getTraderTodayOrders() {

        return new Promise((resolve, reject) => {

            http.get('/order/user').then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /*
     * 发送组合交易订单
     */
    sendOrder(order) {
        return new Promise((resolve, reject) => {
            http.post(`${http.exAddr.indayServer}/api/batchorder/add`, {}, { params: order }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    /*
     * 取消订单
     */
    cancelOrder(instrument) {
        return new Promise((resolve, reject) => {
            http.post(`${http.exAddr.indayServer}/api/batchorder/cancel`, null, {
                params: instrument ? { stock_code: instrument } : {},
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取产品历史订单
     */
    getProductHistoryOrders({ fundId, beginDay, endDay, sort, pageSize, pageNo, exchangeId, instrument, accountId, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/fund/order', {
                params: {
                    fund_id: fundId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    sort: sort,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    account_id: accountId,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取策略历史订单
     */
    getStrategyHistoryOrders({ strategyId, beginDay, endDay, sort, pageSize, pageNo, exchangeId, instrument, accountId, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/strategy/order', {
                params: {
                    strategy_id: strategyId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    sort: sort,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    account_id: accountId,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取账号历史订单
     */
    getAccountHistoryOrders({ accountId, fundId, beginDay, endDay, pageSize, pageNo, exchangeId, instrument, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/account/order', {
                params: {
                    fund_id: fundId,
                    account_id: accountId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取产品历史成交
     */
    getProductHistoryExchanges({ fundId, beginDay, endDay, sort, pageSize, pageNo, exchangeId, instrument, accountId, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/fund/trade', {
                params: {
                    fund_id: fundId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    sort: sort,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    account_id: accountId,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取策略历史成交
     */
    getStrategyHistoryExchanges({ strategyId, beginDay, endDay, sort, pageSize, pageNo, exchangeId, instrument, accountId, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/strategy/trade', {
                params: {
                    strategy_id: strategyId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    sort: sort,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    account_id: accountId,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取账号历史成交
     */
    getAccountHistoryExchanges({ accountId, fundId, beginDay, endDay, pageSize, pageNo, exchangeId, instrument, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/account/trade', {
                params: {
                    fund_id: fundId,
                    account_id: accountId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    getHistoryBalances({ identityId, identityType, beginDay, endDay }) {
        return new Promise((resolve, reject) => {
            http.get('/history/balance', {
                params: {
                    identity_id: identityId,
                    begin_day: beginDay,
                    end_day: endDay,
                    identity_type: identityType,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    getHistoryAccountBalances({ identityId, beginDay, endDay }) {

        return new Promise((resolve, reject) => {
            http.get('/history/sdk/account', { params: { account_id: identityId, begin_day: beginDay, end_day: endDay } }).then(
                (resp) => { resolve(resp.data); },
                (err) => { reject(err); });
        });
    }

    /**
     * 获取今日持仓
     */
    getTodayPositions({ strategyId, accountId, fundId }) {

        return new Promise((resolve, reject) => {
            http.get('/position', {
                params: {
                    strategy_id: strategyId,
                    account_id: accountId,
                    fund_id: fundId,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取产品历史持仓
     */
    getProductHistoryPositions({ fundId, beginDay, endDay, sort, pageSize, pageNo, exchangeId, instrument, accountId, direction }) {
        return new Promise((resolve, reject) => {
            http.get('/history/fund/position', {
                params: {
                    fund_id: fundId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    sort: sort,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    account_id: accountId,
                    direction: direction,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取账号的历史持仓
     */
    getAccountHistoryPositions({ accountId, beginDay, endDay, pageSize, pageNo, fundId, exchangeId, instrument, direction, sort }) {
        return new Promise((resolve, reject) => {
            http.get('/history/account/position', {
                params: {
                    fund_id: fundId,
                    account_id: accountId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    direction: direction,
                    sort: sort,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取策略历史持仓
     */
    getStrategyHistoryPositions({ strategyId, beginDay, endDay, pageSize, pageNo, sort, exchangeId, instrument, direction, accountId }) {
        return new Promise((resolve, reject) => {
            http.get('/history/strategy/position', {
                params: {
                    strategy_id: strategyId,
                    begin_day: beginDay,
                    end_day: endDay,
                    pageSize: pageSize,
                    pageNo: pageNo,
                    exchangeID: exchangeId,
                    instrument: instrument,
                    direction: direction,
                    account_id: accountId,
                    sort: sort,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /* 成交流水 */
    getTodayWaterflows({ strategyId, accountId, fundId, isToday }) {
        return new Promise((resolve, reject) => {
            http.get('/traderecord', {
                params: {
                    strategy_id: strategyId,
                    account_id: accountId,
                    fund_id: fundId,
                    is_today: isToday,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    getDayKlineSeries(data_server_service, instrument, begin_day, end_day) {
        return new Promise((resolve, reject) => {
            http.get(data_server_service + '/history/get_dailybars', {
                params: {
                    symbols: instrument,
                    begin_time: begin_day,
                    end_time: end_day,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    getIoMoney(identity_id, trading_day) {
        return new Promise((resolve, reject) => {
            http.get('/account/cashserial', { params: { identity_id, trading_day } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    insertIoMoney(account_id, { trading_day, in_money, out_money }) {
        return new Promise((resolve, reject) => {
            http.post('/account/cashserial', null, { params: { account_id, trading_day, in_money, out_money }}).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    getLastPrice(instruments) {
        return new Promise((resolve, reject) => {
            http.get(`${http.exAddr.quoteRestfulServer}/quote/v1/tick/last`, {
                params: {
                    instruments: Array.isArray(instruments) ? instruments.join(',') : instruments,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    getMarketLatestPrice(asset_type) {
        return new Promise((resolve, reject) => {
            // const simple_tick_svr_url = `${http.exAddr.quoteRestfulServer}/quote/v1/tick/simple`;
            const simple_tick_svr_url = '/common/tick/simple';
            http.get(simple_tick_svr_url, { params: { asset_type } }).then(
                (resp) => { resolve(resp.data); },
                (err) => { reject(err); },
            );
        });
    }

    /**
     * 查询指定资产类型的合约最新价格
     * @param {Array<Number>} types 
     */
    async getAssetsLatestPrice(types) {
        
        var isAll = !Array.isArray(types) || types.length == 0;
        var assts = systemEnum.assetsType;
        var map = {};

        try {

            if (isAll || types.some(x => x == assts.future.code)) {

                let resp = await this.getMarketLatestPrice(assts.future.code);
                Object.assign(map, resp.data || {});
            }
    
            if (isAll || types.some(x => x == assts.fund.code)) {
    
                let resp = await this.getMarketLatestPrice(assts.fund.code);
                Object.assign(map, resp.data || {});
            }
    
            if (isAll || types.some(x => x == assts.stock.code)) {
    
                let resp = await this.getMarketLatestPrice(assts.stock.code);
                Object.assign(map, resp.data || {});
            }
        }
        catch (ex) {
            console.error(ex);
        }
        
        return map;
    }

    /*
     * 获取可交易列表
     */
    //todo 写死的url
    getOnTradingInstruments() {
        return new Promise((resolve, reject) => {
            http.get(`${http.exAddr.indayServer}/api/trade_list`).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 获取交易信息
     */
    //todo
    getTradingFinance() {
        return new Promise((resolve, reject) => {
            http.get(`${http.exAddr.indayServer}/api/finance`).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    //获取要配置交易字段的
    getTradingDetailProperties() {
        return new Promise((resolve, reject) => {
            http.get(`${http.exAddr.quoteRestfulServer}/quote/v1/indicator`, {}).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }
    //获取配置交易字段的数据
    getTradingDetailData(instruments, indicators) {
        return new Promise((resolve, reject) => {
            http.post(`${http.exAddr.quoteRestfulServer}/quote/v1/indicator`, { instruments, indicators }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    getDailyBars(instruments) {
        return new Promise((resolve, reject) => {
            http.get(`${http.exAddr.quoteRestfulServer}/quote/v1/get_daily_bars`, {
                params: { instruments: instruments.join(',') },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /*
     * 发送组合交易批量订单
     */
    sendBucketOrder(orders) {
        return new Promise((resolve, reject) => {
            http.post(`${http.exAddr.indayServer}/api/batch/orders`, orders).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    getIndexRisk() {
        return new Promise((resolve, reject) => {
            http.get(`${http.exAddr.indayServer}/api/index/risk`).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    getLastDailybars(symbols) {
        return new Promise((resolve, reject) => {
            http.get(`http://211.152.51.189:9988/quote/history/get_last_dailybars`, {
                params: {
                    symbols,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    /**
     * 查询委托接口（条件查询）
     * @param {TradeDataQueryCondition} searchData 
     */
    getTodayEntrust(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/order', { 
                
                params: {

                    user_id: searchData.userId,
                    account_id: searchData.account,
                    fund_id: searchData.fund,
                    strategy_id: searchData.strategy,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    token:searchData.token,
                    instrument: searchData.searchValue,
                    begin_day: new Date().format('yyyyMMdd'),
                    end_day: new Date().format('yyyyMMdd'),
                    // remark: searchData.remark || '',
                    business_flag: searchData.business_flag || '',
                },
                headers: this.headers, 
                responseType: this.responseType 
            })
            .then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); }
            );
		});
    }

    /**
     * 查询历史委托接口
     * @param {TradeDataQueryCondition} searchData 
     */
    getHositoryEntrust(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/history/order', { 
                
                params: {

                    user_id: searchData.userId,
                    account_id: searchData.account,
                    fund_id: searchData.fund,
                    strategy_id: searchData.strategy,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    token:searchData.token,
                    instrument: searchData.searchValue,
                    begin_day:searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day:searchData.checked? new Date().format('yyyyMMdd') :searchData.date[1],
                    // remark: searchData.remark || '',
                    business_flag: searchData.business_flag || '',
                },
                headers: this.headers, 
                responseType: this.responseType 
            })
            .then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); }
            );
		});
    }

    /**
     * 查询历史权益接口
     */
    getHositoryBalance(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/history/balance', {
                params: {
                    user_id: searchData.userId,
                    account_id: searchData.account,
                    identity_id: searchData.strategy ? searchData.strategy : searchData.fund,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    begin_day: searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day: searchData.checked? new Date().format('yyyyMMdd') :searchData.date[1],
                    isSummary: searchData.isSummary,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 查询今天权益接口
     */
    getTodayBalance(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/account/detail', {
                params: {
                    user_id: searchData.userId,
                    account_id: searchData.account,
                    identity_id: searchData.strategy ? searchData.strategy : searchData.fund,
                    // fund_id: searchData.fund,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    begin_day: searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day: searchData.checked? new Date().format('yyyyMMdd') :searchData.date[1],
                    isSummary: searchData.isSummary,
                },
            }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    /**
     * 成交历史查询
     * @param {TradeDataQueryCondition} searchData 查询数据
     */
    getHistoryExchanges(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/history/trade', { 
                
                params: {

                    user_id: searchData.userId,
                    account_id: searchData.account,
                    fund_id: searchData.fund,
                    strategy_id: searchData.strategy,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    token:searchData.token,
                    instrument: searchData.searchValue,
                    begin_day:searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day: searchData.checked? new Date().format('yyyyMMdd') : searchData.date[1],
                },
                headers: this.headers, 
                responseType: this.responseType 
            })
            .then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); }
            );
		});
    }

    /**
     * 成交今天查询
     * @param {TradeDataQueryCondition} searchData 查询数据
     */
    getTodayExchanges(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/traderecord', { 
                
                params: {

                    user_id: searchData.userId,
                    account_id: searchData.account,
                    fund_id: searchData.fund,
                    strategy_id: searchData.strategy,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    token:searchData.token,
                    instrument: searchData.searchValue,
                    begin_day:searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day: searchData.checked? new Date().format('yyyyMMdd') : searchData.date[1],
                },
                headers: this.headers, 
                responseType: this.responseType 
            })
            .then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); }
            );
		});
    }

    /**
     * 持仓查询
     * @param {TradeDataQueryCondition} searchData 查询数据
     */
    getHistoryPosition(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/history/position', { 
                
                params: {

                    user_id: searchData.userId,
                    account_id: searchData.account,
                    fund_id: searchData.fund,
                    strategy_id: searchData.strategy,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    token:searchData.token,
                    instrument: searchData.searchValue,
                    begin_day:searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day: searchData.checked? new Date().format('yyyyMMdd') : searchData.date[1],
                },
                headers: this.headers, 
                responseType: this.responseType 
            })
            .then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); }
            );
		});
    }

    /**
     * 持仓今日查询
     * @param {TradeDataQueryCondition} searchData 查询数据
     */
    getTodayPosition(searchData) {

        return new Promise((resolve, reject) => {

            http.get('../v4/position', { 
                
                params: {

                    user_id: searchData.userId,
                    account_id: searchData.account,
                    fund_id: searchData.fund,
                    strategy_id: searchData.strategy,
                    pageNo: searchData.pageNo,
                    pageSize: searchData.pageSize,
                    token:searchData.token,
                    instrument: searchData.searchValue,
                    begin_day:searchData.checked? new Date().format('yyyyMMdd') : searchData.date[0],
                    end_day: searchData.checked? new Date().format('yyyyMMdd') : searchData.date[1],
                },
                headers: this.headers, 
                responseType: this.responseType 
            })
            .then(
                (resp) => { resolve(this.translate(resp)); },
                (error) => { reject(error); }
            );
		});
    }

    getHistoryNav(identity_type, identity, begin_day, end_day) {

        return new Promise((resolve, reject) => {

            http.get('../v4/history/nav', { params: { identity_type, identity, begin_day, end_day } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
}

module.exports = { repoTrading: new TradingRepository() };
