<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { ElMessage, type FormRules } from 'element-plus';
import { AdminService } from '@/api';
import type { MomOrganization, FormOrganization } from '../../../../xtrade-sdk/dist';
import { hasPermission } from '@/script';
import { MenuPermitOrganizationManagement } from '@/enum';

const { org } = defineProps<{
  org?: MomOrganization;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [data: MomOrganization];
}>();

// 表单校验规则
const rules: FormRules = {
  orgName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
  domain: [
    { required: true, message: '请输入域名', trigger: 'blur' },
    {
      pattern: /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/,
      message: '请输入正确的域名',
      trigger: 'blur',
    },
  ],
  contract: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
    },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormOrganization>({
  orgName: '',
  domain: '',
  contract: '',
  phone: '',
  email: '',
  status: 1,
  introduction: '',
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (org) {
      form.value = {
        orgName: org.orgName,
        domain: org.domain,
        contract: org.contract,
        phone: org.phone,
        email: org.email,
        status: org.status,
        introduction: org.introduction || '',
      };
    } else {
      resetForm();
    }
  }
});

// 重置表单
const resetForm = () => {
  form.value = {
    orgName: '',
    domain: '',
    contract: '',
    phone: '',
    email: '',
    status: 1,
    introduction: '',
  };
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      try {
        if (org) {
          const { errorCode, errorMsg, data } = await AdminService.updateOrg({
            ...org,
            ...form.value,
          });
          if (errorCode === 0) {
            emit('success', data!);
            ElMessage.success('修改成功');
            handleClose();
          } else {
            ElMessage.error(errorMsg || '操作失败');
          }
        } else {
          const { errorCode, errorMsg, data } = await AdminService.createOrg(form.value);
          if (errorCode === 0) {
            emit('success', data!);
            ElMessage.success('添加成功');
            handleClose();
          } else {
            ElMessage.error(errorMsg || '操作失败');
          }
        }
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error('操作失败');
      }
    }
  });
};
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="org ? '编辑机构' : '新建机构'"
    width="600px"
    @close="handleClose"
    append-to-body
    draggable
    destroy-on-close
  >
    <el-form size="large" ref="formRef" :model="form" :rules="rules" label-position="top">
      <div relative>
        <el-form-item label="机构名称" prop="orgName">
          <el-input
            :disabled="!!org"
            maxlength="30"
            v-model="form.orgName"
            placeholder="请输入机构名称"
          >
            <template #prefix>
              <img form-icon src="@/assets/image/svg/通用.svg" />
            </template>
          </el-input>
        </el-form-item>
        <div
          v-if="hasPermission(MenuPermitOrganizationManagement.机构状态)"
          absolute
          top-42
          right-10
          flex
          aic
          gap-10
        >
          <el-switch size="small" v-model="form.status" :active-value="1" :inactive-value="0" />
          <label color="[--g-text-color-7]">{{ form.status === 1 ? '启用' : '禁用' }}</label>
        </div>
      </div>

      <div flex gap-16>
        <el-form-item flex-1 label="联系人" prop="contract">
          <el-input v-model="form.contract" placeholder="请输入联系人">
            <template #prefix>
              <img src="@/assets/image/svg/联系人.svg" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item flex-1 label="电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入电话">
            <template #prefix>
              <img src="@/assets/image/svg/电话.svg" />
            </template>
          </el-input>
        </el-form-item>
      </div>

      <div flex gap-16>
        <el-form-item flex-1 label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱">
            <template #prefix>
              <img src="@/assets/image/svg/邮箱.svg" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item flex-1 label="域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入域名">
            <template #prefix>
              <img src="@/assets/image/svg/域名.svg" />
            </template>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div flex>
        <el-button flex-1 @click="handleClose">取消</el-button>
        <el-button flex-1 type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
