
const IView = require('../../component/iview').IView;
const TabList = require('../../component/tab-list').TabList;
const Tab = require('../../component/tab').Tab;
const Splitter = require('../../component/splitter').Splitter;

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '策略');
    }

    get composeTab() {
        return this.summary.tabs[0];
    }

    buildStrategyList() {

        this.top = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: this.$container.querySelector('.strategy-tabs'),
            $content: this.$container.querySelector('.strategy-list-content'),
            tabCreated: tab => {
                if (tab instanceof Tab) {
                    tab.viewEngine.registerEvent(this.systemEvent.viewContextChange, this.handleContextChange.bind(this));
                }
            }
        });

        this.top.openTab(true, '@admin/strategy-list', '策略列表');
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        if (this.contextInfo === undefined) {
            return;
        }
        this.summary.fireEventOnTab(tab, this.systemEvent.viewContextChange, this.contextInfo);
    }

    handleContextChange(context) {

        if (!this.summary) {
            console.error('strategy changed, but strategy summary is not ready', context);
            return;
        }

        this.contextInfo = context;
        this.summary.fireEventOnAllTabs(this.systemEvent.viewContextChange, context);
        this.composeTab.viewEngine.trigger('set-context-identity', context.strategyId);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {
        // this.adjustTableHeight();
    }

    refresh() {
        this.top.focusedTab.viewEngine.refresh();
    }

    exportSome() {
        this.top.tabs[0].viewEngine.exportSome();
    }

    buildSplitter() {

        let $splitter = this.$container.querySelector('.splitter-bar');
        this.splitter = new Splitter('admin-strategy', $splitter, this.handleSpliting.bind(this), { previousMinHeight: 155, nextMinHeight: 200 });
        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {

        this.top.tabs.forEach(tab => { tab.viewEngine.setHeight(previous_height); });
        this.summary.tabs.forEach(tab => { tab.viewEngine.setHeight(next_height); });
    }

    buildStrategySummaries() {

        const tabs = this.summary = new TabList({

            allowCloseTab: false,
            embeded: true,
            lazyLoad: true,
            $navi: this.$container.querySelector('.summary-tabs'),
            $content: this.$container.querySelector('.summary-content'),
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/overview/components/composition', '概览', { is4Strategy: true });
        tabs.openTab(true, '@shared/order-list', '今日订单');
        tabs.openTab(true, '@shared/position-list', '今日持仓');
        tabs.openTab(true, '@shared/exchange-list', '今日成交');
        tabs.openTab(true, '@shared/history-order-list', '历史订单');
        tabs.openTab(true, '@shared/history-position-list', '历史持仓');
        tabs.openTab(true, '@shared/history-exchange-list', '历史成交');
        tabs.openTab(true, '@shared/history-equity-list', '历史权益');
    }

    build($container) {

        this.$container = $container;
        this.buildStrategySummaries();
        this.buildStrategyList();
        this.buildSplitter();
    }
}

module.exports = View;
