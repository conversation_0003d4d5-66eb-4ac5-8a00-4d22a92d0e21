const { helper } = require("../libs/helper");

/**
 * 算法参数数据类型
 */
const AlgoParamDataTypes = {

    integer: { label: '整数', value: 1 },
    decimal: { label: '浮点', value: 2 },
    time: { label: '时间', value: 3 },
    timeRange: { label: '时间区间', value: 4 },
    text: { label: '字符串', value: 5 },
    uoption: { label: '选项', value: 6 },
};

class GaoyuAlgoHelper {
    
    static isInteger(dataType) {
        return dataType == AlgoParamDataTypes.integer.value;
    }

    static isDecimal(dataType) {
        return dataType == AlgoParamDataTypes.decimal.value;
    }

    static isTime(dataType) {
        return dataType == AlgoParamDataTypes.time.value;
    }

    static isTimeRange(dataType) {
        return dataType == AlgoParamDataTypes.timeRange.value;
    }

    static isText(dataType) {
        return dataType == AlgoParamDataTypes.text.value;
    }

    static isUserOption(dataType) {
        return dataType == AlgoParamDataTypes.uoption.value;
    }

    /**
     * 参数值上行转换（ 数值、字符串、日期、日期区间 》字符串 ）
     * @param {*} dataType 
     * @param {number | string | Date | Array<Date> | null} dataValue
     */
    static upside(dataType, dataValue) {

        if (this.isInteger(dataType) || this.isDecimal(dataType)) {
            return typeof dataValue == 'number' ? dataValue.toString() : null;
        }
        else if (this.isText(dataType)) {
            return typeof dataValue == 'string' ? dataValue.trim() : null;
        }
        else if (this.isUserOption(dataType)) {

            if (helper.isNone(dataValue)) {
                return null;
            }
            else if (typeof dataValue == 'string') {
                return dataValue.toLowerCase() == 'true' ? true: dataValue.toLowerCase() == 'false' ? false : dataValue;
            }
            else {
                return dataValue;
            }
        }
        else if (this.isTime(dataType)) {
            
            if (dataValue instanceof Date) {
                return dataValue.getTime().toString();
            }
            else if (dataValue instanceof Array && dataValue.length >= 1 && dataValue[0] instanceof Date) {
                return dataValue[0].getTime().toString();
            }
            else if (typeof dataValue == 'number' && dataValue > 999999999) {
                return dataValue.toString();
            }
            else {
                return null;
            }
        }
        else if (this.isTimeRange(dataType)) {
            
            if (dataValue instanceof Array && dataValue.length == 2) {

                let first = dataValue[0];
                let start = first instanceof Date ? first : new Date(first);
                let second = dataValue[1];
                let end = second instanceof Date ? second : new Date(second);
                return JSON.stringify([start.getTime(), end.getTime()]);
            }
            else {
                return null;
            }
        }
        else {
            return null;
        }
    }

    /**
     * 参数值下行转换（ 字符串 》数值、字符串、日期、日期区间 ）
     * @param {*} dataType 
     * @param {string | null} dataValue
     */
    static downside(dataType, dataValue) {

        const isstr = typeof dataValue == 'string' && dataValue.trim().length > 0;

        if (this.isInteger(dataType) || this.isDecimal(dataType)) {
            try { return isstr ? Number(dataValue) : null; } catch (ex) { return null; }
        }
        else if (this.isText(dataType)) {
            return isstr ? dataValue.trim() : null;
        }
        else if (this.isUserOption(dataType)) {
            return helper.isNotNone(dataValue) ? dataValue : null;
        }
        else if (this.isTime(dataType)) {
            try { return isstr ? new Date(Number(dataValue)) : null; } catch (ex) { return null; }
        }
        else if (this.isTimeRange(dataType)) {
            
            if (!isstr) {
                return null;
            }
            
            try {

                let arr = JSON.parse(dataValue);
                if (arr instanceof Array && arr.length == 2) {
                    return [new Date(arr[0]), new Date(arr[1])];
                }
                else {
                    return null;
                }
            } 
            catch (ex) {
                return null; 
            }
        }
        else {
            return null;
        }
    }

    /**
     * 格式化为时间或时间区间格式
     * @param {Date | number | Array<Date | number>} dataValue
     */
    static formatTime(dataValue) {

        const pattern = 'hh:mm:ss';

        if (typeof dataValue == 'number' && dataValue > 999999999) {
            return new Date(dataValue).format(pattern);
        }
        else if (dataValue instanceof Date) {
            return dataValue.format(pattern);
        }
        else if (dataValue instanceof Array && dataValue.length >= 1) {
            
            if (dataValue.length == 2) {

                let first = dataValue[0];
                let start = first instanceof Date ? first : new Date(first);
                let second = dataValue[1];
                let end = second instanceof Date ? second : new Date(second);
                return `${start.format('hh:mm:ss')} ~ ${end.format('hh:mm:ss')}`;
            }
            else {

                let first = dataValue[0];
                let time = first instanceof Date ? first : new Date(first);
                return time.format('hh:mm:ss');
            }
        }
        else {
            return '';
        }
    }
}

class AlgoVendor {

    constructor({ vendorId, vendorName }) {
        
        this.vendorId = vendorId;
        this.vendorName = vendorName;
    }
}

class GaoyuAlgoParam {

    constructor({ 
        label, 
        prop, 
        type, 
        remark, 
        defaultValue,
        required = false,
        display,
        uoptions = [{ label: '', value: '' }].splice(1),
    }) {
        
        /** 参数中文说明字段 */
        this.label = label;
        /** 参数英文传值字段 */
        this.prop = prop;
        /** 参数类型 */
        this.type = type;
        /** 备注 */
        this.remark = remark;
        /** 是否必填 */
        this.required = !!required;
        /** 默认值 */
        this.defaultValue = GaoyuAlgoHelper.downside(type, defaultValue);
        /** 参数可选项 */
        this.uoptions = Array.isArray(uoptions) ? uoptions : [];
        /** 是否展示出当前输入项 */
        this.display = display !== false;

        /** 是否正在添加可选项 */
        this.adding = false;
        /** 可选项文字 */
        this.optionLabel = '';
        /** 可选项取值 */
        this.optionValue = '';
    }
}

class GaoyuAlgo {

    constructor({ 
        id, 
        name, 
        externalId, 
        vendorId, 
        brokerId, 
        remark, 
        params = [],
        strategyType,
        userId,
        createTime,
        updateTime,
    }) {

        /** id */
        this.id = id || null;
        /** 外部ID */
        this.externalId = externalId;
        /** 名称 */
        this.name = name;
        /** 算法开发商 */
        this.vendorId = vendorId;
        /** 券商 */
        this.brokerId = brokerId;
        /** 算法说明 */
        this.remark = remark;
        /** 算法参数 */
        this.params = params instanceof Array ? params.map(x => new GaoyuAlgoParam(x)) : [];
        /** 算法类型 */
        this.strategyType = strategyType || 0;
        this.userId = userId;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
}

class GaoyuAlgoParamDefinition {

    constructor({ 
        id, 
        label, 
        prop, 
        dataType, 
        defaultValue,
        required = false,
        remark, 
        userId,
        uoptions = [{ label: '', value: '' }].splice(1),
    }) {

        this.id = id || null;
        /** 参数中文名 */
        this.label = label || null;
        /** 参数英文名 */
        this.prop = prop || null;
        /** 参数填写数据类型 */
        this.dataType = dataType || null;
        /** 参数默认值 */
        this.defaultValue = GaoyuAlgoHelper.downside(dataType, defaultValue);
        /** 是否必填 */
        this.required = !!required;
        /** 参数可选项 */
        this.uoptions = Array.isArray(uoptions) ? uoptions : [];
        /** 参数描述 */
        this.remark = remark || null;
        /** 创建用户ID */
        this.userId = userId;
    }
}

class GaoyuAlgoGroup {

    /**
     * @param {String} name 
     * @param {Array<GaoyuAlgo>} algoes 
     */
    constructor(name, algoes) {
        
        this.name = name;
        this.algoes = algoes;
    }
}

module.exports = {

    AlgoParamDataTypes,
    GaoyuAlgoHelper,
    AlgoVendor,
    GaoyuAlgo,
    GaoyuAlgoParam,
    GaoyuAlgoParamDefinition,
    GaoyuAlgoGroup,
};