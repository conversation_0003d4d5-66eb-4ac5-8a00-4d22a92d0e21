const { TypicalDataView } = require('../../classcial/typical-data-view');
const { SysStrategy } = require('../../../../model/sys-strategy');
const { repoStrategy } = require('../../../../repository/strategy');

class StrategiesView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '策略列表');
        this.registerEvent('refresh', this.refresh.bind(this));
    }

    createTable(options) {

        if (this.userInfo.isCounselor) {

            let $manage_cols = this.$table.querySelectorAll('th[condition="manage-only"]');
            $manage_cols.forEach($col => { $col.remove(); });
        }
        
        super.createTable(options);
    }

    /**
     * @param {SysStrategy} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    /**
     * @param {SysStrategy} record 
     */
    testRecords(record) {

        return this.tableObj.matchKeywords(record) 
            || this.testPy(record.fundName, this.states.keywords)
            || this.testPy(record.strategyName, this.states.keywords);
    }

    async requestRecords() {
        var resp = await repoStrategy.getAll();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`策略列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data || [];
        records.forEach(item => {

            delete item.strategyAccounts;
            delete item.users;
        });

        var strategies = records.map(item => new SysStrategy(item));
        this.tableObj.refill(strategies);
        if (strategies.length > 0) {
            this.tableObj.selectNextRow();
        }
        else {
            this.trigger('selected-one-strategy', null);
        }
    }

    /**
     * @param {SysStrategy} record
     * @param {Boolean} status 
     */
    formatClosePosition(record, status) {
        return '<button class="danger" event.onclick="confirmCloseAll">一键平仓</button>';
    }

    /**
     * @param {SysStrategy} record
     */
    confirmCloseAll(record) {

        this.interaction.showConfirm({

            title: '一键平仓确认',
            message: '一键平仓策略：' + record.strategyName,
            confirmed: () => {
                console.log({ strategyId: record.id }, record);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.closePosition, { strategyId: record.id });
                this.interaction.showSuccess(`平仓请求已发出，目标：${record.strategyName}`);
            },
        });
    }

    /**
     * @param {SysStrategy} record
     */
    handleRowSelected(record) {
        this.trigger('selected-one-strategy', record);
    }

    build($container, options) {

        super.build($container, options, { tableName: 'smt-os', heightOffset: 92 });
        this.requestRecords();
    }
}

module.exports = { StrategiesView };