<script setup lang="tsx">
import { computed, onMounted, ref, shallowRef } from 'vue';
import { Search, SortDown, SortUp } from '@element-plus/icons-vue';
import { compare } from '@/script';
import type { ProductInfo } from '@/types';
import { Repos } from '../../../../xtrade-sdk/dist';

// 定义emit事件
const emit = defineEmits<{
  select: [product: ProductInfo];
}>();

// 列定义
const columns: Array<{
  key: keyof ProductInfo | 'actions';
  title: string;
  width: string;
  sortable: boolean;
}> = [
  {
    key: 'fundName',
    title: '产品名称/代码',
    width: '40%',
    sortable: true,
  },
  {
    key: 'actions',
    title: '操作',
    width: '20%',
    sortable: false,
  },
];

// 产品列表
const records = shallowRef<ProductInfo[]>([]);
// 当前选中行
const activeRow = shallowRef<ProductInfo | null>(null);
// 搜索关键词
const searchQuery = ref('');

// 排序状态
const sortKey = ref<keyof ProductInfo | ''>('');
const sortOrder = ref('desc'); // 'asc' 或 'desc'

// 过滤后的产品列表
const filteredProducts = computed(() => {
  let result = [...records.value];

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      product =>
        product.fundName.toLowerCase().includes(query) || String(product.id).includes(query),
    );
  }

  // 排序
  if (sortKey.value) {
    result.sort((a: any, b: any) =>
      compare(a[sortKey.value], b[sortKey.value], sortOrder.value == 'asc'),
    );
  }

  return result;
});

// 初始化
onMounted(() => {
  fetchProducts();
});

// 渲染状态
const renderStatus = (closedFlag: boolean) => {
  return !closedFlag ? '运行中' : '已清盘';
};

const repoInstance = new Repos.GovernanceRepo();

// 处理排序
const handleSort = (key: keyof ProductInfo) => {
  if (sortKey.value === key) {
    // 如果点击的是当前排序列，则切换排序方向
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    // 如果点击的是新列，则设置为该列降序排序
    sortKey.value = key;
    sortOrder.value = 'desc';
  }
};

// 获取产品列表
const fetchProducts = async () => {
  const data = (await repoInstance.QueryProducts()).data || [];
  records.value = data;

  // 默认选中第一个产品
  if (data.length > 0 && !activeRow.value) {
    selectProduct(data[0]);
  }
};

// 选择产品
const selectProduct = (rowd: ProductInfo) => {
  activeRow.value = rowd;
  emit('select', rowd);
};
</script>

<template>
  <div w-550 mx-8 flex="~ col" h-full>
    <!-- 搜索行 -->
    <div mt-10 pl-4 flex aic jcsb h-36>
      <div flex aic>
        <el-input
          :prefix-icon="Search"
          class="w-150!"
          placeholder="搜索产品"
          v-model="searchQuery"
          clearable
        ></el-input>
      </div>
      <el-button @click="fetchProducts" size="small" color="var(--g-primary)">刷新</el-button>
    </div>

    <!-- 表格头部 -->
    <div pl-12 flex h-32 aic>
      <div
        v-for="col in columns"
        :key="col.key"
        :style="{ width: col.width }"
        flex
        aic
        cursor-pointer
        @click="col.sortable && handleSort(col.key as keyof ProductInfo)"
      >
        <span>{{ col.title }}</span>
        <el-icon v-if="col.sortable && sortKey === col.key" ml-2>
          <component :is="sortOrder === 'asc' ? SortUp : SortDown" />
        </el-icon>
      </div>
    </div>

    <!-- 表格内容 -->
    <div flex-1 min-h-1>
      <el-scrollbar>
        <div
          class="product-item"
          v-for="product in filteredProducts"
          :key="product.id"
          :class="{ active: activeRow?.id === product.id }"
          flex
          aic
          mb-4
          h-60
          border-rd-4
          transition-all-200
          bg="[--g-panel-bg2]"
          hover="bg-[--g-panel-bg3]"
          cursor-pointer
          box-shadow="0 4px 4px 0 rgba(0, 0, 0, 0.4)"
          @click="selectProduct(product)"
        >
          <!-- 产品名称/代码 -->
          <div pl-12 :style="{ width: columns[1].width }" flex="~ col">
            <div c-white mb-2>{{ product.fundName }}</div>
            <div flex aic>
              <div>{{ product.id }}</div>
              <span ml-5 px-4 border-rd-2 bg="[--g-border]">
                {{ renderStatus(product.closedFlag) }}
              </span>
            </div>
          </div>

          <!-- 操作 -->
          <div class="table-cell" :style="{ width: columns[1].width }" px-10 flex aic>
            <el-button size="small" color="var(--g-primary)" @click.stop>一键平仓</el-button>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<style scoped>
.product-item {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.4);
  &.active {
    background-color: var(--g-panel-bg3);
  }
}
</style>
