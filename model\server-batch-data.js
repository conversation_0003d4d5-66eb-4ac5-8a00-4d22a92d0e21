
/**
 * 服务器批量数据（订单 | 持仓 | 成交）
 */

class ServerBatchData {

    constructor(struc) {

        /** 仅当TCP推送全量的，订单 | 持仓 | 成交，具有该字段，其他场合自行维护 */
        this.identityId = struc.identityId;
        this.parentOrderId = struc.parentOrderId;
        let accountIds = struc.accountIds;
        this.accountIds = accountIds instanceof Array ? accountIds : [];
        this.pageSize = struc.pageSize;
        this.pageIdx = struc.pageNo;
        this.pages = struc.totalPages;
        this.total = struc.totalSize;
        /** 排序字段信息 */
        this.sorting = struc.sort;
        let data_list = struc.contents || struc.list;
        this.contents = data_list instanceof Array ? data_list : [];
    }
}

module.exports = { ServerBatchData };