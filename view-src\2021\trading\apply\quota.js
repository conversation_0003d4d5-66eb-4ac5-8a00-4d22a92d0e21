const axios = require('axios').default;
const repoApply = require('../../../../repository/apply').repoApply;
const interaction = require('../../../../libs/interaction').interaction;

module.exports = class Quota {
    async getComponent() {
        let rsp = await axios.get(__filename.split('.')[0] + '.html');
        return {
            template: rsp.data,
            components: {},
            data() {
                return {
                    accountQuotaInfo: [],
                    loading: true,
                };
            },

            created() {
                this.initQuota();
            },

            methods: {
                async initQuota() {
                    let respAccountQuotaInfo = await repoApply.getAccountQuota();
                    if (respAccountQuotaInfo.errorCode != 0) return interaction.showHttpError(`请求额度失败：${resp.errorCode}/${resp.errorMsg}`);
                    this.accountQuotaInfo = respAccountQuotaInfo.data || [];
                    this.loading = false;
                },
            },
        };
    }
};
