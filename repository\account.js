const { FutureMarginRate, V3StandardAccount, AccountDetail, ChildAccountBasic } = require('../model/account');
const { http } = require('../libs/http');
const { ChildAccountDetail } = require('../model/strategy');

class AccountRepository {

    /**
     * @returns {{ errorCode, errorMsg, data: Array<V3StandardAccount> }}
     */
    getAll(condition) {
        return new Promise((resolve, reject) => {
            http
                .get('/account', {
                    params: {
                        fund_id: condition ? condition.fund_id : '',
                        strategy_id: condition ? condition.strategy_id : '',
                    },
                })
                .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    createAccount(account) {
        return new Promise((resolve, reject) => {
            http.post('/account', account, { headers: { 'Content-Type': 'application/json' } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    updateAccount(account) {
        return new Promise((resolve, reject) => {
            http.put('/account', account, { headers: { 'Content-Type': 'application/json' } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    setAccountPreRiskControl(account_id, preRiskControl) {
        return new Promise((resolve, reject) => {
            http.put('/account/updatePreRiskControl', null, { params: { account_id, preRiskControl } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    setAccountAutoApprove(account_id, autoApprove) {
        return new Promise((resolve, reject) => {
            http.put('/account/autoApprove', null, { params: { account_id, autoApprove } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    removeAccount(accountId) {
        return new Promise((resolve, reject) => {
            http.delete('/account', { params: { account_id: accountId } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    getSimpleAccountList(filter_bind, org_id) {
        return new Promise((resolve, reject) => {
            http.get('/account/simple', { params: { filterBind: filter_bind, org_id } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    getUnboundAccounts(org_id) {
        return new Promise((resolve, reject) => {
            http.get('/account/useful/account', { params: { org_id } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    queryUserScopeAccounts() {

        return new Promise((resolve, reject) => {
            http.get('../v4/account/simple').then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 查询账号交易某个合约的保证金
     * @returns {{ errorCode, errorMsg, data: FutureMarginRate }}
     */
    getInstrumentMargin(account_id, instrument) {
        return new Promise((resolve, reject) => {
            http.get('/account/margin', { params: { account_id, instrument }}).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 创建一个子账号
     * @param {ChildAccountBasic} account
     */
    createChildAccount(account) {
        return new Promise((resolve, reject) => {
            http.post('/account/child', account).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 更新一个子账号
     * @param {ChildAccountBasic} account
     */
    updateChildAccount(account) {
        return new Promise((resolve, reject) => {
            http.put('/account/child', account).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    liquidationAccount(accountId) {
        return new Promise((resolve, reject) => {
            http.put('/account/settle', {}, { params: { account_id: accountId } })
                .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    initAccount(accountId) {
        return new Promise((resolve, reject) => {
            http.post('/account/init', {}, { params: { account_id: accountId } })
                .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    getAccountDetail(accountId) {
        return new Promise((resolve, reject) => {
            http.get('/account/detail', { params: { accountId } })
                .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: Array<AccountDetail> }}
     */
    batchGetAccountCash(assetType) {

        return new Promise((resolve, reject) => {
            http.get('/account/batch', { params: { asset_type: assetType } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: Array<AccountDetail> }}
     */
    batchGetAccountDetail(accountIds) {
        return new Promise((resolve, reject) => {
            http.post('/account/batch', accountIds)
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    getAccountFee(accountId) {
        return new Promise((resolve, reject) => {
            http.get('/account/fee', { params: { account_id: accountId } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    saveAccountFee(commission) {
        return new Promise((resolve, reject) => {
            http.post('/account/fee', commission)
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    updateAccountFee(commission) {
        return new Promise((resolve, reject) => {
            http.put('/account/fee', commission)
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    updatePreBalance(params) {
        return new Promise((resolve, reject) => {
            http.post('/account/prebalance', undefined, { params })
                .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    bindTerminal(account_id, terminalIds) {
        terminalIds = terminalIds || [];
        return new Promise((resolve, reject) => {
            http.post(`/account/bind-terminal?account_id=${account_id}`, terminalIds, { headers: { 'Content-Type': 'application/json' } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    overlapFinance({ accountId, overlap }) {
        return new Promise((resolve, reject) => {
            http.post('/account/overlap/detail', {}, { params: { account_id: accountId, overlap: overlap }})
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    availableCheck({ accountId, availableCheck }) {
        return new Promise((resolve, reject) => {
            http.put('/account/availablecheck', {}, { params: { account_id: accountId, available_check: availableCheck }})
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    bindFund(account_id, fund_id) {
        return new Promise((resolve, reject) => {
            http.post('/account/fund-fund', { account_id, fund_id })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: Array<ChildAccountDetail> }}
     */
    getUnboundChildAccounts(strategy_id) {
        return new Promise((resolve, reject) => {
            http.get('/account/child/nobind', { params: { strategy_id } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    bindChildAccounts(strategy_id, child_ids) {
        return new Promise((resolve, reject) => {
            http.put('/account/child/bind', {}, { params: { strategy_id, child_ids: child_ids.join(',') } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    disconnect(accountId) {
        return new Promise((resolve, reject) => {
            http.put('/account/logout', {}, { params: { account_id: accountId } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    connect(accountId) {
        return new Promise((resolve, reject) => {
            http.get('/account/connection_test', { params: { account_id: accountId }})
                .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    getAccountDetailInfo(condition) {
        return new Promise((resolve, reject) => {
            http.get('../v4/account/detail', { params: { 
                identity_id: condition.identity_id,
                user_id: condition.userId,
            } })
            .then( (resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    getSzAccountDetailInfo(condition) {
        return new Promise((resolve, reject) => {
            http.get(`${httpRequest.exAddr.resetfulSzBaseUrl}/../v4/account/detail`, { params: {
                identity_id: condition.identity_id, 
                user_id: condition.userId,
            }})
            .then((resp) => { resolve(resp.data); }, (error) => { reject(error); });
        });
    }

    /**
     * @param {{ identity_id, account_id, userId }} condition 
     */
    qdetail(condition) {
        return new Promise((resolve, reject) => {
            http.get('../v4/account/detail', { params: {
                identity_id: condition.identity_id,
                account_id: condition.account_id,
                user_id: condition.userId,
                pageSize: 999999,
                pageNo: 1,
                isSummary: false,
            }})
            .then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); }
            );
        });
    }

    /**
     * 在普通和急速柜台间进行转账
     */
    transferBetweenCounters(data) {
        return new Promise((resolve, reject) => {
            http.post('account/transFundsOther', {}, { params: data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); }
            );
        });
    }
}

module.exports = { repoAccount: new AccountRepository() };
