const { http } = require('../libs/http');
const pako = require('pako');

class BinaryRepo {

    constructor() {

        this.http = http;
        this.headers = {

            'Content-Type': 'application/json,charset=utf-8',
            'Accept': 'application/octet-stream;charset=UTF-8',
        };
        this.responseType = 'arraybuffer';
    }

    /**
     * 将二进制数据流转换成JSON格式的原始数据
     * @param {*} resp 
     */
    translate(resp) {

        /**
         * 将数据流转化为字符串, 兼容汉字
         * @param {Array} array 
         */
        function Utf8ArrayToStr(array) {

            var out = '', idx = 0, len = array.length, char1, char2, char3, char4;
            while (idx < len) {

                char1 = array[idx++];

                /**
                 * 当单个字节时, 最大值 '01111111', 最小值 '00000000' 右移四位 07, 00
                 * 当两个字节时, 最大值 '11011111', 最小值 '11000000' 右移四位 13, 12
                 * 当三个字节时, 最大值 '11101111', 最小值 '11100000' 右移四位 14, 14
                 */
                
                if (char1 >> 4 <= 7) {
                    out += String.fromCharCode(char1);
                } 
                else if (char1 >> 4 == 12 || char1 >> 4 == 13) {

                    char2 = array[idx++];
                    out += String.fromCharCode(((char1 & 0x1F) << 6) | (char2 & 0x3F));
                } 
                else if (char1 >> 4 == 14) {

                    char2 = array[idx++];
                    char3 = array[idx++];
                    char4 = ((char1 & 0x0F) << 12) | ((char2 & 0x3F) << 6);
                    out += String.fromCharCode(char4 | ((char3 & 0x3F) << 0));
                }
            }

            return out;
        };
        
        var compressed = new Uint8Array(resp.data);
        var uncompressed = pako.ungzip(compressed);
        var raw = JSON.parse(Utf8ArrayToStr(uncompressed));
        return raw;
    }
}

module.exports = { BinaryRepo };
