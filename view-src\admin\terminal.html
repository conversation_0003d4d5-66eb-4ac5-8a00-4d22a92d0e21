<div class="terminal-view-root">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">

            <div class="s-typical-toolbar">

                <el-button type="primary" @click="addTerminal" size="mini">
                    <span class="el-icon-plus"></span> 创建终端
                </el-button>

                <el-input v-model="searching.value" class="s-mgl-10" style="width: 140px;" placeholder="请输入关键词" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>

            </div>

            <data-tables layout="pagination,table" :filters="filters" table-label="终端管理"
                class="s-searchable-table" :default-sort="{prop: 'id', order: 'descending'}"
                :search-def="searchDef" :pagination-props="paginationDef" :table-props="tableProps" :data="terminalList"
                border stripe>
                <el-table-column key="terminalManagementIndex" prop="index" type="index" label="序号" width="80"
                    align="center"></el-table-column>
                <el-table-column sortable key="terminalManagementTerminalName" prop="terminalName" label="终端名称"
                    min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column key="terminalManagementInterfaceType" prop="interfaceType" label="接口类型"
                    min-width="90" sortable="custom">
                    <template slot-scope="scope">
                        <div v-html="formatType(scope.row)"></div>
                    </template>
                </el-table-column>
                <el-table-column key="terminalManagementDescription" prop="description" label="描述信息"
                    min-width="180" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{scope.row.description || '暂无描述信息'}}</span>
                    </template>
                </el-table-column>
                <el-table-column key="terminalManagementStatus" prop="status" label="状态" width="100" align="center" sortable="custom">
                    <template slot-scope='props'>
                        <el-switch v-model="props.row.status" @change="switchEnableStatus(props.row)" :active-value="1" :inactive-value="0"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column key="terminalManagementOperation" prop="operation" width="90" label="操作"
                    class-name="s-col-oper" fixed="right">
                    <template slot-scope="props">
                        <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button el-icon-edit" @click="editRow(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button el-icon-delete s-color-red" @click="delRecord(props.row)"></a>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </data-tables>
            
            <div class="table-4-terminal-list-export" style="display: none;">
                <table>
                    <tr>
                        <th label="终端名称" prop="terminalName"></th>
                        <th label="接口类型" prop="interfaceType" formatter="formatTerminalInterfaceTypeText"></th>
                        <th label="描述信息" prop="description"></th>
                        <th label="状态" prop="status" formatter="formatActivation"></th>
                    </tr>
                </table>
            </div>

            <el-dialog v-drag width="600px" class="terminal-dialog" :visible="dialog.visible" :title="title" :show-close="false"
                :close-on-click-modal="false" :close-on-press-escape="false">
                <el-form class="terminal-form" :model="terminalModel" ref="terminalModel" :rules="rules"
                    label-width="100px">
                    <el-form-item label="终端名称：" prop="terminalName">
                        <el-input :disabled="terminalModel.id != ''" v-model.trim="terminalModel.terminalName" :maxlength="30" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="描述信息：">
                        <el-input v-model.trim="terminalModel.description" placeholder="请输入" type="textarea" :maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item label="密码：" prop="pwd">
                        <el-input v-model.trim="terminalModel.pwd" :type=" isShowPassword ? 'password' : 'input' " :maxlength="20" placeholder="请输入">
                            <i title="查看密码" slot="suffix" class="el-input__icon s-cp iconfont size-16"
                                :class="{'icon-yincang': !isShowPassword, 'icon-xianshi': isShowPassword }"
                                @click="changeInputMode"></i>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="类型：" prop="interfaceType">
                        <el-select v-model="terminalModel.interfaceType" filterable style="width: 100%">
                            <el-option v-for="(item,index) in interfaceTypeOptions" :value="item.value"
                                :key="item.text+index" :label="item.text"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态：" prop="status">
                        <el-radio-group v-model="terminalModel.status">
                            <el-radio :label="1">启用</el-radio>
                            <el-radio :label="0">禁用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button type="primary" @click="saveConfirm" size="small">确定</el-button>
                    <el-button @click="handleClose" size="small">取消</el-button>
                </div>
            </el-dialog>
        </div>
    </template>
</div>