import { SocketDataPackage } from '../types/data-package';

/**
 * 消息数据包编码解码类
 */
export class MessageResolver {

    /**
     * 是否启用数据包加密
     */
    static get IS_ENCRYPTION_ENABLED() {
        return false;
    }

    /**
     * 32位整形占用字节
     */
    static get INT32_TAKEN_BYTES() {
        return 4;
    }

    /**
     * 数据包头部信息占用字节长度
     */
    static get HeaderSize() {
        return 20;
    }

    /**
     * 字符编码
     */
    static get ContentEncoding() {
        return 'utf8' as any;
    }

    constructor() {
        //
    }

    /**
     * 编码要发送到服务器的消息
     */
    encode(message: SocketDataPackage) {
        throw new Error('not implemented');
    }

    /**
     * 解码从服务器收到的消息
     */
    decode(message: Buffer | ArrayBufferLike): SocketDataPackage[] {
        throw new Error('not implemented');
    }
}
