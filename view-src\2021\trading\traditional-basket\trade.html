<div class="trade-form bktform s-full-height">
	<div class="trade-form-inner bktform-internal themed-box s-scroll-bar s-full-height">
		<div class="xtcontainer s-border-box s-full-height">

			<template>
				<div class="xtheader themed-header">
					<span>篮子交易</span>
				</div>
			</template>

			<template>

				<div class="form-external s-unselectable">
	
					<form class="xtform">
	
						<div class="direction-row">

							<el-radio-group v-model="uistates.direction" 
											@change="handleDirectionChange" 
											class="s-full-width">

								<el-radio-button v-for="(item, item_idx) in directions"
												 :key="item_idx"
												 :label="item.code"
												 :style="{ width: 100 / directions.length + '%' }">{{ item.mean }}</el-radio-button>
							</el-radio-group>
							
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">篮子</span>
							<el-input v-model="uistates.instrumentName" readonly></el-input>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">下单方式</span>
							<el-select v-model="uistates.method" @change="handleMethodChange">
								<el-option v-for="(item, item_idx) in methods"
										   :key="item_idx"
										   :value="item.code"
										   :label="item.mean"></el-option>
							</el-select>
						</div>

						<div class="xtinput shorten">
							<span class="unit-txt themed-color" title="单位">{{ theMethod.unit }}</span>
							<span class="xtlabel themed-color">{{ theMethod.label }}</span>
							<el-input placeholder="0" v-model.number="uistates.scale" @change="handleScaleChange"></el-input>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">跟盘价</span>
							<el-select v-model="uistates.stage" @change="handleStageChange">
								<el-option v-for="(item, item_idx) in stages"
										   :key="item_idx"
										   :value="item.code"
										   :label="item.mean"></el-option>
							</el-select>
						</div>

						<div class="xtinput">
							<span class="s-mgr-30 unit-txt themed-color" title="单位">元</span>
							<span class="xtlabel themed-color">偏移量</span>
							<el-input-number placeholder="0"
											 v-model="uistates.priceOffset"
											 :min="-999999" 
											 :max="999999"
											 :step="uistates.priceStep"
											 @change="handleOffsetChange"></el-input-number>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">剔除</span>
							<span class="trade-options">
								<el-checkbox v-model="exclude.suspend">停牌</el-checkbox>
								<el-checkbox v-model="exclude.cash">现金替代</el-checkbox>
								<br>
								<el-checkbox v-model="exclude.ceiling">涨停</el-checkbox>
								<el-checkbox v-model="exclude.floor">跌停</el-checkbox>
							</span>
						</div>
	
						<div class="xtinput button-row basket-button-row">
							<el-button type="primary" @click="hope2Preview" class="s-mgb-10">预览试算</el-button>
							<el-button v-if="isBuy" type="danger" @click="hope2Entrust">买入</el-button>
							<el-button v-else-if="isSell" type="success" @click="hope2Entrust">卖出</el-button>
							<el-button v-else type="primary" @click="hope2Entrust">调仓</el-button>
						</div>
	
					</form>

				</div>

			</template>
		</div>		
	</div>
</div>