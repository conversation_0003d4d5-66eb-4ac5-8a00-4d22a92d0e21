<script setup lang="ts">
import VirtualizedTable from '../../common/VirtualizedTable.vue';
import { computed, ref, useTemplateRef, watch } from 'vue';
import { Repos } from '../../../../../xtrade-sdk/dist';
import type { ColumnDefinition, ProductInfo, RowAction } from '@/types';
import { remove } from '@/script';
import { ElMessage } from 'element-plus';

const props = defineProps<{
  // 至少选择多少个
  least?: number;
  ison: boolean;
  selectedIds: string[];
}>();

const columns: ColumnDefinition<ProductInfo> = [
  { key: 'fundId', title: '产品ID', width: 150, sortable: true },
  { key: 'fundName', title: '产品名称', width: 200, sortable: true },
  { key: 'fundManager', title: '产品经理', width: 120, sortable: true },
  { key: 'establishedDay', title: '成立日期', width: 120, sortable: true },
  { key: 'fundOrganization', title: '产品机构', width: 120, sortable: true },
];

const rowActions: RowAction<ProductInfo>[] = [
  {
    label: '移除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function deleteRow(row: ProductInfo) {
  remove(checkeds.value, x => x === row);
}

const $tableRef = useTemplateRef('$tableRef');
const repoGovenInstance = new Repos.GovernanceRepo();
const targetIds = ref<string[]>([]);
const records = ref<ProductInfo[]>([]);
const checkeds = ref<ProductInfo[]>([]);

watch(
  () => props.ison,
  () => {
    if (props.ison) {
      targetIds.value = [...props.selectedIds];
      request();
    }
  },
  { immediate: true },
);

const selectedId = ref<string>('');
const availables = computed(() => {
  return records.value.filter(x => !checkeds.value.some(y => y.id == x.id));
});

function add2Table() {
  const matched = checkeds.value.find(x => x.id == selectedId.value);
  if (!matched) {
    const matched2 = records.value.find(x => x.id == selectedId.value);
    if (matched2) {
      checkeds.value.push(matched2);
    }
  }

  // reset to empty
  selectedId.value = '';
}

function deleteChecks() {
  const selectedRows = $tableRef.value?.selectedRows || [];

  if (selectedRows.length === 0) {
    ElMessage.warning('请选择');
    return;
  }

  selectedRows.forEach(row => {
    deleteRow(row);
  });
}

async function request() {
  const list = (await repoGovenInstance.QueryProducts()).data || [];
  records.value = list;
  checkeds.value = list.filter(x => targetIds.value.some(id => id == x.id));
}

function getSelectedRows() {
  return checkeds.value;
}

defineExpose({
  getSelectedRows,
});
</script>

<template>
  <div class="choose-panel" h-full flex flex-col>
    <div class="waiting-list" h-40 flex aic gap-10>
      <el-select
        v-model="selectedId"
        @change="add2Table"
        placeholder="请选择要添加的产品"
        style="width: 300px"
        filterable
      >
        <el-option
          v-for="item in availables"
          :key="item.id"
          :label="item.fundName"
          :value="item.id"
        ></el-option>
      </el-select>
      <span class="c-[var(--g-text-color-1)]">剩余可选数量 = {{ availables.length }}</span>
    </div>
    <div flex-1 of-y-hidden>
      <VirtualizedTable
        ref="$tableRef"
        identity="id"
        style="height: 100%"
        :columns="columns"
        :data="checkeds"
        :row-actions="rowActions"
        :row-action-width="80"
        select
        fixed
      >
        <template #actions>
          <el-button
            type="primary"
            @click="deleteChecks"
            size="small"
            :disabled="!$tableRef?.selectedRows.length"
          >
            <i class="iconfont icon-remove"></i>
            <span pl-5>删除勾选</span>
          </el-button>
        </template>
      </VirtualizedTable>
    </div>
  </div>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
