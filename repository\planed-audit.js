const { http } = require('../libs/http');
const { PlanTradeMotherOrderResponse, PlanTradeChildOrderResponse } = require('../model/planed-audit');

class PlanedAuditRepository {
    /**
     * @returns {PlanTradeMotherOrderResponse}
     */
    queryMotherOrders() {

        return new Promise((resolve, reject) => {
            http.post('../yx/display/queryOrderParent').then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    /**
     * @returns {PlanTradeChildOrderResponse}
     */
    queryChildOrders(aopId) {

        return new Promise((resolve, reject) => {
            http.post('../yx/display/queryOrderDetail', {}, { params: { aopId } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }
}

module.exports = {
    PlanedAuditRepository,
};
