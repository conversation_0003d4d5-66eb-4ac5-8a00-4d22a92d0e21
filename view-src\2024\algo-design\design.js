const drag = require('../../../directives/drag');
const { IView } = require('../../../component/iview');
const { AlgoParamDataTypes, GaoyuAlgoHelper, GaoyuAlgoParamDefinition } = require('../../../model/algo-vendor');
const { repoAlgo } = require('../../../repository/algorithm');

class GaoyuAlgoParamDefinitionForm extends GaoyuAlgoParamDefinition {

    constructor(struc) {

        super(struc);
        this.optionLabel = '';
        this.optionValue = '';
    }
}

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '算法参数设计');
    }

    get $form() {
        return this.vapp.$refs.form;
    }

    createApp() {

        this.dialog = {

            visible: true, 
            isEditing: true,
            adding: { visible: false },
        };

        this.dataTypes = AlgoParamDataTypes;
        this.dataTypeList = Object.values(AlgoParamDataTypes);
        this.parameters = [new GaoyuAlgoParamDefinition({})].splice(1);

        this.form = new GaoyuAlgoParamDefinitionForm({

            dataType: this.dataTypes.integer.value,
            userId: this.userInfo.userId,
        });

        this.rules = {

            dataType: [{ required: true, message: '请选择参数数据类型' }],
            label: [{ required: true, message: '请输入参数中文名称' }],
            required: [{ required: true, message: '请指定是否必填' }],
            optionLabel: [{ required: true, message: '请输入选项文字' }],
            optionValue: [{ required: true, message: '请输入选项取值' }],
            prop: [
                { required: true, message: '请输入参数英文名称' },
                { pattern: /^[A-Za-z0-9-_]+$/, message: '英文名称只能包含英文字母，数字和下划线' },
            ],
        };

        this.vapp = new Vue({

            el: this.$design,
            directives: { drag },
            data: {
                
                dialog: this.dialog,
                dataTypes: this.dataTypes,
                form: this.form,
                rules: this.rules,
                parameters: this.parameters,
                uoptions: [{ label: '', value: '' }].splice(1),
            },
            methods: this.helper.fakeVueInsMethod(this, [
                
                this.isInteger,
                this.isDecimal,
                this.isTime,
                this.isTimeRange,
                this.isText,
                this.isUserOption,
                this.handleDataTypeChange,
                this.addNewOption,
                this.confirmNewOption,
                this.cancelNewOption,
                this.deleteOption,
                this.save,
                this.close,
                this.go2table,
                this.goback,
                this.refresh,
                this.edit,
                this.remove,
                this.formatDataType,
                this.formatDefaultValue,
            ]),
        });
    }

    isInteger(dataType) {
        return GaoyuAlgoHelper.isInteger(dataType);
    }

    isDecimal(dataType) {
        return GaoyuAlgoHelper.isDecimal(dataType);
    }

    isTime(dataType) {
        return GaoyuAlgoHelper.isTime(dataType);
    }

    isTimeRange(dataType) {
        return GaoyuAlgoHelper.isTimeRange(dataType);
    }

    isText(dataType) {
        return GaoyuAlgoHelper.isText(dataType);
    }

    isUserOption(dataType) {
        return GaoyuAlgoHelper.isUserOption(dataType);
    }

    clear() {

        const ref = this.form;
        ref.id = null;
        ref.label = null;
        ref.prop = null;
        ref.dataType = this.dataTypes.integer.value;
        ref.defaultValue = null;
        ref.required = false;
        ref.uoptions = [];
        ref.remark = null;
        ref.userId = this.userInfo.userId;
        ref.required = false;
    }

    addNewOption() {
        this.toggleShowAddOption();
    }

    toggleShowAddOption() {

        this.dialog.adding.visible = !this.dialog.adding.visible;
        this.form.optionLabel = '';
        this.form.optionValue = '';
    }

    cancelNewOption() {
        this.toggleShowAddOption();
    }

    confirmNewOption() {
        
        const ref = this.form;
        if (this.helper.isNone(ref.optionLabel) || this.helper.isNone(ref.optionValue)) {
            return this.interaction.showError('选项文字或取值，缺失');
        }
        else if (ref.uoptions.some(x => x.label == ref.optionLabel || x.value == ref.optionValue)) {
            return this.interaction.showError('选择文字或取值，存在重复');
        }
        else {

            let item = { label: ref.optionLabel, value: ref.optionValue };
            if (typeof item.value == 'string' && item.value.toLowerCase() == 'true') {
                item.value = true;
            }
            else if (typeof item.value == 'string' && item.value.toLowerCase() == 'false') {
                item.value = false;
            }

            ref.uoptions.push(item);
            if (this.helper.isNone(ref.defaultValue)) {
                ref.defaultValue = item.value;
            }

            this.toggleShowAddOption();
        }
    }
    
    deleteOption(value) {

        const ref = this.form;
        ref.uoptions.remove(x => x.value == value);
        let choosedSome = this.helper.isNotNone(ref.defaultValue);
        let isChoosedOk = ref.uoptions.some(x => x.value == ref.defaultValue);

        if (choosedSome && !isChoosedOk) {
            ref.defaultValue = null;
        }
    }

    handleDataTypeChange() {
        this.form.defaultValue = null;
    }
    
    save() {

        this.$form.validate(isok => {

            if (isok) {
                this.submit();
            }
            else {
                this.interaction.showError('请按照提示进行输入');
            }
        });
    }

    /**
     * @param {GaoyuAlgoParamDefinitionForm} item 
     */
    typeds(item) {
        return item;
    }

    async submit() {

        const cloned = this.typeds(this.helper.deepClone(this.form));
        delete cloned.optionLabel;
        delete cloned.optionValue;
        cloned.defaultValue = GaoyuAlgoHelper.upside(cloned.dataType, cloned.defaultValue);
        let resp = await repoAlgo.createAlgoParam(cloned);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.clear();
            this.interaction.showSuccess('已保存');
            this.requestParams();
        }
        else {
            this.interaction.showError(`保存失败：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {GaoyuAlgoParamDefinition} item 
     */
    async remove(item) {
        
        let resp = await repoAlgo.deleteAlgoParam(item.id);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.clear();
            this.interaction.showSuccess('已删除');
            this.requestParams();
        }
        else {
            this.interaction.showError(`删除失败：${errorCode}/${errorMsg}`);
        }
    }

    async requestParams() {
        
        let resp = await repoAlgo.queryAlgoParams();
        let { data, errorCode, errorMsg } = resp;

        if (errorCode == 0 && data instanceof Array) {

            this.parameters.clear();
            this.parameters.refill(data.map(x => new GaoyuAlgoParamDefinition(x)));
        }
        else {
            this.interaction.showError(`查询失败：${errorCode}/${errorMsg}`);
        }
    }
    
    close() {

        this.clear();
        this.dialog.visible = false;
    }

    go2table() {
        this.dialog.isEditing = false;
    }

    goback() {
        this.dialog.isEditing = true;
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.requestParams();
    }

    /**
     * @param {GaoyuAlgoParamDefinition} item 
     */
    edit(item) {

        this.goback();
        const ref = this.form;
        Object.assign(ref, item);
        ref.optionLabel = '';
        ref.optionValue = '';
    }

    /**
     * @param {GaoyuAlgoParamDefinition} item 
     */
    formatDataType(item) {

        let matched = this.dataTypeList.find(x => x.value == item.dataType);
        return matched ? matched.label : item.dataType;
    }

    /**
     * @param {GaoyuAlgoParamDefinition} item 
     */
    formatDefaultValue(item) {

        const dtype = item.dataType;
        const dval = item.defaultValue;

        if (this.isInteger(dtype)) {
            return typeof dval == 'number' ? dval.toFixed(0) : dval;
        }
        else if (this.isDecimal(dtype)) {
            return typeof dval == 'number' ? dval.toFixed(2) : dval;
        }
        else if (this.isTime(dtype)) {
            return GaoyuAlgoHelper.formatTime(dval);
        }
        else if (this.isTimeRange(dtype)) {
            return GaoyuAlgoHelper.formatTime(dval);
        }
        else if (this.isText(dtype)) {
            return dval;
        }
        else if (this.isUserOption(dtype)) {
            
            let matched = item.uoptions.find(x => x.value == dval);
            return matched ? matched.label : dval;
        }
        else {
            return '';
        }
    }
    
    showup() {
        this.dialog.visible = true;
    }

    build($container) {

        super.build($container);
        this.$design = $container.querySelector('.algo-design');
        this.registerEvent('showup', this.showup.bind(this));
        this.createApp();
        this.requestParams();
    }
}

module.exports = View;
