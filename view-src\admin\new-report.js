const IView = require('../../component/iview').IView;
const PreviewAreaCom = require('./indicator/components/PreviewArea/index');
const NumberMixin = require('../../mixin/number').NumberMixin;
const Echarts = require('vue-echarts');
const path = require('path');
const os = require('os');
const fs = require('fs');
const electron = require('electron');
const shell = electron.shell;
const remote = require('@electron/remote');
const this_window = remote.getCurrentWebContents();
Vue.component('v-chart', Echarts);

class View extends IView {
    get indicatorRepo() {
        return require('../../repository/indicator').repoIndicator;
    }

    constructor(view_name, stand_alone, { title = '报告' }) {
        super(view_name, stand_alone, title);
        this.$container = null;
        this.vueApp = null;
        this.context = {
            identity: null,
            identityName: null,
            type: null,
            templateId: null,
            restricts: [],
            defaultTemplateId: null,
        };
        this.registerEvent('setContextData', this.setContextData.bind(this));
    }

    setContextData(param) {
        console.log(param);
        this.context.identity = param.identity || null;
        this.context.identityName = param.identityName || null;
        this.context.type = param.type || null;
        this.context.templateId = param.templateId || null;
        this.context.restricts = param.restricts ? param.restricts.split(',') : [];
        this.context.defaultTemplateId = param.defaultTemplateId || null;

        try {
            this.context.regression_detail = JSON.parse(decodeURI(param.regressionParams));
            Object.keys(this.context.regression_detail || {}).forEach((key) => {
                this.context.regression_detail[key] = decodeURI(this.context.regression_detail[key]);
            });
        } catch (e) {
            this.context.regression_detail = null;
        }
    }

    async createApp() {
        const self = this;
        let PreviewAreaIns = new PreviewAreaCom();
        await PreviewAreaIns.setOptions();
        let PreviewArea = await PreviewAreaIns.build();

        this.vueApp = new Vue({
            el: this.$container.querySelector('.new-report'),
            data: {
                currentItem: null,
                templateId: '',
                templateDetail: {},
                regression_detail: this.context.regression_detail,
                controller: self,
                templates: [],
                rawData: [
                    ['交易日', '序列1', '序列2'],
                    ['20190101', 1.0, 1.0],
                    ['20190201', 1.083, 1.015],
                    ['20190301', 0.998, 1.023],
                    ['20190401', 1.004, 1.032],
                    ['20190501', 1.123, 0.995],
                    ['20190601', 1.085, 1.005],
                    ['20190701', 1.157, 1.121],
                ],
                identity: self.context.identity,
                width: 200,
                opacity: 1,
                showPanel: true,
                destroy: false,
            },
            components: {
                PreviewArea,
            },
            computed: {
                selectStyle() {
                    return {
                        width: `${this.width}px`,
                        opacity: this.opacity,
                        overflow: 'hidden',
                        float: 'left',
                        transition: 'all 0.3s ease-in-out',
                    };
                },
            },
            watch: {
                currentItem(val) {
                    if (val == null) {
                        this.removeCurrentClass();
                    }
                },
                templateId() {
                    this.getTemplateDetail();
                },
            },
            mounted() {
                this.shouldGetTemplates();
            },
            methods: {
                formatRegStatus(status) {
                    return `<span class="s-color-${status ? 'green' : 'red'}">${!status ? '未完成' : '完成'}</span>`;
                },
                percentage(number) {
                    return NumberMixin.filters.percentage(number);
                },
                thousands(number) {
                    return NumberMixin.filters.thousands(number);
                },
                shouldGetTemplates() {
                    console.log(self.context);
                    if (self.context.templateId) {
                        this.templateId = self.context.templateId;
                        this.showPanel = false;
                    } else {
                        this.getTemplates();
                    }
                },
                toggle() {
                    this.width = this.width == 200 ? 0 : 200;
                    this.height = this.height == 24 ? 0 : 24;
                    this.opacity = this.opacity == 1 ? 0 : 1;
                    let btn = document.querySelector('.toggle-btn');
                    if (btn.classList.contains('rotate')) {
                        btn.classList.remove('rotate');
                    } else {
                        btn.classList.add('rotate');
                    }
                },
                async getTemplates() {
                    let resp = await self.indicatorRepo.getReportTemplateList({
                        key_word: '',
                        page_size: 99,
                        page_no: 1,
                    });
                    if (resp.errorCode === 0) {
                        this.templates = resp.data || [];
                        //如果restricts有值，说明需要全局的templates的list只能是restricts里面的
                        if (self.context.restricts.length > 0) {
                            this.templates = this.templates.filter((x) => self.context.restricts.includes(x.id));
                        }
                        if (self.context.defaultTemplateId) {
                            this.templateId = self.context.defaultTemplateId;
                        } else if (this.templates.length > 0) {
                            this.templateId = this.templates[0].id;
                        }
                    }
                },
                async getTemplateDetail() {
                    let resp = await self.indicatorRepo.getTemplateDetail(this.templateId);
                    if (resp.errorCode == 0) {
                        this.templateDetail = resp.data;
                    }
                },
                handleExport() {
                    const pdfPath = path.join(os.tmpdir(), `${self.context.identityName}.pdf`);
                    this_window.printToPDF(
                        {
                            marginsType: 1,
                            pageSize: {
                                width: '210mm',
                                height: '297mm',
                            },
                            printBackground: true,
                            printSelectionOnly: false,
                            landscape: true,
                        },
                        function (error, data) {
                            if (error) throw error;
                            fs.writeFile(pdfPath, data, function (error) {
                                if (error) {
                                    throw error;
                                }
                                shell.openExternal('file://' + pdfPath);
                            });
                        },
                    );
                },
            },
        });
    }

    dispose() {
        this.vueApp.destroy = true;
        this.vueApp = null;
        while (this.$container.hasChildNodes()) {
            this.$container.removeChild(this.$container.firstChild);
        }
        this.trigger('close');
    }

    build($container) {
        this.$container = $container;
        this.createApp();
    }
}

module.exports = View;
