<script setup lang="ts">
import { TradeDirectionEnum } from '@/enum/trade';
import { computed } from 'vue';

// 使用defineModel实现双向绑定
const direction = defineModel<number>();
const isBuy = computed(() => direction.value === TradeDirectionEnum.买入);
const isSell = computed(() => direction.value === TradeDirectionEnum.卖出);

function setAs(value: TradeDirectionEnum) {
  direction.value = value;
}
</script>

<template>
  <div class="trade-direction" h-44 flex>
    <!-- <div class="triangle-sample"></div> -->
    <div class="each-button buy" :class="{ active: isBuy }" @click="setAs(TradeDirectionEnum.买入)">
      <span>买入</span>
      <div class="triangle"></div>
    </div>
    <div
      class="each-button sell"
      :class="{ active: isSell }"
      @click="setAs(TradeDirectionEnum.卖出)"
    >
      <span>卖出</span>
      <div class="triangle"></div>
    </div>
  </div>
</template>

<style scoped>
.trade-direction {
  /*
  
  .triangle-sample {

    width: 200px;
    height: 200px;
    background: transparent;
    position: relative;
    overflow: hidden;
  }

  .triangle-sample::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: red;
    clip-path: polygon(0 0, 100% 0, 0 100%);
  }

  */

  .each-button {
    position: relative;
    flex-shrink: 1;
    flex-grow: 1;
    width: 50%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 44px;

    .triangle {
      position: absolute;
      z-index: 1;
      display: none;
      width: 20px;
      height: 100%;
    }

    &.buy {
      &.active {
        background-color: var(--g-red-2);
        .triangle {
          display: block;
          right: 0;
          top: 0;
          background: linear-gradient(to bottom right, transparent 50%, var(--g-block-bg-2) 50%);
        }
      }
    }

    &.sell {
      &.active {
        background-color: var(--g-bg-green);
        .triangle {
          display: block;
          left: 0;
          top: 0;
          background: linear-gradient(to top left, transparent 50%, var(--g-block-bg-2) 50%);
        }
      }
    }
  }
}
</style>
