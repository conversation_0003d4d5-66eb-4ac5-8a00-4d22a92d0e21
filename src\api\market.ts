import { Repos } from '../../../xtrade-sdk';

class MarketService {
  /**
   * 下载合约数据
   * @param assetType - 资产类型
   * @param qualifify - 需要对返回数据再次进行过滤（比如查询股票合约时，默认会顺带返回基金合约数据）
   */
  static async downloadInstruments(assetType: number, qualifify = true) {
    const marketRepo = new Repos.MarketRepo();
    return marketRepo.DownloadInstruments(assetType, qualifify);
  }
}

export default MarketService;
