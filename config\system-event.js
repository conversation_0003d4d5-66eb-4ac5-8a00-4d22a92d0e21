
/**
 * sytem event definition
 */

const helper = require('../libs/helper').helper;
const systemEvent = {

    /**
     * system internal events
     */

    huntWinLandscapeFromMain: 0,
    huntWinLandscapeFromRender: 0,
    huntWinTabViewFromMain: 0,
    huntWinTabViewFromRender: 0,
    globalViewRouting: 0,
    sysLoadingCompleted: 0,
    mainWindowReady: 0,
    exitApp: 0,
    toLogout: 0,
    tokenRefresh: 0,
    simulatedLogout: 0,
    pingPong: 0,
    showErrorDialog: 0,
    fileOrderSetting: 0,
    transmitMsg2TradingServer: 0,
    transmitMsg2QuoteServer: 0,

    /**
     * network connection events
     */

    connEstablished: 0,
    connTimedOut: 0,
    connError: 0,
    connClosed: 0,
    networkStatusChange: 0,
    tradingServerReestablished: 0,
    quoteServerReestablished: 0,
    appInitiativeDisconnect: 0,

    /**
     * login events
     */

    toLoginTradingServer: 0,
    loginTradingServerCompleted: 0,
    toLoginQuoteServer: 0,
    loginQuoteServerCompleted: 0,
    loginRequestValidatedOk: 0,
    loginRequestCompleted: 0,

    /**
     * business events
     */

    subscribeTick: 0,
    unsubscribeTick: 0,
    downloadTemplate: 0,
    ask2OpenApp: 0,
    subscribeAccountChange: 0,
    unsubscribeAccountChange: 0,
    notifyInstruction: 0,

    /**
     * 视图事件
     */
    viewContextChange: 0,
    tabActivated: 0,
    tabInactivated: 0,

    signalMessage: 0,
    highlightWindow: 0,
    tellReceiveTradableListReady: 0,
    renderWindowPrepared: 0,
    auditOrder: 0,
    
    lockScreen: 0,
    unlockScreen: 0,
    reportWindowStatus: 0,
};

for (let event_name in systemEvent) {
    systemEvent[event_name] = 'se-' + helper.convertCamel2Hyphen(event_name);
}

module.exports = { systemEvent };
