const { repoFund } = require('../../../repository/fund');
const { repoAccount } = require('../../../repository/account');
const { AccountDetail } = require('../../../model/account');
const { helper } = require('../../../libs/helper');
const { helperUi } = require('../../../libs/helper-ui');

class Product {

    constructor(struc) {
        
        this.id = struc.fundId;
        this.name = struc.fundName;
        this.strategies = makeStrategies(struc.strategyAccountRetBeans);
        this.accounts = makeAccounts(struc.accounts);
    }
}

class Strategy {

    constructor(struc) {

        this.id = struc.strategyId;
        this.name = struc.strategyName;
        this.accounts = makeAccounts(struc.accounts);
    }
}

class Account {

    constructor(struc) {
        
        this.id = struc.accountId;
        this.name = struc.accountName;
    }
}

class AccountCharacter extends AccountDetail {

    constructor(account) {

        super(account);
        this.isPhysical = helper.isNone(account.strategyId);
        this.isVirtual = !this.isPhysical;
    }
}

/**
 * @param {Array} records 
 * @returns {Array<Product>} 
 */
function makeProducts(records) {
    return records instanceof Array ? records.map(item => new Product(item)) : [];
}

/**
 * @param {Array} records 
 * @returns {Array<Strategy>} 
 */
function makeStrategies(records) {
    return records instanceof Array ? records.map(item => new Strategy(item)) : [];
}

/**
 * @param {Array} records 
 * @returns {Array<Account>} 
 */
function makeAccounts(records) {
    return records instanceof Array ? records.map(item => new Account(item)) : [];
}

/**
 * @returns {Array<{ label: null, value: null }>}
 */
function createPairs() {
    return [];
}

class IdentityRelation {

    constructor() {

        /** 所有产品信息 */
        this.allProducts = makeProducts();
        /** 所有策略信息*/
        this.allStrategies = makeStrategies();
        /** 所有账号信息 */
        this.allAccounts = makeAccounts();

        /** 当前的产品信息 */
        this.products = createPairs();
        /** 当前的策略信息*/
        this.strategies = createPairs();
        /** 当前的账号信息*/
        this.accounts = createPairs();

        this.istates = {

            productId: null,
            strategyId: null,
            accountId: null,

            showProduct: true,
            showStrategy: true,
            showAccount: true,
        };
    }

    async initialize(userId) {

        this.userId = userId;
        let resp = await repoAccount.batchGetAccountCash();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {
            return ;
        }

        /** 全部账号（平铺） */
        let flattens = data instanceof Array ? data.map(x => new AccountCharacter(x)) : [];
        /** 全部实体账号 */
        let physicals = flattens.filter(x => x.isPhysical);
        /** 全部虚拟账号 */
        let virtuals = flattens.filter(x => x.isVirtual);

        /**
         * 全部产品（由实体账号反推产品）
         */

        let all_products = makeProducts();
        physicals.forEach(x => {
            if (!all_products.some(y => y.id == x.fundId)) {
                all_products.push(new Product({ fundId: x.fundId, fundName: x.fundName }));
            }
        });

        all_products.sort((x, y) => helper.compare(x.name, y.name));

        /**
         * 为产品下挂账号、策略
         */
        all_products.forEach(each_product => {

            /**
             * 下挂账号
             */

            let physical_accounts = physicals.filter(x => x.fundId == each_product.id);
            let hosted_accounts = makeAccounts();
            physical_accounts.forEach(x => {
                if (!hosted_accounts.some(y => y.id == x.accountId)) {
                    hosted_accounts.push(new Account({ accountId: helperUi.getProperAccountId(x), accountName: helperUi.formatSelectAccountName(x) }));
                }
            });

            each_product.accounts.refill(hosted_accounts);

            /**
             * 下挂策略
             */

            let virtual_accounts = virtuals.filter(x => x.fundId == each_product.id);
            let hosted_strategies = makeStrategies();
            virtual_accounts.forEach(x => {
                if (!hosted_strategies.some(y => y.id == x.strategyId)) {
                    hosted_strategies.push(new Strategy({ strategyId: x.strategyId, strategyName: x.strategyName }));
                }
            });

            each_product.strategies.refill(hosted_strategies);
        });

        /**
         * 全部策略（由虚拟账号反推产品）
         */

        let all_strategies = makeStrategies();
        virtuals.forEach(x => {
            if (!all_strategies.some(y => y.id == x.strategyId)) {
                all_strategies.push(new Strategy({ strategyId: x.strategyId, strategyName: x.strategyName }));
            }
        });

        all_strategies.sort((x, y) => helper.compare(x.name, y.name));

        /**
         * 为策略下挂账号
         */
        all_strategies.forEach(each_strategy => {

            /**
             * 下挂账号
             */

            let virtual_accounts = virtuals.filter(x => x.strategyId == each_strategy.id);
            let hosted_accounts = makeAccounts();
            virtual_accounts.forEach(x => {
                if (!hosted_accounts.some(y => y.id == x.accountId)) {
                    hosted_accounts.push(new Account({ accountId: helperUi.getProperAccountId(x), accountName: helperUi.formatSelectAccountName(x) }));
                }
            });

            each_strategy.accounts.refill(hosted_accounts);
        });

        /**
         * 全部账号
         */
        let all_accounts = makeAccounts(flattens.map(x => {
            return {
                accountId: helperUi.getProperAccountId(x),
                accountName: helperUi.formatSelectAccountName(x),
            };
        }));

        // console.log({ all_products, all_strategies, all_accounts });
        this.allProducts.refill(all_products);
        this.allStrategies.refill(all_strategies);
        this.allAccounts.refill(all_accounts);

        this.products.refill(this.convert2ProductDtos(all_products));
        this.strategies.refill(this.convert2StrategyDtos(all_strategies));
        this.accounts.refill(this.convert2AccountDtos(all_accounts));
    }
    
    /**
     * @param {Array<Product>} records 
     * @returns 
     */
    convert2ProductDtos(records) {
        return records.map(item => ({ value: item.id, label: `${item.name} - ${item.id}` }));
    }

    /**
     * @param {Array<Strategy>} records 
     * @returns 
     */
    convert2StrategyDtos(records) {
        return records.map(item => ({ value: item.id, label: `${item.name} - ${item.id}` }));
    }

    /**
     * @param {Array<Account>} records 
     * @returns 
     */
    convert2AccountDtos(records) {
        return records.map(item => ({ value: item.id, label: `${item.name}` }));
    }
    
    handleProductChange() {

        this.istates.strategyId = null;
        this.istates.accountId = null;
        
        /**
         * 策略，账号，填入为选中的产品下的所有的策略，账号(选中的产品下的，策略下挂的账号与产品下挂的账号的并集去重);
         */

        if (this.istates.productId) {

            let matched = this.allProducts.find(x => x.id == this.istates.productId);
            let strategies = matched.strategies;
            let accounts = matched.accounts;
            
            this.strategies.refill(this.convert2StrategyDtos(strategies));
            this.accounts.refill(this.convert2AccountDtos(accounts));
        }
        else {

            this.strategies.refill(this.convert2StrategyDtos(this.allStrategies));
            this.accounts.refill(this.convert2AccountDtos(this.allAccounts));
        }
    }
    
    handleStrategyChange() {

        this.istates.accountId = null;
        var prodId = this.istates.productId;
        var straId = this.istates.strategyId;
        var isProductInvolved = !!prodId;
        var isStrategyInvolved = !!straId;
        var intersects = makeAccounts();

        if (isStrategyInvolved) {

            let sacnts = this.allStrategies.find(x => x.id == straId).accounts;
            intersects.refill(sacnts);
        }
        else if (isProductInvolved) {

            let pacnts = this.allProducts.find(x => x.id == prodId).accounts;
            intersects.refill(pacnts);
        }
        else {
            intersects.refill(this.allAccounts);
        }

        this.accounts.refill(this.convert2AccountDtos(intersects));
    }

    handleAccountChange() {
        
        //
    }
}

module.exports = { IdentityRelation };