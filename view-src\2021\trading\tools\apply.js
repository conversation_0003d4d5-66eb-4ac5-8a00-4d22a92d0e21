const { IView } = require('../../../../component/iview');
const ApplyTrade = require('../apply/trade');
const Ballot = require('../apply/ballot');
const Quota = require('../apply/quota');
const Distribution = require('../apply/distribution');


class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '申购');
        this.vueApp = null;
    }

    async createApp(){
        let applyTrade = await new ApplyTrade().getComponent();
        let ballot = await new Ballot().getComponent();
        let quota = await new Quota().getComponent();
        let distribution = await new Distribution().getComponent();
        this.vueApp = new Vue({
            el: this.$container.querySelector('.apply-trade'),
            data: {
                dataTabs: [
                    {name: 'quota', title: '额度', content: 'quota'},
                    {name: 'distribution', title: '配号', content: 'distribution'},
                    {name: 'ballot', title: '中签', content: 'ballot'},
                ],
                activeQryTab: 'quota',
            },
            components:{
                applyTrade,
                ballot,
                quota,
                distribution
            },
        });
    }


    build($container) {

        super.build($container);
        this.createApp();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }

    dispose() {
        this.vueApp.destroy = true;
        this.vueApp = null;
        while (this.$container.hasChildNodes()) {
            this.$container.removeChild(this.$container.firstChild);
        }
    }
}

module.exports = View;
