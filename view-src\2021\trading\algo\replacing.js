const { IView } = require('../../../component/iview');
const { BasketTask } = require('../../../model/basket-task');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '任务追单操作');

        this.methods = this.systemTrdEnum.basketMethod;
        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };

        this.stages = this.systemTrdEnum.bidingStages;
        this.states = {

            title: '任务追单操作',
            visible: false,
            stage: this.stages[0].code,
            offset: 0,
            keywords: null,
        };

        this.pinyinMap = {};
    }

    /**
     * @param {Array<BasketTask>} tasks 
     * @param {Boolean} isChild 
     */
    showup(tasks, isChild) {

        this.states.visible = true;
        this.isChild = isChild;
        this.vueApp.$nextTick(_=> { this.render(this.helper.deepClone(tasks)); });
    }

    /**
     * @param {Array<BasketTask>} tasks
     */
    render(tasks) {
        
        if (this.tableObj === undefined) {

            var $table = this.vueApp.$el.querySelector('.data-list');
            this.tableObj = new SmartTable($table, this.identifyRecord, this, {

                tableName: 'smt-tarp',
                displayName: this.title,
                recordsFiltered: this.handleTableFiltered.bind(this),
            });
    
            this.tableObj.setMaxHeight(430);
        }

        tasks.forEach(item => {
            item.stageName = this.stages[0].mean;
        });

        var cols = ['合约代码', '合约名称'];
        this.tasks = tasks;

        if (this.isChild) {
            this.tableObj.showColumns(cols);
        }
        else {
            this.tableObj.hideColumns(cols);
        }

        this.tableObj.refill(tasks);
        this.tableObj.fitColumnWidth();
    }

    createApp() {

        var $root = this.$container.querySelector('.dialog-replace-tasks');
        this.vueApp = new Vue({

            el: $root,
            data: {

                stages: this.stages,
                states: this.states,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterRecords,
                this.handleStageChange,
                this.handleOffsetChange,
                this.handlePageChange,
                this.handlePageSizeChange,
                this.confirm,
                this.cancel,
            ]),
        });
    }

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.tradingDirection });
    }

    /**
     * @param {BasketTask} record 
     */
    identifyRecord(record) {
        return `${record.taskId}~${record.instrument || 'no-instrument'}`;
    }

    testPy(sample, keywords) {

        let matched_py = this.pinyinMap[sample];
        if (matched_py === undefined) {
            matched_py = this.pinyinMap[sample] = this.helper.pinyin(sample);
        }

        return typeof keywords == 'string' && keywords.length >= 1 && matched_py.indexOf(keywords) >= 0;
    }

    /**
     * @param {BasketTask} record
     */
    formatMethod(record) {

        if (record.executeType == this.methods.volume.code) {
            return this.methods.volume.mean;
        }
        else if (record.executeType == this.methods.weight.code) {
            return this.methods.weight.mean;
        }
        else if (record.executeType == this.methods.weight2Account.code) {
            return this.methods.weight2Account.mean;
        }
        else if (record.executeType == this.methods.weight2Asset.code) {
            return this.methods.weight2Asset.mean;
        }
    }

    /**
     * @param {BasketTask} record
     */
    formatScale(record) {

        if (record.executeType == this.methods.volume.code) {
            return record.executeVolume + this.methods.volume.unit;
        }
        else if (record.executeType == this.methods.weight.code) {
            return record.executeVolume + this.methods.weight.unit;
        }
        else if (record.executeType == this.methods.weight2Account.code) {
            return record.executeVolume + this.methods.weight2Account.unit;
        }
        else if (record.executeType == this.methods.weight2Asset.code) {
            return record.executeVolume + this.methods.weight2Asset.unit;
        }
    }

    filterRecords() {

        var thisObj = this;
        var keywords = this.states.keywords;

        /**
         * @param {BasketTask} record 
         */
        function testRecords(record) {
            return thisObj.tableObj.matchKeywords(record);
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    handleStageChange() {
        
        var stage = this.stages.find(x => x.code == this.states.stage);
        this.tasks.forEach(item => {
            this.tableObj.updateRow({ taskId: item.taskId, instrument: item.instrument, stageName: stage.mean });
        });
    }

    handleOffsetChange() {

        //
    }

    handlePageSizeChange() {

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    confirm() {

        if (!Array.isArray(this.tasks) || this.tasks.length == 0) {
            
            this.hide();
            return;
        }

        if (this.isChild) {

            let taskId = this.tasks[0].taskId;
            let instruments = this.tasks.map(item => item.instrument);

            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.replaceOrder, {
    
                taskId: taskId,
                instruments: instruments,
                priceFollowType: this.states.stage,
                deviation: typeof this.states.offset == 'number' ? this.states.offset : 0,
            });

            this.interaction.showSuccess('子任务追单，请求已发送，数量 = ' + this.tasks.length);
        }
        else {

            this.tasks.forEach(tsk => {

                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.replaceOrder, {
    
                    taskId: tsk.taskId,
                    priceFollowType: this.states.stage,
                    deviation: typeof this.states.offset == 'number' ? this.states.offset : 0,
                });
            });
    
            this.interaction.showSuccess('主任务追单，请求已发送，数量 = ' + this.tasks.length);
        }

        this.hide();
    }

    cancel() {
        this.hide();
    }

    hide() {
        
        this.states.keywords = null;
        this.states.stage = this.stages[0].code;
        this.states.offset = 0;
        this.states.visible = false;
    }

    build($container) {

        super.build($container);
        this.helper.extend(this, ColumnCommonFunc);
        this.createApp();
        this.registerEvent('showup', this.showup.bind(this));
    }
}

module.exports = View;