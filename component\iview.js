const electron = require('electron');
const remote = require('@electron/remote');
const EventEmitter = require('events').EventEmitter;
const fs = require('fs');
const helper = require('../libs/helper').helper;
const helperUi = require('../libs/helper-ui').helperUi;
const interaction = require('../libs/interaction').interaction;
const logging = require('../libs/logging');
const UserInfo = require('../model/user-info').UserInfo;
const Permission = require('../model/menu').Permission;
const dataKey = require('../main-process/app-data-key').dataKey;
const systemEvent = require('../config/system-event').systemEvent;
const systemIndayEvent = require('../config/system-event.inday').systemIndayEvent;
const systemSetting = require('../config/system-setting').systemSetting;
const systemEnum = require('../config/system-enum').systemEnum;
const systemEnumBack = require('../config/system-enum.backup').systemBackupEnum;
const systemPermit = require('../config/system-permit').systemPermit;
const systemTrdEnum = require('../config/system-enum.trading').systemTrdEnum;
const systemUserEnum = require('../config/system-enum.user').systemUserEnum;
const systemBackupEnum = require('../config/system-enum.backup').systemBackupEnum;
const serverEvent = require('../config/server-event').serverEvent;
const serverFunction = require('../config/server-function-code').serverFunction;
const Routing = require('../model/routing').Routing;

class IView extends EventEmitter {
    /**
     * env properties
     */

    get electron() {
        return electron;
    }

    get app() {
        return remote.app;
    }

    get renderProcess() {
        return this.electron.ipcRenderer;
    }

    get thisWindow() {
        return remote.getCurrentWindow();
    }

    /**
     * non-business properties
     */

    get systemEvent() {
        return systemEvent;
    }

    get serverEvent() {
        return serverEvent;
    }

    get systemSetting() {
        return systemSetting;
    }

    get systemEnum() {
        return systemEnum;
    }

    get systemEnumBack() {
        return systemEnumBack;
    }

    get systemPermit() {
        return systemPermit;
    }

    get systemTrdEnum() {
        return systemTrdEnum;
    }

    get systemIndayEvent() {
        return systemIndayEvent;
    }

    get systemUserEnum() {
        return systemUserEnum;
    }

    get systemBackupEnum() {
        return systemBackupEnum;
    }

    get serverFunction() {
        return serverFunction;
    }

    get dataKey() {
        return dataKey;
    }

    get helper() {
        return helper;
    }

    get helperUi() {
        return helperUi;
    }

    get interaction() {
        return interaction;
    }

    get logging() {
        return logging;
    }

    get loggerConsole() {
        return logging.getConsoleLogger();
    }

    get loggerSys() {
        return logging.getSystemLogger();
    }

    get loggerTrading() {
        return logging.getTradingLogger();
    }

    /**
     * contextual properties
     */

    get userInfo() {
        return this.typedsUserInfo(this.app.contextData.userInfo);
    }

    get contextData() {
        return this._appData || (this._appData = this.app.contextData);
    }

    /**
     * current view's routing info
     */

    get routing() {
        if (this._routing === undefined) {
            var routings = window['routings'];
            var matched_struc = routings.find(struc => struc.name == this.viewName);
            this._routing = Routing.duplicate(matched_struc);
        }

        return this._routing;
    }

    /**
     * view height
     */
    get height() {
        return !this.$container ? 0 : this.$container.offsetHeight;
    }

    /**
     * view width
     */
    get width() {
        return !this.$container ? 0 : this.$container.offsetWidth;
    }

    /**
     *Creates an instance of IView.
     * @param {string} view_name
     * @param {*} is_standalone_window
     * @param {*} title
     */
    constructor(view_name, is_standalone_window, title) {
        super();
        this.viewName = view_name;
        this.viewId = `${this.viewName}@${this.helper.makeToken()}`;
        this.isStandAlongWindow = is_standalone_window !== false;
        this.setWindowTitle(title);
        this._monitorBasicEvents();
    }

    /**
     * @param {UserInfo} userInfo 
     */
    typedsUserInfo(userInfo) {
        return userInfo;
    }

    /**
     * set window title
     */
    setWindowTitle(title) {
        if (this.helper.isJson(title)) {
            title = title.title;
        }
        if (typeof title != 'string' || title.trim().length == 0) {
            return;
        }

        this.title = title;
        // window.document.title = title;

        var $header_inner = document.getElementById('window-header-inner');
        if ($header_inner) {
            let product_name = '云信KS主动资产管理交易系统';
            let display_window_name = title.indexOf(product_name) >= 0 ? title : `${title} - ${product_name}`;
            $header_inner.innerText = display_window_name;
        }
    }

    /**
     * to format account name for a select
     * @param {*} account 
     */
    formatSelectAccountName(account) {
        return this.helperUi.formatSelectAccountName(account);
    }

    /**
     * monitor some basic events
     */
    _monitorBasicEvents() {
        if (this.isStandAlongWindow) {
            this.thisWindow.on('close', this._disposeResource.bind(this));
        }
    }

    _disposeResource(event) {
        // dispose operation shall be implemented in child class
        this.dispose();
    }

    /**
     * get data item in the namespace [electron.app.contextData]
     * @param {String|Number} key
     */
    getContextDataItem(key) {
        return typeof key == 'string' || typeof key == 'number' ? this.contextData[key] : undefined;
    }

    /**
     * set data item in the namespace [electron.app.contextData]
     * @param {String|Number} key
     * @param {Object} value
     */
    setContextDataItem(key, value) {
        if (typeof key != 'string' && typeof key != 'number') {
            console.error(`app context data key [${key}] is not an valid key`);
            return false;
        }

        this.contextData[key] = value;
        return true;
    }

    /**
     * log a message (in a specified format which can be recognized by data annalyzing tool) for performance debugging
     * @param {String|Number} topic
     * @param {String|Number} task
     * @param {String|Number|null} given_time
     */
    debugPerf(topic, task, given_time) {
        if (this._randomStr === undefined) {
            this._randomStr = 'perflog-' + this.viewName;
        }

        this.loggerSys.debug(`\t${this._randomStr}-${topic}\t${task}\t${given_time || new Date().getTime()}`);
    }

    /**
     * 设置视图操作权限黑名单
     * @param {Array<Permission>} permits 原子操作权限
     */
    setPermits(permits) {
        this.blackPermits = permits;
    }

    /**
     * 设置视图选项
     * @param {*} options 视图选项
     */
    setOptions(options) {
        this.viewOptions = options;
    }

    /**
     * build the view
     * @param {HTMLElement} $container
     */
    build($container) {
        this.$container = $container;
    }

    /**
     * tell the view to load (html)template & script module & construct the whole view by itself
     * @param {HTMLElement} $container <required>
     * @param {*} options <optional>
     * @param {Function} build_completed_callback <optional>
     */
    loadBuild($container, options, build_completed_callback) {
        if (!($container instanceof HTMLElement)) {
            this.interaction.showAlert('未匹配到渲染视图所依赖的DOM节点');
            return;
        }

        /**
         * file reading error:
         * {"errno":-4058,"code":"ENOENT","syscall":"open","path":"D:\\SourceCode\\Uxmc\\view\\product.css"}
         */

        var routing = this.routing;
        var $internal_root = document.createElement('div');
        $internal_root.classList.add('view-template');
        $internal_root.setAttribute('template-path', routing.html.replace('\\view\\', '\\view-src\\'));
        $container.appendChild($internal_root);

        fs.readFile(routing.html, (err_html, html_content) => {
            $internal_root.innerHTML = html_content.toString();
            if (!routing.css) {
                this.build($container, options);
                if (typeof build_completed_callback == 'function') {
                    build_completed_callback();
                }
                return;
            }

            // with view css setting
            fs.readFile(routing.css, (err_css, css_content) => {
                var $style = document.createElement('style');
                $style.setAttribute('type', 'text/css');
                $style.setAttribute('scoped', true);
                $style.innerText = css_content.toString();

                // 挂载内联样式到View根节点
                $container.insertBefore($style, $internal_root);
                this.build($internal_root, options);
                if (typeof build_completed_callback == 'function') {
                    build_completed_callback();
                }
            });
        });
    }

    /**
     * 为该视图对象，注册持续性事件监听
     */
    registerEvent(event_name, event_handler, one_time = false) {
        if ((typeof event_name != 'string' && typeof event_name != 'number') || event_name.toString().length == 0) {
            throw new Error('event_name is not valid');
        } else if (typeof event_handler != 'function') {
            throw new Error('event_handler is not a function');
        }

        event_name = event_name.toString().trim();
        if (one_time) {
            this.once(event_name, event_handler);
        } else {
            this.on(event_name, event_handler);
        }
    }

    /**
     * 为该视图对象，注册一次性事件监听
     */
    registerOneTimeEvent(event_name, event_handler) {
        this.registerEvent(event_name, event_handler, true);
    }

    /**
     * 取消注册在该视图对上上，指定（或全部）的事件处理程序
     */
    unregisterEvent(event_name, event_handler) {
        if ((typeof event_name != 'string' && typeof event_name != 'number') || event_name.toString().length == 0) {
            throw new Error('event_name is not valid');
        }

        event_name = event_name.toString().trim();
        if (typeof event_handler == 'function') {
            this.removeListener(event_name, event_handler);
        } else {
            this.removeAllListeners(event_name);
        }
    }

    /**
     * 对外提供事件触发
     */
    trigger() {
        var event_name = arguments[0];
        var args = this.helper.getArgumentsSlice(arguments, 1);

        if ((typeof event_name != 'string' && typeof event_name != 'number') || event_name.toString().length == 0) {
            throw new Error('event_name is not valid');
        }

        event_name = event_name.toString().trim();
        if (this.listenerCount(event_name) == 0) {
            // console.info(`event[${event_name}] of view[${this.viewName}] has no listeners registered.`);
            return;
        }

        this.emit(event_name, ...args);
    }

    /**
     * 单一视图，发送消息给主进程的标准方式
     */
    standardSend(channel_name, ...args) {
        if (typeof channel_name != 'string') {
            channel_name = channel_name.toString();
        }

        this.renderProcess.send(channel_name, this.thisWindow.id, this.viewId, ...args);
    }

    /**
     * 提供监听主进程消息的标准方式
     */
    standardListen(channel_name, listener) {
        if (typeof channel_name != 'string') {
            channel_name = channel_name.toString();
        }

        this.renderProcess.on(channel_name, (event, view_id, ...args) => {
            if (this.viewId !== view_id) {
                // console.warn(`${channel_name} message data received not belongs to view [${this.viewName}]`);
                return;
            }
            listener(...args);
        });
    }

    /**
     * 提供监听主进程消息的标准方式（一次性监听）
     */
    standardListenOnce(channel_name, listener) {
        if (typeof channel_name != 'string') {
            channel_name = channel_name.toString();
        }

        this.renderProcess.once(channel_name, (event, view_id, ...args) => {
            if (this.viewId !== view_id) {
                // console.warn(`${channel_name} message data received not belongs to view [${this.viewName}]`);
                return;
            }
            listener(...args);
        });
    }

    /**
     * 视图标准操作 》 设置视图宽度
     * @param { String | Number } width
     */
    setWidth(width) {
        if (!this.$container) {
            return;
        }

        if (typeof width == 'number' && width >= 0) {
            this.$container.style.width = width + 'px';
        } else if (typeof width == 'string') {
            this.$container.style.width = width;
        } else {
            console.error('illegal view width is not accepted', width);
        }
    }

    /**
     * 视图标准操作 》 设置视图高度
     * @param { String | Number } height
     */
    setHeight(height) {
        if (!this.$container) {
            return;
        }

        if (typeof height == 'number' && height >= 0) {
            this.$container.style.height = height + 'px';
        } else if (typeof height == 'string') {
            this.$container.style.height = height;
        } else {
            console.error('illegal view height is not accepted', height);
        }
    }

    /**
     * 视图标准操作 》 刷新
     */
    refresh() {
        console.warn(`view[${this.viewName}] does not implement method [ivew.refresh]`);
    }

    /**
     * 视图标准操作 》 导出数据
     */
    exportSome() {
        console.warn(`view[${this.viewName}] does not implement method [ivew.exportSome]`);
    }

    /**
     * 视图标准操作 》 配置
     */
    config() {
        console.warn(`view[${this.viewName}] does not implement method [ivew.config]`);
    }

    /**
     * 视图标准操作 》 克隆
     */
    clone() {
        var window_options = { width: 800, height: 600, title: this.title };
        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, this.viewName, this.title, window_options);
    }

    /**
     * 视图标准操作 》 释放资源
     */
    dispose() {
        console.warn(`recommended to dispose all resources allocated for view [${this.viewName}]`);
    }

    /**
     * register a handler for window resize event
     * @param {Function} handler callback parameters (window_width[Number], window_height[Number])
     * @param {Number} delay_ms whether the callback should be called with a timeout delay
     */
    lisen2WinSizeChange(handler, delay_ms = 0) {
        if (typeof handler != 'function') {
            console.error('provided window resize handler is not a function', handler);
            return;
        }
        if (typeof delay_ms == 'number' && delay_ms >= 0) {
            this.thisWindow.on('resize', () => {
                setTimeout(() => {
                    handler(...this.thisWindow.getSize(), this.thisWindow.isMaximized());
                }, delay_ms);
            });
        } else {
            this.thisWindow.on('resize', () => {
                handler(...this.thisWindow.getSize(), this.thisWindow.isMaximized());
            });
        }

        /**
         * execute once by default
         */
        setTimeout(() => {
            handler(...this.thisWindow.getSize(), this.thisWindow.isMaximized());
        }, 100);
    }

    /**
     * simulate one time of window size change (all registered listeners will be triggered)
     */
    simulateWinSizeChange() {
        this.thisWindow.emit('resize', ...this.thisWindow.getSize());
    }

    /**
     * 展示视图
     */
    show() {
        if (this.$container) {
            this.$container.style.display = 'block';
        }
    }

    /**
     * 隐藏视图
     */
    hide() {
        if (this.$container) {
            this.$container.style.display = 'none';
        }
    }

    /**
     * listen to window [resize] -- event triggered while resizing
     */
    listen2WinResize(callback) {
        this.thisWindow.on('resize', () => {
            const size = this.thisWindow.getSize();
            callback(size[0], size[1]);
        });
    }

    /**
     * listen to window [resized] -- event triggered after resizing finished
     */
    listen2WinResized(callback) {
        this.thisWindow.on('resized', () => {
            const size = this.thisWindow.getSize();
            callback(size[0], size[1]);
        });
    }
}

module.exports = { IView };
