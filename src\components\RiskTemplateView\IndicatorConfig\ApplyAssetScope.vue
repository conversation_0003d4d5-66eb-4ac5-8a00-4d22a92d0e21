<script setup lang="ts">
import { computed, onMounted, reactive, ref, useTemplateRef, watch } from 'vue';
import type { AssetScopeSetting } from '@/types/riskc';
import { deepClone, distinct, remove } from '@/script';
import { DynamicConditionTypes, ExpressionTypes } from '@/enum/riskc';

import {
  Repos,
  type KindOfAsset,
  type TradeDynamicIndicator,
} from '../../../../../xtrade-sdk/dist';

interface Kind3rdLevelNode {
  level2Code: string;
  id: number;
  code: string;
  name: string;
  kindInfo: KindOfAsset;
}

interface Kind2ndLevelNode {
  level1Code: string;
  code: string;
  name: string;
  children: Kind3rdLevelNode[];
}

interface Kind1stLevelNode {
  code: string;
  name: string;
  children: Kind2ndLevelNode[];
}

const dynamicIdcs = ref<TradeDynamicIndicator[]>([]);
const flattenList = ref<KindOfAsset[]>([]);
const level1s = ref<Kind1stLevelNode[]>([]);
const selectedLevel1Asset = computed(() => {
  return level1s.value.find(x => x.code == formData.level1Code)!;
});

const repoInstance = new Repos.RiskControlRepo();
const dynamicIdcRepoInstance = new Repos.DynamicIndicatorRepo();
const props = defineProps<{ scope: AssetScopeSetting }>();
const states = {
  requiredJob: null as any,
};

watch(
  () => props.scope,
  () => {
    handleContextChange();
  },
  { immediate: true },
);

function handleContextChange() {
  if (level1s.value.length > 0) {
    recover();
  } else {
    states.requiredJob = () => {
      recover();
    };
  }
}

function recover() {
  const all = level1s.value;
  if (all.length == 0) {
    return;
  }

  const { kindCodes, overlaies } = props.scope;
  // 根据底层代码，溯源其最上层选中的类别
  const level1_codes = flattenList.value
    .filter(x => kindCodes.includes(x.kindCode))
    .map(x => x.parentKindCode);
  const diff_level1_codes = distinct(level1_codes, x => x);

  // 根据底层选中代码，以及向上溯源的选中类别，构建出资产勾选情况
  const cmap: { [level1stCode: string]: string[] } = {};
  all.forEach(ast => {
    const is_level1_choosed = diff_level1_codes.some(x => x == ast.code);
    if (is_level1_choosed) {
      const subset = flattenList.value.filter(x => x.parentKindCode == ast.code);
      cmap[ast.code] = kindCodes.filter(kcode => subset.some(x => x.kindCode == kcode));
    } else {
      cmap[ast.code] = [];
    }
  });

  choicesMap.value = cmap;
  formData.level1Code = all[0].code;
  formData.overlaies = [...overlaies];
}

const rules = {
  overlaies: [{ required: false }],
};

const formData = reactive({
  level1Code: '',
  overlaies: [] as number[],
});

/**
 * 资产勾选情况（key/一级资产代码，values/底层资产代码）
 */
const choicesMap = ref<{ [level1stCode: string]: string[] }>({});

/**
 * 所有叶子节点层级的资产代码与名称映射表
 */
const bottomLevelKindCode2NameMap = ref<{ [code: string]: string }>({});

/**
 * 拉平的已选资产
 */
const flattenedChoices = computed(() => {
  return Object.values(choicesMap.value).flat();
});

function handleRemoveChoice(kindCode: string) {
  for (const key in choicesMap.value) {
    const codes = choicesMap.value[key];
    remove(codes, x => x == kindCode);
  }
}

/**
 * 生成动态条件描述
 */
function renderConditionDesc(row: TradeDynamicIndicator) {
  const { conditionType, expressionType, value } = row;
  const matched = DynamicConditionTypes.find(x => x.value == conditionType)!;
  const matched2 = ExpressionTypes.find(x => x.value == expressionType)!;
  return matched.fmt.replace('{0}', matched2.label).replace('{1}', value.toString());
}

function buildLevels(records: KindOfAsset[]): Kind1stLevelNode[] {
  const level_1st_map: { [code: string]: Kind1stLevelNode } = {};
  records.forEach(ast => {
    // check level 1st
    let level1 = level_1st_map[ast.parentKindCode];
    if (level1 === undefined) {
      level1 = {
        code: ast.parentKindCode,
        name: ast.parentKindName,
        children: [],
      };
      level_1st_map[ast.parentKindCode] = level1;
    }

    // check level 2nd
    let level2 = level1.children.find(n => n.code === ast.midKindCode);
    if (level2 === undefined) {
      level2 = {
        level1Code: ast.parentKindCode,
        code: ast.midKindCode,
        name: ast.midKindName,
        children: [],
      };
      level1.children.push(level2);
    }

    // add indicator node
    level2.children.push({
      id: ast.id,
      level2Code: ast.midKindCode,
      code: ast.kindCode,
      name: ast.kindName,
      kindInfo: ast,
    });
  });

  return Object.values(level_1st_map);
}

function makeLevel1Title(group_code: string) {
  const ast = level1s.value.find(x => x.code == group_code);
  return ast ? ast.name : '';
}

async function requestAssets() {
  const list = (await repoInstance.QueryAssetScopes()).data || [];
  flattenList.value = list;

  // 构造底层资产代码与名称映射表
  const deepMap = {} as any;
  list.forEach(x => {
    deepMap[x.kindCode] = x.kindName;
  });

  bottomLevelKindCode2NameMap.value = deepMap;

  // 构造资产树形结构
  level1s.value = buildLevels(list);

  if (typeof states.requiredJob === 'function') {
    states.requiredJob();
    states.requiredJob = null;
  }
}

async function requestDynamicIdcs() {
  dynamicIdcs.value = (await dynamicIdcRepoInstance.getDynamicIndicators()).data || [];
}

const $form = useTemplateRef('$form');

function getSetting() {
  return {
    kindCodes: Object.values(choicesMap.value).flat(),
    overlaies: deepClone(dynamicIdcs.value.filter(x => formData.overlaies.some(id => x.id == id))),
  };
}

function validate() {
  return $form.value!.validate();
}

defineExpose({
  validate,
  getSetting,
});

onMounted(() => {
  requestAssets();
  requestDynamicIdcs();
});
</script>

<template>
  <div class="asset-scope-setting" p-10 h-full flex flex-col>
    <div h-160>
      <el-form ref="$form" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="已选资产">
          <el-tooltip effect="light" :disabled="flattenedChoices.length == 0">
            <template #content>
              <div class="floating-choice-box">
                <template
                  v-for="(group_code, group_idx) in Object.keys(choicesMap)"
                  :key="group_idx"
                >
                  <div class="group-box" v-show="choicesMap[group_code].length > 0">
                    <div class="group-title">{{ makeLevel1Title(group_code) }}</div>
                    <div class="group-members">
                      <span
                        v-for="(item, idx) in choicesMap[group_code]"
                        :key="idx"
                        class="choosed-asset-scope-item"
                      >
                        <span>{{ bottomLevelKindCode2NameMap[item] || item }}</span>
                        <i class="iconfont icon-close" @click="handleRemoveChoice(item)"></i>
                      </span>
                    </div>
                  </div>
                </template>
              </div>
            </template>
            <div class="choice-info">
              <span
                v-for="(item, idx) in flattenedChoices.slice(0, 10)"
                :key="idx"
                class="choosed-asset-scope-item"
              >
                <span>{{ bottomLevelKindCode2NameMap[item] || item }}</span>
              </span>
            </div>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="资产筛选">
          <el-select v-model="formData.level1Code" placeholder="资产类别" filterable clearable>
            <el-option
              v-for="(item, idx) in level1s"
              :key="idx"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="条件叠加" prop="overlaies">
          <el-select
            v-model="formData.overlaies"
            placeholder="请选择要叠加的条件"
            multiple
            collapse-tags
            clearable
          >
            <el-option
              v-for="(item, idx) in dynamicIdcs"
              :key="idx"
              :label="renderConditionDesc(item)"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="selectedLevel1Asset" class="data-list" flex-1 of-auto pt-20 pl-15>
      <el-checkbox-group v-model="choicesMap[selectedLevel1Asset.code]">
        <template v-for="(lv2, lv2_idx) in selectedLevel1Asset.children" :key="lv2_idx">
          <div class="group-title">{{ lv2.name }}</div>
          <div class="choice-box" flex flex-wrap>
            <el-checkbox
              v-for="(item, idx) in lv2.children"
              :key="idx"
              :label="item.name"
              :value="item.code"
            />
          </div>
        </template>
      </el-checkbox-group>
    </div>
  </div>
</template>

<style scoped>
.asset-scope-setting {
  background-color: var(--g-block-bg-2);

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2) !important;
    }

    .el-checkbox {
      width: 150px;
      height: 38px !important;
    }
  }

  .choice-info {
    width: 100%;
    height: 40px;
    padding: 4px 12px;
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    overflow-y: hidden;
    box-shadow: rgb(0, 0, 0) 0px 0px 0px 1px inset;
  }

  .group-title {
    line-height: 50px;
    font-size: 14px;
    font-weight: 400;
  }
}
</style>

<style>
.choosed-asset-scope-item {
  border-radius: 4px;
  border: 1px solid var(--g-border);
  font-size: 12px;
  line-height: 24px;
  padding: 0 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.floating-choice-box {
  width: 400px;
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
  .group-title {
    line-height: 48px;
  }
  .group-members {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
