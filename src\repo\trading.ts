import { BaseRepo } from '../modules/base-repo';
import { RegularOrder, OrderInfo, PositionInfo, TradeRecordInfo, BasketOrder, AlgoOrder } from '../types/trading';
import { ServerMessageCallbackMethod, SocketDataPackage } from '../types/data-package';
import { RiskMessage } from '../types/table/risk-control';
import { ServerFunction } from '../config/server-function';
import { ServerEvent } from '../config/server-event';
import { GetLogger } from '../global-state';

const defaultLogger = GetLogger();

export class TradingRepo extends BaseRepo {

    /**
     * 普通下单
     */
    SendOrder(order: RegularOrder) {
        this.tserver.send({ fc: ServerFunction.SendOrder, body: order });
    }

    /**
     * 篮子下单
     */
    SendBasketOrder(order: BasketOrder) {
        this.tserver.send({ fc: ServerFunction.SendBasketOrder, body: order });
    }

    /**
     * 算法下单
     */
    SendAlgoOrder(order: AlgoOrder) {
        this.tserver.send({ fc: ServerFunction.SendAlgoOrder, body: order });
    }

    /**
     * 批量下单
     */
    async SendBatchOrder(order: any) {
        throw new Error('not implemented');
    }

    /**
     * 撤销普通单
     */
    CancelOrder(orderId: number | string) {
        this.tserver.send({ fc: ServerFunction.CancelOrder, body: { orderId } });
    }

    /**
     * 撤销母单（算法单、批量单、篮子单）
     */
    async CancelMotherOrder(taskIds: number | string | Array<number | string>) {

        if (!Array.isArray(taskIds)) {
            taskIds = [taskIds];
        }

        return this.assist.Post<number[]>('/algorithm/cancel', {}, { taskIds });
    }

    /**
     * 订阅单个订单变化推送
     */
    SubscribeOrderChange(callback: (data: SocketDataPackage<OrderInfo>) => void) {

        this.tserver.subscribe(ServerEvent.OrderChanged, callback);
        defaultLogger.log('to subscribe order change');
    }

    /**
     * 退订单个订单变化推送
     */
    UnsubscribeOrderChange<T = any>(callback: ServerMessageCallbackMethod<T>) {

        this.tserver.unsubscribe(ServerEvent.OrderChanged, callback);
        defaultLogger.log('to unsubscribe order change');
    }

    /**
     * 订阅单个成交变化推送
     */
    SubscribeTradeChange(callback: (data: SocketDataPackage<TradeRecordInfo>) => void) {

        this.tserver.subscribe(ServerEvent.TradeChanged, callback);
        defaultLogger.log('to subscribe trade record change');
    }

    /**
     * 退订单个成交变化推送
     */
    UnsubscribeTradeChange<T = any>(callback: ServerMessageCallbackMethod<T>) {

        this.tserver.unsubscribe(ServerEvent.TradeChanged, callback);
        defaultLogger.log('to unsubscribe trade record change');
    }

    /**
     * 订阅单个持仓变化推送
     */
    SubscribePositionChange(callback: (data: SocketDataPackage<PositionInfo>) => void) {

        this.tserver.subscribe(ServerEvent.PositionChanged, callback);
        defaultLogger.log('to subscribe position change');
    }

    /**
     * 退订单个持仓变化推送
     */
    UnsubscribePositionChange<T = any>(callback: ServerMessageCallbackMethod<T>) {

        this.tserver.unsubscribe(ServerEvent.PositionChanged, callback);
        defaultLogger.log('to unsubscribe position change');
    }

    /**
     * 订阅风控消息
     */
    SubscribeRiskMessage(callback: (data: SocketDataPackage<RiskMessage>) => void) {

        this.tserver.subscribe(ServerEvent.RiskAlertReceived, callback);
        defaultLogger.log('to subscribe risk alert message');
    }

    /**
     * 退订风控消息
     */
    UnsubscribeRiskMessage<T = any>(callback: ServerMessageCallbackMethod<T>) {

        this.tserver.unsubscribe(ServerEvent.RiskAlertReceived, callback);
        defaultLogger.log('to unsubscribe risk alert message');
    }
}