<div class="creation-view">
    <el-dialog width="1400px" title="创建算法交易" :visible="visible" :show-close="false" :close-on-click-modal="false" v-drag>
        <template>
            <div class="creation-body">
                <div class="header themed-header">
                    <div class="input-area">

                        <div class="input-sth">
                            <label class="ctr-label">账号</label>
                            <el-select 
                                placeholder="请选择账号" 
                                class="s-w-150" 
                                v-model="recordId" 
                                @change="handleAccountChange"
                                filterable 
                                clearable>
                                <el-option 
                                    v-for="(item, item_idx) in accounts" 
                                    :key="item_idx" 
                                    :value="getProperAccountId(item)"
                                    :label="formatSelectAccountName(item)"></el-option>
                            </el-select>
                        </div>

                        <div class="input-sth">
                            <label class="ctr-label">算法</label>
                            <el-select 
                                placeholder="请选择算法" 
                                v-model="algoId" 
                                @change="handleAlgoChange"
                                class="s-w-150" 
                                filterable 
                                clearable>								
                                <el-option-group v-for="(group, group_idx) in algoGrps" :key="group_idx" :label="group.name">
                                    <el-option v-for="(item, item_idx) in group.algoes" :key="item_idx" :label="item.name" :value="item.id"></el-option>
                                </el-option-group>
                            </el-select>
                        </div>

                        <div class="input-sth">
                            <label class="ctr-label">母单名称</label>
                            <el-input v-model="taskName" class="s-w-150" clearable></el-input>
                        </div>

                        <div v-if="cost.buy > 0 || cost.sell > 0" class="input-sth">
                            <label class="ctr-label">预估总买入</label>
                            <label class="ctr-value s-color-red">{{ cost.buy >= 0 ? thousandsInt(cost.buy) : '--' }}</label>
                            <label class="ctr-label">预估总卖出</label>
                            <label class="ctr-value s-color-green">{{ cost.sell >= 0 ? thousandsInt(cost.sell) : '--' }}</label>
                        </div>

                    </div>
                    <div class="button-area">
                        <el-button type="primary" @click="confirm">保存</el-button>
                        <el-button type="info" @click="giveup">取消</el-button>
                    </div>
                </div>
                <div class="param-body">
                    <div class="part-left">
                        <div class="part-title left-part-title themed-header ctr-label">
                            <span class="title-text">设置算法</span>
                            <span v-show="isAccountChoosed()" class="account-info s-color-red s-cd">
                                <span>可用资金</span>
                                <span class="max-can-use">{{ thousandsDecimal(totalCanUse) }}</span>
                            </span>
                        </div>
                        <div class="part-body s-pdt-10">
                            <div v-for="(item, item_idx) in algoParams" v-show="!!item.display" :key="item_idx" class="param-row">
                                <label class="row-label">
                                    <span v-if="item.required" class="s-color-red">*</span>
                                    <span>{{ shortize(item.label) }}</span>
                                </label>
                                <span class="row-control">
                                    <template v-if="isIntegerParam(item.type)">
                                        <el-input-number v-model.number="item.defaultValue" :precision="0" :placeholder="item.remark || '请输入整数'" clearable></el-input-number>
                                    </template>
                                    <template v-else-if="isDecimalParam(item.type)">
                                        <el-input-number v-model.number="item.defaultValue" :precision="2" :placeholder="item.remark || '请输入数值'" clearable></el-input-number>
                                    </template>
                                    <template v-else-if="isTimeParam(item.type)">
                                        <el-time-picker v-model="item.defaultValue" :placeholder="item.remark || '请输入时间'" clearable></el-time-picker>
                                    </template>
                                    <template v-else-if="isTimeRangeParam(item.type)">
                                        <el-time-picker v-model="item.defaultValue" range-separator="至" start-placeholder="开始" end-placeholder="结束" is-range clearable></el-time-picker>
                                    </template>
                                    <template v-else-if="isTextParam(item.type)">
                                        <el-input v-model.trim="item.defaultValue" :placeholder="item.remark || '请输入'" clearable></el-input>
                                    </template>
                                    <template v-else-if="isUserOptionParam(item.type)" :placeholder="item.remark || '请选择'">
                                        <el-select v-model="item.defaultValue" clearable>
                                            <el-option v-for="(item2, item2_idx) in item.uoptions" :key="item2_idx" :label="item2.label" :value="item2.value"></el-option>
                                        </el-select>
                                    </template>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="part-right">
                        <div class="part-title themed-header">
                            <span class="ctr-label s-pdr-10">持仓标的，数量 = </span>
                            <span class="ctr-label s-pdr-10 s-color-white">{{ choices.length }}</span>
                            <el-select 
                                placeholder="添加持仓股票" 
                                class="s-mgr-10" 
                                style="width: 330px;" 
                                v-model="choices" 
                                @change="handleChoiceChange" 
                                multiple 
                                filterable 
                                clearable 
                                collapse-tags>
                                <el-option v-for="(item, item_idx) in positions" :key="item_idx" :value="item.instrument" :label="formStockDisplay(item)"></el-option>
                            </el-select>
                            <el-autocomplete
                                    class="s-mgr-10"
                                    placeholder="搜索添加股票"
                                    v-model="keywords"
                                    :fetch-suggestions="suggest"
                                    @keydown.native="handleUserInput"
                                    @clear="handleClearIns"
                                    @select="handleSelect"
                                    clearable>
								<template slot-scope="{ item: ins }">
									<span class="item-code">[{{ ins.instrument }}] </span>
									<span class="item-name">{{ ins.instrumentName }}</span>
								</template>
							</el-autocomplete>
                            <el-popover v-model="importing.visible" @hide="hideImport" placement="bottom" title="批量导入股票" width="200" trigger="manual">
                                <el-button slot="reference" type="primary" @click="importStocks">
                                    <i class="el-icon-bottom-right"></i>
                                    <span>粘贴导入</span>
                                </el-button>
                                <div class="pasted-area s-pd-10">
                                    <div class="import-button-row s-pd-5">
                                        <el-button type="primary" size="mini" @click="extractStocks">导入</el-button>
                                        <el-button type="info" size="mini" @click="hideImport">关闭</el-button>
                                    </div>
                                    <el-input 
                                        v-model="importing.content" 
                                        type="textarea"
                                        :rows="10"
                                        placeholder="粘贴或输入【股票代码】或【股票名称】，如 “SHSE.600919, 600036 300058, 宁德时代 , 002826” ">
                                    </el-input>
                                </div>
                            </el-popover>
                            <el-popover
                                placement="bottom"
                                title="导入算法委托"
                                trigger="hover"
                                >
                                <span class="import-table-sample">
                                    <span class="buttons-row">
                                        <el-button type="primary" size="mini" @click="downloadTemplate('csv')">下载CSV模板</el-button>
                                        <el-button type="primary" size="mini" @click="downloadTemplate('xlsx')">下载EXCEL模板</el-button>
                                    </span>
                                </span>
                                <el-button slot="reference" type="primary" class="s-mgl-10" @click="importTable">
                                    <i class="el-icon-document"></i>
                                    <span>表格导入</span>
                                </el-button>
                            </el-popover>
                        </div>
                        <div class="part-body">
                            <div class="stocks-list">
                                <table>
                                    <tr>
                                        <th label="代码/名称" min-width="140" prop="stock_code" formatter="formatCodeName" sortable overflowt></th>
                                        <!-- <th label="委托下单次数" min-width="110" prop="num" align="right" formatter="formatTimes" sortable thousands-int></th> -->
                                        <th label="买卖方向" min-width="80" prop="direction" formatter="formatDirectionSelect" sortable thousands-int></th>
                                        <th label="最新价" min-width="80" prop="price" align="right" sortable></th>
                                        <th label="目标数量(股)" min-width="80" prop="targetPosition" align="right" formatter="formatTarget" sortable thousands-int></th>
                                        <th label="可平仓位(股)" min-width="80" prop="closablePosition" align="right" sortable thousands-int></th>
                                        <th label="总仓位(股)" min-width="80" prop="totalPosition" align="right" sortable thousands-int></th>
                                        <th label="操作" min-width="60" fixed="right" formatter="formatActions"></th>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>    
    </el-dialog>
</div>