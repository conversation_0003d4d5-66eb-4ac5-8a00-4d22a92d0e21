<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  instrumentCol,
  instrumentNameCol,
  assetTypeCol,
  directionCol,
  yesterdayPositionCol,
  todayPositionCol,
  frozenVolumeCol,
  avgPriceCol,
  marketValueCol,
  floatProfitCol,
  closeProfitCol,
  usedCommissionCol,
  usedMarginCol,
  updateTimeCol,
  accountNameCol,
} from './shared/columnDefinitions';
import { putRow } from '@/script';
import type { AccountInfo, ColumnDefinition } from '@/types';
import VirtualizedTable from '../VirtualizedTable.vue';
import { RecordService, TradingService } from '@/api';
import type {
  PositionInfo,
  SocketDataPackage,
  LegacyFundInfo,
} from '../../../../../xtrade-sdk/dist';
import { TableV2SortOrder, type SortBy } from 'element-plus';

// 定义组件属性
const {
  type,
  activeItem,
  enableAsyncLoading = false,
} = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
  /** 是否启用异步加载模式 */
  enableAsyncLoading?: boolean;
}>();

// 基础列定义
const baseColumns = [
  instrumentCol,
  instrumentNameCol,
  assetTypeCol,
  directionCol,
  yesterdayPositionCol,
  todayPositionCol,
  frozenVolumeCol,
  avgPriceCol,
  marketValueCol,
  floatProfitCol,
  closeProfitCol,
  usedCommissionCol,
  usedMarginCol,
  updateTimeCol,
] as ColumnDefinition<PositionInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.splice(2, 0, accountNameCol as any);
  }
  return cols;
});

const tableRef = useTemplateRef('tableRef');
const positions = shallowRef<PositionInfo[]>([]);
const totalPositionCount = shallowRef(0);

// 异步数据加载器
const asyncDataLoader = async (params: {
  startIndex: number;
  endIndex: number;
  searchQuery?: string;
  sortBy?: SortBy;
}) => {
  if (!activeItem) {
    return { data: [], total: 0 };
  }

  const { startIndex, endIndex, searchQuery, sortBy } = params;

  try {
    // 调用后端 API 获取分页数据
    const response = await RecordService.getTodayPositionsPaged({
      accountId: activeItem.id,
      offset: startIndex,
      limit: endIndex - startIndex + 1,
      search: searchQuery,
      sortField: sortBy?.key as string,
      sortOrder: sortBy?.order,
    });

    return {
      data: response.data || [],
      total: response.total || 0,
    };
  } catch (error) {
    console.error('Failed to load positions:', error);
    ElMessage.error('加载持仓数据失败');
    return { data: [], total: 0 };
  }
};

// 监听账户/产品变化，重新获取持仓数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      if (enableAsyncLoading) {
        // 异步模式下，刷新表格数据
        tableRef.value?.refreshAsyncData();
      } else {
        // 同步模式下，直接获取数据
        fetchPositions();
      }
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    if (enableAsyncLoading) {
      // 异步模式下，获取总数
      fetchTotalCount();
    } else {
      // 同步模式下，获取全部数据
      fetchPositions();
    }
  }

  // 订阅实时数据更新
  TradingService.subscribePositionChange(handlePositionChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribePositionChange(handlePositionChange);
});

/** 监听持仓变化 */
const handlePositionChange = (data: SocketDataPackage<PositionInfo>) => {
  const { body } = data;
  if (body) {
    if (enableAsyncLoading) {
      // 异步模式下，刷新相关数据
      tableRef.value?.refreshAsyncData();
    } else {
      // 同步模式下，更新本地数据
      putRow(body, positions, 'instrument');
    }
  }
};

// 获取持仓数据（同步模式）
const fetchPositions = async () => {
  if (!activeItem) return;

  try {
    const data = await RecordService.getTodayPositions(activeItem.id);
    positions.value = data;
  } catch (error) {
    console.error('Failed to fetch positions:', error);
    ElMessage.error('获取持仓数据失败');
  }
};

// 获取总数（异步模式）
const fetchTotalCount = async () => {
  if (!activeItem) return;

  try {
    const response = await RecordService.getTodayPositionsCount(activeItem.id);
    totalPositionCount.value = response.total || 0;
  } catch (error) {
    console.error('Failed to fetch total count:', error);
    totalPositionCount.value = 0;
  }
};

// 刷新数据
const refreshData = () => {
  if (enableAsyncLoading) {
    tableRef.value?.refreshAsyncData();
  } else {
    fetchPositions();
  }
};

// 平仓选中的持仓
const closeSelectedPositions = () => {
  if (tableRef.value?.selectedRows.length === 0) {
    ElMessage.warning('请选择持仓');
    return;
  }

  ElMessage.warning('待实现');
  // TODO: 实现平仓逻辑
  console.log('平仓选中的持仓:', tableRef.value?.selectedRows);
};

// 预加载下一页数据
const preloadNextPage = () => {
  if (enableAsyncLoading) {
    // 预加载下一页数据以提升用户体验
    tableRef.value?.preloadData(50, 99);
  }
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    select
    fixed
    :columns="columns"
    :data="enableAsyncLoading ? undefined : positions"
    :enable-async-loading="enableAsyncLoading"
    :async-data-loader="enableAsyncLoading ? asyncDataLoader : undefined"
    :total-count="enableAsyncLoading ? totalPositionCount : undefined"
    :page-size="50"
    :sort="{ key: 'updateTime', order: TableV2SortOrder.DESC }"
  >
    <template #actions>
      <div class="actions" flex aic gap-2>
        <el-button @click="refreshData" size="small" color="var(--g-primary)">刷新</el-button>
        <el-button v-if="enableAsyncLoading" @click="preloadNextPage" size="small" type="info">
          预加载
        </el-button>
        <el-button @click="closeSelectedPositions" size="small" type="warning">平仓选中</el-button>
        <span v-if="enableAsyncLoading" class="text-sm text-gray-500">
          异步模式 - 总计 {{ totalPositionCount }} 条
        </span>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped>
.actions {
  gap: 8px;
}
</style>
