<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElDialog, ElButton } from 'element-plus';
import type { ConfirmDialogServiceOption } from '@/types/components/basic';

const props = defineProps<ConfirmDialogServiceOption>();
const dialogVisible = ref(true);
const dialogWidth = ref<number | string>('330px');

const confirmButtonType = computed(() => {
  return props.confirmType || 'danger';
});

const confirmButtonText = computed(() => {
  return props.confirmText || '确认';
});

const cancelButtonType = computed(() => {
  return props.cancelType || 'default';
});

const cancelButtonText = computed(() => {
  return props.cancelText || '取消';
});

function handleConfirm() {
  if (props.onConfirm) {
    props.onConfirm();
  }
}

function handleCancel() {
  if (props.onCancel) {
    props.onCancel();
  }
}

const handleClose = (done: () => void) => {
  done();
  handleCancel();
};
</script>

<template>
  <el-dialog
    class="custom-confirm"
    :title="title"
    v-model="dialogVisible"
    :width="dialogWidth"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :before-close="handleClose"
    draggable
  >
    <template #header>
      <div class="dialog-header">
        <slot name="header">
          <div h-44>
            <i class="iconfont icon-warning-circle-fill" fs-44 style="color: rgb(236, 96, 99)"></i>
          </div>
          <div mt-16 fs-20 fw-400>{{ title }}</div>
        </slot>
      </div>
    </template>
    <div class="dialog-content">
      <slot name="content">
        <div class="msg-text" fs-14 fw-400>{{ message }}</div>
      </slot>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <slot name="footer">
          <div class="button-row confirm" mt-8>
            <el-button :type="confirmButtonType" @click="handleConfirm">
              {{ confirmButtonText }}
            </el-button>
          </div>
          <div class="button-row cancel" mt-16>
            <el-button :type="cancelButtonType" @click="handleCancel">
              {{ cancelButtonText }}
            </el-button>
          </div>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.custom-confirm {
  .dialog-content {
    margin-top: 8px;
    .msg-text {
      color: white;
      opacity: 0.7;
    }
  }

  .dialog-footer {
    text-align: right;

    .button-row {
      height: 44px;
      display: flex;

      > button {
        width: 100%;
        height: 100%;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}
</style>
