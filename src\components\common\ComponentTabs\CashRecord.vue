<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { onMounted, shallowRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import type { CashInfo } from '../../../../../xtrade-sdk';
import { Repos } from '../../../../../xtrade-sdk/dist';
import { tradingDayCol, inMoneyCol, outMoneyCol, createTimeCol } from './shared/columnDefinitions';

const recordsRepo = new Repos.RecordsRepo();

// 定义组件属性
const { activeItem, refreshKey } = defineProps<{
  activeItem?: AccountInfo;
  refreshKey: number;
}>();

// 基础列定义
const columns = [
  tradingDayCol,
  {
    key: 'identityName',
    dataKey: 'identityName',
    title: '账号名称',
    width: 200,
    sortable: true,
  },
  inMoneyCol,
  outMoneyCol,
  {
    key: 'operatorUserName',
    dataKey: 'operatorUserName',
    title: '操作员',
    width: 160,
    sortable: true,
  },
  createTimeCol,
  {
    key: 'type',
    dataKey: 'type',
    title: '备注',
    width: 160,
    sortable: true,
    cellRenderer: ({ cellData }) => (cellData === 1 ? <div>分红入金</div> : <div>---</div>),
    textRenderer: cellData => (cellData === 1 ? '分红入金' : '---'),
  },
] as ColumnDefinition<CashInfo>;

// 出入金数据
const records = shallowRef<CashInfo[]>([]);

// 获取出入金数据
const fetchRecords = async () => {
  if (!activeItem) return;
  const resp = await recordsRepo.getIoMoney(activeItem.id, '');
  if (resp && resp.data) {
    records.value = resp.data;
  } else {
    records.value = [];
  }
};

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchRecords();
    }
  },
  { deep: true },
);

// 监听refreshKey变化
watch(
  () => refreshKey,
  () => {
    if (activeItem) {
      fetchRecords();
    }
  },
);

onMounted(() => {
  if (activeItem) {
    fetchRecords();
  }
});
</script>

<template>
  <div flex="~ col" h-full>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
      :columns
      :data="records"
      flex-1
      min-h-1
    ></VirtualizedTable>
  </div>
</template>

<style scoped></style>
