.creation-view {
    color: white;
}

.creation-body {

    .header {
        
        height: 40px;
        padding-right: 10px;
        border-radius: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .input-area {
            display: flex;
            align-items: center;
        }

        .input-sth {
            display: flex;
            align-items: center;
        }

        .ctr-label {
            display: inline-block;
            margin-left: 10px;
            margin-right: 10px;
        }
    }

    .param-body {

        margin-top: 10px;
        display: flex;

        .part-left {

            width: 30%;
            box-sizing: border-box;
            padding-right: 10px;
            display: flex;
            flex-direction: column;

            .el-input,
            .el-input-number,
            .el-input__inner {
                width: 150px;
            }
        }

        .part-right {

            width: 70%;
            box-sizing: border-box;
            padding-left: 10px;
            display: flex;
            flex-direction: column;
        }

        .part-title {

            padding-left: 10px;
            line-height: 38px;
            font-size: 14px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }

        .left-part-title {

            display: flex;
            justify-content: space-between;
            padding-right: 10px;
        }

        .part-body {

            height: 500px;
            flex-grow: 1;
            background-color: #2A2929;

            .param-row {

                line-height: 38px;
                padding-left: 10px;
                display: flex;
            }

            .row-control {
                flex-grow: 1;
            }

            .el-input__inner {
                text-align: left;
            }
        }
    }

    .ctr-label,
    .row-label {
        color: #aaa;
    }

    .row-label {

        display: inline-block;
        width: 90px;
    }

    .stocks-list {

        .el-input {

            margin-left: 0 !important;
            width: 100%;
        }
    }
}