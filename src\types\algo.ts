import type {
  AlgorithmExecuteTimeEnum,
  ExpirationUnfinishedTreatmentEnum,
  TradeStyleEnum,
} from '@/enum/trade';

/**
 * 算法交易数据接口
 */
export interface AlgorithmTradeInfo {
  /**
   * 最大可买/卖
   */
  maxVolume: number;

  /**
   * 数量
   */
  volume: number;

  /**
   * 算法ID
   */
  algorithmId: number;

  /**
   * 时间类型
   */
  executionTime: AlgorithmExecuteTimeEnum;

  /**
   * 起止时间
   */
  timeRange: [Date, Date];

  /**
   * 量比比例
   */
  volumeRatio: number;

  /**
   * 开盘集合竞价
   */
  openingCallAuction: boolean;

  /**
   * 开盘集合竞价参与比例
   */
  openingCallAuctionParticipation: number;

  /**
   * 开盘集合竞价价格偏移
   */
  openingCallAuctionPriceOffset: number;

  /**
   * 触价
   */
  maxPrice: number;

  /**
   * 到期未成处理
   */
  unfinishedTreatment: ExpirationUnfinishedTreatmentEnum;

  /**
   * 交易风格
   */
  tradingStyle: TradeStyleEnum;

  /**
   * 撤单率
   */
  cancellationRate: number;

  /**
   * 单笔最小量
   */
  minSingleVolume: number;

  /**
   * 投资备注
   */
  investmentNotes: string;
}
