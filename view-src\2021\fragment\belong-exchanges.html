<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords"
				  v-model="states.keywords"
				  @change="handleSearch" clearable></el-input>
	</div>

	<div class="data-list">

		<table>
			<tr>
				<th label="报单编号" 
					min-width="80" 
					prop="exchangeOrderId" overflowt sortable searchable></th>

				<th label="账号名称" 
					min-width="202.0" 
					prop="accountName" 
					formatter="formatAccountName" overflowt filterable sortable searchable></th>

				<th label="发起人" 
					min-width="90" 
					prop="userName" overflowt filterable sortable searchable></th>

				<th label="代码" 
					fixed-width="100" 
					prop="instrument" overflowt filterable sortable searchable></th>

				<th label="名称" 
					fixed-width="80" 
					prop="instrumentName" overflowt filterable sortable searchable></th>

                <th label="方向" 
                    fixed-width="70" 
                    prop="direction" 
                    watch="direction" 
                    formatter="formatDirection"
                    export-formatter="formatDirectionText"
                    export-formatter="formatDirectionText"
                    filter-data-provider="rebindDirection" sortable></th>

                <th label="成交量" 
                    fixed-width="80" 
                    prop="volume"
                    align="right" filterable sortable summarizable thousands-int></th>

                <th label="成交价" 
                    fixed-width="60" 
                    prop="tradedPrice" 
                    align="right" 
                    formatter="formatPrice" sortable></th>

                <th label="手续费" 
                    min-width="80" 
                    prop="commission" 
                    align="right" sortable thousands summarizable></th>

                <th label="成交编号" 
                    min-width="150"
                    prop="tradeId" overflowt sortable searchable></th>

                <th label="成交时间" 
                    fixed-width="100" 
                    prop="tradeTime" 
                    watch="tradeTime" 
                    formatter="formatTime" sortable></th>

                <th label="资产类型" 
                    fixed-width="100" 
                    prop="assetType" 
                    watch="assetType" 
                    formatter="formatAssetType" 
                    filter-data-provider="rebindAssetType" sortable></th>
			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>