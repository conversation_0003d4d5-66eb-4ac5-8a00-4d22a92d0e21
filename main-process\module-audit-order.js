﻿const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;

class AuditOrderModule extends ServerEnvMainModule {

    constructor(module_name) {

        super(module_name);
        this.ordersPool = [];
    }

    /**
     * @param {*} instruction_status
     * @param {Array<Number>} orders 要提交审核的订单ID
     */
    submitAudit(event, instruction_status, orders) {

        if (orders.length == 0) {
            return;
        }

        this.loggerSys.debug('test0331: to audit(pass/reject) instructions: ' + JSON.stringify({ orders, instruction_status }));
        // 组织发送数据
        let body = { instructionIds: orders, instructionStatus: instruction_status };
        let message = { fc: this.serverFunction.auditOrder, reqId: 0, dataType: 1, body: body };
        this.tradingServer.send(message);
    }

    handleInstruction(message_package) {

        let new_order = JSON.parse(message_package.body.toString());
        this.loggerSys.debug('test0331: received instruction: ' + JSON.stringify(new_order));

        /**
         * 当前审核视图暂位于主窗口之内，故消息发往主窗口
         */

        const targetWin = this.centralWindow;
        targetWin && targetWin.webContents.send(this.systemEvent.notifyInstruction, new_order);
    }

    handleFeedback(message_package) {

        /**
         * feedback仅包含 error codee & error message, 并不能标识为某一个订单的处理结果
         * 故，对该通知不作处理
         */
        let audit_feedback = JSON.parse(message_package.body);
        this.loggerSys.debug('test0331: received audit feedback: ' + JSON.stringify(audit_feedback));
    }

    listen2Events() {
        
        // 监听来自界面的订单审核指令
        this.mainProcess.on(this.systemEvent.auditOrder, this.submitAudit.bind(this));
    }

    listen2ServerEvents() {

        this.mainProcess.on(this.systemEvent.sysLoadingCompleted, (event) => {
			
			// 监听来自服务器的待审核订单通知
            this.tradingServer.listen2Event(this.serverEvent.notifyInstruction, this.handleInstruction.bind(this));
            // 监听来自服务器的订单审核反馈
            this.tradingServer.listen2Event(this.serverEvent.serverJudgeResult, this.handleFeedback.bind(this));
		});
    }

    run() {

        this.loggerSys.info('load module order auditing > begin');
        this.listen2Events();
        this.listen2ServerEvents();
        this.loggerSys.info('load module order auditing > end');
    }
}

module.exports = { AuditOrderModule };
