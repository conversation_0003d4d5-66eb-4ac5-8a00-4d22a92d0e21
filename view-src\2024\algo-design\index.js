const { IView } = require('../../../component/iview');
const drag = require('../../../directives/drag');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { SmartTable } = require('../../../libs/table/smart-table');
const { BrokerInfo } = require('../../../model/broker');
const { AlgoParamDataTypes, GaoyuAlgoHelper, AlgoVendor, GaoyuAlgo, GaoyuAlgoParam } = require('../../../model/algo-vendor');
const { repoBroker } = require('../../../repository/broker');
const { repoAlgo } = require('../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '算法参数管理');
    }

    get $cform() {
        return this.toolbarApp.$refs.cform;
    }

    /**
     * @returns {Array<GaoyuAlgo>}
     */
    get algoes() {
        return this.tableObj.extractAllRecords();
    }

    clear() {

        this.$cform.resetFields();
        this.formd.params.clear();
    }

    /**
     * @param {GaoyuAlgo} algo 
     */
    openCreate(algo) {

        this.dialog.visible = true;
        this.toolbarApp.$nextTick(() => {

            this.clear();
            algo && Object.assign(this.formd, this.helper.deepClone(algo));
        });
    }

    openDesign() {
        
        if (this.designer === undefined) {
            
            const ParamDesigner = require('./design');
            const designer = new ParamDesigner('@2024/algo-design/design', false);
            designer.loadBuild(this.$designBox, null, _ => {});
            this.designer = designer;
        }
        else {
            this.designer.trigger('showup');
        }
    }

    toCreate() {

        this.$cform.validate(valid => {

            if (!valid) {
                return this.interaction.showError('请按提示完成表单填写');
            }
            else if (this.formd.params.length == 0) {
                return this.interaction.showError('请至少添加一个参数');
            }

            const isNone = this.helper.isNone;
            const errorIndex = this.formd.params.findIndex(x => isNone(x.label) || isNone(x.prop) || isNone(x.type))

            if (errorIndex >= 0) {

                let matched = this.formd.params[errorIndex];
                let which;

                if (isNone(matched.label)) {
                    which = '名称';
                }
                else if (isNone(matched.prop)) {
                    which = '变量';
                }
                else if (isNone(matched.type)) {
                    which = '类型';
                }

                return this.interaction.showError(`第${errorIndex + 1}行参数【${which}】填写不完整`);
            }

            this.createAlgo(this.helper.deepClone(this.formd));
            this.dialog.visible = false;
        });
    }

    /**
     * @param {GaoyuAlgo} algo 
     */
    editAlgo(algo) {
        this.openCreate(algo);
    }

    toCancel() {
        this.dialog.visible = false;
    }

    confirmEmpty() {

        this.formd.params.push(new GaoyuAlgoParam({

            label: null,
            prop: null,
            type: AlgoParamDataTypes.integer.value,
            remark: null,
            defaultValue: null,
            display: true,
        }));
    }

    deleteParam(item) {
        this.formd.params.remove(x => x === item);
    }

    /**
     * @param {GaoyuAlgoParam} param_item 
     */
    addNewOption(param_item) {
        this.toggleShowAddOption(param_item);
    }

    /**
     * @param {GaoyuAlgoParam} param_item 
     */
    cancelNewOption(param_item) {
        this.toggleShowAddOption(param_item);
    }

    /**
     * @param {GaoyuAlgoParam} param_item 
     */
    toggleShowAddOption(param_item) {

        param_item.adding = !param_item.adding;
        param_item.optionLabel = '';
        param_item.optionValue = '';
    }

    /**
     * @param {GaoyuAlgoParam} param_item 
     */
    confirmNewOption(param_item) {
        
        if (this.helper.isNone(param_item.optionLabel) || this.helper.isNone(param_item.optionValue)) {
            return this.interaction.showError('选项文字或取值，缺失');
        }
        else if (param_item.uoptions.some(x => x.label == param_item.optionLabel || x.value == param_item.optionValue)) {
            return this.interaction.showError('选择文字或取值，存在重复');
        }
        else {

            let item = { label: param_item.optionLabel, value: param_item.optionValue };
            if (typeof item.value == 'string' && item.value.toLowerCase() == 'true') {
                item.value = true;
            }
            else if (typeof item.value == 'string' && item.value.toLowerCase() == 'false') {
                item.value = false;
            }

            param_item.uoptions.push(item);
            if (this.helper.isNone(param_item.defaultValue)) {
                param_item.defaultValue = item.value;
            }

            this.toggleShowAddOption(param_item);
        }
    }
    
    /**
     * @param {GaoyuAlgoParam} param_item 
     */
    deleteOption(param_item, value) {

        param_item.uoptions.remove(x => x.value == value);
        let choosedSome = this.helper.isNotNone(param_item.defaultValue);
        let isChoosedOk = param_item.uoptions.some(x => x.value == param_item.defaultValue);

        if (choosedSome && !isChoosedOk) {
            param_item.defaultValue = null;
        }
    }

    /**
     * @param {GaoyuAlgoParam} param_item 
     */
    handleDataTypeChange(param_item) {
        param_item.defaultValue = null;
    }

    createToolbarApp() {

        this.vendors = [new AlgoVendor({})].splice(1);
        this.brokers = [new BrokerInfo({})].splice(1);
        this.formd = new GaoyuAlgo({});
        this.dataTypes = AlgoParamDataTypes;
        this.dialog = { visible: false };

        this.strategyTypes = [

            { label: '拆单', value: 0 },
            { label: '透传委托', value: 1 },
            { label: 'T0算法', value: 2 },
        ];
        
        this.rules = {

            externalId: { required: false, message: '请输入外部ID' },
            name: { required: true, message: '请输入名称' },
            vendorId: { required: true, message: '请选择算法开发商' },
            brokerId: { required: true, message: '请选择券商' },
            strategyType: { required: false, message: '请选择算法类型' },
            remark: { required: false, message: '算法说明' },
        };

        this.toolbarApp = new Vue({

            el: this.$toolbar,
            directives: { drag },
            data: {

                dialog: this.dialog,
                formd: this.formd,
                rules: this.rules,
                brokers: this.brokers,
                vendors: this.vendors,
                uoptions: [{ label: '', value: '' }].splice(1),
                dataTypes: this.dataTypes,
                strategyTypes: this.strategyTypes,
            },                
            methods: this.helper.fakeVueInsMethod(this, [

                this.isInteger,
                this.isDecimal,
                this.isTime,
                this.isTimeRange,
                this.isText,                
                this.isUserOption,                
                this.openCreate,
                this.openDesign,
                this.toCreate,
                this.toCancel,
                this.confirmEmpty,
                this.deleteParam,
                this.handleDataTypeChange,
                this.addNewOption,
                this.cancelNewOption,
                this.confirmNewOption,
                this.deleteOption,
            ]),
        });
    }

    isInteger(dataType) {
        return GaoyuAlgoHelper.isInteger(dataType);
    }

    isDecimal(dataType) {
        return GaoyuAlgoHelper.isDecimal(dataType);
    }

    isTime(dataType) {
        return GaoyuAlgoHelper.isTime(dataType);
    }

    isTimeRange(dataType) {
        return GaoyuAlgoHelper.isTimeRange(dataType);
    }

    isText(dataType) {
        return GaoyuAlgoHelper.isText(dataType);
    }

    isUserOption(dataType) {
        return GaoyuAlgoHelper.isUserOption(dataType);
    }

    /**
     * @param {GaoyuAlgo} record 
     */
    formatBroker(record) {
        
        let matched = this.brokers.find(x => x.brokerId == record.brokerId);
        return matched ? matched.brokerName : record.brokerId;
    }

    /**
     * @param {GaoyuAlgo} record 
     */
    formatVendor(record) {

        let matched = this.vendors.find(x => x.vendorId == record.vendorId);
        return matched ? matched.vendorName : record.vendorId;
    }

    /**
     * @param {GaoyuAlgo} record 
     */
    formatStrategyType(record) {
        
        let matched = this.strategyTypes.find(x => x.value == record.strategyType);
        return matched ? matched.label : record.strategyType;
    }

    /**
     * @param {GaoyuAlgo} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    createTable() {
        
        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable(this.$table, this.identifyRecord, this, { tableName: 'smt-xf21o', displayName: this.title });
        this.tableObj.setPageSize(999);
        this.registerEvent(this.systemEvent.tabActivated, () => { this.tableObj.fitColumnWidth(); });
    }

    async requestBrokers() {

        let resp = await repoBroker.getAll();
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0 && data instanceof Array) {

            let brokers = data.map(x => new BrokerInfo(x));
            let stockBrokers = brokers.filter(x => x.brokerType == this.systemEnum.assetsType.stock.code)
            this.brokers.refill(stockBrokers);
        }
        else {
            this.interaction.showError(`获取经纪商出错：${errorCode}/${errorMsg}`);
        }
    }

    async requestVendors() {

        // todo24

        this.vendors.refill([
            new AlgoVendor({ vendorId: 'kafang', vendorName: '卡方' }),
            new AlgoVendor({ vendorId: 'haoxing', vendorName: '皓兴' }),
            new AlgoVendor({ vendorId: 'yuliang', vendorName: '宇量' }),
        ]);
    }

    /**
     * @param {GaoyuAlgo} algo 
     */
    async createAlgo(algo) {

        algo.userId = this.userInfo.userId;
        algo.params.forEach(item => {
            item.defaultValue = GaoyuAlgoHelper.upside(item.type, item.defaultValue);
        });

        let resp = await repoAlgo.createInternalAlgo(algo);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.clear();
            this.interaction.showSuccess('已保存');
            this.requestAlgoes();
        }
        else {
            this.interaction.showError(`保存失败：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {GaoyuAlgo} item 
     */
    async deleteAlgo(item) {

        let resp = await repoAlgo.deleteInternalAlgo(item.id);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess('已删除');
            this.tableObj.deleteRow(this.identifyRecord(item));
        }
        else {
            this.interaction.showError(`删除失败：${errorCode}/${errorMsg}`);
        }
    }

    async requestAlgoes() {
        
        let resp = await repoAlgo.queryInternalAlgoes();
        let { data, errorCode, errorMsg } = resp;

        if (errorCode == 0 && data instanceof Array) {
            this.tableObj.refill(data.map(x => new GaoyuAlgo(x)));
        }
        else {
            this.interaction.showError(`算法查询失败：${errorCode}/${errorMsg}`);
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.requestAlgoes();
    }

    async build($container) {

        super.build($container);
        this.$toolbar = $container.querySelector('.toolbar');
        this.$table = $container.querySelector('.data-table');
        this.$designBox = $container.querySelector('.design-box');
        this.createToolbarApp();
        this.createTable();
        await this.requestBrokers();
        await this.requestVendors();
        this.requestAlgoes();
    }
}

module.exports = View;
