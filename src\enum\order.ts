/** 订单状态枚举 */
export enum OrderStatusEnum {
  待审核 = 0,
  订单创建 = 10,
  订单待确认 = 13,
  订单已确认 = 15,
  未成交 = 16,
  部分成交 = 17,
  部分成交撤单 = 18,
  全成 = 19,
  废单 = 30,
  待撤 = 53,
  已撤 = 54,
  已驳回 = 55,
}

/** 已完成订单状态 */
export const COMPLETED_ORDER_STATUSES = [
  OrderStatusEnum.全成,
  OrderStatusEnum.废单,
  OrderStatusEnum.已撤,
  OrderStatusEnum.已驳回,
  OrderStatusEnum.部分成交撤单,
];

/** 未完成订单状态 */
export const NOT_COMPLETED_ORDER_STATUSES = Object.values(OrderStatusEnum)
  .filter(status => typeof status === 'number')
  .filter(status => !COMPLETED_ORDER_STATUSES.includes(status as OrderStatusEnum));

/** 判断订单是否可撤销 */
export const isOrderCancelable = (status: OrderStatusEnum) => {
  return NOT_COMPLETED_ORDER_STATUSES.includes(status);
};
