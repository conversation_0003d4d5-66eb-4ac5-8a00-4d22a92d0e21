class tradingDayRepository {
  get httpContext() {
    return require("../libs/http").http;
  }

  constructor() {}

  addTradingDay(tradingDay) {
    return new Promise((resolve, reject) => {
      this.httpContext
        .post(
          "/tradingDay",
          {},
          {
            params: {
              trading_day: tradingDay
            }
          }
        )
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }

  deleteTradingDay(tradingDay) {
    return new Promise((resolve, reject) => {
      this.httpContext
        .delete("/tradingDay", {
          params: {
            trading_day: tradingDay
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }

  generateTradingDay(year) {
    return new Promise((resolve, reject) => {
      this.httpContext
        .put(
          "/tradingDay",
          {},
          {
            params: {
              year: year
            }
          }
        )
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }

  getCurrentTradingDay() {
    return new Promise((resolve, reject) => {
      this.httpContext.get("/tradingDay").then(
        resp => {
          resolve(resp.data);
        },
        err => {
          reject(err);
        }
      );
    });
  }

  getTradingDayBatch({ beginDay, endDay }) {
    return new Promise((resolve, reject) => {
      this.httpContext
        .get("/tradingDay/batch", {
          params: {
            begin_day: beginDay,
            end_day: endDay
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }
}

module.exports = { repoTradingDay:  new tradingDayRepository() };
