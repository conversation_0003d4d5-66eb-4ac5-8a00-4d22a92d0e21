const remote = require('@electron/remote');
const { AlgoParamPartView } = require('../../2024/algo-param-part-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { NumberMixin } = require('../../../mixin/number');
const { BizHelper } = require('../../../libs/helper-biz');
const { AlgoPositionInfo } = require('../../../model/algot');
const { Position } = require('../../../model/position');
const { AccountDetail } = require('../../../model/account');
const { repoAccount } = require('../../../repository/account');
const { repoPosition } = require('../../../repository/position');
const { repoAlgo, AlgorithmClasses } = require('../../../repository/algorithm');
const { repoTrading } = require('../../../repository/trading');
const drag = require('../../../directives/drag');

class View extends AlgoParamPartView {

    constructor(view_name) {

        super(view_name, '创建算法交易');
        this.positions = [new AlgoPositionInfo({})].splice(1);
        this.directionMap = this.systemTrdEnum.algoTradingDirection;
        this.directions = Object.values(this.directionMap);
    }

    get productId() {
        
        let matched = this.getContextAccount();
        return matched ? matched.fundId : null;
    }

    get strategyId() {

        let matched = this.getContextAccount();
        return matched ? matched.strategyId : null;
    }

    get accountId() {

        let matched = this.getContextAccount();
        return matched ? matched.accountId : null;
    }

    get choices() {
        return this.crdata.choices;
    }

    getContextAccount() {
        return this.accounts.find(x => this.getProperAccountId(x) == this.crdata.recordId);
    }

    /**
     * @param {Array<AlgoPositionInfo>} records 
     */
    typeds(records) {
        return records;
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    typedsOne(record) {
        return record;
    }

    openCreation() {

        const ref = this.crdata;
        ref.recordId = null;
        ref.algoId = null;
        ref.taskName = null;
        ref.visible = true;

        this.reset();
        this.toSync();
    }

    shortize(label) {
        return typeof label == 'string' ? label.substring(0, 7) : label;
    }

    /**
     * @param {AlgoPositionInfo} position 
     */
    formStockDisplay(position) {
        return `${position.instrument} / ${position.instrumentName} / ${position.closablePosition}`;
    }

    reset() {

        this.positions.clear();
        this.tableObj.clear();
        this.tableObj.fitColumnWidth();
        this.attachPrice();
    }

    /**
     * 同步仓位、可用资金数据
     */
    toSync() {
        
        if (this.isAccountChoosed()) {

            this.requestPositions();
            this.requestAccountDetail();
        }
    }

    handleAccountChange() {

        this.requestAlgoes();
        this.reset();
        this.toSync();
    }

    handleAlgoChange() {
        this.alignAlgoParams(this.crdata.algoId);
    }

    isFormalSell(direction) {

        return direction == this.directionMap.sell.code
            || direction == this.directionMap.sell_to_return.code;
    }

    isAsSell(direction) {

        return direction == this.directionMap.sell.code 
            || direction == this.directionMap.borrow_sell.code
            || direction == this.directionMap.sell_to_return.code;
    }

    handleChoiceChange() {
        
        /**
         * 将：持仓【未选中】，且在表格里【方向】为【卖出】的数据行，删除
         */

        let unchecks = this.positions.filter(x => !this.isPositionChoosed(x.instrument));
        unchecks.forEach(x => {

            let matched = this.typedsOne(this.tableObj.getRowData(x.instrument));
            if (matched && this.isAsSell(matched.direction)) {
                this.tableObj.deleteRow(x.instrument);
            }
        });
        
        /**
         * 将：持仓【选中】，且未在表格里的数据行，插入
         */

        this.choices.forEach(instrument => {

            let matched = this.typedsOne(this.tableObj.getRowData(instrument));
            if (matched) {
                return;
            }

            let position = this.matchPosition(instrument);
            let row = this.createRow(instrument, position.instrumentName);
            row.direction = this.directionMap.sell.code;
            row.targetPosition = position.closablePosition;
            this.tableObj.putRow(row);
        });

        this.attachPrice();
    }

    matchPosition(instrument) {
        return this.positions.find(x => x.instrument == instrument);
    }

    createRow(instrument, instrumentName) {

        let matched = this.matchPosition(instrument);
        let position = new AlgoPositionInfo({

            instrument,
            instrumentName,
            // 新添加合约，默认买入方向
            direction: this.directionMap.buy.code,
            price: null,
            totalPosition: matched ? matched.totalPosition : 0,
            closablePosition: matched ? matched.closablePosition : 0,
            targetPosition: matched ? matched.closablePosition : 0,
        });

        return position;
    }

    handleSelect(selected) {
        
        let { instrument, instrumentName } = selected;
        let row = this.createRow(instrument, instrumentName);
        this.tableObj.putRow(row);
        this.sync2Choice(instrument, 'add');
        this.attachPrice();
    }

    /**
     * @param {string} instrument 
     * @param {string} behavior 'add' 或 'delete'
     */
    sync2Choice(instrument, behavior) {

        let position = this.matchPosition(instrument);
        let choosed = this.isPositionChoosed(instrument);

        if (!position) {
            return;
        }

        if (behavior == 'add' && !choosed) {
            
            /**
             * 添加合约，如果对应有持仓，且没有选中，则自动勾选
             */

            this.choices.push(instrument);
        }
        else if (behavior == 'delete' && choosed) {
            
            /**
             * 删除合约，如果对应有持仓，且已选中，则自动取消勾选
             */

            this.choices.remove(x => x == instrument);
        }
    }

    /**
     * @param {String} keywords 
     * @param {Function} callback 
     */
    suggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {

            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleUserInput() {

        const ref = this.crdata;
        if (event.keyCode == 8) {

            event.returnValue = false;
            ref.keywords = null;
            this.handleClearIns();
        }
        else if (typeof ref.keywords == 'string' && ref.keywords.trim().length == 0) {
            this.handleClearIns();
        }
    }

    handleClearIns() {

        var ref = this.crdata;
        ref.keywords = null;
    }

    importStocks() {

        let is_ok = true;

        if (!this.isAccountChoosed()) {

            is_ok = false;
            this.interaction.showError('请选择一个账号');
        }

        this.crdata.importing.visible = is_ok;
    }

    downloadTemplate(fextension) {

        if (fextension == 'csv') {
            this.thisWindow.webContents.downloadURL('https://static.gaoyusoft.com/xtrade/template/算法交易-导入模板.csv');
        }
        else {
            this.thisWindow.webContents.downloadURL('https://static.gaoyusoft.com/xtrade/template/算法交易-导入模板.xlsx');
        }
    }

    importTable() {

        const targetPath = remote.dialog.showOpenDialogSync(this.thisWindow, {
            title: '导入合约',
            filters: [{ name: 'xlsx文件', extensions: ['xlsx', 'xls', 'csv'] }],
        });

        if (!targetPath) {
            return;
        }
        
        this.handleUploadDialog(targetPath);
    }
    
    handleUploadDialog(paths) {

        var sheets = BizHelper.extractRecords(paths[0]);
        var records = sheets.length > 0 ? sheets[0].data : [];
        
        if (!Array.isArray(records) || records.length <= 1) {
            return this.interaction.showAlert('导入文件无数据行（第一行，将视为标题行）');
        }

        var headers = ['代码', '名称', '方向', '数量'];
        var uheaders = records.shift();
        
        if (uheaders.length != headers.length) {
            return this.interaction.showAlert('表格标题列数，与预期列数不符');
        }

        var intersecs = uheaders.slice(0, headers.length);
        if (intersecs.join() != headers.join()) {
            
            for (let idx = 0; idx < headers.length; idx++) {

                let text = headers[idx];
                let utext = uheaders[idx];
                if (typeof utext == 'string' && utext.trim() == text) {
                    continue;
                }

                return this.interaction.showAlert(`第${String.fromCharCode(65 + idx)}列，预期标题 = [${text}]，实际标题 = [${utext}]`);
            }
        }

        var oks = [{ instrument: null, instrumentName: null, direction: null, volume: null }].splice(1);
        var errors = [];
        
        records.forEach((eles, idx) => {

            let rowNo = idx + 2;

            if (!Array.isArray(eles) || eles.length == 0) {

                errors.push({ line: rowNo, message: '未包含数据' });
                return;
            }
            else if (eles.length < headers.length) {
                
                errors.push({ line: rowNo, message: '数据不完整' });
                return;
            }

            let code = eles[0];
            let name = eles[1];
            let direction = eles[2];
            let volume = eles[3];

            // 合约代码

            let isOk = typeof code == 'number' || typeof code == 'string' && code.trim().length > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: '证券代码缺失' });
                return;
            }

            let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, code, true);
            let matches2 = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, code);

            isOk = matches.length == 1 || matches2.length == 1;
            if (!isOk) {

                errors.push({ line: rowNo, message: `证券代码[ ${code} ]无对应合约信息` });
                return;
            }

            // 方向

            if (direction == '买') {
                direction = 1;
            }
            else if (direction == '卖') {
                direction = -1;
            }

            isOk = this.directions.some(x => x.code == direction);
            if (!isOk) {

                errors.push({ line: rowNo, message: `方向[ ${direction} ]不能识别` });
                return;
            }

            // 数量

            isOk = typeof volume == 'number' && Number.isInteger(volume) && volume > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: `数量[ ${volume} ]无效` });
                return;
            }
            
            let mright = matches.length == 1 ? matches : matches2;
            let { instrument, instrumentName } = mright[0];
            oks.push({ instrument, instrumentName, direction, volume });
        });

        var errmsgs = '';
        if (errors.length > 0) {
            errmsgs = (errors.length > 20 ? '（前20条错误信息）<br>' : '') + errors.slice(0, 20).map(item => `行${item.line}：${item.message}`).join('<br/>');
        }

        if (oks.length == 0) {
            return this.interaction.showAlert(`导入文件，未包含有效数据：<br/><br/>${errmsgs}`);
        }

        oks.forEach(data => {
            
            let row = this.createRow(data.instrument, data.instrumentName);
            row.totalPosition = 0;
            row.closablePosition = 0;
            row.targetPosition = data.volume;
            row.direction = data.direction;

            let position = this.matchPosition(data.instrument);
            if (position) {

                row.totalPosition = position.totalPosition;
                row.closablePosition = position.closablePosition;
                
                if (this.isFormalSell(data.direction)) {
                    row.targetPosition = Math.min(position.closablePosition, row.targetPosition);
                }
            }

            this.tableObj.putRow(row);
            this.sync2Choice(data.instrument, 'add');
        });

        this.attachPrice();
    }

    extractStocks() {

        let content = this.crdata.importing.content;
        if (typeof content != 'string' || content.trim().length == 0) {
            return;
        }

        let keywords = content.replace(/\r|\n|\t|[ ]/g, ',').split(',').map(x => x.trim()).filter(x => x.length > 0);
        if (keywords.length == 0) {
            return;
        }

        let stocks = [{ instrument: null, instrumentName: null }].splice(1);
        keywords = keywords.distinct();
        keywords.forEach(each => {

            let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, each);
            let matches2 = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, each, true);
            let results = matches.length == 1 ? matches : matches2.length == 1 ? matches2 : [];

            if (results.length == 1) {

                let { instrument, instrumentName } = results[0];
                stocks.push({ instrument, instrumentName });
            }
        });

        if (stocks.length == 0) {
            return this.interaction.showError('未提取到有效合约');
        }

        this.interaction.showSuccess(`加入数量 = ${stocks.length}`);
        this.hideImport();
        stocks.forEach(x => { this.handleSelect(x); });
    }

    hideImport() {

        const ref = this.crdata.importing;
        ref.visible = false;
        ref.content = null;
    }

    isValidated() {
        
        var ref = this.crdata;

        if (this.helper.isNone(ref.recordId)) {
            return '账号未选择';
        }
        else if (this.algoGrps.length == 0) {
            return '算法列表未加载';
        }
        else if (this.helper.isNone(ref.algoId)) {
            return '算法未选择';
        }
        else if (this.helper.isNone(ref.taskName)) {
            return '算法名称未输入';
        }
        else if (!this.areAlgoParamsApplicable()) {
            return '算法参数值，部分或全部缺失';
        }
        
        let list = this.typeds(this.tableObj.extractAllRecords());
        if (list.length == 0) {
            return '未选择交易合约';
        }

        let errors = list.filter(x => this.helper.isNone(x.direction));
        if (errors.length > 0) {
            return `部分合约未选择交易方向：${errors.map(x => x.instrumentName).join(', ')}`;
        }

        return true;
    }

    collect() {
        
        const result = this.collectAlgoParamsInput();
        const paramStr = JSON.stringify(result.params);

        return this.typeds(this.tableObj.extractAllRecords()).map(item => {

            return {

                direction: item.direction,
                identityId: this.strategyId || this.productId,
                accountId: this.accountId,
                algorithmMappingId: this.crdata.algoId,
                instrument: item.instrument,
                volume: item.targetPosition,
                effectiveTime: result.effectiveTime.value,
                expireTime: result.expireTime.value,
                limitAction: 0,
                afterAction: 0,
                userId: this.userInfo.userId,
                algoParam: paramStr,
                taskName: this.crdata.taskName,
            };
        });
    }

    confirm() {

        var result = this.isValidated();
        if (typeof result == 'string') {
            return this.interaction.showError(result);
        }

        let tasks = this.collect();
        this.send(tasks);
    }

    async send(tasks) {

        let resp = await repoAlgo.order(tasks, 0);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            let map = data || {};
            let errors = [{ code: -1, msg: null }].splice(1);

            for (let key in map) {

                let item = map[key];
                let code = item.errorCode;
                let msg = item.errorMsg;
                if (code != 0) {
                    errors.push({ code, msg });
                }
            }

            if (errors.length == 0) {

                this.interaction.showSuccess('母单任务已保存');
                this.crdata.visible = false;
                this.trigger('save-success');
            }
            else {
                this.interaction.showAlert(`任务创建失败：${errors[0].code}/${errors[0].msg}`);
            }
        }
        else {
            this.interaction.showError(`母单保存失败：${errorCode}/${errorMsg}`);
        }
    }

    giveup() {

        this.reset();
        this.crdata.visible = false;
    }

    setupCreation() {

        this.accounts = [new AccountDetail({})].splice(1);
        this.crdata = {

            visible: true,

            accounts: this.accounts,
            positions: this.positions,
            algoGrps: this.algoGrps,
            choices: [],
            uoptions: [{ label: '', value: '' }].splice(1),

            recordId: null,
            algoId: null,
            taskName: null,
            keywords: null,
            importing: { visible: false, content: '' },
            cost: { buy: null, sell: null },

            totalCanUse: 0,
            algoParams: this.algoParams,
        };

        this.vcreation = new Vue({

            el: this.$container.firstElementChild,
            data: this.crdata,
            mixins: [NumberMixin],
            directives: { drag },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleAccountChange,
                this.handleAlgoChange,
                this.handleChoiceChange,

                this.formStockDisplay,
                this.shortize,
                this.isAccountChoosed,
                this.isIntegerParam,
                this.isDecimalParam,
                this.isTimeParam,
                this.isTimeRangeParam,
                this.isTextParam,
                this.isUserOptionParam,
                
                this.suggest,
                this.handleUserInput,
                this.handleSelect,
                this.handleClearIns,

                this.downloadTemplate,
                this.importStocks,
                this.importTable,
                this.extractStocks,
                this.hideImport,
                this.confirm,
                this.giveup,
                this.formatSelectAccountName,
                this.getProperAccountId,
            ]),
        });

        this.vcreation.$nextTick(() => {
            this.setupTable();
        });
    }

    /**
     * @param {AccountDetail} account 
     */
    getProperAccountId(account) {
        return this.helperUi.getProperAccountId(account);
    }

    setupTable() {

        let $table = this.vcreation.$el.querySelector('.stocks-list');
        this.helper.extend(this, ColumnCommonFunc);
        let tableObj = new SmartTable($table, this.identify, this, { tableName: 'smt-h29cf', displayName: '票池', headerHeight: 32, rowHeight: 32 });
        tableObj.setMaxHeight(999);
        this.tableObj = tableObj;
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    identify(record) {
        return record.instrument;
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    formatCodeName(record) {
        return `${record.instrument} / ${record.instrumentName}`;
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    formatDirectionSelect(record) {

        return `<select event.onchange="switchDirection" class="dir-select">
                    ${this.directions.map(x => `<option value="${x.code}" ${record.direction == x.code ? 'selected' : ''}>${x.mean}</option>`).join('')}
                </select>`;
    }

    /**
     * @param {AlgoPositionInfo} record 
     * @param {HTMLSelectElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousVal 
     * @param {String} fieldName 
     */
    switchDirection(record, $ctr, $cell, previousVal, fieldName) {

        let new_dir = Number($ctr.value);
        this.tableObj.updateRow({ instrument: record.instrument, direction: new_dir });
        this.attachPrice();
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    formatTarget(record) {
        return `<div class="el-input">
                    <input event.onchange="handleTargetChange" value="${record.targetPosition}" type="text" autocomplete="off" class="el-input__inner" />
                </div>`;
    }

    /**
     * @param {AlgoPositionInfo} record 
     * @param {HTMLInputElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousVal 
     * @param {String} fieldName 
     */
    handleTargetChange(record, $ctr, $cell, previousVal, fieldName) {
        
        let target = parseInt($ctr.value);
        let is_ok = typeof target == 'number' && target >= 0;
        $ctr.value = record.targetPosition = is_ok ? target : 0;
        this.attachPrice();
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    formatTimes(record) {
        return `<div class="el-input">
                    <input event.onchange="handleTimesChange" value="${record.num || 0}" type="text" autocomplete="off" class="el-input__inner" />
                </div>`;
    }

    /**
     * @param {AlgoPositionInfo} record 
     * @param {HTMLInputElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousVal 
     * @param {String} fieldName 
     */
    handleTimesChange(record, $ctr, $cell, previousVal, fieldName) {
        
        let num = parseInt($ctr.value);
        let is_ok = typeof num == 'number' && num >= 0;
        $ctr.value = record.num = is_ok ? num : null;
    }

    formatActions(record) {
        return '<button class="danger" event.onclick="deleteEntrustRow">删除</button>';
    }

    /**
     * @param {AlgoPositionInfo} record 
     */
    deleteEntrustRow(record) {

        this.tableObj.deleteRow(record.instrument);
        this.sync2Choice(record.instrument, 'delete');
        this.attachPrice();
    }

    async attachPrice() {

        let rows = this.typeds(this.tableObj.extractAllRecords());
        let nones = rows.filter(x => this.helper.isNone(x.price));

        if (nones.length > 0) {

            var map = await repoTrading.getAssetsLatestPrice();
            nones.forEach(item => {
                this.tableObj.updateRow({ instrument: item.instrument, price: map[item.instrument] || 0 });
            });
        }
        
        let buys = rows.filter(x => !this.isAsSell(x.direction));
        let sells = rows.filter(x => this.isAsSell(x.direction));
        this.crdata.cost.buy = buys.map(x => (x.price || 0) * (x.targetPosition || 0)).sum();
        this.crdata.cost.sell = sells.map(x => (x.price || 0) * (x.direction == this.directionMap.borrow_sell.code ? x.targetPosition : Math.min(x.closablePosition, x.targetPosition || 0))).sum();
    }

    async requestAccounts() {

        var resp = await repoAccount.batchGetAccountCash();
        var { errorCode, errorMsg, data } = resp;
        
        if (errorCode == 0) {

            let accounts = data instanceof Array ? data.map(x => new AccountDetail(x)) : [];
            this.accounts.refill(accounts);
        }
        else {
            this.interaction.showError('账号获取异常：' + errorMsg);
        }
    }

    requestAlgoes() {

        var account = this.accounts.find(x => this.getProperAccountId(x) == this.crdata.recordId);
        if (account) {
            this.queryQualifiedAlgoes(AlgorithmClasses.normal, account.accountId);
        }
        else {

            this.algoGrps.clear();
            this.algoes.clear();
        }
    }

    isAccountChoosed() {
        return this.helper.isNotNone(this.crdata.recordId);
    }

    isPositionChoosed(instrument) {
        return this.choices.indexOf(instrument) >= 0;
    }

    async requestAccountDetail() {
        
        let resp = await repoAccount.qdetail({

            userId: this.userInfo.userId,
            identity_id: this.strategyId || this.productId,
            account_id: this.accountId,
        });

        let { data, errorCode, errorMsg } = resp;
        if (errorCode == 0) {

            let detail_info = (data.list || [])[0] || {};
            let detail = new AccountDetail(detail_info);
            this.crdata.totalCanUse = detail.available || 0;
        }
        else {
            this.interaction.showError(`账号详情获取错误：${errorCode}/${errorMsg}`);
        }
    }

    async requestPositions() {

        let params = {

            user_id: this.userInfo.userId,
            fund_id: this.productId,
            strategy_id: this.strategyId,
            account_id: this.accountId,
        };

        let resp = await repoPosition.query(params);
        let { data, errorCode, errorMsg } = resp;

        if (errorCode != 0) {
            return this.interaction.showError(`仓位查询错误：${errorCode}/${errorMsg}`);
        }

        let list = data.map(x => new Position(x)).map(x => new AlgoPositionInfo({

            instrument: x.instrument,
            instrumentName: x.instrumentName,
            direction: x.direction,
            price: null,
            totalPosition: x.totalPosition,
            closablePosition: x.closableVolume,
            targetPosition: x.closableVolume,
        }));

        this.positions.refill(list);
    }

    build($container) {

        super.build($container);
        this.setupCreation();
        this.requestAccounts();
        this.requestAlgoes();
    }
}

module.exports = View;
