const { IdentityRelationView } = require('../classcial/identity-relation-view');

class AssetToolbarView extends IdentityRelationView {

    constructor(view_name) {

        super(view_name, false);

        this.total = 100;
        this.pageSize = 30;
        this.tdata = {

            istates: this.istates,
            date: [],
            checked: true,
            isSummary: false,
            products: this.products,
            strategies:  this.strategies,
            accounts: this.accounts,
        };
    }
    
    createApp() {

        this.toolbarVueIns = new Vue({

            el: this.$container.firstElementChild,
            data: this.tdata,
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleSearch,
                this.handleProductChange,
                this.handleStrategyChange,
                this.handleAccountChange,
                this.dateTimechange,
                this.handleSummarizeOptionChange,
                this.formatSelectAccountName,
            ]),
        });
    }

    isDateRangeOk() {
        return this.tdata.date instanceof Array && this.tdata.date.length == 2;
    }

    handleSearch() {

        let { checked, date, isSummary } = this.tdata;
        let { productId, strategyId, accountId } = this.istates;
        let searchData = {

            date, 
            checked, 
            isSummary, 
            fund: productId, 
            strategy: strategyId, 
            account: accountId, 
            pageNo: 1, 
            pageSize: 30,
        };
        
        if (!this.isDateRangeOk()) {
            
            /**
             * 未选择有效的日期区间时，自动勾选【当日】选项
             */

            searchData.checked = true;
            this.tdata.checked = true;
        }

        this.interaction.showSuccess('刷新请求已发送');
        this.trigger("search-btn", searchData);
    };

    handleSummarizeOptionChange() {

        this.istates.accountId = null;
        this.istates.showAccount = !this.tdata.isSummary;
    }

    dateTimechange() {
        //
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.handleSearch();
    }
}

module.exports = { AssetToolbarView };