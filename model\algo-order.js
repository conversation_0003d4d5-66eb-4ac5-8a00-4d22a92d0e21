/**
 * 算法单
 */
class AlgoOrder {

    constructor({ accountId, accountName, afterAction, algoParam, algorithmMappingId, algorithmName, algorithmStatus, algorithmType, direction, effectiveTime, expireTime, externalId, fundId, fundName, id, identityId, instrument, instrumentName, limitAction, orderList, orderedVolume, remark, strategyId, strategyName, supplierName, taskId, taskName, tradePrice, tradedVolume, userId, volume }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.afterAction = afterAction;
        this.algoParam = algoParam;
        this.algorithmMappingId = algorithmMappingId;
        this.algorithmName = algorithmName;
        this.algorithmStatus = algorithmStatus;
        this.algorithmType = algorithmType;
        this.direction = direction;
        this.effectiveTime = effectiveTime;
        this.expireTime = expireTime;
        this.externalId = externalId;
        this.fundId = fundId;
        this.fundName = fundName;
        this.id = id;
        this.identityId = identityId;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.limitAction = limitAction;
        this.orderList = orderList;
        this.orderedVolume = orderedVolume;
        this.remark = remark;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.supplierName = supplierName;
        /** 相同的 taskId 属于同一批次 */
        this.taskId = taskId;
        this.taskName = taskName;
        this.tradePrice = tradePrice;
        this.tradedVolume = tradedVolume;
        this.userId = userId;
        this.volume = volume;

        /**
         * algorithmStatus 值如下：
         * 0：进行中
         * 1：已完成
         * 2：已撤销
         */

        this.isCompleted = this.algorithmStatus != 0;
    }
}

class MotherAlgoOrder {

    constructor({ taskId, strategyId, strategyName, orderedVolume, tradedVolume, volume }) {

        this.taskId = taskId;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.orderedVolume = orderedVolume;
        this.tradedVolume = tradedVolume;
        this.volume = volume;
        this.isCompleted = false;
        /** 是否展开 */
        this.isOpened = false;
    }
}

module.exports = { AlgoOrder, MotherAlgoOrder };