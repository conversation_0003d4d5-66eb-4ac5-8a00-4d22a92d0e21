﻿/**
 * system user infrastructure
 */

const { systemUserEnum } = require('../config/system-enum.user');
const UserRole = systemUserEnum.userRole;

class UserInfo {

    constructor(usr_info) {

        let userId = usr_info.id || usr_info.userId;
        this.userId = this.id = userId;
        this.userName = usr_info.username || usr_info.userName;
        this.fullName = usr_info.fullName;
        this.orgId = usr_info.orgId;
        this.orgName = usr_info.orgName;
        this.roleId = usr_info.roleId;
        this.token = usr_info.token;
        this.password = usr_info.password;
        this.status = usr_info.status;
        this.onlineStatus = usr_info.onlineStatus;
        this.phoneNo = usr_info.phoneNo;
        this.firstLogin = !!usr_info.firstLogin;
        
        /**
         * 该角色，是否需要登录行情服务器
         */
        // this.quoteServerRequired = this.roleId != UserRole.superAdmin.code;
        this.quoteServerRequired = false;

        if (this.roleId === UserRole.superAdmin.code) {

            /** 是否超级管理员 */
            this.isSuperAdmin = true;
            this.userRoleName = UserRole.superAdmin.mean;
        }
        else if (this.roleId === UserRole.brokerAdmin.code) {

            /** 是否券商运维人员 */
            this.isBrokerAdmin = true;
            this.userRoleName = UserRole.brokerAdmin.mean;
        }
        else if (this.roleId === UserRole.orgAdmin.code) {

            /** 是否机构管理员 */
            this.isOrgAdmin = true;
            this.userRoleName = UserRole.orgAdmin.mean;
        }
        else if (this.roleId === UserRole.counselor.code) {

            /** 是否投顾 */
            this.isCounselor = true;
            this.userRoleName = UserRole.counselor.mean;
        }
        else if (this.roleId === UserRole.product.code) {

            /** 是否产品经理 */
            this.isProductManager = true;
            this.userRoleName = UserRole.product.code;
        }
        else if (this.roleId === UserRole.riskProtector.code) {

            /** 是否风控员 */
            this.isRiskProtector = true;
            this.userRoleName = UserRole.riskProtector.mean;
        }
        else if (this.roleId === UserRole.observing.code) {

            /** 是否观察员 */
            this.isObserver = true;
            this.userRoleName = UserRole.observing.mean;
        }
        else if (this.roleId === UserRole.tradingMan.code) {

            /** 是否交易员 */
            this.isTradingMan = true;
            this.userRoleName = UserRole.tradingMan.mean;
        }

        this.lastLoginTime = usr_info.lastLoginTime;
        this.lastLoginComputerName = usr_info.lastLoginComputerName;
        this.lastLoginIp = usr_info.lastLoginIp;
        this.lastLoginMachineAddr = usr_info.lastLoginMachineAddr;
    }

    static Clone(sample) {
        return new UserInfo(sample);
    }
}

module.exports = { UserInfo };
