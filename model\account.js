class AccountBrokerInfo {

    constructor({ txPassword, tradeAccount, shSecId, szSecId, yybId, version }) {

        this.txPassword = txPassword;
        this.tradeAccount = tradeAccount
        this.shSecId = shSecId
        this.szSecId = szSecId;
        this.yybId = yybId;
        this.version = version;
    }
}

class AccountTerminal {

    constructor({ id, interfaceType, status, terminalName }) {

        this.id = id;
        this.interfaceType = interfaceType
        this.status = status
        this.terminalName = terminalName;
    }
}

function extractExtInfo(extInfo) {

    if (typeof extInfo == 'string') {
        try { extInfo = JSON.parse(extInfo); } catch (ex) {}
    }
    
    if (!extInfo) {
        extInfo = {};
    }

    return new AccountBrokerInfo(extInfo);
}

class V3StandardAccount {

    constructor({

        accountAlias,
        accountId,
        accountName,
        assetType,
        autoApprove,
        available,
        availableCheck,
        balance,
        bkId,
        brokerId,
        brokerName,
        childAccount,
        closeProfit,
        commission,
        connectionStatus,
        detailId,
        diffBalance,
        extInfo,
        financeAccount,
        financeAccountName,
        frozenCommission,
        frozenMargin,
        fundId,
        fundName,
        funds = [{ fundId: null, fundName: null }].splice(1),
        id,
        identityId,
        increaseAmount,
        initStatus,
        interfaceType,
        isCredit, credit,
        loanBuyBalance,
        loanSellBalance,
        loanSellQuota,
        margin,
        marketValue,
        maxLimitMoney,
        orgId,
        orgName,
        positionProfit,
        preBalance,
        preRiskControl,
        pwd,
        risePercent,
        status,
        strategyId,
        strategyName,
        terminals = [new AccountTerminal({})].splice(1),
        workFlowId,
        workFlowName,
    }) {

        this.accountId = accountId;
        /** 实体账号名称，或子账号别名 */
        this.accountName = accountAlias || accountName;
        this.assetType = assetType;
        this.autoApprove = !!autoApprove;
        this.available = available;
        this.availableCheck = availableCheck;
        this.balance = balance;
        this.bkId = bkId;
        this.brokerId = brokerId;
        this.brokerName = brokerName;
        this.childAccount = this.isChildAccount = !!childAccount;
        this.closeProfit = closeProfit;
        this.commission = commission;
        this.connectionStatus = connectionStatus;
        this.detailId = detailId;
        this.diffBalance = diffBalance;
        this.extInfo = extractExtInfo(extInfo);
        this.financeAccount = financeAccount;
        this.financeAccountName = financeAccountName;
        this.frozenCommission = frozenCommission;
        this.frozenMargin = frozenMargin;
        this.fundId = fundId;
        this.fundName = fundName;
        this.funds = funds;
        this.id = id;
        this.identityId = identityId;
        this.increaseAmount = increaseAmount;
        this.initStatus = initStatus;
        this.interfaceType = interfaceType;
        this.isCredit = this.credit = credit || isCredit;
        this.loanBuyBalance = loanBuyBalance;
        this.loanSellBalance = loanSellBalance;
        this.loanSellQuota = loanSellQuota;
        this.margin = margin;
        this.marketValue = marketValue;
        this.maxLimitMoney = maxLimitMoney;
        this.orgId = orgId;
        this.orgName = orgName;
        this.positionProfit = positionProfit;
        this.preBalance = preBalance;
        this.preRiskControl = !!preRiskControl;
        this.pwd = pwd;
        this.risePercent = risePercent;
        this.status = status;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.terminals = terminals;
        this.workFlowId = workFlowId;
        this.workFlowName = workFlowName;
    }

    /**
     * @param {Array} records 
     * @returns {Array<V3StandardAccount>}
     */
    static Convert(records) {
        return records instanceof Array ? records.map(x => new V3StandardAccount(x)) : records ? [new V3StandardAccount(records)] : [];
    }
}

class AccountSimple {

    constructor(struc) {
        
        this.id = struc.id;
        this.assetType = struc.assetType;
        this.available = struc.available;
        this.balance = struc.balance;
        this.accountId = struc.identityId;
        this.accountName = struc.identityName;
        this.identityType = struc.identityType;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.marketValue = struc.marketValue;
        /** 市场标识：上海SHSE，深圳SZSE */
        this.market = struc.market + '';
        /** 是否为信用账号 */
        this.credit = !!struc.credit;
        /** 为信用账号时，可用融资额度 */
        this.enableCreditBuy = struc.enableCreditBuy || 0;
        this.financeAccount = struc.financeAccount;
        this.preBalance = struc.preBalance;
        this.risePercent = struc.risePercent;

        // this.closeProfit = struc.closeProfit;
        // this.commission = struc.commission;
        // this.connectCount = struc.connectCount;
        // this.connectionStatus = struc.connectionStatus;
        // this.diffBalance = struc.diffBalance;
        // this.frozenCommission = struc.frozenCommission;
        // this.frozenMargin = struc.frozenMargin;
        // this.fundShare = struc.fundShare;
        // this.inMoney = struc.inMoney;
        // this.loanBuyBalance = struc.loanBuyBalance;
        // this.loanSellBalance = struc.loanSellBalance;
        // this.loanSellQuota = struc.loanSellQuota;
        // this.margin = struc.margin;
        // this.nav = struc.nav;
        // this.navRealTime = struc.navRealTime;
        // this.outMoney = struc.outMoney;
        // this.positionProfit = struc.positionProfit;
        // this.status = struc.status;
        // this.strategyId = struc.strategyId;
        // this.tradingDay = struc.tradingDay;
        // this.withdrawQuota = struc.withdrawQuota;
    }

    /**
     * @param {Array} records 
     * @returns {Array<AccountSimple>}
     */
    static Convert(records) {
        return records instanceof Array ? records.map(x => new AccountSimple(x)) : records ? [new AccountSimple(records)] : [];
    }
}

class FutureMarginRate {

    constructor({ id, accountId, exchangeId, instrumentId, brokerId, investorId, investorRange, hedgeFlag, longMarginRatioByMoney, longMarginRatioByVolume, shortMarginRatioByMoney, shortMarginRatioByVolume, isRelative }) {
        
        this.id = id;
        this.accountId = accountId;
        this.exchangeId = exchangeId;
        this.instrumentId = instrumentId;
        this.brokerId = brokerId;
        this.investorId = investorId;
        this.investorRange = investorRange;
        this.hedgeFlag = hedgeFlag;
        this.longMarginRatioByMoney = longMarginRatioByMoney;
        this.longMarginRatioByVolume = longMarginRatioByVolume;
        this.shortMarginRatioByMoney = shortMarginRatioByMoney;
        this.shortMarginRatioByVolume  = shortMarginRatioByVolume;
        this.isRelative = isRelative;
    }
}

class AccountDetail {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.available = struc.available;
        this.balance = struc.balance;
        this.closeProfit = struc.closeProfit;
        this.commission = struc.commission;
        this.connectCount = struc.connectCount;
        this.connectionStatus = struc.connectionStatus;
        this.diffBalance = struc.diffBalance;
        this.isCredit = !!struc.credit;
        this.financeAccount = struc.financeAccount;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.id = struc.id;
        this.identityId = struc.identityId;
        this.identityName = struc.identityName;
        this.identityType = struc.identityType;
        this.inMoney = struc.inMoney;
        this.loanBuyBalance = struc.loanBuyBalance;
        this.loanSellBalance = struc.loanSellBalance;
        this.loanSellQuota = struc.loanSellQuota;
        this.margin = struc.margin;
        this.marketValue = struc.marketValue;
        this.maxLimitMoney = struc.maxLimitMoney;
        this.nav = struc.nav;
        this.navRealTime = struc.navRealTime;
        this.outMoney = struc.outMoney;
        this.positionProfit = struc.positionProfit;
        this.preBalance = struc.preBalance;
        this.risePercent = struc.risePercent;
        this.status = struc.status;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.fundShare = struc.fundShare;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.tradingDay = struc.tradingDay;
        this.withdrawQuota = struc.withdrawQuota;
    }
}

class ChildAccountBasic {

    constructor({ id, accountId, accountName }) {

        /**
         * 其值，主账号对应account id，子账号对应detail id
         * 1. 创建时，为空
         * 2. 编辑时，为具体值
         */
        this.id = id || null;
        /** 对应主账号account id */
        this.accountId = accountId || null;
        /** 子账号别名 */
        this.accountName = accountName || null;
    }
}

module.exports = {

    AccountBrokerInfo,
    AccountTerminal,
    V3StandardAccount,
    AccountSimple,
    AccountDetail,
    FutureMarginRate,
    ChildAccountBasic,
};