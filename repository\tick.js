const { http } = require('../libs/http');
const { helper } = require('../libs/helper');
const { systemTrdEnum } = require('../config/system-enum.trading');
const { HttpTickData } = require('../model/http-tick');

class TickRepository {

    constructor() {

        /**
         * 订阅图谱，key: instrument / value: { timer: number, callbacks: function[] }
         */
        this.submap = {};
    }

    /**
     * 检索匹配某合约的订阅者
     * @param {string} instrument 
     * @returns {{timer: number, callbacks: ((tickd, isReply) => void)[]}|undefined}
     */
    ensureGet(instrument) {
        
        let matched = this.submap[instrument];
        if (matched === undefined) {
            matched = this.submap[instrument] = { timer: null, callbacks: [] };
        }

        return matched;
    }

    /**
     * 订阅合约行情（http模式方式）
     * @param {string} instrument 合约代码
     * @param {Function} callback tick数据回调函数
     */
    async subscribe(instrument, callback) {

        if (!instrument) {
            throw new Error('Instrument cannot be empty.');
        }

        if (typeof callback !== 'function') {
            throw new Error('Callback cannot be empty.');
        }

        const matched = this.ensureGet(instrument);
        if (matched.callbacks.includes(callback)) {

            // 已订阅，即时请求一次
            await this.request(instrument, true);
            return;
        }

        // 添加订阅者
        matched.callbacks.push(callback);

        // 首次添加订阅者时，设置定时器
        if (matched.callbacks.length === 1) {

            clearInterval(matched.timer);
            matched.timer = helper.safeSetInterval(1000 * 3, async () => { await this.request(instrument, false); });
        }
        
        await this.request(instrument, true);
    }

    /**
     * 退订合约行情（http模式方式）
     * @param {string} instrument 合约代码
     * @param {Function} callback tick数据回调函数
     */
    unsubscribe(instrument, callback) {

        if (!instrument) {
            throw new Error('Instrument cannot be empty.');
        }

        if (typeof callback !== 'function') {
            throw new Error('Callback cannot be empty.');
        }

        const matched = this.ensureGet(instrument);
        matched.callbacks.remove(x => x === callback);

        // 无任何订阅者时，清除定时器

        if (matched.callbacks.length === 0) {

            clearInterval(matched.timer);
            matched.timer = null;
        }
    }

    /**
     * @param {string} instrument 目标合约
     * @param {boolean} isReply 是否为订阅回执
     */
    async request(instrument, isReply) {
        
        const resp = await this.getSnapshot(instrument);
        const matched = this.ensureGet(instrument);
        const { data, errorCode, errorMsg } = resp || {};

        if (errorCode === 0 && helper.isJson(data)) {

            const htick = new HttpTickData(data);
            const { askPrice, askVolume, bidPrice, bidVolume } = htick;
            const precision = 3;
            const preclose = +(htick.preClosePrice || 0).toFixed(precision);
            const open = +(htick.openPrice || 0).toFixed(precision);
            const high = +(htick.highPrice || 0).toFixed(precision);
            const low = +(htick.lowPrice || 0).toFixed(precision);
            const upper = +(htick.upperLimitPrice || 0).toFixed(precision);
            const lower = +(htick.lowerLimitPrice || 0).toFixed(precision);
            const last = +(htick.lastPrice || 0).toFixed(precision);
            const tick = {

                time: htick.updateTime,
                preclose: preclose,
                open: open == 0 ? preclose : open,
                high: high == 0 ? preclose : high,
                low: low == 0 ? preclose : low,
                ceiling: upper == 0 ? preclose : upper,
                floor: lower == 0 ? preclose : lower,
                latest: last == 0 ? preclose : last,
                amount: htick.turnover || 0,
                sells: askPrice.map((price, idx) => { return [+price.toFixed(precision), askVolume[idx]]; }),
                buys: bidPrice.map((price, idx) => { return [+price.toFixed(precision), bidVolume[idx]]; }),
            };
            
            const result = { instrument, tickType: systemTrdEnum.tickType.tick, tick };
            matched.callbacks.forEach((callback) => { callback(result, isReply === true); });
        }
        else {
            // console.error('tick data request error', { instrument, resp });
        }
    }

    /**
     * @param {string} instrument 
     */
    getSnapshot(instrument) {

        return new Promise((resolve, reject) => {
            http.get('common/tick/instrument', {
                params: { instrument },
            }).then((resp) => { resolve(resp.data); }, (err) => { reject(err); });
        });
    }
}

module.exports = { TickRepository };
