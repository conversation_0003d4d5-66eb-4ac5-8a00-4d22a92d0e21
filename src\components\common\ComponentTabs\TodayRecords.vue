<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition, ProductInfo } from '@/types';
import { RecordService, TradingService } from '@/api';
import {
  exchangeOrderIdCol,
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  directionCol,
  volumeCol,
  tradedPriceCol,
  commissionCol,
  tradeIdCol,
  tradeTimeCol,
  assetTypeCol,
  accountNameCol,
} from './shared/columnDefinitions';
import { putRow } from '@/script';
import type { SocketDataPackage, TradeRecordInfo } from '../../../../../xtrade-sdk/dist';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : ProductInfo;
}>();

// 基础列定义
const baseColumns = [
  exchangeOrderIdCol,
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  directionCol,
  volumeCol,
  tradedPriceCol,
  commissionCol,
  tradeIdCol,
  tradeTimeCol,
  assetTypeCol,
] as ColumnDefinition<TradeRecordInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.splice(1, 0, accountNameCol as any);
  }
  return cols;
});

// 成交数据
const tradeRecords = shallowRef<TradeRecordInfo[]>([]);

// 监听账户/产品变化，重新获取成交数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchTradeRecords();
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    fetchTradeRecords();
  }
  TradingService.subscribePositionChange(handleRecordChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribePositionChange(handleRecordChange);
});

/** 监听成交变化 */
const handleRecordChange = (data: SocketDataPackage<TradeRecordInfo>) => {
  const { body } = data;
  if (body) {
    putRow(body, tradeRecords, 'tradeId');
  }
};

// 获取成交数据
const fetchTradeRecords = async () => {
  if (!activeItem) return;
  const data = await RecordService.getTodayTrades(activeItem.id);
  console.log(data);
  tradeRecords.value = data;
};
</script>

<template>
  <VirtualizedTable
    :columns="columns"
    :data="tradeRecords"
    fixed
    identity="tradeId"
    :sort="{ key: 'tradeTime', order: TableV2SortOrder.DESC }"
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchTradeRecords" size="small" color="var(--g-primary)">刷新</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
