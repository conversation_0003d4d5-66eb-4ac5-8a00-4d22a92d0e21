<script setup lang="tsx">
import type { RiskRuleExt } from '@/types/riskc';

import {
  formatCheckObject,
  formatRuleDayRange,
  formatRuleTimeRange,
} from '../IndicatorTreeNodeLogic';

const { rules, focusedRuleId } = defineProps<{
  rules: RiskRuleExt[];
  focusedRuleId: number | null;
  componentMap: { [id: number]: string };
}>();

const emitter = defineEmits<{
  focused: [rule: RiskRuleExt];
  edit: [rule: RiskRuleExt];
  delete: [rule: RiskRuleExt];
  unbind: [rule: RiskRuleExt];
}>();

function isIdentityRule(rule: RiskRuleExt) {
  return rule.templateId == 0;
}

function isTemplateRule(rule: RiskRuleExt) {
  return rule.templateId > 0;
}

function setAsFocused(rule: RiskRuleExt) {
  emitter('focused', rule);
}

function editRow(rule: RiskRuleExt) {
  emitter('edit', rule);
}

function deleteRow(rule: RiskRuleExt) {
  emitter('delete', rule);
}

function unbindRow(rule: RiskRuleExt) {
  emitter('unbind', rule);
}
</script>

<template>
  <div class="rule-list" pt-5>
    <div
      v-for="(rule, index) in rules"
      :key="index"
      class="each-rule-row"
      :class="{ is_focused: rule.id == focusedRuleId }"
      @click="setAsFocused(rule)"
      flex
      aic
      lh-30
    >
      <div flex-1 w-200 pl-15 toe>{{ rule.ruleName }}</div>
      <div flex-1 w-200 pl-15 toe>{{ rule.templateName }}</div>
      <div flex aic gap-10 of-x-hidden>
        <div>|</div>
        <div w-270 toe>针对{{ formatCheckObject(rule) }}</div>
        <div>|</div>
        <div w-160 toe>{{ formatRuleDayRange(rule) }}</div>
        <div>|</div>
        <div w-160 toe>{{ formatRuleTimeRange(rule) }} 执行</div>
      </div>
      <div w-90 pl-10 flex gap-10>
        <el-link @click="editRow(rule)" underline>编辑</el-link>
        <span v-if="isIdentityRule(rule)" class="delete-link">
          <el-link @click="deleteRow(rule)" underline>删除</el-link>
        </span>
        <span v-else-if="isTemplateRule(rule)" class="unbind-link">
          <el-link @click="unbindRow(rule)" underline>解绑</el-link>
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.rule-list {
  div {
    color: var(--g-text-color-2);
  }
  .each-rule-row {
    &:hover {
      background-color: var(--g-block-bg-6);
    }
    &.is_focused {
      background-color: var(--g-block-bg-6);
    }
  }
  .delete-link {
    :deep() {
      .el-link__inner {
        color: red;
      }
    }
  }
}
</style>
