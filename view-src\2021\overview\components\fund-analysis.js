const { IView } = require('../../../../component/iview');
const { repoApp } = require('../../../../repository/app');

class View extends IView {

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.targetCode = '';
        this.targetName = '';
    }

    refresh() {
        this.$webview.reload();
    }

    async autoLogin2Fof() {

        if (this.fofuser && new Date().getTime() - this.fofuser.update < 1000 * 60 * 60) {
            return this.fofuser;
        }

        let resp = await repoApp.login2Fof();
        let { errorCode, errorMsg, data } = resp;
        let { id, userName, token } = data || {};
        return (this.fofuser = { id, userName, token, update: new Date().getTime() });
    }

    getReportId() {
        return '168233047007461';
    }

    async visit() {

        console.log(this.targetCode);
        let kvs = [

            // 产品代码
            // { key: 'code', value: this.targetCode },
            // { key: 'name', value: this.targetName },
            { key: 'code', value: 'F-0wwbib' },
            { key: 'name', value: '鲲鹏基金' },
            { key: 'templateId', value: '267936569063553' },
            // { key: 'xtrade_user_id', value: this.userInfo.userId },
            // { key: 'user_id', value: 127 }, // gaoyu_jyy
            { key: 'componentName', value: 'TemplatePreviewRoute' },
            // 从哪里打开的fof页面
            { key: 'from', value: 'xtrade' },
            // 页面内部数据，静默刷新，间隔毫秒数
            { key: 'nointerval', value: 1 },
        ];

        console.log(kvs);
        let fofusr = await this.autoLogin2Fof();
        console.log(JSON.stringify(fofusr));
        let origin = 'https://yitou.gaoyusoft.com';
        // let origin = 'http://fund.gaoyusoft.com';
        // let origin2 = 'http://localhost:8888';
        let targetUrl = `${origin}/#/independentPage?${kvs.map(x => `${x.key}=${x.value}`).join('&')}`;
        let bridgeUrl = `${origin}/#/tokenLogin?token=${fofusr.token}&user_id=${fofusr.id}&from=xtrade&nointerval=1&url=${encodeURIComponent(targetUrl)}`;
        console.log('target url = ' + targetUrl);
        console.log('token redirect url = ' + bridgeUrl);
        this.$webview.src = bridgeUrl;
    }

    setup(options) {

        const { fundCode, fundName, isFund, strategyId, strategyName, isStrategy } = options;
        let $root = this.$container.querySelector('.fund-analysis');
        let $webview = document.createElement('webview');
        $webview.classList.add('s-full-size');
        $root.appendChild($webview);
        this.$webview = $webview;
        this.targetCode = isStrategy ? strategyId : fundCode; // 'F-00pstt'
        this.targetName = isStrategy ? strategyName : fundName;
        this.visit();
    }

    build($container, options) {

        super.build($container);
        this.setup(options);
    }
}

module.exports = View;
