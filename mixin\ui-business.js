
const helperUi = require('../libs/helper-ui').helperUi;
const UiBuisinessMixin = {

    methods: {

        formatAssetType: (asset_type) => {
            return `<span>${helperUi.formatAssetType(asset_type)}</span>`;
        },

        makeDirectionHtml: function(direction, asset_type, effect_flag) {
            return helperUi.makeDirectionHtml(direction, asset_type, effect_flag);
        },

        formatYesNo: function(flag, options) {
            return helperUi.makeYesNoLabelHtml(flag, options);
        },

        formatYesNoText: function(flag, options) {
            return helperUi.makeYesNoLabelText(flag, options);
        },
    },
};

module.exports = { UiBuisinessMixin };
