<div>

	<el-checkbox v-model="checked">只看当日</el-checkbox>

	<el-date-picker v-model="date" 
					type="daterange" 
					value-format="yyyyMMdd"
					range-separator="至" 
					start-placeholder="开始日期" 
					end-placeholder="结束日期" 
					:disabled="checked"
					@change="dateTimechange" 
					class="s-w-150 s-mgl-10 s-mgr-10">
	</el-date-picker>

	<el-select v-model="istates.productId" 
				placeholder="请选择产品" 
				@change="handleProductChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="(item, item_idx) in funds" 
					:key="item_idx" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="istates.accountId" 
				placeholder="请选择账号" 
				@change="handleAccountChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="(item, item_idx) in accounts" 
					:key="item_idx"
					:label="formatSelectAccountName(item)"
					:value="item.value"></el-option>
	</el-select>

	<el-input placeholder="合约代码搜索" prefix-icon="el-icon-search" v-model="searchValue" class="s-w-150 s-mgr-10"></el-input>
	<el-tooltip content="勾选，仅查看与散股有关的交易">
		<el-checkbox v-if="source.isEntrust" v-model="special" class="s-pdr-10">特殊数据</el-checkbox>
	</el-tooltip>
	<el-button type="primary" icon="el-icon-search" @click="handleSearch"></el-button>
	
</div>