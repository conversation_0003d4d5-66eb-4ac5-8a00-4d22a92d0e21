<div class="trade-form bktform s-full-height">
	<div class="trade-form-inner bktform-internal themed-box s-scroll-bar s-full-height">
		<div class="xtcontainer s-border-box s-full-height" style="overflow-y: auto;">

			<template>
				<div class="xtheader themed-header">
					<span>篮子交易</span>
				</div>
			</template>

			<template>

				<div class="form-external s-unselectable">
	
					<form class="xtform">

						<div class="direction-row">
							<el-radio-group v-model="uistates.direction" class="s-full-width">
								<el-radio-button 
									v-for="(item, item_idx) in directions"
									:key="item_idx"
									:label="item.code"
									:style="{ width: 100 / directions.length + '%' }">{{ item.mean }}</el-radio-button>
							</el-radio-group>							
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">篮子</span>
							<el-input v-model="uistates.instrumentName" readonly></el-input>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">下单方式</span>
							<el-select v-model="uistates.method" @change="handleMethodChange">
								<el-option v-for="(item, item_idx) in methods"
										   :key="item_idx"
										   :value="item.code"
										   :label="item.mean"></el-option>
							</el-select>
						</div>

						<div class="xtinput shorten">
							<span class="unit-txt themed-color" title="单位">{{ theMethod.unit }}</span>
							<span class="xtlabel themed-color">{{ theMethod.label }}</span>
							<el-input placeholder="0" v-model.number="uistates.scale"></el-input>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">算法</span>
							<el-select 
								placeholder="请选择算法" 
								v-model="uistates.algoId" 
								@change="handleAlgoChange" 
								class="s-w-150" 
								filterable 
								clearable>
								<el-option-group v-for="(group, group_idx) in algoGrps" :key="group_idx" :label="group.name">
									<el-option v-for="(item, item_idx) in group.algoes" :key="item_idx" :label="item.name" :value="item.id"></el-option>
								</el-option-group>
							</el-select>
						</div>

						<div class="xtinput" v-for="(item, item_idx) in algoParams" v-show="!!item.display" :key="item_idx">
							<label class="xtlabel themed-color">
								<span v-if="item.required" class="s-color-red">*</span>
								{{ shortize(item.label) }}
							</label>
							<template v-if="isIntegerParam(item.type)">
								<el-input-number v-model.number="item.defaultValue" :precision="0" :placeholder="item.remark || '请输入整数'" clearable></el-input-number>
							</template>
							<template v-else-if="isDecimalParam(item.type)">
								<el-input-number v-model.number="item.defaultValue" :precision="2" :placeholder="item.remark || '请输入数值'" clearable></el-input-number>
							</template>
							<template v-else-if="isTimeParam(item.type)">
								<el-time-picker v-model="item.defaultValue" :placeholder="item.remark || '请输入时间'" clearable></el-time-picker>
							</template>
							<template v-else-if="isTimeRangeParam(item.type)">
								<el-time-picker v-model="item.defaultValue" range-separator="至" start-placeholder="开始" end-placeholder="结束" is-range clearable></el-time-picker>
							</template>
							<template v-else-if="isTextParam(item.type)">
								<el-input v-model.trim="item.defaultValue" :placeholder="item.remark || '请输入'" clearable></el-input>
							</template>
							<template v-else-if="isUserOptionParam(item.type)" :placeholder="item.remark || '请选择'">
								<el-select v-model="item.defaultValue" clearable>
									<el-option v-for="(item2, item2_idx) in item.uoptions" :key="item2_idx" :label="item2.label" :value="item2.value"></el-option>
								</el-select>
							</template>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">剔除</span>
							<span class="trade-options">
								<el-checkbox v-model="exclude.suspend">停牌</el-checkbox>
								<el-checkbox v-model="exclude.cash">现金替代</el-checkbox>
								<br>
								<el-checkbox v-model="exclude.ceiling">涨停</el-checkbox>
								<el-checkbox v-model="exclude.floor">跌停</el-checkbox>
							</span>
						</div>
	
						<div class="xtinput button-row basket-button-row">
							<el-button type="primary" @click="hope2Preview" class="s-mgb-10">预览试算</el-button>
							<el-button v-if="isBuy" type="danger" @click="hope2Entrust">买入</el-button>
							<el-button v-else-if="isSell" type="success" @click="hope2Entrust">卖出</el-button>
							<el-button v-else type="primary" @click="hope2Entrust">调仓</el-button>
						</div>
	
					</form>

				</div>

			</template>
		</div>		
	</div>
</div>