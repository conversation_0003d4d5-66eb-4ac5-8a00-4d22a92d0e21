﻿/*
------------
rewriting of element ui original style
------------
*/

.el-button {

    height: 24px;
    border: none;
    border-radius: 2px;
    padding: 2px 10px;
    font-size: 12px;
}

.el-input {

    /* width: unset; */
    font-size: 12px;
}

.el-input__inner,
.el-input-number {

    border: none;
    border-radius: 2px;
    height: 24px;
    line-height: 24px;
}

.el-input-number__decrease {

    width: 24px;
    line-height: 22px;
    border-radius: 2px 0 0 2px;
    top: 2px;
}

.el-input-number__increase {

    width: 24px;
    line-height: 22px;
    border-radius: 0 2px 2px 0;
    top: 2px;
}

.el-input__icon {
    line-height: 28px;
}

.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
    width: 260px;
}

.el-date-editor .el-range-input {

    width: 37%;
    font-size: 12px;
}

.el-range-editor.el-input__inner {

    display: inline-block;
    padding: 0 10px;
}

.el-date-editor .el-range__icon {

    position: relative;
    /* top: -4px; */
    left: -2px;
}

.el-date-editor .el-range__close-icon {
    
    position: relative;
    top: -4px;
    right: -8px;
}

.el-date-editor .el-range-separator {

    line-height: 18px;
    font-size: 12px;
}

.el-range-editor.is-disabled input {
    background-color: unset;
}

.el-radio {

    margin-right: 20px;
    font-size: 12px;
}

.el-radio__label {

    padding-left: 4px;
    font-size: 12px;
}

.el-radio__inner {

    width: 12px;
    height: 12px;
}

.el-radio__inner::after {

    width: 6px;
    height: 6px;
}

.el-radio-button__inner {

    height: 24px;
    padding: 5px 6px;
    font-size: 12px;
}

.el-radio-button:first-child .el-radio-button__inner {
    border-radius: 2px 0 0 2px;
}

.el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0 2px 2px 0;
}

.el-checkbox {
    margin-right: 10px;
}

.el-checkbox__label {

    padding-left: 5px;
    font-size: 12px;
}

.el-checkbox__inner {
    
    width: 12px;
    height: 12px;
}

.el-checkbox__inner::after {

    top: 0;
    left: 3px;
}

.el-select-dropdown {

    border-radius: 0;
    border: none;
}

.el-select-dropdown__item {

    height: 28px;
    line-height: 28px;
    padding: 0 12px;
    font-size: 12px;
}

.el-select-dropdown__item.selected {
    font-weight: normal;
}

.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-input__inner:focus,
.el-form-item.is-error .el-textarea__inner,
.el-form-item.is-error .el-textarea__inner:focus,

.el-input.is-error .el-input__inner,
.el-input.is-error .el-input__inner:focus,
.el-input.is-error .el-input__inner:hover,

.el-autocomplete.is-error .el-input__inner,
.el-autocomplete.is-error .el-input__inner:focus,
.el-autocomplete.is-error .el-input__inner:hover,

.el-input-number.is-error .el-input__inner,
.el-input-number.is-error .el-input__inner:focus,
.el-input-number.is-error .el-input__inner:hover {
    border: 1px solid #EF3939 !important;
}

.el-pagination {
    padding: 0;
}

.el-pagination .el-select .el-input .el-input__inner {
    
    position: relative;
    top: -1px;
    height: 18px;
    line-height: 18px;
}

.el-pagination button,
.el-pagination span:not([class*=suffix]) {
    
    height: 18px;
    line-height: 18px;
    min-width: unset;
    font-size: 12px;
}

.el-pagination .el-input--mini .el-input__icon {
    line-height: 20px;
}

.el-pager li {

    background-color: transparent;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    min-width: unset;
    font-size: 12px;
}

.el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {
    line-height: 18px;
}

.el-pager .more::before {
    line-height: 20px;
}

.el-pagination .btn-next,
.el-pagination .btn-prev,
.el-pagination button:disabled {

    position: relative;
    top: 1px;
    padding: 0 6px;
    background: unset;
}

.el-dialog {
    border-radius: 4px;
}

.el-message-box {
    width: 320px;
}

.el-dialog__header,
.el-message-box__header {

    padding: 0 10px;
    height: 28px;
    line-height: 28px;
    border-radius: 4px 4px 0 0;
}

.el-dialog__title {
    
    font-size: 12px;
    user-select: none;
}

.el-message-box__title {

    line-height: 28px;
    font-size: 14px;
}

.el-dialog__headerbtn,
.el-message-box__headerbtn {

    position: absolute;
    top: 8px;
    right: 10px;
    font-size: 12px;
}

.el-dialog__headerbtn .el-dialog__close,
.el-dialog__headerbtn .el-dialog__close:hover {
    font-weight: bold;
}

.el-dialog__body {

    padding: 5px 10px;
    font-size: 12px;
}

.el-message-box__content {
    font-size: 12px;
}

.el-message-box__btns button {

    float: right;
    margin-left: 10px;
}

.el-message-box__status {
    font-size: 18px !important;
}

.el-dialog__footer {

    height: 36px;
    line-height: 36px;
    padding: 0 20px;
}

.el-dialog__footer button {
    min-width: 50px;
}

.el-popover {

    min-width: 50px;
    padding: 0;
    border: none;
    border-radius: 4px;
    font-size: 12px;
}

.el-popover__title {

    margin-bottom: 0;
    padding: 0 10px;
    line-height: 28px;
    font-size: 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.el-autocomplete-suggestion {

    border: none;
    border-radius: 0;
}

.el-autocomplete-suggestion li {
    font-size: 12px;
}

.el-switch__core {
    height: 16px;
}

.el-switch__core:after {

    top: 0;
    width: 14px;
    height: 14px;
}

.el-switch.is-checked .el-switch__core::after {
    margin-left: -15px;
}

.el-transfer-panel__item.el-checkbox {
    color: #cfd8e9;
}

/* 禁用el-switch的切换动画 */
.el-switch__core {
  transition: none !important;
}

/* 如果需要，也可以禁用圆点的移动动画 */
.el-switch__core:after {
  transition: none !important;
}