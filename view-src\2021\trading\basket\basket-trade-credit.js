const { AlgoParamPartView } = require('../../../2024/algo-param-part-view');
const { CreditOperation } = require('../../model/account');
const { CodeMeanItem, BasketOrderParam } = require('../../model/message');
const { AlgorithmClasses } = require('../../../../repository/algorithm');

class View extends AlgoParamPartView {

     /**
     * 当前选择方向是否为：买入
     */
      get isBuy() {
        return this.uistates.direction == this.directions.buy.code;
    }

    /**
     * 当前选择方向是否为：卖出
     */
    get isSell() {
        return this.uistates.direction == this.directions.sell.code;
    }

    /**
     * 当前选择方向是否为：调仓
     */
    get isAdjust() {
        return this.uistates.direction == this.directions.adjust.code;
    }

    /**
     * 是否为，融资融券
     */
    get isCreditOpen() {
        return this.localStates.creditType == this.creditTypes.open.code;
    }

    /**
     * 是否为，两融归还
     */
    get isCreditClose() {
        return this.localStates.creditType == this.creditTypes.close.code;
    }

    /**
     * 是否为，两融调仓
     */
    get isCreditAdjust() {
        return this.localStates.creditType == this.creditTypes.adjust.code;
    }

    get theMethod() {
        return this.smethods.find(x => x.code == this.uistates.method);
    }

    constructor(view_name) {
        
        super(view_name, '篮子算法交易');

        /**
         * 两融业务大类
         */
        this.creditTypes = {

            open: new CodeMeanItem(1, '融资融券'),
            close: new CodeMeanItem(2, '两融归还'),
            adjust: new CodeMeanItem(3, '两融调仓'),
        };

        /**
         * 适配当前模式的两融大类
         */
        this.screditTypes = (/** @returns {Array<CodeMeanItem>} */ () => { return this.helper.dict2Array(this.creditTypes); })();
        var dirs = this.systemTrdEnum.tradingDirection;
        var bsFlag = this.systemTrdEnum.businessFlag;
        var bsDict = this.businessDict = {

            buyOpen: new CreditOperation('A', bsFlag.credit.code, dirs.buy.code, '融资买入', 'danger'),
            sellOpen: new CreditOperation('B', bsFlag.credit.code, dirs.sell.code, '融券卖出', 'success'),

            buyClose: new CreditOperation('C', bsFlag.close.code, dirs.buy.code, '买券还券', 'danger'),
            sellClose: new CreditOperation('D', bsFlag.close.code, dirs.sell.code, '卖券还款', 'success'),
            securityClose: new CreditOperation('E', bsFlag.closeWithSecurity.code, dirs.sell.code, '现券还券', 'primary')
        };

        /**
         * 委托方向
         */
        this.directions = {

            buy: new CodeMeanItem(dirs.buy.code, dirs.buy.mean),
            sell: new CodeMeanItem(dirs.sell.code, dirs.sell.mean),
            adjust: new CodeMeanItem(0, '调仓'),
        };

        /**
         * 两融业务具体业务操作类型
         */
        this.business = {

            opens: [bsDict.buyOpen, bsDict.sellOpen],
            closes: [bsDict.buyClose, bsDict.sellClose, bsDict.securityClose],
        };

        /**
         * 适配当前模式的两融业务类型
         */
        
        this.sbusiness = (/** @returns {Array<CreditOperation>} */ () => { return []; })();
        this.sbusiness.merge(this.business.opens);
        var defaultBiz = this.business.opens[0];

        /**
         * 委托方式
         */
        this.methods = this.systemTrdEnum.basketMethod;
        var methods = this.methods;
        this.smethods = [

        methods.volume,
        methods.weight,
        methods.weight2Account,
        methods.weight2Asset,
        ];
 
        this.stages = this.systemTrdEnum.bidingStages;
        /** 选择的目标交易账号（accountId列表） */
        this.accounts = [];

          /**
         * 下单面板通用核心状态
         */
        this.uistates = {

            direction: this.directions.buy.code,
            instrument: null,
            instrumentName: null,
            method: this.methods.volume.code,
            scale: 1,
            algoId: null,
        };
        
        this.exclude = {

            suspend: false,
            cash: false,
            ceiling: false,
            floor: false,
        };
        /**
         * 本地状态对象
         */
        this.localStates = {

            creditType: this.creditTypes.open.code,
            businessCode: defaultBiz.code,
            buttonType: defaultBiz.buttonType,
            buttonText: defaultBiz.mean,
            effectBoxClass: '',
        };

        this.registerEvent('set-as-basket', this.handleBasketChange.bind(this));
        this.registerEvent('account-changed', this.handleAccountChange.bind(this));
    }

    /**
     * @param {Array} account_ids 
     */
    handleAccountChange(account_ids) {

        this.accounts = Array.isArray(account_ids) ? account_ids : [];
        this.requestAlgoes();
    }

    /**
     * @param {Number} basketId 
     * @param {String} basketName 
     * @param {Boolean} isEtf 
     */
     handleBasketChange(basketId, basketName, isEtf) {

        this._isEtfBasket = isEtf;
        this.uistates.instrument = basketId || null;
        this.uistates.instrumentName = basketName || null;
        this.smethods.clear();
        this.smethods.merge(isEtf ? [this.methods.volume] : this.helper.dict2Array(this.methods));
        this.uistates.method = this.smethods[0].code;
    }


    handleCreditTypeChange() {
        this.resetBusiness();
    }

    handleBusinessChange() {
        this.updateButton();
    }

    resetBusiness() {

        var bizs = this.sbusiness;
        bizs.clear();

        if (this.isCreditOpen) {
            bizs.merge(this.business.opens);
        }
        else if (this.isCreditClose) {
            bizs.merge(this.business.closes);
        }

        this.localStates.businessCode = bizs.length > 0 ? bizs[0].code : null;
        this.localStates.effectBoxClass = this.isCreditClose ? 'minified' : '';
        this.updateButton();
    }

    getSelectedBiz() {

        let selected = this.business.opens.find(x => x.code == this.localStates.businessCode);
        if (selected === undefined) {
            selected = this.business.closes.find(x => x.code == this.localStates.businessCode);
        }

        return selected;
    }

    updateButton() {

        if (this.isCreditAdjust) {
            this.uistates.direction = this.directions.adjust.code;
            this.localStates.buttonText = this.creditTypes.adjust.mean;
            this.localStates.buttonType = 'primary';
        }
        else {

            let selected = this.getSelectedBiz();
            this.localStates.buttonText = selected.mean;
            this.localStates.buttonType = selected.buttonType;
            this.uistates.direction = selected.direction;
        }
    }


    formatDirName() {
        return this.getSelectedBiz().mean;
    }

    isBusinessApplicable() {
        return !this.isCreditAdjust;
    }

    handleAlgoChange() {
        this.alignAlgoParams(this.uistates.algoId);
    }

    shortize(label) {
        return typeof label == 'string' ? label.substring(0, 7) : label;
    }

    hope2Entrust() {
        
        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        this.trigger('place-basket-orders', this.formParams());
    }
    
    hope2Preview() {

        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        this.trigger('preview-basket-orders', this.formParams());
    }
    
    areParamsOk() {

        var uistates = this.uistates;

        if (!uistates.instrument) {
            return '请选择篮子';
        }

        if (!(uistates.scale > 0)) {
            return '委托' + this.theMethod.label + '无效';
        }

        if (this.theMethod.code == this.methods.weight2Asset.code && uistates.scale > 100) {
            return `${this.methods.weight2Asset.mean}，不能超过100%`;
        }

        if (this.algoes.length == 0) {
            return '没有可用算法';
        }
        else if (this.helper.isNone(uistates.algoId)) {
            return '算法未选择';
        }
        else if (!this.areAlgoParamsApplicable()) {
            return '算法参数值，部分或全部缺失';
        }

        return true;
    }

    getAlgoName() {
        
        let matched = this.algoes.find(x => x.id == this.uistates.algoId);
        return matched ? matched.algorithmName : null;
    }
    
    formParams() {

        const states = this.uistates;
        const matchedMethod = this.theMethod;
        const business = this.getSelectedBiz();
        const result = this.collectAlgoParamsInput();
        const orderParams = {

            direction: states.direction,
            directionName: business ? business.mean : this.localStates.buttonText,
            businessFlag: business ? business.business : null,

            basketId: states.instrument,
            basketName: states.instrumentName,

            method: states.method,
            methodName: matchedMethod.mean,
            methodLabel: matchedMethod.label,
            methodUnit: matchedMethod.unit,

            scale: states.scale,
            stage: 0,
            stageName: null,
            offset: 0,
            adjustType: 2,

            algoId: states.algoId,
            algoName: this.getAlgoName(),

            effectiveTime: result.effectiveTime.value,
            expireTime: result.expireTime.value,
            algoParam: JSON.stringify(result.params),
        };
        
        return new BasketOrderParam(orderParams, this.helper.deepClone(this.exclude));
    }

    handleMethodChange() {
        this.uistates.scale = 0;
    }

    async requestAlgoes() {

        if (this.accounts.length == 0) {

            this.algoGrps.clear();
            this.algoes.clear();
        }
        else {

            await this.queryQualifiedAlgoes(AlgorithmClasses.normal, this.accounts.join(','));
            this.uistates.algoId = this.algoes.length > 0 ? this.algoes[0].id : null;
        }
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                channel: this.channel,
                modes: this.modes,
                creditTypes: this.screditTypes,
                businesses: this.sbusiness,
                methods: this.smethods,
                stages: this.stages,
                algoParams: this.algoParams,
                algoGrps: this.algoGrps,
                uistates: this.uistates,
                localStates: this.localStates,
                exclude : this.exclude,
                validation: this.validation,
                uoptions: this.uoptions,
            },

            computed: {

                isBusinessApplicable: () => { return this.isBusinessApplicable(); },
                theMethod: () => { return this.theMethod; },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleCreditTypeChange,
                this.handleBusinessChange,
                this.hope2Entrust,
                this.handleMethodChange,
                this.handleAlgoChange,
                this.hope2Preview,
                this.shortize,
                this.isIntegerParam,
                this.isDecimalParam,
                this.isTimeParam,
                this.isTimeRangeParam,
                this.isTextParam,
                this.isUserOptionParam,
            ]),
        });
    }

    build($container) {
        
        super.build($container);
        this.createApp();
    }
}

module.exports = View;