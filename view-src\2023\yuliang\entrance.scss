.yuliang-root {

    box-sizing: border-box;
    padding-left: 240px;
    overflow: hidden;

    &.wider {
        padding-left: 0;
        .left-fixed {
            width: 0;
        }
    }

    * {
        box-sizing: border-box;
    }

    .left-fixed {

        float: left;
        margin-left: -240px;
        width: 240px;
        padding-top: 32px;
        
        .account-title {

            margin-top: -32px;
            height: 32px;
            line-height: 32px;
            background-color: #283A56;
            border-right: 1px solid #666;
            border-bottom: 1px solid #666;
        }

        .accounts-box {

            border-right: 1px solid #666;
            overflow-y: auto;

            .account-item {

                height: 32px;
                line-height: 32px;
                padding: 0 60px 0 10px;
                border-bottom: 1px solid #888;
                cursor: default;

                .text {
                    display: inline-block;
                }

                .btn {

                    float: right;
                    margin-right: -50px;
                    margin-top: 3px;
                    display: none;
                }

                &:hover {
                    .btn {
                        display: block;
                        cursor: default;
                    }
                }
            }
        }
    }
    .right {

        position: relative;
        padding-top: 32px;

        .toggle-btn {

            position: absolute;
            left: 10px;
            top: 3px;
        }

        .tabs-box {

            margin-top: -32px;
            height: 32px;
            padding-left: 130px;
        }

        .pages-box {
            .tabcontent-box {
                webview {
                    height: 100% !important;
                }
            }
        }
    }
}