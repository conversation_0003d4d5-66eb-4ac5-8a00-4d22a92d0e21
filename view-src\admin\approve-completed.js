
const ApproveController = require('./approve').ApproveController;

class View extends ApproveController {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '已审核订单');
    }

    handleInstruction(event, instruction) {
        
        const rowd = this.reshapeOrder(instruction);
        if (rowd.instructionStatus == this.statuses.waiting.code) {

            let rkey = this.identifyRecord(rowd);
            if (this.tableObj.hasRow(rkey)) {
                this.tableObj.deleteRow(rkey);
            }
        }
        else {
            this.tableObj.putRow(rowd);
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestInstructions(false, '已审核');
    }

    exportSome() {
        this.tableObj.exportAllRecords(`已审核订单-${new Date().format('yyyyMMdd')}`);
    }

    build($container) {

        super.build($container);
        this.setupTable('smt-aac');
        this.requestInstructions(false, '已审核');
        this.createToolbarApp([]);
        this.lisen2WinSizeChange(this.handleWinSizeChangeProxy);
        this.simulateWinSizeChange();
    }
}

module.exports = View;
