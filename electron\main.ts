import { app, BrowserWindow, ipcMain } from 'electron';
import { fileURLToPath } from 'node:url';
import path from 'node:path';

process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';
const __dirname = path.dirname(fileURLToPath(import.meta.url));

process.env.APP_ROOT = path.join(__dirname, '..');
// web端生产环境地址
export const WEB_PROD_URL = 'https://webtrade.gaoyusoft.com';
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'];
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron');
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist/electron');

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST;

ipcMain.on('window-minimize', e => {
  const window = BrowserWindow.fromWebContents(e.sender)!;
  window.minimize();
});

ipcMain.on('window-close', e => {
  const window = BrowserWindow.fromWebContents(e.sender)!;
  window.close();
});

ipcMain.on('window-maximize', e => {
  const window = BrowserWindow.fromWebContents(e.sender)!;
  if (window.isMaximized()) {
    window.unmaximize();
  } else {
    window.maximize();
  }
});

let win: BrowserWindow | null;

function createWindow() {
  win = new BrowserWindow({
    show: false,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false,
      nodeIntegration: true,
    },
    frame: false,
    width: 800,
    height: 600,
    minWidth: 800,
    minHeight: 600,
    backgroundColor: '#eee',
  });

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    // win.loadFile(path.join(RENDERER_DIST, 'index.html'));
    win.loadURL(WEB_PROD_URL);
  }

  win.once('ready-to-show', () => {
    win?.show();
  });
  win.on('maximize', () => {
    console.log(123, win?.webContents.getTitle());

    win?.webContents.send('window-state-changed', win.isMaximized());
  });
  win.on('unmaximize', () => {
    win?.webContents.send('window-state-changed', win.isMaximized());
  });
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
    win = null;
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.whenReady().then(() => {
  createWindow();
  // loadNsServer(() => win!);
});
