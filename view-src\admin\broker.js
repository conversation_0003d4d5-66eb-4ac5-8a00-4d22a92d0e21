const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');
const { SmartTable } = require('../../libs/table/smart-table');

class View extends BaseAdminView {

    get repoBroker() {
        return require('../../repository/broker').repoBroker;
    }

    constructor(view_name) {
        super(view_name, '经纪商管理');
        this.$container = null;
        this.brokerList = [];
        this.brokerListHash = {};
        this.form = {
            broker: {
                brokerId: null,
                brokerName: null,
                assetType: null,
                id: null,
            },
            ip: {
                ip: null,
                port: null,
            },
        };

        this.currentBroker = null;

        this.assetsTypes = [
            { code: 1, name: '期货', className: 'label s-bg-red' },
            { code: 2, name: '股票', className: 'label s-bg-green' },
        ];

        this.searching = {
            prop: ['brokerName', 'brokerId'],
            value: '',
        };

        this.lastRefreshTime = new Date();
        this.getBrokerList();
    }

    createApp() {
        let controller = this;
        this.tableProps = { maxHeight: 500, ...this.systemSetting.tableProps };
        this.vueApp = new Vue({
            el: this.$container.querySelector('.broker-view-root'),
            components: {
                DataTables: DataTables.DataTables,
            },
            directives: {
                drag,
            },
            data: {
                searching: this.searching,
                filters: [this.searching],
                brokerList: this.brokerList,
                assetsTypes: this.assetsTypes,
                currentBroker: this.currentBroker,
                tableProps: this.tableProps,
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'el-icon-search',
                    },
                },
                dialog: {
                    broker: {
                        visible: false,
                        title: null,
                        form: Object.assign({}, this.form.broker),
                        rules: {
                            brokerId: [
                                { type: 'string', required: true, message: '请输入经纪商代码' },
                                { pattern: /\w{1,10}/, message: '不支持中文, 不超过10个字符' },
                                {
                                    validator(rule, value, callback) {
                                        if (controller.brokerList.filter(row => row != controller.currentBroker).some(row => row.brokerId === value)) {
                                            callback('经纪商代码重复');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                                controller.systemSetting.specialCharacterFilterRule,
                                controller.systemSetting.limitInputLengthRule,
                            ],
                            brokerName: [
                                { type: 'string', required: true, message: '请输入经纪商名称' },
                                controller.systemSetting.specialCharacterFilterRule,
                                controller.systemSetting.limitInputLengthRule,
                            ],
                            assetType: [{ type: 'number', required: true, message: '请选择经纪商类型' }],
                        },
                        ipList: [],
                    },
                    ip: {
                        visible: false,
                        title: null,
                        form: this.form.ip,
                        rules: {
                            ip: [
                                { type: 'string', required: true, message: '请输入ip' },
                                {
                                    pattern: /^(((25[0-5])|(2[0-4]\d)|(1\d\d)|([1-9]\d)|\d)(\.((25[0-5])|(2[0-4]\d)|(1\d\d)|([1-9]\d)|\d)){3}$|^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6})|(((http:\/\/)|(https:\/\/))?([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(\/)?)$/,
                                    message: '请输入ip地址或域名',
                                },
                            ],
                            port: [
                                { type: 'string', required: true, message: '请输入端口' },
                                { pattern: /^\d{1,5}$/, message: '格式不正确' },
                            ],
                        },
                        activeRow: null,
                    },
                },
                lastTime: new Date(),
            },
            mixins: [],
            methods: {
                refresh() {
                    controller.refresh();
                },
                handleRemoveBroker(row) {
                    controller.interaction.showConfirm({
                        title: '警告',
                        message: '确认要删除当前券商吗？',
                        confirmed: () => {
                            let brokerId = row.id;
                            controller.removeFromBrokerList(brokerId).then(flag => {
                                if (flag) {
                                    controller.brokerList.remove(cdt => cdt.id === brokerId);
                                    delete controller.brokerListHash[brokerId];
                                }
                            });
                        },
                    });
                },
                renderBrokerType(row) {
                    return controller.renderBrokerType(row);
                },
                openBrokerCreationDialog() {
                    this.currentBroker = controller.currentBroker = null;
                    this.setDialogData();
                    this.dialog.broker.visible = true;
                    this.$nextTick(() => {
                        this.$refs.brokerForm.clearValidate();
                    });
                },
                handleEditBroker(row) {
                    this.currentBroker = controller.currentBroker = row;
                    this.setDialogData(row);
                    this.dialog.broker.visible = true;
                    this.$nextTick(() => {
                        this.$refs.brokerForm.clearValidate();
                    });
                },
                setDialogData(row) {
                    this.dialog.broker.ipList = [];
                    if (!row) {
                        this.dialog.broker.title = '添加经纪商';
                        Object.keys(this.dialog.broker.form).forEach(key => {
                            this.dialog.broker.form[key] = null;
                        });
                    } else {
                        this.dialog.broker.title = `修改经纪商 ${row.brokerName}`;
                        Object.keys(row).forEach(key => {
                            this.dialog.broker.form[key] = row[key];
                        });
                        if (row.servers) {
                            let list = row.servers.split(',');
                            this.dialog.broker.ipList = list.map(url => {
                                let arr = url.split(':');
                                return {
                                    ip: arr[0],
                                    port: arr[1],
                                };
                            });
                        }
                    }
                },
                handleSaveBroker() {
                    this.$refs.brokerForm.validate(async valid => {
                        if (valid) {
                            if (this.dialog.broker.ipList.length == 0) {
                                controller.interaction.showWarning('请至少添加一个服务器地址');
                                return;
                            }
                            let info = Object.assign({}, this.dialog.broker.form, { servers: null });
                            info.servers = this.dialog.broker.ipList.map(row => `${row.ip}:${row.port}`).join();
                            if (typeof info.assetType !== 'undefined') {
                                info.brokerType = info.assetType;
                            }
                            let result = await controller.saveBroker(info);
                            if (result.flag) {
                                this.dialog.broker.visible = false;
                                this.updateTable(result.data);
                            }
                        }
                    });
                },
                updateTable(result) {
                    if (typeof controller.brokerListHash[result.id] === 'undefined') {
                        controller.brokerList.unshift(result);
                    } else {
                        let index = controller.brokerList.findIndex(cdt => cdt.id == result.id);
                        this.$set(this.brokerList, index, result);
                    }
                    controller.brokerListHash[result.id] = result;
                },
                handleOpenIpDialog() {
                    this.dialog.ip.activeRow = null;
                    this.dialog.ip.form = Object.assign({}, controller.form.ip);
                    this.dialog.ip.title = '添加IP';
                    this.dialog.ip.visible = true;
                    this.$nextTick(() => {
                        this.$refs.ipForm.clearValidate();
                    });
                },
                handleEditIp(row) {
                    this.dialog.ip.activeRow = row;
                    this.dialog.ip.title = `修改 ${row.ip}`;
                    this.dialog.ip.form.ip = row.ip;
                    this.dialog.ip.form.port = row.port;
                    this.dialog.ip.visible = true;
                    this.$nextTick(() => {
                        this.$refs.ipForm.clearValidate();
                    });
                },
                handleDeleteIp(row) {
                    controller.interaction.showConfirm({
                        message: `确定删除 ${row.ip}:${row.port} ?`,
                        confirmed: () => {
                            this.dialog.broker.ipList = this.dialog.broker.ipList.filter(item => item !== row);
                        },
                    });
                },
                handleSaveIp() {
                    let activeRow = this.dialog.ip.activeRow;
                    this.$refs.ipForm.validate(valid => {
                        if (valid) {
                            let info = {
                                ip: this.dialog.ip.form.ip,
                                port: this.dialog.ip.form.port,
                            };
                            if (!activeRow) {
                                this.dialog.broker.ipList.push(info);
                            } else {
                                Vue.set(
                                    this.dialog.broker.ipList,
                                    this.dialog.broker.ipList.findIndex(item => item === this.dialog.ip.activeRow),
                                    info,
                                );
                            }
                            this.dialog.ip.visible = false;
                        }
                    });
                },
            },
        });
        this.vueApp.$nextTick(() => {
            this.resizeWindow();
        });
    }

    //把broker格式化成form需要的类型
    standardBroker(broker) {
        if (!broker) {
            return {};
        }
        let standardObj = { assetType: broker.brokerType, ...broker };
        return standardObj;
    }

    async getBrokerList() {
        let loading = this.interaction.showLoading({
            text: '请求经纪商列表...',
        });
        try {
            const resp = await this.repoBroker.getAll();
            if (resp.errorCode == 0) {
                let list = resp.data;
                this.brokerList.clear();
                this.brokerList.merge(list.map(this.standardBroker));
                list.forEach(broker => {
                    this.brokerListHash[broker.id] = broker;
                });
                this.brokerList = this.brokerList.orderByDesc(cdt => cdt.id);
            } else {
                this.interaction.showHttpError('获取经纪商失败');
            }
        } catch (error) {
            this.interaction.showError('获取经纪商失败');
        }
        loading.close();
    }

    async saveBroker(item) {
        let output = {
            flag: false,
            data: null,
        };
        try {
            let resp = typeof item.id == 'number' ? await this.repoBroker.updateBroker(item) : await this.repoBroker.createBroker(item);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('保存经纪商成功');
                output.flag = true;
                if (typeof resp.data != 'undefined') {
                    output.data = resp.data;
                }
            } else {
                console.log(resp);
                this.interaction.showHttpError('保存经纪商失败, 详细信息："' + resp.errorMsg + '"');
            }
        } catch (error) {
            console.error(error);
            this.interaction.showError('保存经纪商失败');
        }
        return Promise.resolve(output);
    }

    async removeFromBrokerList(brokerId) {
        let loading = this.interaction.showLoading({
            text: '正在操作中...',
        });

        let flag = false;

        try {
            const resp = await this.repoBroker.removeBroker(brokerId);
            if (resp.errorCode === 0) {
                flag = true;
                this.interaction.showSuccess('操作成功!');
            } else {
                this.interaction.showHttpError('删除失败，详细信息:"' + resp.errorMsg + '"');
            }
        } catch (exp) {
            this.interaction.showHttpError('删除失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(flag);
    }

    resizeWindow() {
        
        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.maxHeight = winHeight - 140;
    }

    createTable4Export() {

        const $table = this.$container.querySelector('.table-4-broker-list-export');
        this.table4Export = new SmartTable($table, (rd) => rd.id, this, { tableName: 'smt-xz3hud', displayName: this.title });
    }

    renderBrokerType(row) {
        var matched = this.assetsTypes.find(item => item.code === row.brokerType);
        return matched ? `<span class="s-flag ${matched.className}">${matched.name}</span>` : '<span>未知</span>';
    }

    renderBrokerTypeText(row) {
        var matched = this.assetsTypes.find(item => item.code === row.brokerType);
        return matched ? matched.name : '未知';
    }

    refresh() {

        let now = new Date();
        if (now.getTime() - this.lastRefreshTime.getTime() >= 2000) {
            this.getBrokerList();
            this.lastRefreshTime = now;
        } 
        else {
            this.interaction.showWarning('您的操作太快，2S后重试！');
        }
    }

    exportSome() {

        this.table4Export.refill(this.brokerList);
        this.table4Export.exportAllRecords(`经纪商-${new Date().format('yyyyMMdd')}`);
    }

    build($container) {
        this.$container = $container;
        this.createApp();
        this.createTable4Export();
    }
}

module.exports = View;
