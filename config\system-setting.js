﻿/**
 * convenient setting for non business scenarios
 */

const systemSetting = {

    nullValue: '---',
    httpTimeout: 60 * 1000,

    tablePagination: {

        pageSize: 30,
        pageSizes: [30, 50, 100, 300, 500],
        currentPage: 1,
        layout: 'prev, pager, next, sizes, total'
    },

    tableProps: {

        highlightCurrentRow: true,
        border: false,
        stripe: true
    },

    /**
     * todo19: the below data members and logic units should be moved to a proper place
     */

    defaultAccountPassword: '123456',

    // something inproper to be here
    specialCharacterFilterRule: {

        trigger: 'blur',
        validator: (rule, value, callback) => {
            let reg_exp = /^(\~)|(\!)|(\@)|(\#)|(\$)|(\%)|(\^)|(\&)|(\*)|(\()|(\))|(\+)|(\=)|(\[)|(\])|(\{)|(\})|(\|)|(\\)|(\;)|(\:)|(\')|(\")|(\,)|(\.)|(\/)|(\<)|(\>)|(\?)$/;
            reg_exp.test(value) ? callback(new Error('您的输入包含非法字符')) : callback();
        },
    },
    
    limitInputLengthRule: {

        trigger: 'blur',
        validator: (rule, value, callback) => {

            let lengthLimit = 100;
            let length = 0;
            let compareArr = value.split('');
            let regExp = /[\u4e00-\u9fa5]+/g;
            //对汉字的特殊处理，2个字符的长度
            compareArr.forEach(currentChar => {

                if (regExp.test(currentChar)) {
                    length += 2;
                } 
                else {
                    length += 1;
                }
            });

            if (length > lengthLimit) {
                callback(new Error('你的输入不能超过100个字符'));
            } 
            else {
                callback();
            }
        }
    }
};

module.exports = { systemSetting };