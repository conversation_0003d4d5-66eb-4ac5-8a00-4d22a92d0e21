import { IdcComponentNameDef } from './ComponentNameDef';
import { RiskStepControl } from '@/enum/riskc';

const { instruction, entrusting, progressing, marketClosed } = RiskStepControl;
const Defs = IdcComponentNameDef;

type MethodSignatureType = {
  [componentName: string]: number;
};

function all() {
  return instruction.value | entrusting.value | progressing.value | marketClosed.value;
}

function onlyEntrusting() {
  return entrusting.value;
}

export const IndicatorCheckObjectMap: MethodSignatureType = {
  [Defs.BlackList]: all(),
  [Defs.WhiteList]: all(),
  [Defs.SingleOrderMaxVolume]: onlyEntrusting(),
  [Defs.AccountOrderMaxVolume]: onlyEntrusting(),
  [Defs.SingleOrderMaxAmount]: onlyEntrusting(),
  [Defs.NetBuyAmount]: onlyEntrusting(),
  [Defs.TradeFrequency]: onlyEntrusting(),
  [Defs.OrderCancelRate]: onlyEntrusting(),
  [Defs.InvalidOrderRate]: onlyEntrusting(),
  [Defs.IndayReversedDirection]: onlyEntrusting(),
  [Defs.SelfTrade]: onlyEntrusting(),
  [Defs.PriceDeviation]: onlyEntrusting(),
  [Defs.PriceLimit]: onlyEntrusting(),
  [Defs.MarketValue]: all(),
  [Defs.MarketValueRatio]: all(),
  [Defs.MarketCapitalRatio]: all(),
  [Defs.FlowableMarketCapitalRatio]: all(),
  [Defs.NavStopLoss]: all(),
  [Defs.FuturesPositionRatio]: all(),
  [Defs.FuturesMargin]: all(),
};
