const fs = require('fs');
const Routing = require('../model/routing').Routing;
const BaseWindow = require('./base-window').BaseWindow;

/**
 * 该窗口模板用于全占有式的视图
 */

class WinLandscape extends BaseWindow {

    constructor() {
        super('@win-landscape', true);
    }

    loadCss(css_file_path) {

        var $link = document.getElementById(this.linkId);
        if ($link != null) {
            $link.remove();
        }

        $link = document.createElement('link');
        $link.id = this.linkId;
        $link.rel = 'stylesheet';
        $link.href = css_file_path;
        document.head.appendChild($link);
    }

    buildApp() {
        
        var routing = this.routing;
        if (routing.cssLink) {
            this.loadCss(routing.cssLink);
        }

        console.log('to build app by routing > ', routing);

        // load html content
        fs.readFile(routing.html, (err, html_content) => {

            this.$container.innerHTML = html_content.toString();
            let ModuleLib = require(routing.script);
            let module_ins = new ModuleLib(routing.name, true);
            // 所有view都继承于IView接口
            module_ins.build(this.$container);
        });
    }

    /**
     * @param {Routing} routing 和路由对象相同结构的纯数据
     */
    specifyViewName(routing) {

        /**
         * 1. 全占有式窗口的视图，在加载中采用确定；
         * 2. 设置为视图名称，便于路由单元进行正确的构造；
         */
        this.viewName = routing.name;
    }

    listen2Events() {

        this.renderProcess.on('build-app', (event, routing) => {
            
            console.log('landscape window is asked to build app by routing = ' + JSON.stringify(routing));
            this.specifyViewName(routing);
            this.buildApp();
        });
    }

    build() {

        this.linkId = 'linked-style-' + this.helper.makeToken();
        this.$container = document.body.firstElementChild;
        this.brocastReady();
        this.listen2Events();
    }
}

module.exports = WinLandscape;