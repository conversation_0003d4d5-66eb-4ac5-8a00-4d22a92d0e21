const drag = require('../../../directives/drag');
const { helper } = require('../../../libs/helper');
const { <PERSON><PERSON><PERSON>el<PERSON> } = require('../../../libs/helper-biz');
const { IView } = require('../../../component/iview');
const { SmartTable } = require('../../../libs/table/smart-table');
const { TabList } = require('../../../component/tab-list');
const { Tab } = require('../../../component/tab');
const { Splitter } = require('../../../component/splitter');
const { SanguItemInfo, SanguOperationWays } = require('../../../model/sangu');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repoAccountAround } = require('../../../repository/account-around');
const { repoTrading } = require('../../../repository/trading');

/**
 * 检查项类型结构
 */
class CheckTypeItem {

    /**
     * @param {String} name 
     * @param {String} code 
     * @param {String} mean 
     */
    constructor(name, code, mean) {

        this.timeName = 'time_of_' + name;
        this.flagName = 'flag_of_' + name;
        this.code = code;
        this.mean = mean;
        this.isCover = mean.indexOf('覆盖') >= 0;
        this.isCheck = mean.indexOf('检测') >= 0;
    }
}

/**
 * 检查项
 */
const CheckTypes = [

    new CheckTypeItem('wtfg', 0, '委托覆盖'),
    new CheckTypeItem('cjfg', 1, '成交覆盖'),
    new CheckTypeItem('ccfg', 2, '持仓覆盖'),
    new CheckTypeItem('zjfg', 3, '资金覆盖'),
    new CheckTypeItem('wtjc', 4, '委托检测'),
    new CheckTypeItem('cjjc', 5, '成交检测'),
    new CheckTypeItem('zjjc', 6, '资金检测'),
    // new CheckTypeItem('nbccbd', 7, '内部持仓比对'),
];

const CheckTypesMap = {};

/**
 * @param {*} time_name 
 * @returns {CheckTypeItem}
 */
function Seek(time_name) {
    return CheckTypesMap[time_name];
}

const PlaceHolder = '--';

/**
 * 账号比对和覆盖检测结果项结构
 */
class AccountCheckResult {

    constructor({ accountId, accountName, checkFlag, checkTime, checkType }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.checkFlag = checkFlag;
        this.checkTime = checkTime;
        this.checkType = checkType;
    }
}

/**
 * 列表数据结构（账号监控记录结构）
 */
class AccountResult {

    constructor({ accountId, accountName, assetType, checks = [new AccountCheckResult({})].splice(1) }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.assetType = assetType;

        let thisObj = this;
        let checkResults = checks || [];

        CheckTypes.forEach(ct => {

            let matched = checkResults.find(cr => cr.checkType == ct.code);
            thisObj[ct.timeName] = matched ? matched.checkTime : undefined;
            thisObj[ct.flagName] = matched ? !!matched.checkFlag : undefined;
        });
    }
}

class Controller extends IView {

    constructor(view_name) {

        super(view_name, false, '账号监控');
        this.userModes = { super: 'super', org: 'org', broker: 'broker' };
        this.checkTypes = CheckTypes;
        this.dialogSetting = {

            title: '比对详情',
            record: null, 
            typeCode: null,
            typeName: null,
            visible: false,
            onlyReal: false,
            isPosCompare: false,
            cachedResp: null,
        };

        this.sanguDialog = {

            title: '散股处理',
            visible: false,
            /** 散股原始数据 */
            matrix: [],
            /** 散股数据对象结构 */
            records: [new SanguItemInfo([])].splice(1),
            /** 预估交易金额 */
            estimate: 0,
        };
    }

    createToolbarApp() {

        this.scopedTypes = {

            all: { code: 'all', mean: '全部' },
            ok: { code: 'ok', mean: '各项均正常' },
            cover: { code: 'cover', mean: '错误 = 覆盖类' },
            check: { code: 'check', mean: '错误 = 检测类' },
            anyError: { code: 'any-error', mean: '错误 = 任意项' },
        };

        var assetsType = this.systemEnum.assetsType;
        this.assetTypes = {

            all: { code: 'all', mean: '全部账号' },
            stock: assetsType.stock,
            future: assetsType.future,
            option: assetsType.option,
        };

        this.searching = {

            keywords: '',
            assetType: this.assetTypes.all.code,
            checkType: this.scopedTypes.all.code,
        };

        var pagination = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: pagination.pageSizes,
            pageSize: pagination.pageSize,
            layout: pagination.layout,
            total: 0,
            page: 1,
        };

        var checkTypeOptions = [];
        checkTypeOptions.merge(helper.dict2Array(this.scopedTypes));
        /** 此处使用time name赋值给code字段，便于筛选时对接查找索引 */
        checkTypeOptions.merge(CheckTypes.map(item => ({ code: item.timeName, mean: `仅错误 = ${item.mean}` })));

        this.toolbar = new Vue({

            el: this.$container.querySelector('.part-upper > .toolbar'),
            data: {

                checkTypes: checkTypeOptions,
                assetTypes: helper.dict2Array(this.assetTypes),
                searching: this.searching,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterRecords,
                this.doRealtimePosCheck,
                this.openSanguDialog,
                this.exportSome, 
                this.refresh, 
                this.config, 
                this.handlePageSizeChange, 
                this.handlePageChange,
            ]),
        });
    }

    /**
     * @param {AccountResult} record 
     */
    identifyRecord(record) {
        return record.accountId;
    }

    /**
     * 建立检测类型索引，提高后续访问检测类型数据的效率
     */
    setupCheckTypesMap() {
        CheckTypes.forEach(item => { CheckTypesMap[item.timeName] = item; });
    }

    setupTable() {

        var $table = this.$container.querySelector('.part-upper > table');
        var $before = $table.querySelector('th.cell-row-actions');
        var $header = $before.parentNode;

        CheckTypes.forEach(item => {

            let $th = document.createElement('th');
            $th.setAttribute('label', item.mean);
            $th.setAttribute('prop', item.timeName);
            $th.setAttribute('watch', `${item.timeName},${item.flagName}`);
            $th.setAttribute('formatter', 'formatCheckResult');
            $th.setAttribute('export-formatter', 'formatCheckResultText');
            $th.setAttribute('min-width', '80');
            $th.setAttribute('sortable', true);
            $th.setAttribute('overflowt', true);
            $header.insertBefore($th, $before);
        });

        this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-aam',
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: (filtered_count) => { this.paging.total = filtered_count; },
            rowSelected: (account) => { this.handleAccountChange(account); },
        });

        this.tableObj.setPageSize(this.paging.pageSize);
        this.tableObj.setMaxHeight(200);
    }

    setupDynamicPart() {

        let $splitter = this.$container.querySelector('.splitter-bar');
        let $summary = this.$container.querySelector('.account-summary');

        if (this.isSuperMode) {

            $splitter.remove();
            $summary.remove();
            this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
        }
        else {

            this.buildSplitter($splitter);
            this.setupSummary($summary);
        }
    }

    /**
     * @param {Number|String} time 
     */
    formatTimeValue(time) {

        return this.helper.isNotNone(time) ? time : PlaceHolder;

        // if (typeof time == 'number' || typeof time == 'string') {

        //     time = time.toString();
        //     while (time.length < 6) {
        //         time = '0' + time;
        //     }

        //     return `${time.substring(0, 2)}:${time.substring(2, 4)}:${time.substring(4, 6)}`;
        // }
        // else {
        //     return PlaceHolder;
        // }
    }

    /**
     * @param {AccountResult} record
     * @param {String} time_val 
     * @param {String} time_name 
     */
    formatCheckResult(record, time_val, time_name) {

        let matched = Seek(time_name);
        let isOk = record[matched.flagName];
        // console.log({ record, time_val, time_name, matched, isOk });

        if (isOk === undefined) {
            return '<span class="s-color-red">0</span>';
        }
        else if (isOk === true) {
            return `<span>${this.formatTimeValue(time_val)}</span>`;
        }
        else {
            return `<a title="点击查看详情" class="s-color-red s-cp s-underline" event.onclick="showDetailError">${time_val}</a>`;
        }
    }

    /**
     * @param {AccountResult} record 
     * @param {String} time_val 
     * @param {String} time_name 
     */
    formatCheckResultText(record, time_val, time_name) {
        return time_val;
    }

    formatActions() {

        return `<span class="smart-table-action">
                    <a class="lock-button icon-button">...</a>
                    <ul>
                        <li><a class="icon-button iconfont icon-fugai" event.onclick="doCover">覆盖</a></li>
                        <li><a class="icon-button iconfont icon-qingsuan" event.onclick="doTradeCheck">成交检测</a></li>
                        <li><a class="icon-button iconfont icon-qingsuan" event.onclick="doOrderCheck">委托检测</a></li>
                        <li><a class="icon-button iconfont icon-qingsuan" event.onclick="doBalanceCheck">权益检测</a></li>
                    </ul>
                </span>`;
    }

    isInternalPositionCompare(typeCode) {
        return CheckTypes.some(x => x.code == typeCode && x.mean == '内部持仓比对');
    }

    /**
     * @param {AccountResult} record 
     * @param {HTMLElement} $link 
     * @param {HTMLTableCellElement} $cell 
     * @param {String} time_val 
     * @param {String} time_name 
     */
    showDetailError(record, $link, $cell, time_val, time_name) {

        let matched = Seek(time_name);
        Object.assign(this.dialogSetting, {
            record, 
            typeCode: matched.code, 
            typeName: matched.mean, 
            isPosCompare: this.isInternalPositionCompare(matched.code),
        });        
        this.showDetailDialog(record, matched.code, matched.mean);
    }

    /**
     * 
     * @param {AccountResult} record 
     * @param {Number} typeCode
     * @param {String} typeName
     */
    showDetailDialog(record, typeCode, typeName) {

        var thisObj = this;

        function showDialog() {
            
            const ref = thisObj.dialogSetting;
            ref.title = `${record.accountName} / 比对详情`;
            ref.visible = true;
            ref.onlyReal = false;

            thisObj.detailApp.$nextTick(() => {

                thisObj.setAsTableBox(thisObj.detailApp.$el.querySelector('.table-box'));
                thisObj.$tableBox.innerHTML = '';
                thisObj.request2ShowDetail(record, typeCode, typeName);
            });
        }

        if (this.detailApp) {

            showDialog();
            return;
        }

        this.detailApp = new Vue({

            el: this.$container.querySelector('.dialog-error-list'),
            directives: { drag },
            data: {
                dialog: this.dialogSetting,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.closeDialog,
                this.handleRealAccountFilter,
            ]),
        });

        this.detailApp.$nextTick(() => { showDialog(); });
    }

    get $tsangu() {
        return this.sanguApp.$refs.$tsangu;
    }

    openSanguDialog() {

        if (!this.sanguApp) {

            this.sanguApp = new Vue({

                el: this.$container.querySelector('.dialog-sangu-list'),
                directives: { drag },
                data: {
                    dialog: this.sanguDialog,
                },
                methods: this.helper.fakeVueInsMethod(this, [
                    this.thousandsInt,
                    this.formatOperWay,
                    this.onekeySellSangu,
                    this.closeSanguDialog,
                    this.handleSelectionChange,
                ]),
            });
        }

        const ref = this.sanguDialog;
        ref.estimate = 0;
        ref.visible = true;

        this.sanguApp.$nextTick(() => {
            this.$tsangu.clearSelection();
            this.requestSangus();
        });
    }

    thousandsInt(value) {
        return typeof value == 'number' && value > 0 ? value.thousands() : '0';
    }

    /**
     * @param {SanguItemInfo} row 
     */
    formatOperWay(row) {

        if (row.operateWay == SanguOperationWays.closePos) {
            return `<span class="s-color-red">${row.operateWay}</span>`;
        }
        else {
            return row.operateWay;
        }
    }

    /**
     * @returns {Array<SanguItemInfo>}
     */
    getCheckedSangus() {
        return this.$tsangu.selection;
    }

    onekeySellSangu() {

        if (this.sanguDialog.matrix.length == 0) {
            this.interaction.showError('当前没有散股');
            return;
        }

        let checkeds = this.getCheckedSangus();
        if (checkeds.length == 0) {
            this.interaction.showError('未选择散股');
            return;
        }

        let oks = checkeds.filter(x => x.sanguPosition > 0);
        if (oks.length == 0) {
            this.interaction.showError(`勾选目标，可平仓数量 = 0`);
            return;
        }

        let closes = checkeds.filter(x => x.operateWay == SanguOperationWays.closePos);

        /**
         * 统计每个账号对应的平仓金额
         */
        let groups = [{ accountId: null, accountName: null, closeAmount: 0 }].splice(1);
        closes.forEach(item => {

            let matched = groups.find(grp => grp.accountId == item.accountId);
            if (matched == undefined) {
                matched = { accountId: item.accountId, accountName: item.accountName, closeAmount: 0 };
                groups.push(matched);
            }

            matched.closeAmount += item.price * item.sanguPosition;
        });

        let amountThreshold = 50000;
        let overs = groups.filter(x => x.closeAmount > amountThreshold);
        let totalAmount = groups.map(x => x.closeAmount).sum();
        let mentions = [
            ['勾选数量', checkeds.length],
            ['平仓数量', closes.length],
            ['平仓账号数量', groups.length],
            ['平仓金额', this.thousandsInt(totalAmount)],
        ];

        if (overs.length > 0) {
            mentions.push([
                '超额提醒', 
                `有${overs.length}个账号的平仓金额 > ${this.thousandsInt(amountThreshold)}，分别为：${overs.map(x => x.accountName).join()}`,
                's-color-red',
            ]);
        }
        
        let message = mentions.map(item => `<div><span>${item[0] || '----'}${item[0] ? '： ' : ''}</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        this.interaction.showConfirm({

            title: '一键卖出散股确认',
            message: message,
            confirmed: () => {
                this.sendOutSellSanguRequest(checkeds);
            },
        });
    }

    /**
     * @param {Array<SanguItemInfo>} targets 
     */
    async sendOutSellSanguRequest(targets) {

        console.log('to send out close request', this.helper.deepClone(targets));
        let resp = await repoAccountAround.sanguSell(targets);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess('散股平仓请求，已发送');
            this.$tsangu.clearSelection();
            this.requestSangus();
        }
        else {
            this.interaction.showError(`散股平仓，处理失败：${errorCode}/${errorMsg}`);
        }
    }

    closeSanguDialog() {
        this.sanguDialog.visible = false;
    }

    handleSelectionChange() {

        let checkeds = this.getCheckedSangus();
        let closes = checkeds.filter(x => x.operateWay == SanguOperationWays.closePos);
        this.sanguDialog.estimate = closes.map(x => x.price * x.sanguPosition).sum();
    }

    /**
     * 查询全局汇总的散股详情
     */
    async requestSangus() {
        
        let resp = await repoAccountAround.sanguQuery();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0 && data instanceof Array) {
            
            data.shift();
            this.sanguDialog.matrix = data;
            let records = data.map(arr => new SanguItemInfo(arr));

            let stockHash = BizHelper.stocksHash;
            let resp2 = await repoTrading.getMarketLatestPrice(this.systemEnum.assetsType.stock.code);
            let priceMap = resp2.data || {};

            records.forEach(item => {

                /**
                 * 补充合约名称
                 */
                let matched = stockHash[item.instrument];
                item.instrumentName = matched == undefined ? null : matched.instrumentName;

                /**
                 * 补充最新价格
                 */
                item.price = priceMap[item.instrument] || 0;
            });

            this.sanguDialog.records = records;
            this.$tsangu.toggleAllSelection();
            setTimeout(() => { this.handleSelectionChange(); }, 100);
        }
        else {
            this.interaction.showError(`散股查询出错：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {HTMLElement} $tableBox 
     */
    setAsTableBox($tableBox) {
        this.$tableBox = $tableBox;
    }

    closeDialog() {
        this.dialogSetting.visible = false;
    }

    handleRealAccountFilter() {
        
        let { record, typeCode, typeName, cachedResp } = this.dialogSetting;
        this.$tableBox.innerHTML = '';
        this.request2ShowDetail(record, typeCode, typeName, cachedResp);
    }

    /**
     * @param {AccountResult} record
     */
    doCover(record) {
        this.executeGeneralTask(record, '覆盖', repoAccountAround.overlap, [record.accountId, true]);
    }

    /**
     * @param {AccountResult} record
     */
    doTradeCheck(record) {
        this.executeGeneralTask(record, '成交检测', repoAccountAround.tradeCheck, [record.accountId]);
    }

    /**
     * @param {AccountResult} record
     */
    doOrderCheck(record) {
        this.executeGeneralTask(record, '委托检测', repoAccountAround.orderCheck, [record.accountId]);
    }

    /**
     * @param {AccountResult} record
     */
    doBalanceCheck(record) {
        this.executeGeneralTask(record, '权益检测', repoAccountAround.balanceCheck, [record.accountId]);
    }

    /**
     * execute a general task
     * @param {AccountResult} record
     * @param {String} task_name
     * @param {Function} task
     * @param {Array} args
     * @param {Function} callback
     */
    async executeGeneralTask(record, task_name, task, args, callback) {

        this.interaction.showConfirm({

            title: '操作确认',
            message: `<span>是否确认，对账号 [${record.accountName}] 执行 [${task_name}] 操作</span>`,
            confirmed: async () => {

                let resp = await task.call(repoAccountAround, ...args);

                if (typeof callback == 'function') {
                    callback(resp);
                } 
                else {

                    if (resp.errorCode === 0) {
                        this.interaction.showSuccess(`[${task_name}] 操作已执行`);
                    } 
                    else {
                        this.interaction.showError(`操作 [${task_name}] 发生异常：${resp.errorCode}/${resp.errorMsg}`);
                    }
                }
            },
        });
    }

    adjustTableHeight(win_width, win_height, is_maximized) {
        this.tableObj.setMaxHeight(win_height - (is_maximized ? 180 : 165));
    }

    _handleActivation() {
        this.tableObj.fitColumnWidth();
    }

    /**
     * @param {HTMLElement} $splitter
     */
    buildSplitter($splitter) {

        this.splitter = new Splitter('admin-account-monitor', $splitter, this.handleSpliting.bind(this), { previousMinHeight: 155, nextMinHeight: 200 });
        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    /**
     * @param {Number} previous_height
     * @param {Number} next_height
     */
    handleSpliting(previous_height, next_height) {

        this.tableObj.setMaxHeight(previous_height - 75);
        this.summary.tabs.forEach((tab) => { tab.viewEngine.setHeight(next_height); });
    }

    /**
     * @param {HTMLElement} $summary
     */
    setupSummary($summary) {

        var tablist = new TabList({

            allowCloseTab: false,
            embeded: true,
            lazyLoad: false,
            $navi: $summary.querySelector('.summary-tabs'),
            $content: $summary.querySelector('.summary-content'),
            tabCreated: this.handleTabCreated.bind(this),
        });

        tablist.openTab(true, '@shared/order-list', '今日订单');
        tablist.openTab(true, '@shared/position-list', '今日持仓');
        tablist.openTab(true, '@shared/exchange-list', '今日成交');
        tablist.openTab(true, '@shared/history-order-list', '历史订单');
        tablist.openTab(true, '@shared/history-position-list', '历史持仓');
        tablist.openTab(true, '@shared/history-exchange-list', '历史成交');
        tablist.openTab(true, '@shared/history-equity-list', '历史权益');
        tablist.openTab(true, '@shared/history-cash-inout', '出入金');

        this.summary = tablist;
    }

    /**
     * @param {Tab} tab
     */
    handleTabCreated(tab) {

        if (this.contextInfo) {
            this.summary.fireEventOnTab(tab, this.systemEvent.viewContextChange, this.constructContextParms(this.contextInfo));
        }
    }

    /**
     * @param {AccountResult} account
     */
    handleAccountChange(account) {

        if (this.isSuperMode) {
            return;
        }

        if (this.contextInfo && this.contextInfo.accountId === account.accountId) {
            return;
        }

        this.contextInfo = account;
        if (this.summary) {
            this.summary.fireEventOnAllTabs(this.systemEvent.viewContextChange, this.constructContextParms(account));
        }
    }

    /**
     * @param {AccountResult} account
     */
    constructContextParms(account) {

        return {

            accountId: account.accountId,
            accounts: [
                {
                    accountId: account.accountId,
                    accountName: account.accountName,
                    assetType: account.assetType,
                    isCredit: !!account.credit,
                },
            ],
        };
    }

    /**
     * @param {Array<Array<String | Number>>} errors
     */
    specializeCheckResult(errors) {

        /**
         * 返回的数据列，需要与预期的列完全一致（顺序可不一样）
         */
        let expected = ['账号ID', '账号名称', '合约_方向', '合约名称', '持仓量', '差异'];
        let titles = errors.shift();
        if (expected.slice(0).sort().join() != titles.slice(0).sort().join()) {

            // title对不上，则认为titles缺失，第一行为实际数据
            errors.unshift(titles);
            return errors;
        }

        /**
         * 数据列对应索引
         */
        let indexes = {
            
            accountId: titles.indexOf(expected[0]),
            accountName: titles.indexOf(expected[1]),
            instrumentDirection: titles.indexOf(expected[2]),
            instrumentName: titles.indexOf(expected[3]),
            volume: titles.indexOf(expected[4]),
            // 该值为0表示虚拟账号，非0代表真实账号
            diff: titles.indexOf(expected[5]),
        };

        /**
         * 生成排序键值
         * @param {Array<String | Number>} values 
         */
        function makeCompareKey(values) {
            return `${values[indexes.instrumentName]}/${values[indexes.diff] == 0 ? 1 : 9}`;
        }

        /**
         * 对数据进行排序，按合约进行归类，单个合约内部，按照真实优先虚拟次之
         */
        errors.sort((a, b) => {

            let a_key = makeCompareKey(a);
            let b_key = makeCompareKey(b);
            return this.helper.compare(a_key, b_key, false);
        });

        if (this.dialogSetting.onlyReal) {
            errors.remove(values => values[indexes.diff] == 0);
        }

        errors.unshift(titles);
        return errors;
    }

    /**
     * 请求账号检测状态信息
     * @param {AccountResult} record 
     * @param {Number} typeCode
     * @param {String} typeName
     */
    async request2ShowDetail(record, typeCode, typeName, cached = undefined) {

        if (this._isRequestingDetail) {
            return;
        }

        this._isRequestingDetail = true;
        let scopeInfo = `账号( ${record.accountName} )的检测项( ${typeName} )`;
        let $loading = this.interaction.showLoading({ text: `获取${scopeInfo}详情...` });
        let resp = this.helper.isJson(cached) ? cached : await repoAccountAround.queryDetail(record.accountId, typeCode);
        $loading.close();
        this._isRequestingDetail = false;
        this.dialogSetting.cachedResp = this.helper.deepClone(resp);

        if (resp.errorCode != 0) {

            this.interaction.showError(`获取${scopeInfo}失败，${resp.errorCode}/${resp.errorMsg}`);
            return;        
        }

        let resultStr = (resp.data || {}).result;
        if (helper.isNone(resultStr) || typeof resultStr != 'string') {

            this.closeDialog();
            this.interaction.showMessage(`${scopeInfo}<br/>无详情`);
            return;
        }

        let errors;
        try {
            errors = JSON.parse(resultStr);
        }
        catch(ex) {
            errors = resultStr;
        }

        if (typeof errors == 'string') {
            
            this.closeDialog();
            this.interaction.showAlert({ message: `${scopeInfo}
                                                <br/>详情如下：
                                                <br/>
                                                <div class="content-box s-scroll-bar">${errors}</div>`, customClass: 'message-detail-error' });
            return;
        }

        if (!(errors instanceof Array) || errors.length == 0) {
            
            this.closeDialog();
            this.interaction.showAlert({ message: `${scopeInfo}
                                                <br/>详情如下：
                                                <br/>
                                                <div class="content-box s-scroll-bar">${JSON.stringify(errors)}</div>`, customClass: 'message-detail-error' });
            return;
        }

        const isPosCompare = this.dialogSetting.isPosCompare;
        if (isPosCompare) {
            errors = this.specializeCheckResult(errors);
        }

        let titles = errors[0];
        if (!(titles instanceof Array) || titles.length <= 1) {

            this.closeDialog();
            this.interaction.showAlert(`${scopeInfo}<br/>无明确错误信息`);
            return;
        }

        let cols = titles.map((headerText, index) => {

            let propName = index;
            let width = 50 + typeof headerText == 'string' ? headerText.length * 20 : 50;
            return `<th label="${headerText}" prop="${propName}" min-width="${width}" formatter="formatContent" ${isPosCompare ? '' : 'sortable'} overflowt></th>`;
        });

        let tableDef = `<table><tr>${cols}</tr></table>`;
        let $tableWrapper = document.createElement('div');
        this.$tableBox.appendChild($tableWrapper);
        $tableWrapper.innerHTML = tableDef;

        function identifyErrorRecord(error) {
            return error.errorItemId;
        }

        let errorRecords = errors.slice(1);
        errorRecords.forEach((item, item_idx) => { item.errorItemId = item_idx; });
        this.tableDetail = new SmartTable($tableWrapper, identifyErrorRecord, this, {

            tableName: 'smt-aamd',
            displayName: 'account-monitor-error',
        });

        this.tableDetail.setPageSize(*********);
        this.tableDetail.setMaxHeight(500);
        this.tableDetail.refill(errorRecords);
    }

    formatContent(record, fieldValue, fieldName) {

        if (typeof fieldValue == 'number') {

            return Number.isInteger(fieldValue) ? ColumnCommonFunc.thousands(record, fieldValue, fieldName)
                                                : ColumnCommonFunc.thousandsDecimal(record, fieldValue, fieldName);
        }
        else {
            return fieldValue;
        }
    }

    /**
     * 请求账号检测状态信息
     * @param {Boolean} isInterval 是否为定时刷新
     */
    async requestStates(isInterval) {

        if (this._isRequesting) {
            return;
        }

        this._isRequesting = true;
        let $loading;

        if (!isInterval) {
            $loading = this.interaction.showLoading({ text: '获取账号监控数据...' });
        }

        let resp = await repoAccountAround.queryAll();
        let records = resp.data || [];

        if (!isInterval) {
            $loading.close();
        }

        this._isRequesting = false;

        if (resp.errorCode != 0 || !(records instanceof Array)) {

            this.interaction.showError(`获取账号列表失败，${resp.errorCode}/${resp.errorMsg}`);
            return;        
        }

        let results = records.map(item => new AccountResult(item));
        
        if (!isInterval) {

            this.tableObj.refill(results);

            if (results.length > 0) {
                this.handleAccountChange(results[0]);
            }
        }
        else {
            results.forEach(act => { this.tableObj.putRow(act); });
        }
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.searching.keywords;
        var astype = this.searching.assetType;
        let cktype = this.searching.checkType;

        /**
         * @param {AccountResult} record
         */
        function matchKeywords(record, keywords) {
            return !keywords || record.accountName.indexOf(keywords) >= 0 || thisObj.helper.pinyin(record.accountName).indexOf(keywords) >= 0;
        }

        /**
         * @param {AccountResult} record
         */
        function matchAssetType(record, astype) {

            if (astype == thisObj.assetTypes.all.code || helper.isNone(astype)) {
                return true;
            }
            else {
                return record.assetType == astype;
            }
        }

        /**
         * @param {AccountResult} record
         */
        function matchCheckType(record, cktype) {

            if (cktype == thisObj.scopedTypes.all.code || helper.isNone(cktype)) {
                return true;
            }

            if (cktype == thisObj.scopedTypes.ok.code) {
                return !CheckTypes.some(item => record[item.flagName] == false || record[item.flagName] == undefined);
            }

            if (cktype == thisObj.scopedTypes.cover.code) {
                return CheckTypes.some(item => item.isCover && record[item.flagName] == false);
            }

            if (cktype == thisObj.scopedTypes.check.code) {
                return CheckTypes.some(item => item.isCheck && record[item.flagName] == false);
            }

            if (cktype == thisObj.scopedTypes.anyError.code) {
                return CheckTypes.some(item => record[item.flagName] == false);
            }

            let matched = Seek(cktype);
            return record[matched.flagName] == false;
        }

        /**
         * @param {AccountResult} record
         */
        function searcher(record) {
            return matchKeywords(record, keywords) && matchAssetType(record, astype) && matchCheckType(record, cktype);
        }

        this.paging.page = 1;
        this.tableObj.setPageIndex(1, false);
        this.tableObj.customFilter(searcher);
    }

    async doRealtimePosCheck() {
        
        let resp = await repoAccountAround.realtimePosCheck();
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess(`请求已处理`);
            this.refresh();
        }
        else {
            this.interaction.showError(`处理失败：${errorCode}/${errorMsg}`);
        }
    }

    exportSome() {
        this.tableObj.exportAllRecords(`账号监控列表-${new Date().format('yyyyMMdd')}`);
    }

    refresh() {
        
        this.searching.keywords = null;
        this.searching.assetType = this.assetTypes.all.code;
        this.searching.checkType = this.scopedTypes.all.code;
        this.paging.page = 1;
        this.requestStates();
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    dispose() {
        clearInterval(this.refreshTimer);
    }

    async build($container, view_option) {

        super.build($container);

        const tag = (view_option || {}).tag;
        this.isSuperMode = tag === this.userModes.super;
        this.isOrgMode = tag === this.userModes.org;
        this.isBrokerMode = tag === this.userModes.broker;

        this.helper.extend(this, ColumnCommonFunc);
        this.createToolbarApp();
        this.setupCheckTypesMap();
        this.setupTable();
        this.setupDynamicPart();
        this.registerEvent(this.systemEvent.tabActivated, this._handleActivation.bind(this));
        this.requestStates();
        this.refreshTimer = setInterval(() => { this.requestStates(true); }, 1000 * 60);
    }
}

module.exports = Controller;