<!DOCTYPE html>
<html>

<head>
    <title>performance log analysis</title>
    <style type="text/css">

    body {
        padding: 20px;
    }
    
    * {
        font-size: 12px;
        font-family: 'Microsoft YaHei';
    }

    textarea {
        width: 100%;
        min-height: 200px;
    }

    button {
        margin-top: 10px;
    }

    #result {
        margin-top: 10px;
    }

    .inner {
        display: inline-block;
        line-height: 16px;
        padding: 2px 8px;
        border-radius: 5px;
    }

    .summary-total {
        margin-bottom: 10px;
    }

    .summary-total .inner {
        font-weight: bold;
        background-color: #e2781a;
    }

    .summary-topic {
        margin: 10px 0;
    }

    .summary-topic .inner {
        background-color: #db81cf;
    }

    .step-info {
        line-height: 16px;
        height: 20px;
        margin-left: 20px;
        margin-bottom: 5px;
    }

    .step-info .step-job {

        display: inline-block;
        width: 500px;
        height: 100%;
        padding-left: 8px;
        padding-right: 8px;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        background-color: #5285a1;
        color: white;

        overflow: hidden;
        word-wrap: normal;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .step-info .step-job .ts {
        padding-right: 5px;
        border-right: 1px solid white;
    }

    .step-info .step-cost-bar {

        display: inline-block;
        height: 100%;
        position: relative;
        left: -3px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        background-color: #166ecb;
        color: white;
    }

    .step-info .step-cost {
        position: relative;
        top: -5px;
    }

    </style>
</head>

<body>
    <div class="body-main">
		<textarea id="log"></textarea>
        <div>
            <input id="option-by-group" type="checkbox" onchange="analysis()" checked />
            <label for="option-by-group">INTO GROUOPS</label>
            <button onclick="analysis()">ANALYZE</button>
        </div>
        <div id="result"></div>
    </div>
    <script type="text/javascript" src="libs/3rd/vue.js"></script>
    <script type="text/javascript" src="libs/3rd/element-ui.js"></script>
    <script type="text/javascript" src="libs/type-extension.js"></script>
    <script type="text/javascript">

        var ele_log = document.getElementById('log');
        var ele_group_option = document.getElementById('option-by-group');
        var ele_result = document.getElementById('result');
        var salt = 'xh3nsdyUWNYF6e269JVFHUJW52ksnslko023u-';

        function extractInfo (input, group_by_topic) {

            var log_lines = input.split('\n');
            log_lines.remove(x => x.indexOf(salt) <= 0);
            if(log_lines.length == 0) {
                return null;
            }

            var refines = log_lines.map(single_line => {
                let secs = single_line.split('\t');
                return { topic: secs[1].replace(salt, '').trim(), step: secs[2].trim(), time_stamp: parseInt(secs[3].trim()), cost: 0 };
            }).orderBy(x => x.time_stamp);

            var start_ts = refines[0].time_stamp;
            var end_ts = refines[refines.length - 1].time_stamp;
            var sum = { total: end_ts - start_ts, topics: {} };
            var topic_grouped_sorted = refines.groupBy(x => x.topic);

            for(let topic_name in topic_grouped_sorted) {

                var steps = topic_grouped_sorted[topic_name];
                var group_start_ts = steps[0].time_stamp;
                var group_end_ts = steps[steps.length - 1].time_stamp;
                sum.topics[topic_name] = group_end_ts - group_start_ts;
            }

            if(!group_by_topic) {

                for(let idx = 1; idx < refines.length; idx++) {
                    refines[idx].cost = refines[idx].time_stamp - refines[idx - 1].time_stamp;
                }
                return { steps: refines, sum: sum };
            }
            else {
                var topic_groups = [];
                for(let topic_name in topic_grouped_sorted) {

                    let grp_steps = topic_grouped_sorted[topic_name];
                    for(let idx = 1; idx < grp_steps.length; idx++) {
                        grp_steps[idx].cost = grp_steps[idx].time_stamp - grp_steps[idx - 1].time_stamp;
                    }
                    topic_groups.push({ name: topic_name, steps: grp_steps });
                }
                return { groups: topic_groups, sum: sum };
            }
        }

		function analysis() {

            var log_content = ele_log.value.trim();
            ele_result.innerHTML = '';

            if(log_content.length == 0) {
                return;
            }

            saveLog2Storage(log_content);
            var should_group_by_topic = ele_group_option.checked;
            var report = extractInfo(log_content, should_group_by_topic);

            if(report == null) {
                ele_result.innerHTML = 'no validated input!!!';
                return;
            }
            
            var line_total = document.createElement('div');
            line_total.classList.add('summary-total');
            line_total.innerHTML = `<span class="inner">all topics consumed: ${report.sum.total}ms</span>`;
            ele_result.appendChild(line_total);

            if(should_group_by_topic) {

                var t_topic = document.createElement('div');
                t_topic.classList.add('summary-topic');

                report.groups.forEach(function(this_topic_group) {

                    let cloned_topic = t_topic.cloneNode();
                    let topic_cost = report.sum.topics[this_topic_group.name];
                    let topic_cost_in_total = (100 * topic_cost / (report.sum.total || 1)).toFixed(2);

                    cloned_topic.innerHTML = `<span class="inner">${this_topic_group.name} > ${topic_cost}ms(${topic_cost_in_total})%</span>`;
                    ele_result.appendChild(cloned_topic);
                    putupSteps(this_topic_group.steps, topic_cost);
                });
            }
            else {
                putupSteps(report.steps, report.sum.total);
            }
        }

        function putupSteps(steps, all_steps_cost) {

            var full_width = 800;
            var t_step = document.createElement('div');
            t_step.classList.add('step-info');

            steps.forEach(function(this_step) {
                    
                let cloned_step = t_step.cloneNode();
                let ts = new Date(this_step.time_stamp);
                let ms_str = ts.getMilliseconds().toString();
                while(ms_str.length < 3) { ms_str = '0' + ms_str; }
                let ts_str = ts.format('yyyy-MM-dd hh:mm:ss') + ' ' + ms_str;
                let cost_width = parseFloat((full_width * this_step.cost / all_steps_cost).toFixed(3));
                cloned_step.innerHTML = `<span class="step-job"><span class="ts">${ts_str}</span>&nbsp;${this_step.step}</span>
                                            <span class="step-cost-bar" style="width:${cost_width}px;"></span>
                                                <span class="step-cost">${this_step.cost}&nbsp;ms</span>`;
                ele_result.appendChild(cloned_step);
            });
        }

        function recoverLogFromStorage () {
            ele_log.value = localStorage['perf-log'] || '';
        }

        function saveLog2Storage (log) {
            localStorage['perf-log'] = log || '';
        }

        if(!localStorage['perf-log']) {

            saveLog2Storage(`[logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}control-center	do-aaa	1542345746901
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}request-option-list	do-bbb	1542345787030
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}tab-page-creation	do-ccc	1542345746910
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}control-center	do-ddd	1542345737765
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}tab-page-creation	do-eee	1542345781387
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}request-option-list	do-fff	1542345778921
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}control-center	do-ggg	1542345705811
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}tab-page-creation	do-hhh	1542345759790
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}request-option-list	do-iii	1542345718622
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}control-center	do-jjj	1542345735503
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}tab-page-creation	do-kkk	1542345725999
                            [logger-sys][yyyy-MM-dd hh:mm:ss fff]	${salt}request-option-list	do-lll	1542345725669`);
        }

        recoverLogFromStorage();

    </script>
</body>

</html>
