<div class="apply-trade">

  <!-- user input area -->
  <apply-trade></apply-trade>

  <div class="apply-tab">
    <el-tabs v-model="activeQryTab" type="card">
      <el-tab-pane
              v-for="item in dataTabs"
              :key="item.name"
              :label="item.title"
              :name="item.name">
        <component :is="item.content"></component>
      </el-tab-pane>
    </el-tabs>
  </div>

</div>
