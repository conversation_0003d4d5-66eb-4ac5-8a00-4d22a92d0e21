
const loggerConsole = require('./logging').getConsoleLogger();
const helper = require('./helper').helper;

/**
 * N级订阅者树形关系
 */
class TreeMap {

    constructor() {
    
        this.loggerConsole = loggerConsole;
        // N层树形结构，每个末端叶子节点的值为true，代表存在的含义
        this.treeData = {};
    }

    register(node_value, ...chained_keys) {

        if(!this._areLevelKeysOk(chained_keys)) {
            this.loggerConsole.error(`lib[tree-map] > level keys are not ok<for register>: ${chained_keys}`);
            return;
        }

        helper.setValue(this.treeData, chained_keys, node_value);
    }

    unregister(...chained_keys) {

        if(!this._areLevelKeysOk(chained_keys)) {
            this.loggerConsole.error(`lib[tree-map] > level keys are not ok <for unregister>: ${chained_keys}`);
            return;
        }

        helper.deleteKey(this.treeData, chained_keys);
    }

    request(...chained_keys) {
        
        if(!this._areLevelKeysOk(chained_keys)) {
            this.loggerConsole.error(`lib[tree-map] > level keys are not ok <for request>: ${chained_keys}`);
            return;
        }
        return helper.readValue(this.treeData, chained_keys);
    }

    _areLevelKeysOk(...chained_keys) {

        if(chained_keys.length == 0) {
            return false;
        }

        for(let level of chained_keys) {
            if(typeof level == 'string' || typeof level == 'number' || typeof level == 'boolean') {
                return true;
            }
        }

        return false;
    }
}

module.exports = { TreeMap };