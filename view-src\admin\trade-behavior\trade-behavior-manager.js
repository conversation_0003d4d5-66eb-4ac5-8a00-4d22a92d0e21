const { IView } = require('../../../component/iview');
const { BrowserWindow } = require('@electron/remote');

class TradeBehaviorManager {

    /**
     * @param {IView} hostView 
     */
    constructor(hostView) {

        this.hostView = hostView;
        this.dialogWin = null;
    }

    /**
     * 参数分别为：
     * 1）identityType 主体类型，对应 identity type；
     * 2）identity  主体id
     * 3）name 主体名称
     */
    openSetting(args = { identityType, identity, name }) {
    
        if (this.dialogWin && !this.dialogWin.isDestroyed()) {

            this.dialogWin.show();
            this.dialogWin.webContents.send('set-context-data', args);
            return;
        }
    
        var window_options = { width: 555, height: 725, minimizable: false, maximizable: false, highlight: true };
        this.hostView.renderProcess.once(this.hostView.systemEvent.huntWinTabViewFromRender, (event, win_id) => {

            this.dialogWin = BrowserWindow.fromId(win_id);
            this.dialogWin.on('closed', () => { this.dialogWin = null; });
            setTimeout(() => { this.dialogWin.webContents.send('set-context-data', args); }, 1000);
        });
    
        this.hostView.renderProcess.send(this.hostView.systemEvent.huntWinTabViewFromRender, '@admin/trade-behavior/trade-behavior', null, window_options);
    }
}

module.exports = { TradeBehaviorManager };
