const { IView } = require('../../component/iview');
const drag = require('../../directives/drag');
const { repoRisk } = require('../../repository/risk');

class GlobalRiskControlView extends IView {

    constructor(view_name, is_standalone_window, title = '交易行为设置') {

        super(view_name, is_standalone_window, title);

        this.griskc = {

            /** 是否启用 */
            riskEnable: false,
            /** 订单自成交检查 */
            orderSelfTransaction: true,
            /** 沪深各市场流速（笔/秒） */
            flowSpeed: 0,
            /** 账户总委托数（笔） */
            totalEntrust: 0,
            /** 净买入金额（万） */
            netBuyAmount: 0,
            /** 单笔委托金额（万） */
            singleOrderAmount: 0,
            /** 单笔委托数量（手） */
            singleOrderVolume: 0,
            /** 比例规则启用阈值 */
            ratioEnableThreshold: 0,
            /** 撤单比 */
            cancelRatio: 0,
            /** 废单比 */
            invalidRatio: 0,
            /** 委托价格偏离度（价格笼子，仅股票有效） */
            cagePriceLimit: 2,
        };
    }

    resetMap(map) {

        for (let key in map) {
            let val = map[key];
            map[key] = typeof val == 'boolean' ? false : null;
        }
    }

    async requestSetting() {
        
        let loading = this.interaction.showLoading({ text: '正在获取设置...' });
        let resp = await repoRisk.getGlobalRiskControlSetting();
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            if (this.helper.isJson(data)) {
                
                let {
                    riskEnable,
                    orderSelfTransaction,
                    flowSpeed,
                    totalEntrust,
                    netBuyAmount,
                    singleOrderAmount,
                    singleOrderVolume,
                    ratioEnableThreshold,
                    cancelRatio,
                    invalidRatio,
                    cagePriceLimit,

                } = data;

                let latest = { 

                    riskEnable: !!riskEnable,
                    orderSelfTransaction,
                    flowSpeed,
                    totalEntrust,
                    netBuyAmount,
                    singleOrderAmount,
                    singleOrderVolume,
                    ratioEnableThreshold,
                    cancelRatio,
                    invalidRatio,
                    cagePriceLimit,
                };
                
                Object.assign(this.griskc, latest);
            }
            else {
                this.resetMap(this.griskc);
            }
        }
        else {
            this.interaction.showError(`查询错误：${errorCode}/${errorMsg}`);
        }

        loading.close();
    }

    async save() {

        let resp = await repoRisk.saveGlobalRiskControlSetting(this.griskc);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {
            this.interaction.showSuccess(`已保存`);
        }
        else {
            this.interaction.showError(`保存错误：${errorCode}/${errorMsg}`);
        }

        this.requestSetting();
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.querySelector('.global-risk-ctr-view'),
            directives: { drag },
            data: {
                griskc: this.griskc,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.save,
            ]),
        });
    }

    build($container) {

        this.$container = $container;
        this.createApp();
        this.requestSetting();
    }
}

module.exports = GlobalRiskControlView;
