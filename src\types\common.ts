import { TickType } from '../config/trading';

/**
 * Any object
 */
export interface AnyObject {
  [key: string]: any;
}

/** 分页结构2 */
export interface AlterPagedResult<T> {
  list: T[];
  pageNo: number;
  pageSize: number;
  totalPages: number;
  totalSize: number;
}

/**
 * HTTP响应信息
 */
export interface HttpResponseData<T = any> {
  /** 错误代码 */
  errorCode: number;
  /** 错误信息 */
  errorMsg?: string;
  /** 返回业务数据 */
  data?: T;
}

export interface NavSerie {
  nav: number;
  sumNav: number;
  sumProfit: number;
  tradingDay: string;
}

/**
 * 合约数据结构
 */
export interface InstrumentInfo {
  /** 资产类型 */
  assetType: number;
  /** 是否为融资融券标的（0和1） */
  creditBuy: number;
  /** 交易所ID */
  exchangeId: string;
  /** 到期日期 */
  expireDate: string;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 是否为抵押品标志（0和1） */
  isCollateral: number;
  /** 最新价格 */
  lastPrice: number;
  /** 涨停价 */
  upperLimitPrice: number;
  /** 跌停价 */
  lowerLimitPrice: number;
  /** 期权类型 */
  optionType: number;
  /** 昨收盘价 */
  preClosePrice: number;
  /** 价格最小变动单位 */
  priceTick: number;
  /** 行权价 */
  strikePrice: number;
  /** 潜在指向合约代码 */
  underlyingInstrument: string;
  /** 成交量乘数 */
  volumeMultiple: number;
}

/**
 * 简单TICK行情数据结构
 */
export interface SimpleTick {
  /** 合约代码 */
  instrument: string;
  /** 当前最新价格 */
  lastPrice: number;
}

/**
 * 标准TICK行情数据结构
 */
export interface StandardTick {
  /**
   * 卖1 ~ 卖10 的价格数组（小到大）
   */
  askPrice: number[];

  /**
   * 卖1 ~ 卖10 的挂单量数组（小到大）
   */
  askVolume: number[];

  /**
   * 买1 ~ 买10 的价格数组（大到小）
   */
  bidPrice: number[];

  /**
   * 买1 ~ 买10 的挂单量数组（大到小）
   */
  bidVolume: number[];

  /**
   * 交易所名称
   */
  exchange: string;

  /**
   * 当日最高价
   */
  highPrice: number;

  /**
   * 合约代码
   */
  instrumentID: string;

  /**
   * 最新成交价
   */
  lastPrice: number;

  /**
   * 当日最低价
   */
  lowPrice: number;

  /**
   * 涨停价
   */
  upperLimitPrice: number;

  /**
   * 跌停价
   */
  lowerLimitPrice: number;

  /**
   * 开盘价
   */
  openPrice: number;

  /**
   * 当前持仓量
   */
  position: number;

  /**
   * 昨收盘价
   */
  preClosePrice: number;

  /**
   * 结算价
   */
  settlePrice: number;

  /**
   * 时间字符串（格式化后的时间）
   */
  strTime: string;

  /**
   * 成交额
   */
  turnover: number;

  /**
   * 数据更新时间戳或字符串
   */
  updateTime: string;

  /**
   * 成交量
   */
  volume: number;
}

/**
 * 成交队列TICK行情数据结构
 */
export interface TransactionTick {
  /** 交易所代码，例如 "SZSE" */
  exchange: string;
  /** 证券代码，例如 "SZSE.001979" */
  instrumentID: string;
  /** 更新时间戳（秒），例如 1558494678.58 */
  updateTime: number;
  /** 时间字符串，格式为 "YYYY-MM-DD HH:mm:ss" */
  strTime: string;
  /** 索引或序列号 */
  index: number;
  /** 价格 */
  price: number;
  /** 成交量（字符串类型） */
  volume: string;
  /** 成交金额 */
  turnover: number;
  /** 方向：1买，-1卖 */
  direction: number;
  /** 订单类别代码，例如 "0C" */
  orderKindFunctionCode: string;
  /** 卖方订单编号 */
  askOrder: number;
  /** 买方订单编号 */
  bidOrder: number;
}

/**
 * K线TICK数据结构
 */
export interface KLineTick {
  /** 交易所代码，例如 "SHSE" */
  exchange: string;
  /** 证券代码，例如 "SHSE.600282" */
  instrumentID: string;
  /** K线类型（单位：秒），例如 60 表示1分钟线 */
  barType: number;
  /** 时间字符串，格式为 "YYYY-MM-DD HH:mm:ss" */
  strTime: string;
  /** 更新时间戳（秒），例如 1557711000 */
  updateTime: number;
  /** 开盘价 */
  openPrice: number;
  /** 最高价 */
  highPrice: number;
  /** 最低价 */
  lowPrice: number;
  /** 收盘价 */
  closePrice: number;
  /** 成交量 */
  volume: number;
  /** 成交金额 */
  turnover: number;
  /** 前收盘价 */
  preClosePrice: number;
  /** 持仓量 */
  position: number;
  /** 复权因子 */
  adjFactor: number;
  /** 标志位 */
  flag: number;
}

/**
 * 订阅TICK行情类型与回调函数参数类型映射
 */
export interface TickType2CallbackParamMap {
  [TickType.simple]: SimpleTick;
  [TickType.tick]: StandardTick;
  [TickType.transaction]: TransactionTick;
  [TickType.kline]: KLineTick;
}

/**
 * Tick数据推送回调函数
 */
export type TickCallbackMethod<T extends keyof TickType2CallbackParamMap> = (
  tick: TickType2CallbackParamMap[T],
) => void;
