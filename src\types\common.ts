export interface AnyObject {
  [key: string]: any;
}

export interface MetaDataItem {
  label: string;
  value: number | string;
}

export interface TableColumnBasic {
  title: string;
  datakey: string;
}

export interface TableColumnConfigParamMeta {
  /** 表格名称 */
  name: string;
  /** 全量表格列 */
  columns?: TableColumnBasic[];
  /** 分组全量表格列 */
  groups?: { category: string; columns: TableColumnBasic[] }[];
  /** 勾选的列 */
  selected: string[];
}

export interface TableColumnConfigParam extends TableColumnConfigParamMeta {
  /** 勾选的列 */
  callback: (selected: string[]) => void;
}

export function getEmptyTableColumnConfig() {
  return {
    name: '',
    columns: [],
    groups: [],
    selected: [],
    callback: () => {},
  } as TableColumnConfigParam;
}

/**
 * 页面原子操作
 */
export interface AtomOperation {
  /** 记录ID */
  id: number;
  /** 适配用户类型 */
  userType: number;
  /** 权限名称 */
  permissionZhName: string;
  /** 权限映射 */
  permissionName: string;
  /** TCP功能码（如果为TCP通信） */
  functionCode: number | null;
  /** URL地址（如果为HTTP通信） */
  url: string | null;
  /** 通信方式（'GET' | 'POST' | 'PUT' | 'DELETE' | 'TCP'） */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'TCP';
  /** 该操作归属菜单ID */
  menuId: number;
  /** 权限默认为开启状态？？ */
  defaultPermission: 0 | 1;
  /** 创建时间戳 */
  createTime: number;
  /** 更新时间戳 */
  updateTime: number;
}

export interface DraftOperationInfo {
  name: string;
  key: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'TCP' | null;
  desc?: string;
  functionCode?: number;
  url?: string;
  type: string;
}

/**
 * 页面操作定义
 */
export interface MenuOperationDefinition {
  menuId: number;
  name: string;
  key: string;
  operations: DraftOperationInfo[];
}
