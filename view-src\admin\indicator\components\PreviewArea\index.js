const BaseComponent = require('../BaseComponent');
const draggable = require('vuedraggable');
const PreviewBoxCom = require('./PreviewBox/index');
const helper = require('../../../../../libs/helper').helper;

module.exports = class PreviewArea extends BaseComponent {
    constructor() {
        super(__dirname);
    }

    async setOptions() {
        let PreviewBox = await new PreviewBoxCom().build();
        this.options = {
            components: {
                draggable,
                PreviewBox,
            },
            props: {
                templateDetail: {
                    type: Object,
                },
                rawData: {
                    type: Array,
                },
                draggable: {},
                identityId: {},
                controller: {},
                destroy: {},
                diff: { default: 75 },
            },
            data() {
                return {
                    options: {
                        group: {
                            name: 'list',
                        },
                        handle: '.iconfont',
                        disabled: !this.draggable,
                    },
                    previewList: [],
                    splitStyle: {},
                    scrolling: false,
                    hoveringIndex: -1,
                    dragItem: {},
                    dragStyle: {},
                    fullStyle: {
                        width: '100%',
                        minWidth: '100%',
                        maxWidth: '100%',
                    },
                    attached: {
                        value: '3.1415926',
                        float: -1,
                        rawData: [],
                        yAxisName: '',
                        height: 400,
                        widget: 'chart',
                        type: 'line',
                        smooth: false,
                        data: [],
                        columns: [],
                        inverse: false,
                        percent: false,
                        stack: null,
                        style: {
                            width: '100%',
                            minWidth: '100%',
                            maxWidth: '100%',
                        },
                        wStyle: {},
                        position: {},
                        relatedIndex: 0,
                        current: false,
                    },
                };
            },
            computed: {
                designMode() {
                    return !this.identityId;
                },
                interval() {
                    return this.templateDetail.layout ? this.templateDetail.layout.interval : 0;
                },
                dragName() {
                    return this.dragItem.name;
                },
                currentItem() {
                    return this.previewList.find((item) => item.current);
                },
                currentIndex() {
                    return this.previewList.indexOf(this.currentItem);
                },
            },
            watch: {
                templateDetail() {
                    this.previewList = this.digestControls(this.templateDetail.controls);
                },
                previewList() {
                    this.setPosition();
                },
            },
            methods: {
                digestControls(data) {
                    if (Array.isArray(data)) {
                        return data.map((item) => {
                            let newItem = helper.deepClone(item);
                            Object.keys(this.attached).forEach((key) => {
                                if (newItem[key] === undefined) {
                                    newItem[key] = this.attached[key];
                                }
                            });
                            return newItem;
                        });
                    } else {
                        return [];
                    }
                },
                handleClick(e) {
                    if (!this.designMode) return;
                    const $current = e.currentTarget;
                    let index = this.getIndex($current);
                    this.updateCurrent(index);
                },
                handleDown(e) {
                    if (!this.designMode) return;
                    const index = this.getIndex(e.currentTarget);
                    let flag = true;

                    document.onmousemove = (e) => {
                        if (flag) {
                            this.dragItem = this.previewList[index];
                            flag = false;
                        }
                        this.dragStyle = {
                            top: `${e.clientY - 20}px`,
                            left: `${e.clientX - 60}px`,
                        };
                        this.shouldDelete(e.clientX, e.clientY);
                        this.renderHoverBox(e.clientX, e.clientY, index);
                    };

                    document.onmouseup = (e) => {
                        document.onmousemove = null;
                        document.onmouseup = null;
                        this.$nextTick(() => {
                            setTimeout(() => {
                                this.dropItem(index, e.clientX, e.clientY);
                            }, 0);
                        });
                    };
                },
                shouldDelete(mouseLeft, mouseTop) {
                    let dragWidget = this.$parent.$refs.dragWidget;
                    let configArea = this.$parent.$refs.configArea;
                    if (mouseLeft < 160) {
                        dragWidget.deleting = true;
                        configArea.deleting = false;
                    } else if (mouseLeft > 160 + this.$el.clientWidth) {
                        dragWidget.deleting = false;
                        configArea.deleting = true;
                    } else {
                        dragWidget.deleting = false;
                        configArea.deleting = false;
                    }
                },
                renderHoverBox(mouseLeft, mouseTop, index) {
                    let matched = this.previewList.find((box) => {
                        let { left, top, height, width } = box.position;
                        return mouseTop >= top && mouseTop <= top + height && mouseLeft >= left && mouseLeft <= left + width;
                    });
                    if (matched && this.previewList.indexOf(matched) !== index) {
                        this.hoveringIndex = this.previewList.indexOf(matched);
                        let { top, height, width, left } = matched.position;
                        this.splitStyle = {
                            width: `${width}px`,
                            height: `${height}px`,
                            top: `${top}px`,
                            left: `${left}px`,
                        };
                    } else {
                        this.hoveringIndex = -1;
                        this.splitStyle = {};
                    }
                },
                dropItem(oldIndex, mouseLeft, mouseTop) {
                    if (mouseLeft < 160 || mouseLeft > 160 + this.$el.clientWidth) {
                        let oldItem = this.previewList[oldIndex];
                        if (oldItem.relatedIndex !== 0) {
                            let relatedItem = this.previewList[oldIndex + oldItem.relatedIndex];
                            this.$set(this.previewList, oldIndex + oldItem.relatedIndex, {
                                ...relatedItem,
                                style: this.fullStyle,
                                relatedIndex: 0,
                            });
                        }
                        this.previewList.splice(oldIndex, 1);
                        let dragWidget = this.$parent.$refs.dragWidget;
                        let configArea = this.$parent.$refs.configArea;
                        dragWidget.deleting = false;
                        configArea.deleting = false;
                    }

                    const newIndex = this.hoveringIndex;
                    if (newIndex >= 0 && newIndex !== oldIndex) {
                        let list = this.previewList;
                        let oldItem = list[oldIndex];
                        let newItem = list[newIndex];
                        const oldObj = {
                            style: oldItem.style,
                            relatedIndex: oldItem.relatedIndex,
                        };
                        const newObj = {
                            style: newItem.style,
                            relatedIndex: newItem.relatedIndex,
                        };

                        oldItem = { ...oldItem, ...newObj };
                        newItem = { ...newItem, ...oldObj };
                        this.$set(this.previewList, oldIndex, newItem);
                        this.$set(this.previewList, newIndex, oldItem);
                    }
                    this.dragItem = {};
                    this.splitStyle = {};
                    this.hoveringIndex = -1;
                    this.updateCurrent(this.currentIndex);
                },
                getIndex(dom) {
                    const boxes = this.$refs.previewArea.querySelectorAll('.preview-box');
                    const boxList = Array.from(boxes);
                    return boxList.findIndex((item) => item == dom);
                },
                updateCurrent(itemIndex) {
                    this.previewList.forEach((item, index) => {
                        item.current = index == itemIndex;
                    });
                    this.$emit('update-current', this.currentItem, itemIndex);
                },
                getBoxClass(item) {
                    return this.designMode && item.current ? 'current' : '';
                },
                getBoxStyle(item) {
                    return item.style || this.fullStyle;
                },
                setPosition() {
                    this.$nextTick(() => {
                        setTimeout(() => {
                            let eles = this.$refs.previewArea.querySelector('.drag-area').querySelectorAll('.preview-box');
                            eles.forEach((ele, index) => {
                                let item = this.previewList[index];
                                if (item) {
                                    item.position = ele.getBoundingClientRect();
                                }
                            });
                            this.scrolling = false;
                        }, 200);
                    });
                },
                handleScroll(e) {
                    if (this.scrolling) return;
                    this.scrolling = true;
                    this.setPosition();
                },
                recoverBox(item, index) {
                    const handleIndex = item.relatedIndex > 0 ? index : index - 1;
                    let handleItem = this.previewList[handleIndex];
                    handleItem.style = this.fullStyle;
                    handleItem.relatedIndex = 0;
                },
            },
        };
    }
};
