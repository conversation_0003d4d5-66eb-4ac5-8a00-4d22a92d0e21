const { http } = require('../libs/http');

/**
 * 资产结构
 */
class AssetComposite {
    
    constructor({ buyMarketValue, futureAvailable, futureMarketValue, futureProfit, loanSellValue, sellMarketValue, stockAvailable, stockMarketValue, stockProfit, t0Profit }) {

        /** 多头市值 */
        this.buyMarketValue = buyMarketValue;
        /** 融券市值 */
        this.loanSellValue = loanSellValue;
        /** 空头市值 */
        this.sellMarketValue = sellMarketValue;
        /** T0盈亏 */
        this.t0Profit = t0Profit;

        /** 股票可用资金 */
        this.stockAvailable = stockAvailable;
        /** 股票市值 */
        this.stockMarketValue = stockMarketValue;
        /** 股票盈亏 */
        this.stockProfit = stockProfit;

        /** 期货可用资金 */
        this.futureAvailable = futureAvailable;
        /** 期货市值 */
        this.futureMarketValue = futureMarketValue;
        /** 期货盈亏 */
        this.futureProfit = futureProfit;
    }
}

/**
 * 资产结构
 */
class NavData {
    
    constructor({ fundId, fundName, navList }) {

        this.fundId = fundId;
        this.fundName = fundName;
        this.navList = Array.isArray(navList) ? navList : [];
    }
}

/**
 * 算法统计数据
 */
class AlgoStatisticsData {
    
    constructor({ buyBalance, finishRate, sellBalance, t0Exposure, totalBalance }) {

        this.buyBalance = buyBalance;
        this.finishRate = finishRate;
        this.sellBalance = sellBalance;
        this.t0Exposure = t0Exposure;
        this.totalBalance = totalBalance;
    }
}

class CockpitRepository {
    
    /**
     * 查询个人或机构名下，产品资产结构
     * @returns {{ errorCode, errorMsg, data: AssetComposite }}
     */
    qcomposition() {
        return new Promise((resolve, reject) => {
            http.get('/statistics/cockpit/asset').then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
    
    /**
     * 查询资产净值数据
     * @returns {{ errorCode, errorMsg, data: Array<NavData> }}
     */
    qnav() {
        return new Promise((resolve, reject) => {
            http.get('/statistics/cockpit/nav').then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
    
    /**
     * 查询算法统计数据
     * @returns {{ errorCode, errorMsg, data: AlgoStatisticsData }}
     */
    qalgo() {
        return new Promise((resolve, reject) => {
            http.get('/statistics/cockpit/algo').then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
}

module.exports = { 

    AssetComposite,
    NavData,
    AlgoStatisticsData,
    repoCockpit: new CockpitRepository(),
};
