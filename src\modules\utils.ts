import type { AnyObject } from '../types/common';

class Utils {

    static isNone(obj: any) {
        return obj === undefined || obj === null || (typeof obj == 'string' && obj.trim().length == 0);
    }

    static isNotNone(obj: any) {
        return !this.isNone(obj);
    }

    static isJson(obj: any) {
        
        let class2type = {};
        let toString = class2type.toString;
        let hasOwn = class2type.hasOwnProperty;
        let fnToString = hasOwn.toString;
        let ObjectFunctionString = fnToString.call(Object);

        if (!obj || toString.call(obj) !== '[object Object]') {
            return false;
        }

        let proto = Object.getPrototypeOf(obj);

        // Objects with no prototype (e.g., `Object.create( null )`) are plain
        if (!proto) {
            return true;
        }

        // Objects with prototype are plain if they were constructed by a global Object function
        let ctor = hasOwn.call(proto, 'constructor') && proto.constructor;
        return typeof ctor === 'function' && fnToString.call(ctor) === ObjectFunctionString;
    }

    static isNotJson(obj: any) {
        return !this.isJson(obj);
    }

    /**
     * to clone something deeply
     */
    static deepClone<T extends AnyObject>(obj: T): T {

        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        let result: any = typeof obj.splice === 'function' ? [] : {};
        let key;

        if (obj && typeof obj === 'object') {

            for (key in obj) {

                if (obj[key] && typeof obj[key] === 'object') {
                    result[key] = this.deepClone(obj[key]);
                } 
                else {
                    result[key] = obj[key];
                }
            }

            return result;
        }

        return obj;
    }

    /**
     * to assign fields from one object to another deeply
     */
    static deepAssign(target: any, source: any) {

        if (this.isNotJson(target) || this.isNotJson(source)) {
            return;
        }

        for (let key in source) {

            let val_source = source[key];
            let val_target = target[key];

            if (this.isJson(val_source)) {

                if (this.isJson(val_target)) {
                    this.deepAssign(val_target, val_source);
                } 
                else {
                    target[key] = this.deepClone(val_source);
                }
            } 
            else if (Array.isArray(val_source)) {

                if (val_target === undefined) {
                    target[key] = this.deepClone(val_source);
                }
            } 
            else {

                if (this.isJson(val_target) || Array.isArray(val_target)) {
                    //
                } 
                else {
                    target[key] = val_source;
                }
            }
        }

        return target;
    }

    /**
     * to compare two values
     * @param asc sorting, by default = ascending
     * @returns a > b = 1; a < b = -1; a = b = 0;
     */
    static compare(a: any, b: any, asc = true) {

        const Regex = {

            all_cn: /^[\u4E00-\u9FA5]+$/,
            has_cn: /[\u4E00-\u9FA5]+/,
            no_cn: /^[a-zA-Z0-9~!@#$%^&*()-_=+\[\]\\<>,;"'./\|]+$/,
            all_en: /^[a-zA-Z]+$/,
            all_digit: /^[0-9]+$/,
        };

        const bigger = 1, same = 0, smaller = -1;
        const flag = asc ? 1 : -1;
        let result = same;

        if (a === b) {
            return same;
        }

        let _1st = {

            isempty: a === undefined || a === null || (typeof a == 'string' && a.trim().length == 0),
            isnum: typeof a == 'number',
            isstr: typeof a == 'string',
        };

        let _2nd = {

            isempty: b === undefined || b === null || (typeof b == 'string' && b.trim().length == 0),
            isnum: typeof b == 'number',
            isstr: typeof b == 'string',
        };

        let tostr = (value: object) => {
            return value.toString();
        };

        let is_cn = (value: string) => {
            return Regex.all_cn.test(value);
        };

        if (_1st.isempty) {
            result = _2nd.isempty ? same : smaller;
        } 
        else if (_1st.isnum) {

            if (_2nd.isempty) {
                result = bigger;
            } 
            else if (_2nd.isnum) {
                result = a > b ? bigger : a < b ? smaller : same;
            } 
            else if (_2nd.isstr) {
                result = tostr(a).localeCompare(b);
            }
        } 
        else if (_1st.isstr) {

            if (_2nd.isempty) {
                result = bigger;
            } 
            else if (_2nd.isnum) {
                result = a.localeCompare(tostr(b));
            } 
            else if (_2nd.isstr) {

                let len_a = a.length;
                let len_b = b.length;
                let shorter = Math.min(len_a, len_b);

                for (let cur = 0; cur < shorter; cur++) {

                    let c1 = a[cur];
                    let c2 = b[cur];

                    if (c1 != c2) {

                        if (is_cn(c1)) {

                            if (is_cn(c2)) {
                                result = c1.localeCompare(c2, 'zh-CN');
                            } 
                            else {
                                result = bigger;
                            }
                        } 
                        else {

                            if (is_cn(c2)) {
                                result = smaller;
                            } 
                            else {
                                result = c1.localeCompare(c2);
                            }
                        }

                        break;
                    }
                }

                if (result == same) {
                    result = len_a > len_b ? bigger : len_a < len_b ? smaller : same;
                }
            }
        }

        return result * flag;
    }

    /**
     * to generate a random number
     */
    static random(lower: number, uppper: number, is_integer = true) {

        let rd = Math.random() * (uppper - lower + 1) + lower;
        return !!is_integer ? +rd.toFixed(0) : rd;
    }

    /**
     * to generate a random string
     */
    static randomString(length = 16) {

        let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let maxPos = chars.length;
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return result;
    }

    /**
     * to devide a number
     */
    static devideNumber(total: number, every: number) {

        if (total <= 0 || every <= 0) {
            return [total];
        }

        let results: number[] = [];
        let left = total;

        while (left >= every) {

            results.push(every);
            left -= every;
        }

        if (left > 0) {
            results.push(left);
        }

        return results;
    }

    /**
     * to extract distinct values from an array
     * @param arr the array of elements
     * @param predicate predictor function
     */
    static distinct<T, K = T>(arr: T[], predicate: (obj: T) => any): K[] {

        let is_func = typeof predicate == 'function';
        let distincts = [];

        for (let idx = 0; idx < arr.length; idx++) {

            let ele = arr[idx];
            if (distincts.indexOf(is_func ? predicate(ele) : ele) < 0) {
                distincts.push(is_func ? predicate(ele) : ele);
            }
        }

        return distincts;
    }

    /**
     * to unify values of an array
     * @param arr the array of elements
     * @param primaryKey predictor function
     */
    static unique<T, K = T>(arr: T[], primaryKey: (obj: T) => any): K[] {

        let is_func = typeof primaryKey == 'function';
        let uniques = [];
        let keysMap = {} as any;

        for (let idx = 0; idx < arr.length; idx++) {

            let ele = arr[idx] as any;
            let key = is_func ? primaryKey(ele) : JSON.stringify(ele);

            if (keysMap[key] === undefined) {

                keysMap[key] = true;
                uniques.push(ele);
            }
        }

        return uniques;
    }

    /**
     * to group members in an array
     */
    static groupBy<T, K>(arr: Array<K>, predicate: (obj: K) => T) {

        const map: any = {};

        if (Array.isArray(arr) && typeof predicate == 'function') {

            arr.forEach(ele => {

                let key = predicate(ele) as any;
                if (map[key] == undefined) {
                    map[key] = [];
                }
                map[key].push(ele);
            });
        }

        return Object.entries(map).map(x => ({ key: x[0] as T, members: x[1] as K[] }));
    }

    /**
     * to summarize an array and return the total value
     * @param digits the array of numbers
     */
    static sum(digits: Array<number>) {

        let total = 0;
        digits.forEach(x => (total += x));
        return total;
    }

    /**
     * to find the maximum value in a series of numbers, and return the value
     * @param digits the array of numbers
     */
    static max(digits: Array<number>) {

        let value = digits[0];
        digits.forEach(x => x > value && (value = x));
        return value;
    }

    /**
     * to find the maximum value in a series of numbers, and return the index
     * @param digits the array of numbers
     */
    static maxIndex(digits: Array<number>) {

        let value = digits[0];
        let index = 0;
        digits.forEach((x, _) => { if (x > value) { value = x; index = _; } });
        return index;
    }

    /**
     * to find the minimal value in a series of numbers, and return the value
     * @param digits the array of numbers
     */
    static min(digits: Array<number>) {

        let value = digits[0];
        digits.forEach(x => x < value && (value = x));
        return value;
    }

    /**
     * to find the minimal value in a series of numbers, and return the index
     * @param digits the array of numbers
     */
    static minIndex(digits: Array<number>) {

        let value = digits[0];
        let index = 0;
        digits.forEach((x, _) => { if (x < value) { value = x; index = _; } });
        return index;
    }

    /**
     * to calculate the average value of a series of numbers
     * @param digits the array of numbers
     */
    static avg(digits: Array<number>) {

        if (digits.length == 0) {
            return 0;
        }

        let sumed = this.sum(digits);
        return sumed / digits.length;
    }

    /**
     * to delete(remove) some elements from an array
     * @param arr the target array
     */
    static remove<T = any>(arr: Array<T>, predict: (ele: T) => boolean) {

        let indexes = [] as number[];
        arr.forEach((ele, ele_idx) => predict(ele) === true && indexes.push(ele_idx));
        while (indexes.length > 0) {
            arr.splice(indexes.pop()!, 1);
        }
    }

    /**
     * to cast a camel format into hyphen format
    */
    static camel2Hyphen(naming: string, options?: { upper?: boolean, lower?: boolean }) {

        if (typeof naming != 'string') {
            return naming;
        }

        let after = naming.replace(/([A-Z])/g, '-$1');
        if (options?.upper) {
            after = after.toUpperCase();
        }
        else if (options?.lower) {
            after = after.toLowerCase();
        }

        return after;
    }

    /**
     * to cast a snake format into camel format
    */
    static snakeToCamel(naming: string, options?: { firstUpper?: boolean }) {

        if (typeof naming != 'string') {
            return naming;
        }

        let after = naming.split('_').map(words => words.charAt(0).toUpperCase() + words.slice(1)).join('');
        if (options?.firstUpper !== true) {
            after = after[0].toLowerCase() + after.substring(1);
        }

        return after;
    }

    /**
     * to format date (can be Date object, UTC timestamp, date time string) into [yyyy-MM-dd] format
     * @param source input value to be formatted
     */
    static formatDate(source: Date | number | string) {
        return this.formatDateTime(source, 'yyyy-MM-dd');
    }

    /**
     * to format date (can be Date object, UTC timestamp, date time string) into [yyyyMMdd] format
     * @param source input value to be formatted
     */
    static formatDate8(source: Date | number | string) {
        return this.formatDateTime(source, 'yyyyMMdd');
    }

    /**
     * to format date time(can be Date object, UTC timestamp, date time string) with a given pattern
     * @param source input value to be formatted
     * @param pattern output pattern string(by default = 'yyyy-MM-dd hh:mm:ss')
     */
    static formatDateTime(source: Date | number | string, pattern?: string) {

        if (!source || source == '--') {
            return '--';
        }

        /**
         * [yyyyMMdd] formatter is commonly used across the web
        */
        if (typeof source == 'number' && source.toString().length == 8) {
            source = source.toString();
        }

        if (typeof source == 'string' && /^\d{8}$/.test(source)) {
            source = `${source.substring(0, 4)}/${source.substring(4, 6)}/${source.substring(6, 8)}`;
        }

        /**
        * the minimal length is [yyyyMMdd]/8
        */
        if (typeof source == 'string' && source.length < 8) {
            return source;
        }

        if (pattern == undefined || pattern == null) {
            pattern = 'yyyy-MM-dd hh:mm:ss';
        }

        let dt = source instanceof Date ? source : new Date(source);
        let o: any = {

            'M+': dt.getMonth() + 1,
            'd+': dt.getDate(),
            'h+': dt.getHours(),
            'm+': dt.getMinutes(),
            's+': dt.getSeconds(),
            'q+': Math.floor((dt.getMonth() + 3) / 3),
            'S': dt.getMilliseconds(),
        };

        if (/(y+)/.test(pattern)) {
            pattern = pattern.replace(RegExp.$1, (dt.getFullYear() + '').substr(4 - RegExp.$1.length));
        }

        for (let k in o) {

            if (new RegExp('(' + k + ')').test(pattern)) {
                pattern = pattern.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
            }
        }

        return pattern;
    }

    /**
     * to convert a matrix to a list of json objects
     */
    static convertMatrix2Json<T = any>(fields: Array<string>, matrix: Array<Array<any>>) {

        const rows: Array<T> = matrix.map(values => {

            const rowd: any = {};
            fields.forEach((column_name, col_idx) => {
                // write value to each field
                rowd[column_name] = values[col_idx];
            });

            return rowd;
        });

        return rows;
    }

    /**
     * to transpose a matrix
     */
    static transpose(matrix: Array<Array<any>>) {
        return matrix.length == 0 ? [] : matrix[0].map((_, col_idx) => matrix.map(row => row[col_idx]));
    }

    /**
     * to sleep a few moment
     */
    static async sleep(ms: number) {
        return new Promise(resolve => {
            setTimeout(function () {
                resolve(null);
            }, ms);
        });
    }

    /**
     * to execute a method intervally & safely
     */
    static safeSetInterval(interval: number, callback: () => void) {

        let isRunning = false; // 标记是否正在执行回调

        const intervalId = setInterval(() => {

            if (!isRunning) {

                isRunning = true;
                Promise.resolve(callback()).finally(() => { isRunning = false; });
            }
        }, interval);

        return intervalId;
    }
}

export default Utils;
