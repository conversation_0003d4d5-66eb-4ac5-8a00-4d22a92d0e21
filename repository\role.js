const http = require('../libs/http').http;

class RoleRepository {
    getAll() {
        return new Promise((resolve, reject) => {
            http.get('/role').then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    updateRole(role) {
        return new Promise((resolve, reject) => {
            http.put('/role', role).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    createRole(role) {
        return new Promise((resolve, reject) => {
            http.post('/role', role).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    removeRole(roleId) {
        return new Promise((resolve, reject) => {
            http.delete('/role', { params: { role_id: roleId } }).then(
                resp => {
                    resolve(resp.data);
                },
                err => {
                    reject(err);
                },
            );
        });
    }

    saveRoleRights(role_id, rights) {
        return new Promise((resolve, reject) => {
            http.put('/role/detail', rights, { params: { role_id: role_id } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getRoleRights(role_type) {
        return new Promise((resolve, reject) => {
            http.get('/role/detail', { params: { role_id: role_type } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getRoleRightsFullList() {
        return new Promise((resolve, reject) => {
            http.get('/permission').then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getAllMenus() {
        return new Promise((resolve, reject) => {
            http.get('/menu').then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    qYuLiangAccounts(menu_id) {
        return new Promise((resolve, reject) => {
            http.get('/menu/account', { params: { menu_id } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    qYuLiangPageUrl(menu_id, account_id) {
        return new Promise((resolve, reject) => {
            http.get('/menu/third', { params: { menu_id, account_id } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
}

module.exports = { repoRole: new RoleRepository() };
