const { systemEnum } = require('../../../config/system-enum');
const { AccountDetail } = require('../../../model/account');

class WeightedAccountDetail extends AccountDetail {

    constructor(struc) {

        super(struc);
        /** 权重系数 */
        this.multiple = 0;
        /** 按规则计算得出的可交易数量 */
        this.volume = 0;
        /** 合约可用多头仓位 */
        this.longPosition = 0;
        /** 合约可用空头仓位 */
        this.shortPosition = 0;
        /** 短合约代码 */
        this.shortInstrument = null;
        /** 合约代码 */
        this.instrument = null;
        /** 合约名称 */
        this.instrumentName = null;
        /** 合约乘数 */
        this.multiple = 1;
        /** （期货）多头保证金比例，按金额 */
        this.longMarginRatioByMoney = null;
        /** （期货）多头保证金比例，按数量（一般不会用到） */
        this.longMarginRatioByVolume = null;
        /** （期货）空头保证金比例，按金额 */
        this.shortMarginRatioByMoney = null;
        /** （期货）空头保证金比例，按数量（一般不会用到） */
        this.shortMarginRatioByVolume = null;
    }

    /**
     * @param {WeightedAccountDetail} account 
     */
    static MakeId(account) {
        return `${account.accountId}/${account.fundId}/${account.strategyId || 'strategy-is-none'}`;
    }
}

class GroupMember {

    constructor(struc) {

        /** 账号ID */
        this.accountId = struc.accountId;
        /** 账号名称 */
        this.accountName = struc.accountName;
        /** 账号所属机构ID */
        this.orgId = struc.orgId;
        /** 该账号在组内所占权重 */
        this.multiple = struc.multiple;

        /** 所属基金ID */
        this.fundId = struc.fundId;
        /** 所属基金名称 */
        this.fundName = struc.fundName;

        var strategyId = struc.strategyId;
        if (strategyId != null && strategyId != undefined) {

            /** 是否绑定了策略 */
            this.isStrategyOn = true;
            /** 绑定的策略ID */
            this.strategyId = strategyId;
            /** 绑定的策略名称 */
            this.strategyName = struc.strategyName;
        }
    }
}

class SimpleAccountItem {
    
    constructor({
        id,
        accountName,
        assetType,
        credit,
        funds,
        financeAccount,
    }) {
 
        var { fundId, fundName } = (funds || [])[0] || {};
        this.accountId = id;
        this.accountName = accountName;
        this.financeAccount = financeAccount;
        this.assetType = assetType;
        this.isCredit = !!credit;
        this.fundId = fundId;
        this.fundName = fundName;
    }
}

class AccountGroup {

    constructor(struc) {

        /**
         * @returns {Array<GroupMember>}
         */
        function AllocateGroupMembers() {
            return [];
        }

        this.groupId = struc.groupId;
        this.groupName = struc.groupName;
        this.createUserId = struc.createUserId;
        this.createTime = struc.createTime;
        this.members = AllocateGroupMembers();
    }

    /**
     * @param {Array} records 
     * @returns {Array<AccountGroup>}
     */
    static Convert(records) {

        /**
         * @param {*} groupId 
         * @returns {AccountGroup}
         */
        function seekGroup(groupId) {
            return dict[groupId];
        }

        let dict = {};
        let groups = [];
        records.forEach(item => {

            if (dict[item.groupId] === undefined) {
                groups.push(dict[item.groupId] = new AccountGroup(item));
            }

            let matched = seekGroup(item.groupId);
            matched.members.push(new GroupMember(item));
        });

        return groups;
    }

    /**
     * @param {*} struc 
     * @returns {GroupMember}
     */
    static ConvertMember(struc) {
        return new GroupMember(struc);
    }
}

class AccountPosition {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.avgPrice = struc.avgPrice;
        this.closeProfit = struc.closeProfit;
        this.direction = struc.direction;
        this.frozenTodayVolume = struc.frozenTodayVolume;
        this.frozenVolume = struc.frozenVolume;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.id = struc.id;
        this.identityId = struc.identityId;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.marketValue = struc.marketValue;
        this.positionCost = struc.positionCost;
        this.todayPosition = struc.todayPosition;
        this.tradingDay = struc.tradingDay;
        this.updateTime = struc.updateTime;
        this.usedCommission = struc.usedCommission;
        this.usedMargin = struc.usedMargin;
        this.yesterdayPosition = struc.yesterdayPosition;
    }

    /**
     * @param {Array} records 
     * @returns {Array<AccountPosition>}
     */
    static Convert(records) {
        
        return records instanceof Array ? records.map(item => new AccountPosition(item)) : 
        typeof records == 'object' && records != null ? [new AccountPosition] : [];
    }
}

class OrderPreview {

    constructor({
        id,
        accountId,
        accountName,
        fundId,
        fundName,
        strategyId,
        strategyName,
        instrument,
        instrumentName,
        multiple,
        assetType,
        direction,
        positionEffect,
        orderPrice,
        volumeOriginal,
        businessFlag,
        margin,
        available,
        balance,
        longMarginRatioByMoney,
        longMarginRatioByVolume,
        shortMarginRatioByMoney,
        shortMarginRatioByVolume,
        remark,

        adjustFlag,
        cancelledVolume,
        commission,
        coverFlag,
        createTime,
        errorCode,
        financeAccount,
        forceClose,
        foreign,
        frozenCommission,
        frozenMargin,
        frozenVolume,
        hedgeFlag,
        identityId,
        orderPriceType,
        orderStatus,
        receiveExchangeOrderIdTime,
        receiveOrderTime,
        sendTerminalTime,
        tradedAmount,
        tradedPrice,
        tradedVolume,
        tradingDay,
        updateTime,
        userId,
        userName,
    }) {

        if (typeof orderPrice != 'number' || orderPrice < 0) {
            orderPrice = 0;
        }

        if (typeof volumeOriginal != 'number') {
            volumeOriginal = 0;
        }
        
        this.id = id;
        this.accountId = accountId;
        this.accountName = accountName;
        this.fundId = fundId;
        this.fundName = fundName;
        this.strategyId = strategyId;
        this.strategyName = strategyName;

        this.shortInstrument = typeof instrument != 'string' || instrument.indexOf('.') < 0 ? instrument : instrument.split('.')[1];
        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.assetType = assetType;
        this.direction = direction;
        this.positionEffect = positionEffect;
        this.businessFlag = businessFlag;

        this.margin = margin || 0;
        this.available = available;
        this.balance = balance;
        this.orderPrice = orderPrice;

        this.longMarginRatioByMoney = longMarginRatioByMoney;
        this.longMarginRatioByVolume = longMarginRatioByVolume;
        this.shortMarginRatioByMoney = shortMarginRatioByMoney;
        this.shortMarginRatioByVolume = shortMarginRatioByVolume;

        var is_stock = assetType == systemEnum.assetsType.stock.code;
        var one_hand_margin = 0;
        var total_margin = 0;
        var max_can_open = null;

        if (!is_stock
            && typeof multiple == 'number' 
            && typeof longMarginRatioByMoney == 'number' 
            && typeof longMarginRatioByVolume == 'number' 
            && typeof shortMarginRatioByMoney == 'number' 
            && typeof shortMarginRatioByVolume == 'number') {

            /**
             * 经xsq确认，单手保证金为by money，by volume从未见过有值
             */
            let long_margin_ratio = Math.max(longMarginRatioByMoney, longMarginRatioByVolume);
            let short_margin_ratio = Math.max(shortMarginRatioByMoney, shortMarginRatioByVolume);
            one_hand_margin = orderPrice * multiple * (direction > 0 ? long_margin_ratio : short_margin_ratio);
        }

        if (orderPrice > 0) {

            if (is_stock) {
                max_can_open = available >= 0 ? Math.floor(available / orderPrice / 100) * 100 : null;
            }
            else {
                
                /** 耗用保证金 */
                total_margin = one_hand_margin * volumeOriginal;
                /** 最大可开手数 */
                max_can_open = one_hand_margin > 0 && available >= 0 ? Math.floor(available / one_hand_margin) : null;
            }
        }

        /** 原始委托数量 */
        this.volumeOriginal = typeof max_can_open == 'number' && max_can_open >= 0 ? Math.min(max_can_open, volumeOriginal) : volumeOriginal;
        /** 股票资产交易金额 */
        this.amount = orderPrice * this.volumeOriginal;

        /** 每手占用保证金 */
        this.oneHandMargin = one_hand_margin;
        /** 总占用保证金 */
        this.totalMargin = total_margin;
        /** 最大可开数量 */
        this.maxCanOpen = max_can_open;

        this.remark = {

            /** 备注文字 */
            content: null,
            /** 备注class name */
            clsname: null,
        };

        if (remark && typeof remark == 'object') {

            this.remark.content = remark.content;
            this.remark.clsname = remark.clsname;
        }

        this.adjustFlag = adjustFlag;
        this.cancelledVolume = cancelledVolume;
        this.commission = commission;
        this.coverFlag = coverFlag;
        this.createTime = createTime;
        this.errorCode = errorCode;
        this.financeAccount = financeAccount;
        this.forceClose = forceClose;
        this.foreign = foreign;
        this.frozenCommission = frozenCommission;
        this.frozenMargin = frozenMargin;
        this.frozenVolume = frozenVolume;
        this.hedgeFlag = hedgeFlag;
        this.identityId = identityId;
        this.orderPriceType = orderPriceType;
        this.orderStatus = orderStatus;
        this.receiveExchangeOrderIdTime = receiveExchangeOrderIdTime;
        this.receiveOrderTime = receiveOrderTime;
        this.sendTerminalTime = sendTerminalTime;
        this.tradedAmount = tradedAmount;
        this.tradedPrice = tradedPrice;
        this.tradedVolume = tradedVolume;
        this.tradingDay = tradingDay;
        this.updateTime = updateTime;
        this.userId = userId;
        this.userName = userName;
    }
}

class BasketOrderPreview extends OrderPreview {

    constructor(struc, latestPrice) {

        super(struc);
        
        this.afterAction = struc.afterAction;
        this.algoParam = struc.algoParam;
        this.algorithmClass = struc.algorithmClass;
        this.algorithmMappingId = struc.algorithmMappingId;
        this.algorithmStatus = struc.algorithmStatus;
        this.algorithmType = struc.algorithmType;
        this.buyVolume = struc.buyVolume;
        this.effectiveTime = struc.effectiveTime;
        this.expireTime = struc.expireTime;
        this.exposure = struc.exposure;
        this.limitAction = struc.limitAction;
        this.sellVolume = struc.sellVolume;
        this.t0Profit = struc.t0Profit;
        this.volume = struc.volume;

        this.latestPrice = latestPrice;
        this.estimatedAmount = this.volumeOriginal * this.latestPrice;
    }
}

class CreditOperation {

    /**
     * @param {Number|String} code 
     * @param {Number|String} business 
     * @param {Number|String} direction
     * @param {String} mean
     * @param {String} buttonType
     */
    constructor(code, business, direction, mean, buttonType) {

        this.code = code;
        this.business = business;
        this.direction = direction;
        this.mean = mean;
        this.buttonType = buttonType;
    }
}

class Basket {

    /**
     * @param {*} struc 
     * @param {Boolean} isEtf
     */
    constructor(struc, isEtf) {

        this.isEtf = isEtf;
        /** ETF篮子代码 */
        this.etfCode = struc.code;
        this.basketId = struc.basketId;
        this.basketName = struc.basketName;
        
        if (this.basketName === undefined || this.basketName === null) {
            this.basketName = this.basketId;
        }

        this.totalWeight = struc.totalWeight = 0;
        /** 该成员暂不使用，避免VUE挂载时，进行深度数据绑定 */
        this.members = (/** @returns {Array<BasketItem>} */ function() { return []; })();
        this.createUser = struc.createUser;
        this.isMemberLoaded = false;
    }
}

class BasketItem {

    constructor(recordId, struc) {

        this.recordId = recordId;
        this.basketId = struc.basketId;
        this.basketName = struc.basketName;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.amount = struc.amount;
        this.weight = struc.weight;
        this.createUser = struc.createUser;
    }

    /**
     * @param {BasketItem} sample 
     */
    static Clone(sample) {
        
        var cloned = new BasketItem(-1, {});
        for (let key in sample) {
            cloned[key] = sample[key];
        }
        return cloned;
    }
}

module.exports = {

    WeightedAccountDetail,
    SimpleAccountItem,
    AccountGroup,
    AccountPosition,
    OrderPreview,
    BasketOrderPreview,
    CreditOperation,
    Basket,
    BasketItem,
};