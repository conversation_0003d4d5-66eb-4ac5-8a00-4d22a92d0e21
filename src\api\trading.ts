import { Repos, type RegularOrder } from '../../../xtrade-sdk';
import type { ServerMessageCallbackMethod } from '../../../xtrade-sdk';

const tradingRepo = new Repos.TradingRepo();

class TradingService {
  /**
   * 撤销订单
   */
  static cancelOrder(orderId: number | string) {
    tradingRepo.CancelOrder(orderId);
  }

  /**
   * 普通下单
   */
  static sendOrder(order: RegularOrder) {
    tradingRepo.SendOrder(order);
  }

  /**
   * 订阅订单变化
   */
  static subscribeOrderChange(callback: ServerMessageCallbackMethod) {
    tradingRepo.SubscribeOrderChange(callback);
  }

  /**
   * 取消订阅订单变化
   */
  static unsubscribeOrderChange(callback: ServerMessageCallbackMethod) {
    tradingRepo.UnsubscribeOrderChange(callback);
  }

  /**
   * 订阅成交变化
   */
  static subscribeTradeChange(callback: ServerMessageCallbackMethod) {
    tradingRepo.SubscribeTradeChange(callback);
  }

  /**
   * 取消订阅成交变化
   */
  static unsubscribeTradeChange(callback: ServerMessageCallbackMethod) {
    tradingRepo.UnsubscribeTradeChange(callback);
  }

  /**
   * 订阅持仓变化
   */
  static subscribePositionChange(callback: ServerMessageCallbackMethod) {
    tradingRepo.SubscribePositionChange(callback);
  }

  /**
   * 取消订阅持仓变化
   */
  static unsubscribePositionChange(callback: ServerMessageCallbackMethod) {
    tradingRepo.UnsubscribePositionChange(callback);
  }
}

export default TradingService;
