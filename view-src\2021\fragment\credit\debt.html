<div class="typical-data-view">

	<div class="user-toolbar themed-box">
		<!---->
	</div>

	<div class="data-list">
		<table>
			<tr>
				<th label="ID" min-width="80" prop="id" overflowt sortable searchable></th>
				<th label="账号ID" min-width="80" prop="accountId" overflowt sortable searchable></th>
				<th label="账号名称" min-width="202.0" prop="accountName" formatter="formatAccountName" overflowt sortable searchable></th>
				<th label="代码" fixed-width="100" prop="instrument" overflowt sortable searchable></th>
				<th label="名称" fixed-width="80" prop="instrumentName" overflowt sortable searchable></th>
				<!-- <th label="交易日" fixed-width="80" prop="tradingDay"  sortable></th> -->
				<th type="program" label="类型" fixed-width="90" prop="targetType" formatter="formatTargetType" sortable overflowt></th>
				<th label="比率" fixed-width="80" prop="ratio" align="right" summarizable thousands></th>
				<th label="状态" fixed-width="60" prop="status" align="right" formatter="formatStatus"></th>
				<th label="总额度" fixed-width="80" prop="totalQuote" align="right" summarizable thousands></th>
				<th label="可用" fixed-width="60" prop="enableAvailable" align="right"></th>
				<!-- <th type="program" label="修改时间" fixed-width="100" prop="updateTime" 
					formatter="formatDate" sortable></th> -->
			</tr>
		</table>
	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>
