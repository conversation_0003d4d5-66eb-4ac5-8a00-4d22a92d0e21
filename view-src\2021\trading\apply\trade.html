<div class="apply-user-input">
    <div class="apply-input-container">
        <div class="apply-trade-container">
            <div class="apply-trade-header">
                <span>新股申购</span>
            </div>
            <div class="apply-input">
                <div class="apply-input-item">
                    <div class="apply-input-item-label">账号</div>
                    <div class="apply-input-item-content">
                        <el-select v-model="applyInfo.accountId" @change="handleUserInput" filterable clearable>
                            <el-option v-for="(item, item_idx) in accounts" :key="item_idx" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">申购代码</div>
                    <div class="apply-input-item-content">
                        <el-select v-model="applyInfo.purchaseCode" @change="handleUserInput" filterable clearable>
                            <el-option v-for="(item, item_idx) in purchaseCodes" :key="item_idx" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">申购数量</div>
                    <div class="apply-input-item-content">
                        <el-input-number size="mini" v-model="applyInfo.purchaseAmount" :step="applyInfo.orderStep" :max="applyInfo.orderMax" :min="applyInfo.orderMin"></el-input-number>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">新股名称</div>
                    <div class="apply-input-item-content">
                        <span>{{applyInfo.purchaseName}}</span>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">发行价格</div>
                    <div class="apply-input-item-content">
                        <span>{{applyInfo.purchasePrice}}</span>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">发行数量</div>
                    <div class="apply-input-item-content">
                        <span>{{applyInfo.issueAmount}}</span>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">最大申购数</div>
                    <div class="apply-input-item-content">
                        <span>{{applyInfo.maxPurchase}}</span>
                    </div>
                </div>

                <div class="apply-input-item">
                    <div class="apply-input-item-label">最小申购数</div>
                    <div class="apply-input-item-content">
                        <span>{{applyInfo.minPurchase}}</span>
                    </div>
                </div>

                <div class="apply-input-item">
                    <el-button type="primary" @click="singleAccountApply" class="s-w-120">申购</el-button>
                </div>
            </div>
        </div>
    </div>

    <div class="apply-account-container">
        <div class="apply-account-header">
            <div class="s-w-150 s-mg-5">
                <el-input placeholder="关键字过滤" v-model="filterName" prefix-icon="el-icon-search" @input="searchAccountIpo"></el-input>
            </div>
            <div>
                <el-button type="primary" class="s-w-100 s-mg-5" @click="multiAccountApply">一键申购</el-button>
            </div>
        </div>
        <vxe-table
            border
            stripe
            highlight-hover-row
            highlight-current-row
            show-overflow
            resizable
            :auto-resize="true"
            height="323px"
            :loading="loading"
            :align="'center'"
            ref="accountIpoTable"
            @checkbox-all="selectAll"
            @checkbox-change="selectChange"
            :data="filterIpoAccounts"
            size="mini"
        >
            <vxe-table-column type="checkbox" class-name="new-share-select" width="80" fixed="left"></vxe-table-column>

            <vxe-table-column field="accountName" title="账号名称" show-overflow :filters="accountNameFilters" :filter-method="filterAccountName" sortable min-width="130" fixed="left">
            </vxe-table-column>
            <vxe-table-column field="financeAccount" title="资金账号" show-overflow sortable min-width="120"></vxe-table-column>
            <vxe-table-column field="purchaseName" title="证券名称" :filters="purchaseNameFilters" :filter-method="filterPurchaseName" sortable min-width="120"></vxe-table-column>
            <vxe-table-column field="purchaseCode" title="证券代码" sortable min-width="120"></vxe-table-column>
            <vxe-table-column field="purchasePrice" title="发行价" type="number" sort-type="number" min-width="80"></vxe-table-column>
            <vxe-table-column field="issueAmount" title="发行数量" type="number" sort-type="number" min-width="90"></vxe-table-column>
            <vxe-table-column field="minPurchase" title="最小申购数量" type="number" sort-type="number" min-width="60"></vxe-table-column>
            <vxe-table-column field="maxPurchase" title="最大申购数量" type="number" sort-type="number" min-width="60"></vxe-table-column>
            <vxe-table-column field="purchaseAmount" title="申购数量" type="number" sort-type="number" min-width="60"></vxe-table-column>
            <template #empty>
                <span>
                    <span>今天没有股票可以申购!</span>
                </span>
            </template>
        </vxe-table>
    </div>

    <el-dialog title="新股申购" :visible.sync="dialogTableVisible">
        <vxe-table
            stripe
            highlight-hover-row
            highlight-current-row
            show-overflow
            :align="'center'"
            :auto-resize="true"
            ref="orderPreviewTable"
            :data="selectApplyAccounts"
            max-height="400"
            @checkbox-all="selectAll"
            @checkbox-change="selectChange"
            size="mini"
        >
            <vxe-table-column field="financeAccount" title="资金账号" show-overflow></vxe-table-column>
            <vxe-table-column field="accountName" title="账号名称" show-overflow></vxe-table-column>
            <vxe-table-column field="purchaseCode" title="证券代码"></vxe-table-column>
            <vxe-table-column field="purchaseName" title="证券名称"></vxe-table-column>
            <vxe-table-column field="purchaseAmount" title="申购数量" type="number"></vxe-table-column>
        </vxe-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible=false" size="mini">取 消</el-button>
            <el-button type="primary" @click="applyRequest" size="mini">确 定</el-button>
        </div>
    </el-dialog>
</div>
