<script setup lang="ts">
import { ref, watch, onMounted, useTemplateRef, inject, shallowRef } from 'vue';
import ConvertPinyin from '@/libs/pinyin-converter';
import type { InstrumentInfo } from '@/types';
import { INSTRUMENT_SELECT_KEY } from '@/keys';
import { AssetTypeEnum } from '@/enum';
import { MarketService } from '@/api';

const instrumentSelect = inject(INSTRUMENT_SELECT_KEY, ref(''));

// 定义搜索结果项的类型
interface SearchResultItem {
  value: string;
  item: InstrumentInfo;
}

const { placeholder, assetType = AssetTypeEnum.股票 } = defineProps<{
  placeholder?: string;
  /** 资产类型 */
  assetType?: number;
}>();

// 使用defineModel实现双向绑定
const instrument = defineModel<InstrumentInfo | undefined>();

// 输入框的值
const inputValue = ref('');

// 搜索结果
const searchResults = ref<InstrumentInfo[]>([]);

// 创建一个ref引用el-autocomplete组件
const autocompleteRef = useTemplateRef('autocompleteRef');

const instruments = shallowRef<InstrumentInfo[]>([]);

// 监听modelValue变化
watch(
  () => instrument.value,
  newVal => {
    if (newVal) {
      inputValue.value = `${newVal.instrument} ${newVal.instrumentName}`;
    } else {
      inputValue.value = '';
    }
  },
  { immediate: true },
);

// 监听instrumentSelect变化
watch(instrumentSelect, newVal => {
  if (newVal) {
    select(newVal);
  }
});

// 初始化
onMounted(() => {
  // 如果有初始值，设置输入框的值
  if (instrument.value) {
    inputValue.value = `${instrument.value.instrument} ${instrument.value.instrumentName}`;
  }
  getInstruments();
});

const getInstruments = async () => {
  instruments.value = await MarketService.downloadInstruments(assetType, true);
};

// 搜索方法
const handleSearch = (query: string, cb: (data: SearchResultItem[]) => void) => {
  if (query) {
    // 过滤合约代码或名称包含查询字符串的合约
    const results = instruments.value.filter((item: InstrumentInfo) => {
      const lowerQuery = query.toLowerCase();
      // 检查合约代码是否包含查询字符串
      if (item.instrument.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 检查合约名称是否包含查询字符串
      if (item.instrumentName.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 获取合约名称的拼音
      const pinyin = ConvertPinyin(item.instrumentName, false);
      // 检查拼音是否包含查询字符串
      if (pinyin.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // 获取合约名称的拼音首字母
      const pinyinInitials = ConvertPinyin(item.instrumentName, true);
      // 检查拼音首字母是否包含查询字符串
      if (pinyinInitials.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      return false;
    });

    // 转换为el-autocomplete需要的格式
    const formattedResults = results.map((item: InstrumentInfo) => ({
      value: `${item.instrument} ${item.instrumentName}`,
      item,
    }));

    // 回调返回结果
    cb(formattedResults);

    // 保存搜索结果
    searchResults.value = results;

    // 如果只有一个匹配结果，自动选择并关闭下拉框
    if (results.length === 1) {
      setTimeout(() => {
        instrument.value = results[0];
        // 关闭下拉框
        if (autocompleteRef.value) {
          autocompleteRef.value.blur();
        }
      }, 0);
    }
  } else {
    cb([]);
    searchResults.value = [];
  }
};

// 选择合约
const handleSelect = (item: SearchResultItem) => {
  if (item && item.item) {
    instrument.value = item.item;
  }
};

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  // 如果按下Backspace键，清空输入并设置instrument为undefined
  if (event.key === 'Backspace' && inputValue.value) {
    instrument.value = undefined;
    inputValue.value = '';
    event.preventDefault(); // 阻止默认行为
  }
};

// 清空选择
const handleClear = () => {
  instrument.value = undefined;
  instrumentSelect.value = '';
};

/** 外部调用方法，用于触发选中指定instrument */
const select = (code: string) => {
  const result = instruments.value.find(x => x.instrument === code);
  if (result) {
    instrument.value = result;
  } else {
    console.warn('合约不存在：', code);
  }
};

defineExpose({
  select,
});
</script>

<template>
  <div class="instrument-input" w-full>
    <el-autocomplete
      ref="autocompleteRef"
      v-model="inputValue"
      :fetch-suggestions="handleSearch"
      :placeholder="placeholder || '请输入合约代码或名称'"
      :trigger-on-focus="false"
      clearable
      @select="(item: any) => handleSelect(item)"
      @clear="handleClear"
      @keydown="handleKeydown"
      w-full
    >
      <template #default="{ item }">
        <div flex justify-between>
          <span mr-15>{{ item.item.instrument }}</span>
          <span>{{ item.item.instrumentName }}</span>
        </div>
      </template>
    </el-autocomplete>
  </div>
</template>

<style scoped>
.instrument-input {
  width: 100%;
}
</style>
