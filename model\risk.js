/**
 * 风控分组内含条目（可能为产品、策略、账号）
 */
class RiskGroupItem {
    
    constructor({
        id,
        orgId,
        groupId,
        groupName,
        identity,
        identityName,
        /** systemEnum.identityType */
        identityType,
        createTime,
        updateTime,
    }) {
        this.id = id;
        this.orgId = orgId;
        this.groupId = groupId;
        this.groupName = groupName;
        /** 占位字段，不使用 */
        this.fundId = null;
        /** 占位字段，不使用 */
        this.fundName = null;
        this.identity = identity;
        this.identityName = identityName;
        this.identityType = identityType;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
}

/**
 * 风控分组
 * 组合可包含：产品、策略、账号
 */
class RiskGroup {
    
    constructor({
        id, 
        orgId, 
        groupName, 
        remark, 
        createTime, 
        updateTime,
        receivers = [{ receiverId: null, receiverName: null }].splice(1),
        setting = { enableAlgoTrade: 1, enableCommonTrade: 0 },
    }) {

        this.id = id;
        this.orgId = orgId;
        this.groupName = groupName;
        this.remark = remark;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.receivers = Array.isArray(receivers) ? receivers : [];

        let { enableAlgoTrade, enableCommonTrade } = setting || {};
        this.setting = { enableAlgoTrade, enableCommonTrade };
        this.items = [new RiskGroupItem({})].splice(1);
    }
}

class WarningRisk {

    constructor({
        id,
        configurationId,
        warningType,
        minWarningValue,
        maxWarningValue,
        minRelative,
        maxRelative,
        minNetValue,
        maxNetValue,
        minNetRelative,
        maxNetRelative,
    }) {

        this.id = id;
        this.configurationId = configurationId;
        this.warningType = warningType;
        this.minWarningValue = minWarningValue;
        this.maxWarningValue = maxWarningValue;
        this.minRelative = minRelative;
        this.maxRelative = maxRelative;
        this.minNetValue = minNetValue;
        this.maxNetValue = maxNetValue;
        this.minNetRelative = minNetRelative;
        this.maxNetRelative = maxNetRelative;
    }
}

class NormalRisk {

    constructor({ 
        id, 
        configurationId, 
        riskKindId, 
        classificationId, 
        rangeCode, 
        byInstrument, 
        weight,
    }) {

        this.id = id;
        this.configurationId = configurationId;
        this.riskKindId = riskKindId;
        this.classificationId = classificationId;
        this.rangeCode = rangeCode;
        this.byInstrument = byInstrument;
        this.weight = weight;
    }
}

/**
 * 风控配置结构
 */
class RiskConfiguration {
    
    constructor({
        id,
        createUser,
        identity,
        identityType,
        configurationName,
        beginTime,
        endTime,
        expression,
        beforeCheck,
        afterCheck,
        checkInterval,
        sendMail,
        sendMessage,
        msgReceiverId,
        msgReceiverName,
        mailReceiverId,
        mailReceiverName,
        isActive,
        templateId,
        templateName,
        orgId,

        riskWarnings = [new WarningRisk({})].splice(1),
        riskNorms = [new NormalRisk({})].splice(1),
    }) {

        this.id = id;
        this.createUser = createUser;
        this.identity = identity;
        this.identityType = identityType;
        this.configurationName = configurationName;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.expression = expression;
        this.beforeCheck = beforeCheck;
        this.afterCheck = afterCheck;
        this.checkInterval = checkInterval;
        this.sendMail = sendMail;
        this.sendMessage = sendMessage;
        this.msgReceiverId = msgReceiverId;
        this.msgReceiverName = msgReceiverName;
        this.mailReceiverId = mailReceiverId;
        this.mailReceiverName = mailReceiverName;
        this.isActive = isActive;
        this.templateId = templateId;
        this.templateName = templateName;
        this.orgId = orgId;

        this.riskWarnings = Array.isArray(riskWarnings) ? riskWarnings : [];
        this.riskNorms = Array.isArray(riskNorms) ? riskNorms : [];
    }
}

module.exports = { 

    RiskGroup,
    RiskGroupItem,
    NormalRisk,
    WarningRisk,
    RiskConfiguration,
};