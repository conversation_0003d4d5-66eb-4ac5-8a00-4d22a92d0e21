const httpRequest = require('../libs/http').http;
class maintainRepository {

    constructor(){

    }

    getService() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/cache').then(res => {
                resolve(res.data);
            }, err => {
                reject(err);
            });
        });
    }

    getCache({ serviceName, methodName, page, count, args }) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/cache', args, {
                params: {
                    service_name: serviceName,
                    method_name: methodName,
                    page: page,
                    count: count
                }
            }).then(res => {
                resolve(res.data);
            }, err => {
                reject(err);
            });
        });
    }


    getRecover(serviceName) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/cache/recover', {}, {
                params: {
                    service_name: serviceName
                }
            }).then(res => {
                resolve(res.data);
            }, err => {
                reject(err);
            });
        });
    }

    getTransactionData(orgId, monitorId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/organization/monitor', {
                params: {
                    org_id: orgId,
                    monitor_id: monitorId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

}

module.exports = { repoMaintain: new maintainRepository() };