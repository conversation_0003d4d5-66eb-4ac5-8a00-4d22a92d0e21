<div class="levels s-full-height">

	<div class="levels-internal themed-box s-full-height">
		
		<div class="level1-frame xtcontainer s-border-box s-full-height">

			<template>

				<div class="xtheader themed-header">

					<span>
						<template v-if="!!states.instrument">
							{{ formatViewTitle() }}
						</template>
						<template v-else>{{ channel.mean }}</template>
					</span>

					<el-popover placement="bottom"
								title="点击价格联动设置"
								width="130"
								trigger="click">

						<a slot="reference" class="s-pull-right themed-color themed-hover-color" title="设置">
							<i class="iconfont icon-shezhi11"></i>
						</a>

						<div class="xtpop-body">
							<div class="switch-item">
								<span>带入方向</span>
								<el-switch v-model="uistates.checkOpt.changeDirection"></el-switch>
							</div>
							<div class="switch-item">
								<span>触发委托</span>
								<el-switch v-model="uistates.checkOpt.promptConfirm"></el-switch>
							</div>
						</div>

					</el-popover>
				</div>

				<div v-for="(item, item_idx) in levels" :key="item_idx">

					<div class="level-item s-unselectable"
						 :style="{
							'line-height': uistates.lineHeight + 'px',
							'margin-top': item_idx == 0 ? uistates.margin / 2 + 'px' : 0,
							'margin-bottom': item_idx == levels.length - 1 ? uistates.margin / 2 + 'px' : 0,
							}">
						
						<template v-if="item.price == 0">
							<span class="level-hands s-border-box">--</span>
							<span class="level-price s-border-box">--</span>
						</template>

						<template v-else>

							<span class="level-hands s-border-box" 
							  :class="item.colorClass">{{ simplyHands(item.hands) }}</span>

							<span class="level-price s-border-box s-hover-underline" 
							  :class="item.colorClass"
							  @click="handleSelect(item)">{{ precisePrice(item.price) }}</span>

						</template>

						<span class="level-name s-border-box">{{ item.label }}</span>
					</div>

					<div v-if="item_idx == 4" 
						class="seperator themed-bg"
						:style="{ margin: uistates.margin + 'px 1px' }"></div>
						
				</div>

			</template>

		</div>

	</div>

</div>