const { TradeRecordView } = require('../module/trade-record-view');
const { TradeRecord } = require('../../../../model/trade-record');
const { repoTradeRecord } = require('../../../../repository/traderecord');

class View extends TradeRecordView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '我的成交');
        this.states = {
            keywords: null,
        };
    }

    async queryFirstScreen() {
        return await repoTradeRecord.quickMemQuery({ trade_user_id: this.userInfo.userId, pageSize: this.paging.pageSize, pageNo: 1 });
    }

    async queryAll() {
        return await repoTradeRecord.batchMemQuery({ trade_user_id: this.userInfo.userId });
    }

    listen2DataChange() {
        this.standardListen(this.serverEvent.exchangeChanged, this.handleExchangeChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.exchangeChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.exchangeChanged]);
    }

    consumeBatchPush(titles, contents, totalSize) {

        super.consumeBatchPush(titles, contents, totalSize);
        var records = TradeRecordView.ModelConverter.formalizeTradeRecords(titles, contents);
        this.tableObj.refill(records);
        this.filterExchanges();
    }

    /**
     * @param {*} struc
     */
    handleExchangeChange(struc) {
        
        var exchange = new TradeRecord(struc);
        var isAdjustPos = exchange.adjustFlag;

        if (isAdjustPos) {
            return;
        }
        else if (this.isRecordAssetQualified(exchange.assetType)) {
            this.tableObj.putRow(exchange);
        }
    }

    // resetControls() {

    //     super.resetControls();
    //     this.states.keywords = null;
    // }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },

            methods: this.helper.fakeVueInsMethod(this, [
                this.filterExchanges,
            ]),
        });
    }

    filterByChannel() {
        this.filterExchanges();
    }

    filterExchanges() {

        var thisObj = this;
        var keywords = this.states.keywords;

        /**
         * @param {TradeRecord} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {TradeRecord} record 
         */
        function testRecords(record) {
            return thisObj.isRecordAssetQualified(record.assetType) && (thisObj.tableObj.matchKeywords(record) || filterByPinyin(record));
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    build($container) {

        super.build($container, 'smt-fue');
        this.subChange();
        this.turn2Request();
    }
}

module.exports = View;