const RouteMaps = require('../config/routemaps');
const { TemplateAppConfig, TemplateAppProvider } = require('../config/webapp');

const PermissionDataStructure = {

    /** 操作名称 */
    permissionName: null,
    /** 归属菜单ID */
    menuId: 1,
    /** 该操作是否为HTTP方式，false/非http方式，如果为http方式则为get/post/put/delete */
    method: false,
    /** 数据传输方式为TCP方式时，上行功能代码 */
    functionCode: 1,
    /** http方式调用，则为目标URL，否则忽略 */
    url: null,
};

class Permission {

    constructor(struc = PermissionDataStructure) {

        /** 操作名称 */
        this.permissionName = struc.permissionName;
        /** 归属菜单ID */
        this.menuId = struc.menuId;
        /** 该操作采用的数据提交方式 TCP | GET | POST | PUT | DELETE */
        this.method = struc.method;
        /** 数据传输方式为TCP方式时，上行功能代码 */
        this.functionCode = struc.functionCode;
        /** http方式调用，则为目标URL，否则忽略 */
        this.url = struc.url;
    }
}

const MenuDataStructure = {

    /** 菜单ID */
    id: 0,
    /** 菜单名称 */
    menuName: null,
    /** 菜单图标 */
    menuIcon: null,
    /** 菜单顺序（小前大后） */
    sequence: 0,
    /** 视图名称（视图加载路径） */
    viewLocation: null,
    /** 是否为APP外链 */
    isApp: false,
};

class Menu {

    constructor(struc = MenuDataStructure, permissions, parentId) {
        
        /** 菜单ID */
        this.menuId = struc.id;
        /** 菜单名称 */
        this.menuName = struc.menuName;
        /** 菜单图标（当前的menu icon配置并非图标，废弃该数据） */
        this.menuIcon = struc.menuIcon = undefined;
        /** 菜单顺序（小前大后） */
        this.sequence = struc.sequence || 0;
        /** 是否为APP外链 */
        this.isApp = !!struc.isApp;
        /**
         * 视图名称（视图加载路径）-- 服务端数据并不包含该信息，由本地配置进行扩充
         * 对于外链APP类型的菜单入口，则直接使用该入口信息指定的URL地址
        */
        this.viewLocation = this.isApp ? struc.viewLocation : (struc.viewLocation = null);
        /** 是否为子菜单 */
        this.isChild = typeof parentId == 'number';

        if (this.isChild) {
            this.parentId = parentId;
        }

        /**
         * @param {Array} permissions 
         * @returns {Array<Permission>}
         */
        function boxize(permissions) {
            return permissions.map(item => new Permission(item));
        }

        /**
         * @returns {Array<Menu>}
         */
        function allocate() {
            return [];
        }

        /** 操作权限（黑名单） */
        this.blackPermits = permissions instanceof Array ? boxize(permissions) : [];
        /** 包含的子菜单（当该菜单为父菜单时，适用） */
        this.children = allocate();
        /** 是否父级菜单处于展开状态（当该菜单为父菜单时，适用） */
        this.expanded = false;
        /** 映射视图和图标信息 */
        this.extend();
    }

    /**
     * 为菜单扩充本地视图映射和图标信息
     */
    extend() {

        /**
         * @returns {{name, path, icon, tag }}
         */
        function extractConfig(menu_id) {
            return RouteMaps[menu_id];
        }

        let matched = extractConfig(this.menuId);
        if (matched) {
            
            this.viewLocation = matched.path;

            if (matched.tag) {

                /** 菜单项携带的TAG信息 */
                this.menuTag = matched.tag;
            }
        }

        if (!this.menuIcon && matched) {
            this.menuIcon = matched.icon || 'iconfont icon-chanpin';
        }
    }

    /**
     * duplicate an instance
     * @param {Menu} base can be instance of [Menu] or not
     * @returns {Menu}
     */
    static Duplicate(base) {

        var copied = new Menu({});

        for (let key in copied) {
            delete copied[key];
        }

        for (let key in base) {

            if (key == 'blackPermits') {

                let records = base[key];
                copied[key] = records instanceof Array ? records.map(item => new Permission(item)) : [];
            }
            else {
                copied[key] = base[key];
            }
        }

        return copied;
    }

    /**
     * 将平行的原始菜单数组，转换成具有树形结构的菜单树
     * @param {Array<{ active, id, menuIcon, menuName, parentMenuId, sequence, userType }>} sys_menus 系统全局菜单
     * @param {Array<{ menuId, menuIcon, menuName }>} role_menus 属于当前用户的菜单结构
     * @returns {Array<Menu>}
     */
    static ConstructTree(sys_menus, role_menus) {

        if (sys_menus.length == 0 || role_menus.length == 0) {
            return [];
        }

        /**
         * @param {Menu} menu 
         */
        function asmenu(menu) {
            return menu;
        }

        /**
         * @param {Array<Menu>} menus 
         */
        function asmenus(menus) {
            return menus;
        }

        // 全局菜单快速查询map
        const sysMenusMap = {};
        sys_menus.forEach(menu => {
            sysMenusMap[menu.id] = menu;
        });

        // 角色菜单快速查询map
        const roleMenusMap = {};
        role_menus.forEach(menu => {
            roleMenusMap[menu.menuId] = menu;
        });

        // 树形节点map（根节点与子节点混合，下一步再构建树形结构）
        const treeNodes = {};
        
        // 遍历每一个角色菜单项
        role_menus.forEach(roleMenu => {

            let menuId = roleMenu.menuId;
            let sysMenu = sysMenusMap[menuId];
            let { parentMenuId } = sysMenu || {};
            let menuNode = asmenu(treeNodes[menuId]);
            
            // 创建菜单对象
            if (menuNode == undefined) {
                menuNode = treeNodes[menuId] = new Menu(sysMenu, [], parentMenuId);
            } 
            else {

                // 如果有重复，使用后遇到的更新之前已加入的
                menuNode.menuName = roleMenu.menuName;
                menuNode.menuIcon = roleMenu.menuIcon;
            }
            
            // 如果有父节点
            if (parentMenuId) {

                let pid = parentMenuId;
                let parentSysMenu = sysMenusMap[pid];
                let parentMenuNode = asmenu(treeNodes[pid]);
                
                // 检查父节点是否已存在
                if (roleMenusMap[pid]) {

                    // 检查父节点是否已存在
                    if (parentMenuNode == undefined) {
                        parentMenuNode = treeNodes[pid] = new Menu(parentSysMenu, [], pid);
                    }

                    parentMenuNode.children.push(menuNode);
                } 
                else {

                    // 父节点不存在，则将当前子菜单直接作为父节点
                    menuNode.isRoot = true;
                }
            } 
            else {

                // 当前菜单本身即为父节点
                menuNode.isRoot = true;
            }
        });
        
        // 构造树结构
        const menuTree = asmenus([]);
        Object.values(treeNodes).forEach(node => {

            const { isRoot, children } = asmenu(node);

            if (isRoot) {

                // 对子节点进行排序
                if (children.length > 0) {

                    children.sort((a, b) => {
                        return (a.sequence || 0) - (b.sequence || 0);
                    });
                }

                menuTree.push(node);
            }
        });
        
        // 对根节点进行排序
        menuTree.sort((a, b) => {
            return (a.sequence || 0) - (b.sequence || 0);
        });
        
        return menuTree;
    }

    /**
     * 构造外链应用菜单
     * @param {Array} records 
     */
    static ConstructAppMenus(records) {

        return records.map(item => {

            let { id, url, appName, appIcon } = item;
            if (typeof url == 'string' && url.startsWith(TemplateAppConfig.TemplateUrlPrefix)) {

                let tmpl_str = url.replace(TemplateAppConfig.TemplateUrlPrefix, '');
                let tmpl = JSON.parse(tmpl_str);
                let providers = Object.values(TemplateAppProvider);
                let matched = providers.find(x => x.type == tmpl.appType);
                
                if (matched) {
                    url = matched.urlFormatter(tmpl);
                }
            }

            return new Menu({

                id: 'APP-' + id,
                menuName: appName,
                viewLocation: url,
                menuIcon: appIcon,
                sequence: 0,
                isApp: true,
            });
        });
    }
}

module.exports = { Menu, Permission };