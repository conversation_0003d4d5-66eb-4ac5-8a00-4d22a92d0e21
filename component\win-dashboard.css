
.win-body.with-menu-on {

	/* 左侧显示完整菜单时，此宽度为菜单宽度 */
	padding-left: 180px;
}

/*
	side menu
*/

.win-side {

	position: fixed;
	left: 0;
	top: 0;
	z-index: 777;
	width: 180px;
	height: 100%;
	padding: 34px 0 30px 0;
}

.win-side-inner .collapser {

	height: 32px;
	line-height: 32px;
	padding-left: 16px;
	padding-right: 8px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

.win-side-inner .collapser i {
	float: right;
}

.menu-root .menu-item {

	height: 32px;
	line-height: 32px;
	cursor: pointer;
}

.menu-root i {
	font-size: 14px;
}

.menu-root .menu-icon {
	font-size: 12px;
}

.menu-root .menu-item.selected {
	opacity: 1;
}

.menu-root .menu-level-1 {

	padding-left: 16px;
    padding-right: 8px;
}

.menu-root .menu-level-2 {

	padding-left: 25px;
    padding-right: 8px;
}

.menu-root .sub-menu-box .menu-icon {

	position: relative;
    top: -1px;
	/* display: inline-block; */
	display: none;
	width: 6px;
	height: 6px;
	border-radius: 3px;
	background-color: white;
}

.menu-root .menu-name {
	padding-left: 5px;
}

.menu-root .sub-menu-box .menu-name {

	padding-left: 8px;
}

.menu-root .menu-toggler {
	float: right;
}

/*
	footer
*/

.win-footer {

	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 888;
	width: 100%;
	height: 32px;
    line-height: 32px;
    overflow: hidden;
}


.win-footer .about-server,
.win-footer .about-user,
.win-footer .about-trading-day,
.win-footer .about-version,
.win-footer .about-logout {

	height: 100%;
	padding-left: 10px;
	padding-right: 10px;
	border-right: 1px solid #5f6063;
}

.win-footer .network-status {

	height: 100%;
	padding-left: 10px;
	padding-right: 10px;
}

.win-footer .network-status i {

	font-size: 18px;
	position: relative;
    top: 2px;
}

.win-footer .network-status .content,
.win-footer .network-status .pingpong {

	border-left: 1px solid #5f6063;
	padding-left: 5px;
	padding-right: 5px;
	font-size: 14px;
}

.win-footer .network-status.error i,
.win-footer .network-status.error .content {
	color: #e93030;
}

.win-footer .network-status.success i,
.win-footer .network-status.success .content {
	color: #1BBE48;
}

/*
	content
*/

.win-content {

	width: 100%;
	height: 100%;
	padding: 2px;
}

.win-content .menu-expander {

	line-height: 28px;
    padding: 0 5px;
    display: none;
    float: left;
}

.win-content .tab-placeholder {
	height: 0;
}

.win-content > .win-top-content {
	padding-top: 28px;
}

/*
	window locker
*/

.window-locker {

	display: none;
    height: 100%;
    width: 100%;
    border-radius: 9px;
    box-sizing: border-box;
}

.window-locker.visible {

	display: block;
	position: fixed;
	z-index: 2001;
	left: 0;
    top: 0;
}

.window-locker .shadow {

    display: table;
    height: 100%;
    width: 100%;
    background-color: #606057;
}

.window-locker .content {

    display: table-cell;
    vertical-align: middle;
    text-align: center;
}

.window-locker .unlock-form {

    display: inline-block;
    padding: 5px 20px 30px 20px;
    border: 2px solid #807575;
    border-radius: 3px;
}

.window-locker .unlock-form .title {

    line-height: 46px;
    font-size: 16px;
    display: block;
}

.window-locker .unlock-form .el-input {
    width: 260px;
}

.window-locker .unlock-form .el-input__inner {

    height: 28px;
    line-height: 28px;
}
