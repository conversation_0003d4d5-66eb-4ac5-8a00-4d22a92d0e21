
const BizHelper = require('../libs/helper-biz').BizHelper;
const systemEnum = require('../config/system-enum').systemEnum;
const CompletedOrderStatusCodeMap = {
    [systemEnum.orderStatus.partialCanceled.code]: true, 
    [systemEnum.orderStatus.traded.code]: true, 
    [systemEnum.orderStatus.invalid.code]: true, 
    [systemEnum.orderStatus.canceled.code]: true,
    [systemEnum.orderStatus.reject.code]: true,
};
const { SpecialTradeRecordRemark } = require('./trading');

class Order {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.adjustFlag = struc.adjustFlag !== undefined && struc.adjustFlag !== null && struc.adjustFlag !== 0;
        this.assetType = struc.assetType;
        this.businessFlag = struc.businessFlag;
        this.cancelledVolume = struc.cancelledVolume;
        this.commission = struc.commission;
        this.coverFlag = struc.coverFlag;
        this.createTime = struc.createTime || '';
        this.customId = struc.customId;
        this.direction = struc.direction;
        this.errorCode = struc.errorCode;
        this.errorMsg = struc.errorMsg;
        this.exchangeOrderId = struc.exchangeOrderId || '';
        this.financeAccount = struc.financeAccount;
        this.forceClose = struc.forceClose !== undefined ? !!struc.forceClose : !!struc.isForceClose;;
        this.foreign = struc.foreign !== undefined ? !!struc.foreign : !!struc.isForeign;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.frozenVolume = struc.frozenVolume;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.hedgeFlag = struc.hedgeFlag;
        this.id = struc.id;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        // this.isCompleted = struc.isCompleted;
        this.orderPrice = struc.orderPrice;
        this.orderPriceType = struc.orderPriceType;
        this.orderStatus = struc.orderStatus;
        this.orderTime = struc.orderTime || '';
        this.positionEffect = struc.positionEffect;
        this.receiveExchangeOrderIdTime = struc.receiveExchangeOrderIdTime;
        this.receiveOrderTime = struc.receiveOrderTime;
        this.remark = this._translateRemark(struc.remark || '');
        this.sendTerminalTime = struc.sendTerminalTime;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName || '';
        this.tradeTime = struc.tradeTime || '';
        this.tradedPrice = struc.tradedPrice;
        this.tradedVolume = struc.tradedVolume;
        this.tradingDay = struc.tradingDay;
        this.updateTime = struc.updateTime;
        this.userId = struc.userId;
        this.userName = struc.userName;
        this.volumeOriginal = struc.volumeOriginal;

        this.sourceIpMac = struc.sourceIpMac;
        this.ipMac = struc.ipMac;
        this.parentOrderId = struc.parentOrderId;
        this.localOrderId = struc.localOrderId;
        this.optionType = struc.optionType;

        this._enrich(struc);
    }

    /**
     * @param {String} remark 
     */
    _translateRemark(remark) {
        
        switch (remark) {

            case SpecialTradeRecordRemark.SellIndividualStock: return '卖出散股';
            case SpecialTradeRecordRemark.DevideStock: return '分红送股';
            default: return remark;
        }
    }

    _enrich(struc) {

        /** 最小价格变动，如：股票0.01，某期货合约0.5 */
        this.priceTick = BizHelper.getPriceTick(struc.instrument);
        /** 标准价格精度（价格小数点后，小数位数），如：股票2，某期货合约0，某期权1 */
        this.pricePrecision = BizHelper.getPricePrecision(struc.instrument);        
        this.tradedAmount = typeof this.tradedPrice == 'number' ? this.tradedVolume * this.tradedPrice : 0;
        this.floatProfit = 0;
        this.isCompleted = CompletedOrderStatusCodeMap[this.orderStatus] === true;
    }
}

module.exports = { Order };