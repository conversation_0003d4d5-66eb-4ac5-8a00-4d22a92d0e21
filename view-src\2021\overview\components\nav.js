const { IView } = require('../../../../component/iview');
const { repoTrading } = require('../../../../repository/trading');
const { repoFund } = require('../../../../repository/fund');

class IdentityNavView extends IView {

    /**
     * @param {*} view_name 
     * @param {*} is_standalone_window 
     */
    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window);
        this.benchmarks = this.systemEnum.references;
        this.states = { benchmark: null };
    }

    createToolbar() {

        new Vue({

            el: this.$container.querySelector('.nav-chart-view > .user-toolbar'),
            data: {

                benchmarks:  this.benchmarks,
                states:  this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.handleBenchmarkChange,
            ]),
        });
    }

    handleBenchmarkChange(benchm) {
        console.log(benchm);
    }

    async requestHistory() {

        if (this.helper.isNone(this.identityId)) {

            console.log('no contextual identity has been set');
            return;
        }

        var resp = null;
        var serieses = [];

        if (this.is4Product) {

            resp = await repoFund.getHistoryNav(this.systemEnum.identityType.fund.code, this.identityId);
            serieses = (resp.data || {}).series || [];
        }
        else {

            let identity_type = this.is4Strategy ? this.systemEnum.identityType.strategy.code : this.is4Account
                                                ? this.systemEnum.identityType.account.code : 0;

            resp = await repoTrading.getHistoryNav(identity_type, this.identityId);
            serieses = resp.data || [];
        }

        this.setChartData(serieses);
    }

    /**
     * @param {Array} serieses 
     */
    setChartData(serieses) {

        var navs = [];
        var sumNavs = [];
        var xaxises = [];

        serieses.forEach(item => {
            
            xaxises.push(item.tradingDay);
            navs.push(item.nav);
            sumNavs.push(item.sumNav);
        });

        var dtos = [];

        if (this.is4Product) {

            dtos = [
                {
                    name: this.chartTitles[0],
                    type: 'line',
                    smooth: true,
                    data: navs,
                },
                {
                    name: this.chartTitles[1],
                    type: 'line',
                    smooth: true,
                    data: sumNavs,
                }
            ];
        }
        else {

            dtos = [{

                name: this.chartTitles[0],
                type: 'line',
                smooth: true,
                data: navs,
            }];
        }

        this.chartIns.setOption({

            xAxis:{ data: xaxises },
            series: dtos,
        });
    }

    createChart() {

        var ECharts = require('echarts');
        var $root = this.$container.querySelector('.nav-chart-view > .chart');
        var chartIns = ECharts.init($root);
        var option = {
           
            tooltip: {

                trigger: 'axis',

                /**
                 * @param {Array} points 
                 */
                formatter: (points) => { 

                    let name = points[0].name;
                    let items = this.is4Product ? points.map(item => `${item.seriesName}：${item.value.toFixed(4)}`)
                                                : points.map(item => `${item.seriesName}：${item.value.toFixed(2)}%`);

                    return `交易日：${name}<br>${items.join('<br>')}`;
                },
            },
            color:['#F9BE1F', '#65A1FF', '#55D1E7', '#50CE85'],
            legend: {

                data: this.chartTitles,
                textStyle: { fontSize: 12, color:'#FFFFFF' },
                itemHeight: 9,
                icon: 'circle',
            },
            grid: {

                left: '3%', 
                right: '4%', 
                bottom: '3%', 
                containLabel: true 
            },
            xAxis: {

                type: 'category',
                boundaryGap: false,
                axisLabel: {

                    show: true,
                    textStyle: { color: '#999999', fontSize: 14 },
                },
                data: [],
            },
            yAxis: {

                type: 'value',
                axisLabel: {

                    show: true,
                    textStyle: { color: '#E5E5E5' },
                    formatter: this.is4Product ? '{value}' : '{value}%',
                },
            },
            series: this.chartTitles.map(title => ({

                name: title,
                type: 'line',
                symbol: 'none',
                smooth: true,
                data: [],
            })),
        };

        chartIns.setOption(option);
        this.chartIns = chartIns;   
    }

    handleIdentityChange(identityId) {

        this.identityId = identityId;
        this.requestHistory();
    }

    handleHeightChange(height) {
        this.chartIns.resize({ height : height - 102 });
    }

    /**
     * @param {*} $container 
     * @param {{ is4Product: Boolean, is4Strategy: Boolean, is4Account: Boolean }} options 
     */
    build($container, options) {

        super.build($container);
        
        var is4Product = !!options.is4Product;
        this.is4Product = is4Product;
        this.is4Strategy = !!options.is4Strategy;
        this.is4Account = !!options.is4Account;
        this.title = is4Product ? '净值走势' : '累计收益率走势';
        this.chartTitles = is4Product ? ['单位净值', '累计净值'] : ['累计收益率'];

        this.createToolbar();
        this.createChart();
        this.registerEvent('set-context-identity', this.handleIdentityChange.bind(this));
        this.registerEvent('set-chart-height', this.handleHeightChange.bind(this));
    }
}

module.exports = IdentityNavView;