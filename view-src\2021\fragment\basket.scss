.basket-group {

    .basket-group-internal {
        overflow: hidden;
    }

    .xtheader {

        position: relative;

        .el-select {
            margin-left: 8px;
        }
    
        .el-checkbox {
            margin-left: 12px;
        }
    }

    .table-external {
        padding-right: 5px;
    }
}

.basket-group-item {

    .group-name {
        padding-right: 12px;
    }

    .group-opers {

        .btn {
            
            padding: 5px 0 5px 5px;
            opacity: 0.6;
        }

        .btn:hover {
            opacity: 1;
        }
    }
}

.xt-table-row-input {
    width: 50px;
}

.import-basket-instrument-sample-pic {

    display: inline-block;
    width: 457px;
    height: 93px;
    background-image: url(../asset/image/template-sample/basket-instrument.png);
}