<div class="prices s-full-height">
	<div class="prices-internal themed-box s-full-height">
		<div class="xtcontainer s-border-box s-full-height">
			
			<template>
				<div class="xtheader themed-header">
					<template v-if="!!states.instrument">
						{{ formatViewTitle() }}
					</template>
					<template v-else>{{ channel.mean }}</template>
				</div>
			</template>

			<template v-for="(item, item_idx) in prices">

				<div class="price-item themed-top-border s-border-box s-unselectable"
					 :key="item_idx"
					 :style="{ height: uistates.rowHeight + 'px', 'line-height': uistates.rowHeight + 'px' }">
					
					<span class="price-label s-border-box">{{ item.label }}</span>
	
					<a v-if="item.isPercent" 
						class="price-value s-border-box"
						:class="item.colorClass">{{ item.value.toFixed(2) }}%</a>

					<a v-else-if="item.isAmount"
						class="price-value s-border-box">{{ item.value }}</a>
	
					<a v-else
						class="price-value s-border-box s-hover-underline"
						:class="item.colorClass"
						@click="handleSelect(item.value)">{{ item.value.toFixed(states.precision) }}</a>
	
				</div>
			</template>
		</div>
	</div>
</div>