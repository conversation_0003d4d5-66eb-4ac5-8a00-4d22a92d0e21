<script setup lang="ts">
import { TRADE_CHANNELS } from '@/enum/trade';
import { type TradeChannel } from '@/types';

// import { ref } from 'vue';

const channels = TRADE_CHANNELS;

const activeChannel = defineModel<TradeChannel>();

const handleClickChannel = (channel: TradeChannel) => {
  activeChannel.value = channel;
};
</script>

<template>
  <div flex h-40 aic pl-12>
    <div
      v-for="channel in channels"
      :key="channel.name"
      :class="activeChannel?.name === channel.name ? 'important:tag-active' : ''"
      cursor-pointer
      hover="border-#30486D c-white"
      flex
      aic
      jcc
      h-22
      fs-14
      border="1 solid transparent"
      p="x-12"
      rounded="2"
      m="r-2"
      transition-all-200
      @click="handleClickChannel(channel)"
    >
      {{ channel.label }}
    </div>
  </div>
</template>

<style scoped></style>
