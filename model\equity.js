/**
 * 权益
 */
class Equity {

    constructor(struc) {

        this.id = struc.id;
        this.identityId = struc.identityId;
        this.identityName = struc.identityName;
        this.tradingDay = struc.tradingDay;

        this.available = struc.available;
        this.balance = struc.balance;
        this.closeProfit = struc.closeProfit;
        this.commission = struc.commission;
        this.dayProfit = struc.dayProfit;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.fundShare = struc.fundShare;
        this.inMoney = struc.inMoney;
        this.margin = struc.margin;
        this.marketValue = struc.marketValue;
        this.nav = struc.nav;
        this.outMoney = struc.outMoney;
        this.positionProfit = struc.positionProfit;
        this.preBalance = struc.preBalance;
        this.risePercent = struc.risePercent;

        this._enrich(struc);
    }

    _enrich(struc) {

        //
    }
}

module.exports = { Equity };