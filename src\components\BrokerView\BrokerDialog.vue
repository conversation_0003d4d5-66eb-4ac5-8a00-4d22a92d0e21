<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';
import { type MomBroker, type FormBroker, BrokerTypeEnum } from '../../../../xtrade-sdk/dist';
import { deleteConfirm } from '@/script/interaction';
import { enumToArray } from '@/script';

interface ServerInfo {
  ip: string;
  port: number;
}

const { broker } = defineProps<{
  broker?: MomBroker;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [data: MomBroker];
}>();

// 表单校验规则
const rules = {
  brokerId: [{ required: true, message: '请输入经纪商代码', trigger: 'blur' }],
  brokerName: [{ required: true, message: '请输入经纪商名称', trigger: 'blur' }],
  brokerType: [{ required: true, message: '请选择经纪商类型', trigger: 'change' }],
};

const ipRules = {
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    {
      pattern:
        /^(((25[0-5])|(2[0-4]\d)|(1\d\d)|([1-9]\d)|\d)(\.((25[0-5])|(2[0-4]\d)|(1\d\d)|([1-9]\d)|\d)){3}$|^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6})|(((http:\/\/)|(https:\/\/))?([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(\/)?)$/,
      message: '请输入ip地址或域名',
      trigger: 'blur',
    },
  ],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
};

const formRef = useTemplateRef('formRef');
const ipFormRef = useTemplateRef('ipFormRef');

const form = ref<FormBroker>({
  brokerId: '',
  brokerName: '',
  brokerType: 1,
  servers: '',
});

// 服务器地址列表
const serverList = ref<ServerInfo[]>([]);

// 服务器地址对话框
const serverDialogVisible = ref(false);
const serverForm = ref<ServerInfo>({
  ip: '',
  port: 0,
});
const editIpIndex = ref(-1);

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (broker) {
      form.value = {
        brokerId: broker.brokerId,
        brokerName: broker.brokerName,
        brokerType: broker.brokerType,
        servers: broker.servers,
      };
      // 解析服务器地址列表
      try {
        serverList.value = broker.servers.split(',').map((item: string) => {
          const [ip, port] = item.split(':');
          return { ip, port: Number(port) };
        });
      } catch {
        serverList.value = [];
      }
    } else {
      resetForm();
    }
  }
});

// 重置表单
const resetForm = () => {
  form.value = {
    brokerId: '',
    brokerName: '',
    brokerType: 1,
    servers: '',
  };
  serverList.value = [];
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 经纪商类型选项
const brokerTypeOptions = enumToArray(BrokerTypeEnum);

// 添加服务器地址
const handleAddServer = () => {
  editIpIndex.value = -1;
  serverForm.value = { ip: '', port: 0 };
  serverDialogVisible.value = true;
};

// 编辑服务器地址
const handleEditServer = (index: number) => {
  serverForm.value = { ...serverList.value[index] };
  serverDialogVisible.value = true;
  editIpIndex.value = index;
};

// 删除服务器地址
const handleDeleteServer = (index: number) => {
  deleteConfirm('删除服务器', '确定要删除这个服务器地址吗？').then(result => {
    if (!result) return;
    serverList.value.splice(index, 1);
    form.value.servers = serverList.value.map(server => `${server.ip}:${server.port}`).join(',');
  });
};

// 保存服务器地址
const handleSaveServer = () => {
  ipFormRef.value?.validate(async valid => {
    if (valid) {
      if (editIpIndex.value >= 0) {
        serverList.value[editIpIndex.value] = { ...serverForm.value };
      } else {
        serverList.value.push({ ...serverForm.value });
      }

      form.value.servers = serverList.value.map(server => `${server.ip}:${server.port}`).join(',');
      serverDialogVisible.value = false;
    }
  });
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (form.value.servers === '') {
        ElMessage.error('请至少添加一个服务器地址');
        return;
      }
      if (broker) {
        const { errorCode, errorMsg, data } = await AdminService.updateBroker({
          ...broker,
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success', data!);
          ElMessage.success('修改成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      } else {
        const { errorCode, errorMsg, data } = await AdminService.createBroker({
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success', data!);
          ElMessage.success('添加成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      }
    }
  });
};
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="broker ? '编辑经纪商' : '新建经纪商'"
    width="700px"
    @close="handleClose"
    draggable
    destroy-on-close
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="经纪商代码" prop="brokerId">
        <el-input v-model="form.brokerId" placeholder="请输入经纪商代码" :disabled="!!broker" />
      </el-form-item>

      <el-form-item label="经纪商名称" prop="brokerName">
        <el-input v-model="form.brokerName" placeholder="请输入经纪商名称" />
      </el-form-item>

      <el-form-item label="经纪商类型" prop="brokerType">
        <el-select v-model="form.brokerType" placeholder="请选择经纪商类型">
          <el-option
            v-for="option in brokerTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="服务器地址">
        <div w-full>
          <div flex justify-between items-center mb-10>
            <span text-14 font-medium>服务器列表</span>
            <el-button size="small" type="primary" @click="handleAddServer">
              <i class="iconfont icon-add" mr-5></i>
              添加
            </el-button>
          </div>

          <el-table :data="serverList" border size="small" max-height="200">
            <el-table-column prop="ip" label="IP地址" />
            <el-table-column prop="port" label="端口" />
            <el-table-column label="操作" width="120" align="center">
              <template #default="{ $index }">
                <el-button size="small" type="primary" link @click="handleEditServer($index)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" link @click="handleDeleteServer($index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 服务器地址编辑对话框 -->
  <el-dialog
    v-model="serverDialogVisible"
    title="服务器地址"
    width="300px"
    draggable
    destroy-on-close
    append-to-body
  >
    <el-form label-width="80px" :model="serverForm" :rules="ipRules" ref="ipFormRef">
      <el-form-item label="IP地址" prop="ip">
        <el-input v-model="serverForm.ip" placeholder="请输入IP地址" />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input-number
          style="width: 100%"
          v-model="serverForm.port"
          :min="1"
          :max="65535"
          placeholder="请输入端口"
          :controls="false"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="serverDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveServer">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
:deep() {
  .el-input__inner {
    text-align: left;
  }
}
</style>
