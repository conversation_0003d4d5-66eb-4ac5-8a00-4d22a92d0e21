<script setup lang="ts">
import ClassView from './ClassView.vue';
import ClassMemberView from './ClassMemberView.vue';
import EmbedChooseFundView from '@/components/common/EntityChooseDialog/EmbedChooseFundView.vue';
import EmbedChooseAccountView from '@/components/common/EntityChooseDialog/EmbedChooseAccountView.vue';
import { computed, ref } from 'vue';
import { TradeClassificationType, TradeClassificationTypes } from '../../../../xtrade-sdk/dist';

const classType = ref(TradeClassificationType.Asset);
const classId = ref<number | null>(null);
const className = ref<string | null>(null);
const isMemberEditAllowed = ref<boolean>(true);

const is4Asset = computed(() => {
  return classType.value == TradeClassificationType.Asset;
});

const is4Product = computed(() => {
  return classType.value == TradeClassificationType.ProductGroup;
});

const is4Account = computed(() => {
  return classType.value == TradeClassificationType.AccountGroup;
});

function handleClassChange(
  new_class_id: number | null,
  new_class_name: string | null,
  is_member_edit_allowed: boolean,
) {
  classId.value = new_class_id;
  className.value = new_class_name;
  isMemberEditAllowed.value = !!is_member_edit_allowed;
}
</script>

<template>
  <div class="class-root-view" h-full w-full flex>
    <div w-500 flex-1 of-y-hidden>
      <div h-50 p-b-12>
        <el-tabs v-model="classType">
          <el-tab-pane
            v-for="item in TradeClassificationTypes"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <ClassView :class-type="classType" @class-change="handleClassChange"></ClassView>
    </div>
    <div w-500 flex-1 of-y-hidden>
      <template v-if="is4Asset">
        <ClassMemberView
          :class-type="classType"
          :class-id="classId"
          :class-name="className"
          :is-member-edit-allowed="isMemberEditAllowed"
        ></ClassMemberView>
      </template>
      <template v-else-if="is4Product">
        <EmbedChooseFundView
          :class-type="classType"
          :class-id="classId"
          :class-name="className"
        ></EmbedChooseFundView>
      </template>
      <template v-else-if="is4Account">
        <EmbedChooseAccountView
          :class-type="classType"
          :class-id="classId"
          :class-name="className"
        ></EmbedChooseAccountView>
      </template>
    </div>
  </div>
</template>

<style scoped></style>
