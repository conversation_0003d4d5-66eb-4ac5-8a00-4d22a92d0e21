import { GlobalState, Repos } from '../../../xtrade-sdk';

const defaultLogger = GlobalState.GetLogger();
const userRepo = new Repos.UserRepo();

class LoginService {
  static async login(username: string, password: string, mac: string, os: string, quote = false) {
    defaultLogger.info('Frontend login', { username, password, mac, os, quote });
    return await this.loginViaWs(username, password, mac, os, quote);
  }

  /**
   * 通过Web Socket方式登录
   */
  private static async loginViaWs(
    username: string,
    password: string,
    mac: string,
    os: string,
    quote = false,
  ) {
    const resp = await userRepo.SignIn(username, password, mac, os, quote);
    return resp;
  }

  /**
   * 用户注销
   */
  static async logout() {
    return await userRepo.SignOut();
  }
}

export default LoginService;
