import { BaseRepo } from '../modules/base-repo';
import { TradeDynamicIndicator } from '../types/table/dynamic-indicator';

const BaseUrl = '../v4/risk/dynamicIndicator';

export class DynamicIndicatorRepo extends BaseRepo {
  constructor() {
    super();
  }

  /**
   * 获取所有动态指标
   */
  async getDynamicIndicators() {
    return this.assist.Get<TradeDynamicIndicator[]>(`${BaseUrl}/all`);
  }

  /**
   * 创建动态指标
   */
  async createDynamicIndicator(data: TradeDynamicIndicator) {
    delete (data as any).id;
    return this.assist.Post<TradeDynamicIndicator>(`${BaseUrl}`, {}, data);
  }

  /**
   * 更新动态指标
   */
  async updateDynamicIndicator(data: TradeDynamicIndicator) {
    return this.assist.Put<TradeDynamicIndicator>(`${BaseUrl}`, {}, data);
  }

  /**
   * 删除动态指标
   */
  async deleteDynamicIndicator(dyIndicatorId: number) {
    return this.assist.Delete<TradeDynamicIndicator>(`${BaseUrl}`, { dyIndicatorId });
  }
}