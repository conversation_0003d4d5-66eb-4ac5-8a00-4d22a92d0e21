const { TaskEntrustStatus } = require('./algot');

/**
 * 篮子交易，任务结构
 */
class BasketTask {

    /**
     * @param {Boolean} isChild 
     */
    constructor(isChild, {

        basketId,
        basketName,
        cancelMoney,
        cancelVolume,
        completionRate,
        createTime,
        effectiveTime,
        executeType,
        executeUser,
        executeVolume,
        expireTime,
        id,
        orderRegulation,
        priceDeviation,
        priceFollowType,
        targetMoney,
        targetVolume,
        taskDetail,
        taskStatus,
        taskType,
        tradedMoney,
        tradedVolume,

        instrument,
        instrumentName,

        accountId,
        accountName,
        fundId,
        fundName,
        strategyId,
        strategyName,
        algorithmMappingId,
        algorithmMappingName,
        orderIds,
    }) {

        this.isChild = isChild;
        
        this.basketId = basketId;
        this.basketName = basketName;
        this.cancelMoney = cancelMoney;
        this.cancelVolume = cancelVolume;
        this.completionRate = completionRate;
        this.createTime = createTime;
        this.effectiveTime = effectiveTime;
        this.executeType = executeType;
        this.executeUser = executeUser;
        this.expireTime = expireTime;
        this.executeVolume = executeVolume;
        this.id = this.taskId = id;
        this.orderRegulation = orderRegulation;
        this.priceDeviation = priceDeviation;
        this.priceFollowType = priceFollowType;
        this.targetMoney = targetMoney;
        this.targetVolume = targetVolume;
        this.taskDetail = taskDetail;
        this.taskStatus = taskStatus;
        this.taskType = this.direction = taskType;
        this.tradedMoney = tradedMoney;
        this.tradedVolume = tradedVolume;

        /** 合约代码（子任务专属） */
        this.instrument = instrument;
        /** 合约名称（子任务专属） */
        this.instrumentName = instrumentName;

        this.accountId = accountId;
        this.accountName = accountName;
        this.fundId = fundId;
        this.fundName = fundName;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.algorithmMappingId = algorithmMappingId;
        this.algorithmMappingName = algorithmMappingName;

        this.leftVolume = targetVolume - tradedVolume - cancelVolume;
        this.leftMoney = targetMoney - tradedMoney - cancelMoney;
        this.isCompleted = this.leftVolume == 0;
        this.orderIds = isChild ? orderIds || [] : [];
    }
}

class BasketMotherTask {

    constructor({
        
        accountId,
        accountName,
        algoParam,
        algorithmMappingId,
        algorithmName,
        createTime,
        effectiveTime,
        expireTime,
        financeAccount,
        fundId,
        fundName,
        runningStock,
        strategyId,
        strategyName,
        taskId,
        totalBuyTarget,
        totalSellTarget,
        totalTarget,
        totalTraded
    }) {

        if (typeof algoParam == 'string') {
            try { algoParam = JSON.parse(algoParam); } catch(ex) {}
        }
        
        this.accountId = accountId;
        this.accountName = accountName;
        this.algoParam = algoParam || {};
        this.algorithmMappingId = algorithmMappingId;
        this.algorithmName = algorithmName;
        this.createTime = createTime;
        this.effectiveTime = effectiveTime;
        this.expireTime = expireTime;
        this.financeAccount = financeAccount;
        this.fundId = fundId;
        this.fundName = fundName;
        this.runningStock = runningStock;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.taskId = taskId;
        this.totalBuyTarget = totalBuyTarget;
        this.totalSellTarget = totalSellTarget;
        this.totalTarget = totalTarget;
        this.totalTraded = totalTraded;

        /**
         * 任务状态（包含业务状态）
         */
        this.status = runningStock == 0
                    || totalTraded == totalTarget
                    || this.expireTime <= Date.now() ? TaskEntrustStatus.completed : TaskEntrustStatus.running;
    }
}

class BasketChildTask {

    constructor({
        
        accountId,
        accountName,
        afterAction,
        algoParam,
        algorithmMappingId,
        algorithmName,
        algorithmStatus,
        algorithmType,
        direction,
        effectiveTime,
        expireTime,
        financeAccount,
        fundId,
        fundName,
        id,
        identityId,
        instrument,
        instrumentName,
        limitAction,
        orderedVolume,
        taskId,
        tradeAmount,
        tradePrice,
        tradedVolume,
        userId,
        volume
    }) {

        if (typeof algoParam == 'string') {
            try { algoParam = JSON.parse(algoParam); } catch(ex) {}
        }

        this.accountId = accountId;
        this.accountName = accountName;
        this.afterAction = afterAction;
        this.algoParam = algoParam;
        this.algorithmMappingId = algorithmMappingId;
        this.algorithmName = algorithmName;
        this.algorithmStatus = algorithmStatus;
        this.algorithmType = algorithmType;
        this.direction = direction;
        this.effectiveTime = effectiveTime;
        this.expireTime = expireTime;
        this.financeAccount = financeAccount;
        this.fundId = fundId;
        this.fundName = fundName;
        this.id = id;
        this.identityId = identityId;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.limitAction = limitAction;
        this.orderedVolume = orderedVolume;
        this.taskId = taskId;
        this.tradeAmount = tradeAmount;
        this.tradePrice = tradePrice;
        this.tradedVolume = tradedVolume;
        this.userId = userId;
        this.volume = volume;
    }
}

module.exports = { 
    
    BasketTask,
    BasketMotherTask,
    BasketChildTask,
};