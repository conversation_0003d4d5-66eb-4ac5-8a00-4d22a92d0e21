﻿const electron = require('electron');
const BrowserWindow = electron.BrowserWindow;
const MainModule = require('./main-module').MainModule;

class SafetyModule extends MainModule {

    get delayMinute() {

        if (this._lockDelay === undefined) {
            this._lockDelay = this.app.GeneralSettings.lockScreen;
        }

        return this._lockDelay;
    }

    constructor(module_name) {

        super(module_name);

        this.defaultDelayms = 1000 * 60 * 60 * 20;
        this.states = {

            delayms: this.defaultDelayms,
            allWindowsLocked: false,
        };

        this.windowStatus = {
            
            created: 'created',
            focused: 'focused',
            blured: 'blured',
            closed: 'closed',
        };

        /**
         * key: window id
         * value: boolean, window is focused or not
         */
        this.windowMap = {};
    }

    send2AllWindows(event_name) {

        this.loggerConsole.info('to send event/' + event_name);
        
        const all_wins = BrowserWindow.getAllWindows();
        for (let the_win of all_wins) {
            the_win.webContents.send(event_name);
        }
    }

    loopCheckAppActivation() {
        
        var total = 0;
        var active = 0;

        for (let window_id in this.windowMap) {

            total++;
            if (this.windowMap[window_id] === true) {
                active++;
            }
        }

        if (total == 0) {
            return;
        }
        
        if (active == 0) {

            this.loggerConsole.info(new Date().getTime() + ' > no windows are active, all windows to be locked');
            this.send2AllWindows(this.systemEvent.lockScreen);
        }
        else {
            this.loggerConsole.info(new Date().getTime() + ' > ' + active + ' windows are active');
            /**
             * 有窗口处于激活状态，此时可能：
             * 1. 所有窗口都未锁定 - 正常
             * 2. 有1个或多个窗口处于激活状态，但已锁定 - 用户可输入密码激活
             */
        }
    }

    resetLoop() {

        if (this.task) {

            clearInterval(this.task);
            this.task = 0;
        }

        this.task = setInterval(() => { this.loopCheckAppActivation(); }, this.states.delayms);
    }

    updateWindowStatus(window_id, window_status) {

        switch (window_status) {

            case this.windowStatus.created:
            case this.windowStatus.focused: {

                this.windowMap[window_id] = true;
                break;
            }

            case this.windowStatus.blured: {

                this.windowMap[window_id] = false;
                this.resetLoop();
                break;
            }

            case this.windowStatus.closed: {

                delete this.windowMap[window_id];
                this.resetLoop();
                break;
            }
        }
    }

    requestUnlockAllWindows(password) {

        if (this.userInfo.password === password || this.userInfo.password === this.helper.aesEncrypt(password)) {

            this.send2AllWindows(this.systemEvent.unlockScreen);
            this.resetLoop();
        }
        else {
            electron.dialog.showMessageBox({ title: '解锁提示', message: '输入密码与登录密码不匹配' });
        }
    }
    
    run() {

        if (this.delayMinute !== true && typeof this.delayMinute != 'number') {
            return;
        }
        
        if (typeof this.delayMinute == 'number') {
            this.states.delayms = this.delayMinute * 60 * 1000;
        }

        this.mainProcess.on(this.systemEvent.reportWindowStatus, (event, window_id, window_status) => { this.updateWindowStatus(window_id, window_status); });
        this.mainProcess.on(this.systemEvent.lockScreen, (event) => { this.send2AllWindows(this.systemEvent.lockScreen); });
        this.mainProcess.on(this.systemEvent.unlockScreen, (event, password) => { this.requestUnlockAllWindows(password); });
        this.resetLoop();
    }
}

module.exports = { SafetyModule };
