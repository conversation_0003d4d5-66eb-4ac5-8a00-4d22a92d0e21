/**
 * 特殊的交易（委托、成交、持仓）记录行上的 Remark 字段内容
 */
const SpecialTradeRecordRemark = {

    /** 卖出散股 */
    SellIndividualStock: 'sell-individual-stock',
    /** 分红送股 */
    DevideStock: 'dividend-stock',
};

class TradeDataQueryCondition {

    constructor({ userId, token, date, checked, remark, business_flag, fund, strategy, account, searchValue, pageNo, pageSize }) {

        this.userId = userId;
        this.token = token;
        this.date = date;
        this.checked = checked;
        this.remark = remark;
        this.business_flag = business_flag;
        this.fund = fund;
        this.strategy = strategy;
        this.account = account;
        this.searchValue = searchValue;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }
}

module.exports = {

    SpecialTradeRecordRemark,
    TradeDataQueryCondition,
};