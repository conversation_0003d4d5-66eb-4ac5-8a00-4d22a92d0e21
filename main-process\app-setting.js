﻿/*
    global data & context data
*/

const electron = require('electron');
const app = electron.app;
const dataKey = require('./app-data-key').dataKey;
const EventEmitter = require('events').EventEmitter;
const fs = require('fs');
const path = require('path');
const { isDev } = require('../config/environment');
const isDarwin = process.platform.toLowerCase() == 'darwin';
const appDir = isDarwin && !isDev ? path.join(__dirname, '../../../../../') : process.cwd();

// set the max number of listeners listening on a same named channel
EventEmitter.defaultMaxListeners = 200;

/**
 * 服务器管理单元
 */

app.serverManager = null;

/**
 * 交易服务器 & 行情服务器，状态信息
 */

app.serverStates = {

    isTradingServerConnected: false,
    isQuoteServerConnected: false,
};

/**
 * 应用程序上下文数据（字典）
 */

const ContextData = {};
for (let each_key in dataKey) {
    let key_name = dataKey[each_key];
    ContextData[key_name] = null;
}

// app context data
app.contextData = ContextData;

/**
 * app settings
 */
var appSettings = null;

function readSetAppSettings() {

    var config_file = path.join(appDir, 'settings.js');
    
    if (!fs.existsSync(config_file)) {
        return;
    }

    try {
        appSettings = require(config_file);
    }
    catch (ex) {
        //
    }
}

readSetAppSettings();

/**
 * APP常规设置
 */
const GeneralSettings = {

    /**
     * 软件运行期间，的锁屏时间设置
     * 1. [true], 采用系统默认的20分钟锁屏时间延迟，进行锁屏
     * 2. [false], 系统禁止锁屏
     * 3. [0]，视为[false]处理
     * 4. [5 ~ 720], 系统按照指定的时间延迟，进行锁屏
     * 5. [未指定，或任何其他]，视为[true]处理
     */
    lockScreen: true,

    /**
     * 系统主要的RESTFUL服务，采用HTTP还是HTTPS访问方式，默认为HTTP
     */
    https: false,
};

/**
 * 加密选项
 */
const encryptionOptions = {
    
    /**
     * 是否对用户登录密码、交易账号密码、终端等密码数据，传输前进行加密
     */
    encryptPasscode: false,

    /**
     * 是否对服务器SOCKET数据包数据加密
     */
    encryptMessage: false,

    /**
     * 是否对系统日志文件加密
     */
    encryptLog: false,
};

/**
 * URL选项
 */
const urls = {
    
    /**
     * 黑白名单URL
     */
    blackAndWhiteUrl: null,
};

/**
 * 其他选项
 */
const otherSysSettings = {
    
    /**
     * 部署券商机房识别码，用于识别部署的机房
     */
    brokerCode: '',
};

function readLockScreenSettings() {

    var lock_screen = appSettings.lockScreen;
    if (typeof lock_screen == 'boolean') {
        GeneralSettings.lockScreen = lock_screen;
    }
    else if (typeof lock_screen == 'number' && lock_screen >= 5 && lock_screen <= 720) {
        GeneralSettings.lockScreen = lock_screen;
    }
    else {
        GeneralSettings.lockScreen = true;
    }
}

function readHttpsSettings() {

    var https = appSettings.https;
    if (typeof https == 'boolean') {
        GeneralSettings.https = https;
    }
    else if (typeof https == 'number' && (https === 0 || https === 1)) {
        GeneralSettings.https = !!https;
    }
    else {
        GeneralSettings.https = false;
    }
}

function readEncryptionSettings() {

    var encryp_opt = appSettings.encryption;
    if (!encryp_opt) {
        return;
    }

    if (typeof encryp_opt.passcode == 'boolean') {
        encryptionOptions.encryptPasscode = encryp_opt.passcode;
    }

    if (typeof encryp_opt.message == 'boolean') {
        encryptionOptions.encryptMessage = encryp_opt.message;
    }

    if (typeof encryp_opt.log == 'boolean') {
        encryptionOptions.encryptLog = encryp_opt.log;
    }
}

function readBlackAndWhiteUrl() {
    
    var url = appSettings.blackAndWhiteUrl;
    urls.blackAndWhiteUrl = url;
}

function readOtherSettings() {
    
    const { brokerCode } = appSettings;
    otherSysSettings.brokerCode = (typeof brokerCode == 'string' || typeof brokerCode == 'number') ? brokerCode.toString().trim() : '';
}

if (appSettings) {

    readLockScreenSettings();
    readHttpsSettings();
    readEncryptionSettings();
    readBlackAndWhiteUrl();
    readOtherSettings();
}

/**
 * 常规设置，写入根数据节点
 */
app.GeneralSettings = GeneralSettings;

/**
 * 加密选项，写入根数据节点
 */
app.encryptionOptions = encryptionOptions;

/**
 * 各类URLs设置
 */
app.urls = urls;

/**
 * 各类其它设置
 */
app.otherSysSettings = otherSysSettings;

/**
 * 发布软件版本
 */
var appVersion = null;

var readSetAppVersion = () => {

    var version_file = path.join(appDir, 'version');
    
    if (!fs.existsSync(version_file)) {
        return;
    }

    try {
        let content = fs.readFileSync(version_file, { encoding: 'utf8' });
        if (content && content.length > 0) {
            appVersion = content.toString().substr(0, 50);
        }
    }
    catch (ex) {
        //
    }
}

readSetAppVersion();
app.contextData.appVersion = appVersion;

/**
 * 硬盘序列号
 */
app.contextData.diskSN = null;

module.exports = {};