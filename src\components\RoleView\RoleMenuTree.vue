<script setup lang="tsx">
import { computed, onMounted, ref, watch } from 'vue';
import type { MomRole, MomMenuTree } from '../../../../xtrade-sdk/dist';
import MenuPermission from './MenuPermission.vue';
import { AdminService } from '@/api';
import { ElMessage } from 'element-plus';
import type { RoleMenuPermissionTree } from '@/types';

const { role } = defineProps<{
  role?: MomRole;
}>();

const fullMenus = ref<RoleMenuPermissionTree[]>([]);
const roleMenus = ref<MomMenuTree[]>([]);
const initStatusCount = ref(0);

// 是否可以保存菜单（只有选中角色时才能保存）
const canSaveMenu = computed(() => !!role?.id);

// 监听角色变化，重新查询权限数据
watch(
  () => role?.id,
  async newRoleId => {
    if (newRoleId) {
      await loadRolePermissions(newRoleId);
    }
  },
);

onMounted(() => {
  getFullMenuAndPermissions();
});

const getFullMenuAndPermissions = async () => {
  const rawData = await AdminService.getMenuTree();
  const menus: RoleMenuPermissionTree[] = [];
  const addCheckedStatus = (menuList: MomMenuTree[]): RoleMenuPermissionTree[] => {
    return menuList.map(menu => ({
      ...menu,
      checked: false,
      menListPermission: menu.menListPermission?.map(permission => ({
        ...permission,
        checked: false,
      })),
      children: menu.children?.length ? addCheckedStatus(menu.children) : undefined,
    }));
  };
  menus.push(...addCheckedStatus(rawData));
  fullMenus.value = menus;
};

// 加载角色菜单和权限
const loadRolePermissions = async (roleId: number) => {
  const rawData = await AdminService.getRoleMenuTree(roleId);
  roleMenus.value = rawData;
  initStatusCount.value++;
};

// 保存菜单权限
const saveMenuPermissions = async () => {
  if (!role?.id) {
    ElMessage.warning('请先选择角色');
    return;
  }

  // 构建保存数据结构
  const saveData = buildSaveData();
  // 调用API保存
  const { errorCode, errorMsg } = await AdminService.saveRoleMenuPermissions(role.id, saveData);

  if (errorCode === 0) {
    ElMessage.success('保存成功');
  } else {
    ElMessage.error(errorMsg || '保存失败');
  }
};

// 构建保存数据结构
const buildSaveData = () => {
  const menuPermissionMap = new Map<number, number[]>();

  // 递归收集选中的菜单和权限
  const collectCheckedItems = (menus: RoleMenuPermissionTree[]) => {
    menus.forEach(menu => {
      if (menu.checked) {
        // 收集选中的权限ID
        const permissionIds: number[] = [];
        if (menu.menListPermission) {
          menu.menListPermission.forEach(permission => {
            if (permission.checked) {
              permissionIds.push(permission.id);
            }
          });
        }

        // 只有当菜单被选中时才添加到map中
        menuPermissionMap.set(menu.id, permissionIds);
      }

      // 递归处理子菜单
      if (menu.children) {
        collectCheckedItems(menu.children);
      }
    });
  };

  collectCheckedItems(fullMenus.value);

  // 构建最终数据结构
  return Array.from(menuPermissionMap.entries()).map(([menuId, permissionIds]) => ({
    menuId,
    permissionIds,
  }));
};
</script>

<template>
  <div h-full flex flex-col>
    <!-- 操作按钮 -->
    <div mb-4 flex gap-2 h-60 aic jcsb px-20>
      <el-button type="primary" :disabled="!canSaveMenu" @click="saveMenuPermissions">
        保存菜单
      </el-button>
    </div>

    <!-- 菜单权限树 -->
    <div flex-1 overflow-auto>
      <MenuPermission
        :init-status-count="initStatusCount"
        :full-menus="fullMenus"
        :role-menus="roleMenus"
      />
    </div>
  </div>
</template>

<style scoped>
.menu-tree {
  :deep() {
    .el-tree-node__children.has-button {
      display: flex;
      flex-wrap: wrap;
      padding-left: 36px;
      div {
        width: 220px;
        .el-tree-node {
          &__content {
            padding-left: 0 !important;
          }
        }
      }
    }
  }
}
</style>
