const { IView } = require('../../component/iview');
const echarts = require('echarts');
const { NumberMixin } = require('../../mixin/number');
const { repoCockpit } = require('../../repository/cockpit');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '首页');
        this.colors = ['#4472C4', '#ED7D31', '#996699', '#FFC000', '#CC6699', '#99CC00', '#663366'];
    }

    makePieChartOpts(name, { legend_width }) {

        return {

            title: {
                text: name,
                left: 'center',
                textStyle: { color: 'white' },
            },
            tooltip: {
                trigger: 'item',
            },
            legend: {
                bottom: 0,
                width: legend_width,
                itemWidth: 10,
                itemHeight: 10,
                textStyle: { color: 'white' },
            },
            color: this.colors,
            series: [
                {
                    name,
                    type: 'pie',
                    center: ['50%', '48%'],
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#0C1016',
                        borderWidth: 4,
                    },
                    label: {
                        show: false,
                        position: 'center',
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 24,
                            fontWeight: 'bold',
                        },
                    },
                    labelLine: {
                        show: false,
                    },
                    data: [],
                },
            ],
        };
    }

    createPieCharts($product, $stock, $future) {

        this.$cproduct = echarts.init($product);
        this.$cproduct.setOption(this.makePieChartOpts('产品资产结构', { legend_width: 200 }));
        
        this.$cstock = echarts.init($stock);
        this.$cstock.setOption(this.makePieChartOpts('股票资产结构', { legend_width: '100%' }));
        
        this.$cfuture = echarts.init($future);
        this.$cfuture.setOption(this.makePieChartOpts('期货资产结构', { legend_width: '100%' }));
    }

    makeMinutes() {

        let ranges = [

            { start: 930, end: 959 },
            { start: 1000, end: 1059 },
            { start: 1100, end: 1130 },
            { start: 1301, end: 1359 },
            { start: 1400, end: 1459 },
        ];

        let results = [];
        ranges.forEach(item => {
            for (let idx = item.start; idx <= item.end; idx++) {
                let minute = (idx <= 999 ? '0' : '') + idx;
                results.push(`${minute.substring(0, 2)}:${minute.substring(2)}`);
            }
        });

        results.push('15:00');
        return results;
    }

    /**
     * @param {Array<{ axisValue, color, value, seriesName, data, dataIndex }>} points 
     */
    formatNavTooltip(points) {

        let lines = points.map(x => `<span class="series-dot" style="background-color:${x.color};"></span>
                                    <span class="series-name">${x.seriesName}</span>
                                    <span class="series-value ${NumberMixin.methods.classBenefit(x.value)}">${x.value.toFixed(2)}%</span>`);

        return `<span class="nav-chart-tooltip">${points[0].axisValue}<br/>${lines.join('<br/>')}</span>`;
    }

    createNavChart($chart) {

        this.$cnav = echarts.init($chart);
        this.$cnav.setOption({

            title: {
                text: '净值预估',
                left: 'center',
                textStyle: { color: 'white' },
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: { type: 'cross' },
                formatter: this.formatNavTooltip.bind(this),
            },
            legend: {
                bottom: 0,
                itemWidth: 30,
                itemHeight: 10,
                padding: [0, 30, 0, 40],
                textStyle: { color: 'white' },
                type: 'scroll',
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                axisTick: { show: false },
                axisLabel: { 
                    color: 'white',
                    margin: 12, 
                    interval: 0,
                    formatter: function (value, index) {
                        return ['09:30', '10:00', '10:30', '11:00', '11:30', '13:30', '14:00', '14:30', '15:00'].indexOf(value) >= 0 ? value : '';
                    }
                },
                data: this.makeMinutes(),
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: 'white',
                    formatter: function (value, index) {
                        return `${value.toFixed(2)}%`;
                    }
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { 
                    lineStyle: { type: 'dashed', color: 'white', opacity: 0.2 },
                },
            },
            color: this.colors,
            series: [],
        });
    }

    /**
     * @param {{ name, value, color }} point 
     */
    formatAmountTooltip(point) {

        return `<span class="nav-chart-tooltip">
                    <span class="series-dot" style="background-color:${point.color};"></span>
                    <span class="series-name">${point.name}</span>
                    <span class="series-value ${NumberMixin.methods.classBenefit(point.value)}">${point.value.thousands()}</span>
                </span>`;
    }

    createTradeChart($chart) {

        this.$ctrade = echarts.init($chart);
        this.$ctrade.setOption({

            title: {
                text: '盯市盈亏',
                left: 'center',
                textStyle: { color: 'white' },
            },
            tooltip: {
                trigger: 'item',
                formatter: this.formatAmountTooltip.bind(this),
            },
            legend: {
                bottom: 0,
                itemWidth: 10,
                itemHeight: 10,
                textStyle: { color: 'white' },
            },
            grid: {
                left: '10%',
                containLabel: true
            },
            color: this.colors,
            xAxis: {
                type: 'category',
                axisLabel: { interval: 0, color: 'white', margin: 12 },
                data: ['股票盈亏', 'T0盈亏', '期货盈亏'],
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: 'white',
                    formatter: function (value, index) {
                        return value.thousands();
                    }
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { 
                    lineStyle: { type: 'dashed', color: 'white', opacity: 0.2 },
                },
            },
            series: [],
        });
    }

    getItemColor(params) {
        return this.colors[params.dataIndex];
    }

    /**
     * @param {{ name, value, color }} point 
     */
    formatAlgoTooltip(point) {
        
        let is_rate = point.name == '算法完成率';
        let value = is_rate ? point.value.toFixed(2) + '%' : point.value.thousands();
        let clsname = is_rate ? '' : NumberMixin.methods.classBenefit(point.value);
        return `<span class="nav-chart-tooltip">
                    <span class="series-dot" style="background-color:${point.color};"></span>
                    <span class="series-name">${point.name}</span>
                    <span class="series-value ${clsname}">${value}</span>
                </span>`;
    }

    createAlgoChart($chart) {

        this.$calgo = echarts.init($chart);
        this.$calgo.setOption({

            title: {
                text: '算法统计',
                left: 'center',
                textStyle: { color: 'white' },
            },
            tooltip: {
                trigger: 'item',
                formatter: this.formatAlgoTooltip.bind(this),
            },
            legend: {
                bottom: 0,
                itemWidth: 10,
                itemHeight: 10,
                textStyle: { color: 'white' },
            },
            grid: {
                left: '10%',
                containLabel: true
            },
            color: this.colors,
            xAxis: {
                type: 'category',
                axisLabel: { interval: 0, color: 'white', margin: 12 },
                data: ['总金额', '买入金额', '卖出金额', 'T0敞口', '算法完成率'],
            },
            yAxis: [
                {
                    type: 'value',
                    axisLabel: {
                        color: 'white',
                        formatter: function (value, index) {
                            return value.thousands();
                        }
                    },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { 
                        lineStyle: { type: 'dashed', color: 'white', opacity: 0.2 },
                    },
                },
                {
                    type: 'value',
                    axisLabel: {
                        color: 'white',
                        formatter: function (value, index) {
                            return `${value.toFixed(0)}%`;
                        }
                    },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { show: false },
                }
            ],
            series: [],
        });
    }

    /**
     * @param {number} value 
     * @param {number} precison 
     */
    tofixed(value, precison) {
        return typeof value == 'number' ? +value.toFixed(precison) : value;
    }

    async qcomposition() {

        let resp = await repoCockpit.qcomposition();
        let { errorCode, errorMsg, data } = resp;
        if (!(errorCode == 0 && this.helper.isJson(data))) {
            return;
        }

        // 产品汇总

        let opts1 = this.$cproduct.getOption();
        opts1.series[0].data = [

            { name: '股票市值', value: this.tofixed(data.stockMarketValue, 2) },
            { name: '期货市值', value: this.tofixed(Math.abs(data.futureMarketValue), 2) },
            { name: '股票可用资金', value: this.tofixed(data.stockAvailable, 2) },
            { name: '期货可用资金', value: this.tofixed(data.futureAvailable, 2) },
        ];

        this.$cproduct.setOption(opts1);
        
        // 股票

        let opts2 = this.$cstock.getOption();
        opts2.series[0].data = [

            { name: '股票市值', value: this.tofixed(data.stockMarketValue, 2) },
            { name: '可用资金', value: this.tofixed(data.stockAvailable, 2) },
            { name: '融券市值', value: this.tofixed(Math.abs(data.loanSellValue), 2) },
        ];

        this.$cstock.setOption(opts2);

        // 期货

        let opts3 = this.$cfuture.getOption();
        opts3.series[0].data = [

            { name: '空头市值', value: this.tofixed(Math.abs(data.sellMarketValue), 2) },
            { name: '多头市值', value: this.tofixed(data.buyMarketValue, 2) },
            { name: '可用资金', value: this.tofixed(data.futureAvailable, 2) },
        ];

        this.$cfuture.setOption(opts3);

        // 盯市

        let opts4 = this.$ctrade.getOption();
        let trade_series = {
            type: 'bar',
            barMaxWidth: 50,
            itemStyle: { normal: { color: this.getItemColor.bind(this) } },
            data: [data.stockProfit, data.t0Profit, data.futureProfit],
        };
        opts4.series = [trade_series];
        // opts4.series[0].data.forEach((x, idx) => opts4.series[0].data[idx] = this.helper.makeRandomNum(100000, 500000, false));
        this.$ctrade.setOption(opts4);
    }

    async qnav() {

        let resp = await repoCockpit.qnav();
        let { errorCode, errorMsg, data } = resp;
        if (!(errorCode == 0 && Array.isArray(data))) {
            return;
        }

        let opts = this.$cnav.getOption();
        opts.series = data.map(item => ({ type: 'line', symbol: 'none', name: item.fundName, data: item.navList }));
        this.$cnav.setOption(opts);
    }

    async qalgo() {

        let resp = await repoCockpit.qalgo();
        let { errorCode, errorMsg, data } = resp;
        if (!(errorCode == 0 && this.helper.isJson(data))) {
            return;
        }

        const { totalBalance, buyBalance, sellBalance, t0Exposure, finishRate } = data; 
        const opts = this.$calgo.getOption();
        let series_profit = {

            type: 'bar',
            barMaxWidth: 50,
            xAxisIndex: 0,
            yAxisIndex: 0,
            itemStyle: { normal: { color: this.getItemColor.bind(this) } },
            data: [totalBalance, buyBalance, sellBalance, t0Exposure],
        };

        let series_ratio = {

            type: 'bar',
            barGap: '-100%',
            barMaxWidth: 50,
            xAxisIndex: 0,
            yAxisIndex: 1,
            itemStyle: { normal: { color: this.getItemColor.bind(this) } },
            data: [,,,,typeof finishRate == 'number' ? finishRate * 100 : finishRate],
        };

        opts.series = [series_profit, series_ratio];
        // opts.series[0].data.forEach((x, idx) => opts.series[0].data[idx] = this.helper.makeRandomNum(100000, 500000, false));
        // opts.series[1].data.forEach((x, idx) => opts.series[1].data[idx] = this.helper.makeRandomNum(0, 100));

        this.$calgo.setOption(opts);
    }

    handleResize(width, height) {
        this.$cnav.resize();
    }

    listen2Events() {

        this.listen2WinResized(this.handleResize.bind(this));
        this.registerEvent('on-view-visible', () => { this.$cnav.resize(); });
        this.lisen2WinSizeChange(() => { this.$cnav.resize(); });
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.qcomposition();
        this.qnav();
        this.qalgo();
    }
    
    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    config() {
        this.interaction.showMessage('该页面未提供设置');
    }

    build($container) {

        super.build($container);

        const $ref = this.$container;
        const $top = $ref.querySelector('.pie-chart-area');
        const $bottom = $ref.querySelector('.algo-chart-area');
        
        let $product = $top.querySelector('.chart-product');
        let $stock = $top.querySelector('.chart-stock');
        let $future = $top.querySelector('.chart-future');
        let $nav = $ref.querySelector('.nav-chart-area');
        let $trade = $bottom.querySelector('.chart-trade');
        let $algo = $bottom.querySelector('.chart-algo');

        this.createPieCharts($product, $stock, $future);
        this.createNavChart($nav);
        this.createTradeChart($trade);
        this.createAlgoChart($algo);
        this.listen2Events();
        
        this.qcomposition();
        this.qnav();
        this.qalgo();
    }
}

module.exports = View;
