const { TypicalDataView } = require('../classcial/typical-data-view');
const { ModelConverter } = require('../../../model/model-converter');
const { TradeRecord } = require('../../../model/trade-record');
const { ExchangeTitles } = require('./bases/trade-record-title');
const { repoTradeRecord } = require('../../../repository/traderecord');

class View extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '成交记录');
        this.titles = ExchangeTitles;
    }

    /**
     * @param {TradeRecord} record 
     */
    testRecords(record) {
        
        var kw = this.states.keywords;
        return this.tableObj.matchKeywords(record) 
            || this.testPy(record.instrumentName, kw)
            || this.testPy(record.strategyName, kw)
            || this.testPy(record.fundName, kw)
            || this.testPy(record.accountName, kw)
            || this.testPy(record.userName, kw);
    }

    handleContextChange(belongId) {

        if (belongId === this.belongId) {
            return;
        }

        this.belongId = belongId;
        this.tableObj.clear();
        
        if (this.helper.isNone(belongId)) {
            this.stop2Refresh();
        }
        else {
            
            this.resetControls();
            this.requestRecords();
            this.resume2Refresh();
        }
    }

    async requestRecords() {

        var resp = await repoTradeRecord.requestBelongExchanges(this.belongId);
        var records = (resp.data || {}).contents || [];
        records.shift();
        var exchanges = ModelConverter.formalizeTradeRecords(this.titles, records);
        this.tableObj.refill(exchanges);
    }

    refresh() {
        
        if (this.helper.isNotNone(this.belongId)) {
            super.refresh();
        }
    }

    build($container, options) {

        super.build($container, options, {

            tableName: 'smt-fbe',
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
        });

        /** 监听上下文切换 */
        this.registerEvent('set-context', this.handleContextChange.bind(this));
        this.stop2Refresh();
        this.timelyRefresh(1000 * 30);
    }
}

module.exports = View;