<script setup lang="ts">
import { type ComponentTab } from '@/types';
import { computed, defineAsyncComponent, onMounted, ref, watch } from 'vue';
const { tabs } = defineProps<{
  tabs: ComponentTab[];
}>();

const components: Record<string, unknown> = {};

const activeName = ref('');

tabs.forEach(tab => {
  components[tab.component] = defineAsyncComponent(
    () => import(`./ComponentTabs/${tab.component}.vue`),
  );
});

const componentName = computed(() => components[activeName.value]);

const activeTab = computed(() => tabs.find(tab => tab.component === activeName.value));

const visibleTabs = computed(() => tabs.filter(tab => !tab.show || tab.show()));

watch(
  () => visibleTabs.value,
  newTabs => {
    if (!newTabs.some(tab => tab.component === activeName.value)) {
      activeName.value = newTabs[0]?.component ?? '';
    }
  },
  { immediate: true },
);

onMounted(() => {
  activeName.value = visibleTabs.value[0].component;
});
</script>

<template>
  <div class="component-tabs" h-full flex flex-col of-y-hidden>
    <el-tabs type="card" v-model="activeName">
      <el-tab-pane
        v-for="tab in visibleTabs"
        :key="tab.component"
        :label="tab.label"
        :name="tab.component"
      />
    </el-tabs>
    <KeepAlive>
      <component
        flex-1
        min-h-1
        of-hidden
        :is="componentName"
        v-bind="activeTab?.props || {}"
        v-on="activeTab?.events || {}"
      ></component>
    </KeepAlive>
  </div>
</template>

<style scoped>
.component-tabs {
  .el-tabs {
    background-color: var(--g-block-bg-5);
  }
  :deep() {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        .el-tabs__nav-scroll {
          .el-tabs__nav {
            .el-tabs__item {
              &.is-active {
                background-color: var(--g-active);
              }
              &:hover {
                background-color: var(--g-bg-hover-4);
              }
            }
          }
        }
      }
    }
  }
}
</style>
