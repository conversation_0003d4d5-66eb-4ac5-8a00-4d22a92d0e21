<div class="scan-view-root themed-box s-full-height">
    
    <div class="scan-toolbar">

        <el-input placeholder="扫单文件夹路径" v-model="folder" class="scan-folder" clearable></el-input>
        <el-button type="primary" class="s-mgl-10" @click="chooseSetFolder">选取路径</el-button>
        <el-input placeholder="扫单频率" v-model.number="scanFrequency" class="scan-frequency s-mgl-10" clearable></el-input>
        <label class="scan-frequency-unit s-pdl-10">毫秒</label>

        <el-select v-model="accountIds" 
                    @change="handleAccountsChange"
                    :disabled="running"
                    class="scan-account s-mgl-10" 
                    placeholder="请选择扫单账号" multiple filterable clearable collapse-tags>
                    
            <el-option v-for="(item, item_idx) in accounts"
                       :key="item_idx"
                       :value="item.id"
                       :label="item.name"></el-option>
        </el-select>

        <el-button v-if="running" type="danger" class="s-mgl-10" @click="stopScan">停止扫单</el-button>
        <el-button v-else type="primary" class="s-mgl-10" @click="startScan">启动扫单</el-button>
        
		<el-tooltip>
            <span slot="content">
                <!-- <span>每{{ downloadFrequency }}秒，软件将自动导出一次</span>
                <label class="s-color-red" v-if="lastDownloadTime">，最近导出：{{ lastDownloadTime }}</label>
                <label class="s-color-red" v-else>，即将自动导出</label> -->
                <label class="s-color-red" v-if="lastDownloadTime">最近导出：{{ lastDownloadTime }}</label>
            </span>
            <el-button type="primary" class="purple-btn" @click="downloadAccountData">
                下载最新账号数据 <i class="el-icon-download"></i>
            </el-button>
        </el-tooltip>

        <!-- <el-button type="primary" @click="toggleShowHistory">
            <span v-if="showHistory">隐藏委托文件 <i class="el-icon-arrow-up"></i></span>
            <span v-else>查看委托文件 <i class="el-icon-arrow-down"></i></span>
        </el-button> -->

        <el-tooltip>
            <span slot="content">
                <span>每{{ refreshFrequency }}秒，软件将自动刷新一次</span>
                <label class="s-color-red" v-if="lastRefreshTime">，最近刷新：{{ lastRefreshTime }}</label>
                <label class="s-color-red" v-else>，即将自动刷新</label>
            </span>
            <el-button type="success" @click="refreshHistory">刷新列表</el-button>
        </el-tooltip>

        <el-button @click="downloadTemplate" type="success">下载模板 <i class="el-icon-download"></i></el-button>

    </div>

    <div class="scan-notice" v-show="network.showNetworkNotice === true">

        <el-button v-if="network.connected"
					type="success"
					@click="closeNetworkNotice">知道了</el-button>
					
        <el-alert :title="network.connected ? '网络正常' : '网络错误'"
                  :description="network.connected ? 
                               '网络连接已恢复，您可以尝试（停止/启动）文件扫单功能，当前为状态为 --- ' + (network.isScanRunning ? '运行中' : '已停止') : 
                               '网络发生断开，系统正常尝试重连...'"
                  :type="network.connected ? 'success' : 'error'"
                  :closable="false"
                  show-icon></el-alert>
    </div>

    <div class="table-scan-history">
        <table>
			<tr>
				<th label="文件解析时间" prop="resolveTime" min-width="150" sortable></th>
				<th label="文件名称" prop="fileName" min-width="300" sortable searchable overflow-tooltip></th>
				<th label="操作类型" prop="operType" min-width="100" sortable></th>
			</tr>
		</table>
    </div>
 
</div>