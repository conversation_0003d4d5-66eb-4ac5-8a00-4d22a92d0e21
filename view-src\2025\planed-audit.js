const { IView } = require('../../component/iview');
const { PlanTradeMotherOrder, PlanTradeChildOrder } = require('../../model/planed-audit');
const { PlanedAuditRepository } = require('../../repository/planed-audit');
const { NumberMixin } = require('../../mixin/number');

class PlanedAuditView extends IView {
    constructor(view_name) {

        super(view_name, false, '计划审核');
        this.motherOrders = [new PlanTradeMotherOrder({})].slice(1);
        this.childOrders = [new PlanTradeChildOrder({})].slice(1);
        this.states = { current: null };
        this.repo = new PlanedAuditRepository();
    }

    /**
     * @param {PlanTradeMotherOrder} data
     */
    typedParent(data) {
        return data;
    }

    /**
     * @param {PlanTradeChildOrder} data
     */
    typedSon(data) {
        return data;
    }

    async requestMotherOrders() {

        const resp = await this.repo.queryMotherOrders();
        const { status, listParent } = resp;
        const { isSuccess, responseCode, responseMsg, warnMsg } = status || {};

        if (isSuccess && responseCode == '0000') {

            this.motherOrders.length = 0;
            this.motherOrders.push(...listParent);
            this.interaction.showSuccess(`母单列表查询成功：${listParent.length}条数据`);
        } 
        else {
            this.interaction.showError(`母单查询失败：${responseCode}/${responseMsg || warnMsg}`);
        }

        if (this.motherOrders.length > 0) {

            try {

                const first = this.motherOrders[0];
                this.states.current = first;
                this.vueIns.$refs.$mother.setCurrentRow();
                this.requestChildOrders(first.aopId);
            }
            catch (ex) {
                console.error(ex);
            }
        }
        else {

            this.childOrders.length = 0;
            this.states.current = null;
        }
    }

    async requestChildOrders(mother_order_id) {

        const resp = await this.repo.queryChildOrders(mother_order_id);
        const { status, listDetail } = resp;
        const { isSuccess, responseCode, responseMsg, warnMsg } = status || {};

        if (isSuccess && responseCode == '0000') {

            this.childOrders.length = 0;
            this.childOrders.push(...listDetail);
            this.interaction.showSuccess(`母单详情查询成功：${listDetail.length}条数据`);
        } 
        else {
            this.interaction.showError(`子单查询失败：${responseCode}/${responseMsg || warnMsg}`);
        }
    }

    /**
     * @param {PlanTradeMotherOrder} row 
     */
    handleSelectMotherOrder(row) {

        this.states.current = row;
        this.requestChildOrders(row.aopId);
    }

    formatCurrentMotherInfo() {

        if (!this.states.current) {
            return '';
        }

        const { aopId, fileName, prodName, prodCode } = this.typedParent(this.states.current);
        return `(母单ID：${aopId}，文件名：${fileName || 'N/A'}，产品名称：${prodName || 'N/A'}，产品代码：${prodCode || 'N/A'})`;
    }


    /**
     * @param {PlanTradeMotherOrder} a 
     * @param {PlanTradeMotherOrder} b 
     */
    sortAuditStatus(a, b, c) {
        return this.helper.compare(a.auditStatus, b.auditStatus);
    }

    /**
     * @param {PlanTradeMotherOrder} a 
     * @param {PlanTradeMotherOrder} b 
     */
    sortAuditMsg(a, b, c) {
        return this.helper.compare(a.auditMsg, b.auditMsg);
    }

    /**
     * @param {PlanTradeMotherOrder} a 
     * @param {PlanTradeMotherOrder} b 
     */
    sortOrderStatus(a, b, c) {
        return this.helper.compare(a.orderStatus, b.orderStatus);
    }

    /**
     * @param {PlanTradeMotherOrder} a 
     * @param {PlanTradeMotherOrder} b 
     */
    sortValidateStatus(a, b, c) {
        return this.helper.compare(a.validateStatus, b.validateStatus);
    }

    thousands(row, column) {
        return NumberMixin.methods.thousands(row[column.property], false, 2);
    }

    thousandsInt(row, column) {
        return NumberMixin.methods.thousands(row[column.property], true);
    }

    createApp() {

        this.vueIns = new Vue({
            el: this.$container.querySelector('.planed-audit'),
            data: {
                parentOrders: this.motherOrders,
                childOrders: this.childOrders,
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleSelectMotherOrder,
                this.formatCurrentMotherInfo,
                this.sortAuditStatus,
                this.sortAuditMsg,
                this.sortOrderStatus,
                this.sortValidateStatus,
                this.thousands,
                this.thousandsInt,
            ]),
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.requestMotherOrders();
    }
}

module.exports = PlanedAuditView;
