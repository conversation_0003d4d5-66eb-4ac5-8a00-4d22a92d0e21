<div class="algo-design">
    <el-dialog
        class="algo-param-design-dialog"
        :title="dialog.isEditing ? '算法参数维护' : '已有参数'"
        :width="dialog.isEditing ? '520px' : '1000px'"
        :show-close="true"
        v-bind:close-on-click-modal="false" 
        v-bind:close-on-press-escape="false"
        :visible="dialog.visible"
        @close="close"
        v-drag
    >
        <template v-if="dialog.isEditing">
            <div class="s-pd-20">
                <el-form
                    ref="form"
                    class="parameter-form"
                    size="mini"
                    label-position="left"
                    label-width="80px"
                    :model="form"
                    :rules="rules"
                >
                    <el-form-item label="中文名称" prop="label">
                        <el-input size="mini" v-model.trim="form.label" clearable></el-input>
                    </el-form-item>
    
                    <el-form-item label="英文名" prop="prop">
                        <el-input v-model.trim="form.prop" clearable></el-input>
                    </el-form-item>
    
                    <el-form-item label="数据类型" prop="dataType">
                        <el-radio-group v-model="form.dataType" @change="handleDataTypeChange">
                            <el-radio v-for="(item, index) in dataTypes" :key="index" :label="item.value">{{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
    
                    <el-form-item label="默认值" prop="defaultValue" class="input-default-value">
                        <template v-if="isInteger(form.dataType)">
                            <el-input-number v-model.number="form.defaultValue" :precision="0" clearable></el-input-number>
                        </template>
                        <template v-else-if="isDecimal(form.dataType)">
                            <el-input-number v-model.number="form.defaultValue" :precision="2" clearable></el-input-number>
                        </template>
                        <template v-else-if="isTime(form.dataType)">
                            <el-time-picker v-model="form.defaultValue" placeholder="时间" clearable></el-time-picker>
                        </template>
                        <template v-else-if="isTimeRange(form.dataType)">
                            <el-time-picker v-model="form.defaultValue" range-separator="至" start-placeholder="开始" end-placeholder="结束" is-range clearable></el-time-picker>
                        </template>
                        <template v-else-if="isText(form.dataType)">
                            <el-input v-model.trim="form.defaultValue" clearable></el-input>
                        </template>
                        <template v-else-if="isUserOption(form.dataType)">
                            <div class="select-user-options">
                                <el-select v-model="form.defaultValue" :popper-append-to-body="false" clearable>
                                    <el-option 
                                        v-for="(item2, item2_idx) in form.uoptions" 
                                        :key="item2_idx" 
                                        :label="item2.label" 
                                        :value="item2.value">
                                        <span class="option-item">
                                            <span class="option-item-label">{{ item2.label }}</span>
                                            <span class="option-item-delete" @click.stop="deleteOption(item2.value)">
                                                <i class="el-icon-close"></i>
                                            </span>
                                        </span>
                                    </el-option>
                                </el-select>
                                <el-tooltip content="添加选项">
                                    <i class="add-button el-icon-plus" @click="addNewOption"></i>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-form-item>
    
                    <template v-if="dialog.adding.visible">
                        <el-form-item label="选项文字" prop="optionLabel">
                            <el-input v-model.trim="form.optionLabel" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="选项取值" prop="optionValue">
                            <el-input v-model.trim="form.optionValue" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button size="small" type="primary" @click="confirmNewOption">确定添加选项</el-button>
                            <el-button size="small" @click="cancelNewOption">取消</el-button>
                        </el-form-item>
                    </template>

                    <el-form-item label="是否必填" prop="required">
                        <el-radio-group v-model="form.required" size="small">
                            <el-radio-button :label="true" >必填</el-radio-button>
                            <el-radio-button :label="false">非必填</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
    
                    <el-form-item label="参数描述">
                        <el-input type="textarea" :rows="2" v-model.trim="form.remark" clearable></el-input>
                    </el-form-item>
    
                </el-form>
            </div>
            <div v-if="!dialog.adding.visible" slot="footer">
                <el-button size="small" type="primary" @click="save">确定</el-button>
                <el-button size="small" @click="close">取消</el-button>
                <el-button size="small" type="success" @click="go2table" style="width: 120px;">
                    <i class="iconfont icon-zhankai"></i>
                    <span>查看已有参数</span>
                </el-button>
            </div>
        </template>
        <template v-else>
            <div class="s-pd-20">
                <el-table max-height="800" :data="parameters">
                    <el-table-column label="中文名称" min-width="100px" prop="label" sortable show-overflow-tooltip></el-table-column>
                    <el-table-column label="英文名称" min-width="100px" prop="prop" sortable show-overflow-tooltip></el-table-column>
                    <el-table-column label="数据类型" min-width="80px" prop="dataType" sortable>
                        <template slot-scope='props'>{{ formatDataType(props.row) }}</template>
                    </el-table-column>
                    <el-table-column label="默认值" min-width="120px" prop="defaultValue" sortable show-overflow-tooltip>
                        <template slot-scope='props'>{{ formatDefaultValue(props.row) }}</template>
                    </el-table-column>
                    <el-table-column label="参数描述" min-width="200px" prop="remark" sortable show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" min-width="60px">
                        <template slot-scope='props'>
                            <a class="el-icon-edit" @click="edit(props.row)" title="编辑"></a>
                            <a class="el-icon-delete s-color-red" @click="remove(props.row)" title="删除"></a>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div slot="footer">
                <el-button size="small" type="primary" @click="refresh">
                    <span>刷新</span>
                </el-button>
                <el-button size="small" type="success" @click="goback">
                    <i class="iconfont icon-zhankai"></i>
                    <span>返回</span>
                </el-button>
            </div>
        </template>
    </el-dialog>
</div>