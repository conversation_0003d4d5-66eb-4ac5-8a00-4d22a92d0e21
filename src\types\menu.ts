import type { MomMenuTree, MomPermission } from '../../../xtrade-sdk/dist';

/** 角色菜单权限接口 */
export interface RoleMenuPermission {
  menuId: number;
  permissionIds: number[];
}

/** 角色菜单 */
export interface RoleMenuPermissionTree extends MomMenuTree {
  checked: boolean;
  menListPermission?: RoleMomPermission[];
  children?: RoleMenuPermissionTree[];
}

export interface RoleMomPermission extends MomPermission {
  checked: boolean;
}
