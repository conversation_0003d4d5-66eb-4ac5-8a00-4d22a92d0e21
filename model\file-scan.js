class TypedEntrust {

    constructor(entrust_type) {
        /**
         * 扫单委托类型（normal普通委托 | algo算法委托）
         */
        this.entrust_type = entrust_type;
    }
}

class NormalUserEntrust extends TypedEntrust {

    constructor({ entrust_type, instrument, asset_type, direction, price, price_type, volume, position_effect, hedge_flag, product_id, account_id, customId, remark }) {
        
        super(entrust_type);
        this.instrument = instrument;
        this.asset_type = asset_type;
        this.direction = direction;
        this.price = price;
        this.price_type = price_type;
        this.volume = volume;
        this.position_effect = position_effect;
        this.hedge_flag = hedge_flag;
        this.product_id = product_id;
        this.account_id = account_id;
        this.customId = customId;
        this.remark = remark;
    }
}

class AlgoUserEntrust extends TypedEntrust {

    constructor({ entrust_type, product_id, strategy_id, account_id, algo_id, instrument, instrument_name, direction, volume, start_time, end_time, params, remark }) {

        super(entrust_type);
        this.product_id = product_id;
        this.strategy_id = strategy_id;
        this.account_id = account_id;
        this.algo_id = algo_id;
        this.instrument = instrument;
        this.instrument_name = instrument_name;
        this.direction = direction;
        this.volume = volume;
        this.start_time = start_time;
        this.end_time = end_time;
        this.params = params;
        this.remark = remark;
        this.limited_on = limited_on;
        this.expired_on = expired_on;
    }
}

module.exports = {

    NormalUserEntrust,
    AlgoUserEntrust,
};