const { IView } = require('../../../component/iview');
const { TabList } = require('../../../component/tab-list');
const { Tab } = require('../../../component/tab');
const { repoRole } = require('../../../repository/role');
const { YuLiangAccount } = require('../../../model/external/yuliang');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '宇量算法交易');
        this.accounts = [new YuLiangAccount({})].splice(1);
        this.states = { opened: true };
        this.openeds = {};
    }

    setup() {
        
        const app = new Vue({

            el: this.$container.querySelector('.yuliang-root'),
            data: {
                accounts: this.accounts,
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.open, this.toggle]),
        });

        
        app.$nextTick(() => {

            this.setupTab();
            this.customizeWindow();
        });
    }

    setupTab() {

        const $container = this.$container;
        this.tabc = new TabList({

            hideTab4OnlyOne: false,
            lazyLoad: false,
            cannotCloseLastOne: false,
            $navi: $container.querySelector('.tabs-box'),
            $content: $container.querySelector('.pages-box'),
            tabClosed: this.tabClosed.bind(this),
        });
    }

    customizeWindow() {

        this.thisWindow.maximize();
        this.renderProcess.emit('toggle-menu');
    }

    toggle() {
        this.states.opened = !this.states.opened;
    }

    /**
     * @param {YuLiangAccount} account 
     */
    async open(account) {

        // 打开某个账号时，始终自动隐藏账号列表区块
        this.states.opened = false;

        if (this.openeds[account.accountId]) {

            let matched = this.tabc.tabs.find(x => x.options.title == account.accountName);            
            matched && this.tabc.setFocus(matched);
            return;
        }

        let resp = await repoRole.qYuLiangPageUrl(this.menuId, account.accountId);
        let { data, errorCode, errorMsg } = resp;

        if (errorCode != 0) {
            this.interaction.showError(`账号登录失败，${errorCode}/${errorMsg}`);
            return;
        }
        else if (typeof data != 'string' || data.length == 0) {
            this.interaction.showError(`账号登录信息缺失`);
            return;
        }

        let resp2 = JSON.parse(data);
        let url = resp2.data;
        let ecode = resp2.errorCode;
        let emsg = resp2.errorMsg;

        if (ecode != 0 || typeof url != 'string' || url.length == 0) {
            this.interaction.showError(`账号登录URL解析失败，${ecode}/${emsg}`);
            return;
        }

        console.log('to open page = ' + url);
        this.tabc.openTab(true, url, account.accountName, this.helper.deepClone(account));
        this.openeds[account.accountId] = true;
    }

    /**
     * @param {Tab} tab 
     */
    tabClosed(tab) {

        let which = this.typeds(tab.viewOptions);
        if (which) {
            delete this.openeds[which.accountId];
        }
    }

    /**
     * @param {YuLiangAccount} data 
     */
    typeds(data) {
        return data;
    }

    async requestAccounts() {
        
        let $loading = this.interaction.showLoading({ text: `获取账号列表...` });

        try {
            
            let resp = await repoRole.qYuLiangAccounts(this.menuId);
            let { data, errorCode, errorMsg } = resp;
            if (errorCode === 0 && data instanceof Array) {

                let accounts = data.map(x => new YuLiangAccount(x));
                this.accounts.refill(accounts.sort((a, b) => this.helper.compare(a.accountName, b.accountName)));
            } 
            else {
                this.interaction.showError(`账号获取失败，${errorCode}/${errorMsg}`);
            }
        }
        catch (ex) {

            this.interaction.showError(`账号获取异常`);
            console.error(ex);
        }
        finally {
            $loading.close();
        }
    }

    build($container, options) {

        super.build($container);
        let { menuId, menuName } = options || {};
        this.menuId = menuId;
        this.menuName = menuName;
        this.setup();
        this.requestAccounts();
    }
}

module.exports = View;
