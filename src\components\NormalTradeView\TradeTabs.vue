<script setup lang="ts">
import { ref, shallowRef, useTemplateRef, watch } from 'vue';
import TradePanel from './TradeTabs/TradePanel.vue';
import TradeLevels from './TradeTabs/TradeLevels.vue';
import TradeDirection from './TradeTabs/TradeDirection.vue';
import type { TradeChannel, StandardTick } from '@/types';
import { TradeDirectionEnum } from '@/enum/trade';
import { TickType } from '../../../../xtrade-sdk';
import { TickService } from '@/api';

const { activeChannel, activeAccount } = defineProps<{
  activeChannel: TradeChannel;
  activeAccount?: any;
}>();

// 最新tick
const lastTick = shallowRef<StandardTick>();

// 交易方向：买入/卖出
const direction = ref(TradeDirectionEnum.买入); // 默认买入

// 引用TradePanel组件
const tradePanelRef = useTemplateRef('tradePanelRef');

// 先取消订阅旧合约tick行情，再订阅新合约行情
watch(
  () => tradePanelRef.value?.selectedInstrument,
  async (ins, oldIns) => {
    if (oldIns) {
      await TickService.unsubscribeTick(oldIns!.instrument, TickType.tick, updateTick);
      lastTick.value = undefined;
    }
    if (ins) {
      TickService.subscribeTick(ins.instrument, TickType.tick, updateTick);
    }
  },
);

const updateTick = (data: StandardTick) => {
  lastTick.value = data;
};

// 处理点击价格档位事件
const handleClickLevel = (price: number) => {
  if (tradePanelRef.value) {
    tradePanelRef.value.price = price;
  }
};
</script>

<template>
  <div flex="~ col">
    <!-- 买入/卖出切换 -->
    <TradeDirection v-model="direction" />
    <!-- 交易面板 -->
    <el-scrollbar>
      <div flex="~ 1" min-h-1>
        <!-- 左侧交易面板 -->
        <TradePanel
          w-270
          :last-tick="lastTick"
          :direction="direction"
          :active-account="activeAccount"
          :active-channel="activeChannel"
          ref="tradePanelRef"
        />
        <!-- 右侧价格区域 -->
        <div flex-1>
          <TradeLevels :last-tick="lastTick" @click-level="handleClickLevel" />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped></style>
