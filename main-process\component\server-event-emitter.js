﻿
const EventEmitter = require('events');
class ServerEventEmitter extends EventEmitter {

    constructor(event_handler_kv) {

        super();
        if(event_handler_kv) {
            this.registerCoreHandlers(event_handler_kv);
        }        
    }

    registerCoreHandlers(event_handler_kv) {

        for (var event_name in event_handler_kv) {
            this.register(event_name, event_handler_kv[event_name]);
        }
    }

    register(event_name, handler) {

        if((typeof event_name === 'string' || typeof event_name === 'number') && typeof handler == 'function') {
            this.on(event_name, handler);
        }
        else {
            console.error(`cannot register event with given event name = [${event_name}] and handler`);
        }
    }

    unregister(event_name, handler) {
        
        if((typeof event_name === 'string' || typeof event_name === 'number')) {
            typeof handler == 'function' ? this.removeListener(event_name, handler) : this.removeAllListeners(event_name);
        }
        else {
            console.error(`cannot unregister event with given event name = [${event_name}] and handler`);
        }
    }
}

module.exports = { ServerEventEmitter };