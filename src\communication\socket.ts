import Utils from '../modules/utils';
import { CallbackRegisterType, ICommunication, SocketError } from './protocol';
import { ServerFunction } from '../config/server-function';
import { MessageDataType, SocketServerType } from '../config/architecture';
import { ServerEvent } from '../config/server-event';
import { LoginResult } from '../config/core';
import { SocketDataPackage } from '../types/data-package';
import { DynamicEventService } from '../middleware/dynamic-event-service';
import { GetLogger } from '../global-state';
import { UserLoginResponse } from '../types/table/admin';

type SocketNoParamCallbackFunction = { once: boolean, callback: () => void };
type SocketErrorCallbackFunction = { once: boolean, callback: (err: SocketError) => void };

const defaultLogger = GetLogger();

/**
 * 全局请求ID标识（保证全局不重复递增）
 */
const globalRequestId = { value: 0 };

/**
 * Socket 通信类
 */
export class SocketCommunication implements ICommunication {

    private _server_type: string;
    private _host: string;
    private _port: number;
    private _hbTask?: NodeJS.Timeout;

    private connectedCallbacks: SocketNoParamCallbackFunction[] = [];
    private closedCallbacks: SocketNoParamCallbackFunction[] = [];
    private timeoutedCallbacks: SocketNoParamCallbackFunction[] = [];
    private erroredCallbacks: SocketErrorCallbackFunction[] = [];
    
    protected msgService: DynamicEventService;

    get host() {
        return this._host;
    }

    get port() {
        return this._port;
    }

    get server() {
        return `${this.host}:${this.port}`;
    }

    get serverType() {
        return this._server_type;
    }

    get nextReqId() {
        return (++globalRequestId.value);
    }

    constructor(server_type: string, host: string, port: number) {

        this._server_type = server_type;
        this._host = host;
        this._port = port;
        this.msgService = new DynamicEventService();
    }

    protected startHeartBeat() {

        this._hbTask = setInterval(() => {
            this.send({ fc: ServerFunction.HeartBeat, reqId: 0, dataType: MessageDataType.text });
        }, 5000);
    }

    protected stopHeartBeat() {
        clearInterval(this._hbTask);
    }

    protected handleConnected() {
        
        const callbacks = this.connectedCallbacks;

        if (callbacks.length == 0) {
            defaultLogger.warn('Server connected (no callbacks)', this.serverType, this.server);
        }
        else {

            callbacks.forEach(cbk => {
                try { cbk.callback(); } catch(ex) { this.outputCallbackException('connect', cbk.callback, ex); }
            });

            Utils.remove(callbacks, cbk => !!cbk.once);
        }
    }

    protected handleClosed() {

        this.stopHeartBeat();
        const callbacks = this.closedCallbacks;

        if (callbacks.length == 0) {
            defaultLogger.warn('Server disconnected (no callbacks)', this.serverType, this.server);
        }
        else {

            callbacks.forEach(cbk => {
                try { cbk.callback(); } catch(ex) { this.outputCallbackException('close', cbk.callback, ex); }
            });

            Utils.remove(callbacks, cbk => !!cbk.once);
        }
    }

    protected handleTimeouted() {

        this.stopHeartBeat();
        const callbacks = this.timeoutedCallbacks;

        if (callbacks.length == 0) {
            defaultLogger.warn('Server connection timeout (no callbacks)', this.serverType, this.server);
        }
        else {

            callbacks.forEach(cbk => {
                try { cbk.callback(); } catch(ex) { this.outputCallbackException('timeout', cbk.callback, ex); }
            });

            Utils.remove(callbacks, cbk => !!cbk.once);
        }
    }

    protected handleErrored(err: SocketError) {

        this.stopHeartBeat();
        const callbacks = this.erroredCallbacks;

        if (callbacks.length == 0) {
            defaultLogger.warn('Server connection error (no callbacks)', err, this.serverType, this.server);
        }
        else {

            callbacks.forEach(cbk => {
                try { cbk.callback(err); } catch(ex) { this.outputCallbackException('error', cbk.callback, ex); }
            });

            Utils.remove(callbacks, cbk => !!cbk.once);
        }
    }

    private outputCallbackException(event: string, callback: Function, ex: unknown) {
        defaultLogger.error('Server event callback calling exception', event, callback.name || '[anonymous]', ex);
    }

    protected handleMessageReceived(data: SocketDataPackage) {

        if (data.fc == ServerEvent.HeartBeat) {

            defaultLogger.trace('Received server heart beating message');
            return;
        }

        defaultLogger.trace('Received a data message', data.fc, data.fcEvent, data);
        this.msgService.fire(data.fc, data);
    }

    /**
     * 预检数据包结构完整性，并补充缺失的字段
     */
    protected precheck(message: SocketDataPackage) {

        let { fc, reqId, dataType, body } = message;

        // 参数合法性检查

        if (fc === null || fc === undefined) {
            fc = 0;
        }

        if (body === null || body === undefined) {
            body = '';
        }

        if (reqId === null || reqId === undefined) {
            reqId = (globalRequestId.value++);
        }

        if (dataType === null || dataType === undefined) {
            dataType = Utils.isJson(body) ? MessageDataType.json : MessageDataType.text;
        }

        //  附带设置功能码名称
        let fcEvent = ServerFunction[fc];
        Object.assign(message, { fc, fcEvent, reqId, dataType, body });
    }

    injectMessage(serverEvent: ServerEvent, data: SocketDataPackage) {
        this.msgService.fire(serverEvent, data);
    }

    onConnected(rgtype: CallbackRegisterType, callback?: () => void) {
        this.registerCallback(this.connectedCallbacks, rgtype, callback);
    }

    onTimeouted(rgtype: CallbackRegisterType, callback?: () => void) {
        this.registerCallback(this.timeoutedCallbacks, rgtype, callback);
    }

    onErrored(rgtype: CallbackRegisterType, callback?: (err: SocketError) => void) {
        this.registerCallback(this.erroredCallbacks, rgtype, callback);
    }

    onClosed(rgtype: CallbackRegisterType, callback?: () => void) {
        this.registerCallback(this.closedCallbacks, rgtype, callback);
    }

    private registerCallback(targets: { once: boolean, callback: Function }[], rgtype: CallbackRegisterType, callback?: Function) {

        if (rgtype == 'bind' || rgtype == 'once') {
            
            if (typeof callback == 'function') {

                const matched = targets.find(cbk => cbk.callback === callback);
                if (matched == undefined) {
                    targets.push({ once: rgtype == 'once', callback });
                }
            }
        }
        else if (rgtype == 'unbind') {

            if (typeof callback == 'function') {
                Utils.remove(targets, cbk => cbk.callback === callback);
            }
            else {

                while(targets.length > 0) {
                    targets.pop();
                }
            }
        }
    }

    subscribe(server_event: number | string, callback: (data: SocketDataPackage) => void): void {
        this.msgService.sub(server_event, callback);
    }

    unsubscribe(server_event: number | string, callback?: (data: SocketDataPackage) => void): void {
        this.msgService.unsub(server_event, callback);
    }

    /**
     * 登录Socket服务器
     * @param userName - 用户名
     * @param password - 密码
     * @param mac - MAC地址
     * @param os - 操作系统信息
     */
    async signin(userName: string, password: string, mac: string, os: string) {
        
        const dataBody = { userName, password, mac, os, sessionId: '0' };
        const thisObj = this;

        return new Promise<UserLoginResponse>(resolve => {

            /**
             * 登录成功后，定时发送心跳包，保持活跃状态
             */
            function keepAlive(data: SocketDataPackage) {
                
                const userInfo = data.body! as UserLoginResponse;
                const isOk = userInfo.errorCode == 0;

                /**
                 * 如果登录成功，则开启心跳包发送
                 */

                if (isOk) {

                    defaultLogger.info('Logon succeed, to keep alive!', userInfo, thisObj.serverType, thisObj.server);
                    thisObj.stopHeartBeat();
                    thisObj.startHeartBeat();
                }
                else {
                    defaultLogger.info('Logon failed, do not keep alive!', userInfo, thisObj.serverType, thisObj.server);
                }
            }

            /**
             * 完成登录
             */
            function finishSignIn(data: SocketDataPackage) {
                
                const response = (data.body || {}) as UserLoginResponse;
                const { errorCode } = response;
                const matched = Object.values(LoginResult).find(x => x.Value == errorCode);
                response.errorMsg = matched ? matched.Label : null;
                resolve(response);
            }

            /**
             * 处理登录返回结果
             */
            function handleLogonReply(data: SocketDataPackage) {
                
                keepAlive(data);
                finishSignIn(data);
                thisObj.unsubscribe(ServerEvent.LogOnAnswered, handleLogonReply);
            }

            function handleOnceConnected() {
                
                // 取消与重新订阅登录回执
                thisObj.unsubscribe(ServerEvent.LogOnAnswered, handleLogonReply);
                thisObj.subscribe(ServerEvent.LogOnAnswered, handleLogonReply);
                const dataType = thisObj.serverType == SocketServerType.TradeServer ? MessageDataType.tradeServerLogon : MessageDataType.quoteServerLogon;
                // 发送登录数据包
                thisObj.send({ fc: ServerFunction.UserLogOn, reqId: 0, dataType, body: dataBody });
            }
            
            const unconnected = LoginResult.UnConnected;
            thisObj.onTimeouted('once', () => { resolve({ errorCode: unconnected.Value, errorMsg: '服务器连接超时' } as any); });
            thisObj.onClosed('once', () => { resolve({ errorCode: unconnected.Value, errorMsg: '服务器连接中断' } as any); });
            thisObj.onErrored('once', () => { resolve({ errorCode: unconnected.Value, errorMsg: '服务器连接错误' } as any); });
            thisObj.onConnected('once', handleOnceConnected);
            thisObj.connect();
        });
    }

    /**
     * 从Socket服务器注销
     */
    async signout(): Promise<void> {

        return new Promise<void>(resolve => {

            this.stopHeartBeat();
            this.send({ fc: ServerFunction.UserLogout, reqId: 0, dataType: MessageDataType.text });
            defaultLogger.info('Send out logout request', this.serverType, this.server);
            resolve();
        });
    }

    connect(): void {
        throw new Error('implemented in derived class');
    }

    disconnect(): void {
        throw new Error('implemented in derived class');
    }

    send(data: SocketDataPackage) {
        throw new Error('implemented in derived class');
    }

    dispose(): void {

        this.connectedCallbacks.length = 0;
        this.closedCallbacks.length = 0;
        this.timeoutedCallbacks.length = 0;
        this.erroredCallbacks.length = 0;
        this.stopHeartBeat();
    }
}