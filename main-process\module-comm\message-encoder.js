﻿﻿/**
 * responsibilities: 
 * 1. encoding a message sent to server
 * 2. decoding a message received from server
*/
const electron = require('electron');
const app = electron.app;
const encryptMessageAllowed = app.encryptionOptions.encryptMessage;

class MessageEncoder {

    constructor() {

        this.headerLen = 20;
        this.messageSlice = null;
    }

    encode(message) {

        if (message.fc === undefined || message.fc === null) {
            message.fc = 0;
        }
        if (message.reqId === undefined || message.reqId === null) {
            message.reqId = 0;
        }
        if (message.dataType === undefined || message.dataType === null) {
            message.dataType = 0;
        }
        if (!message.body) {
            message.body = '';
        }

        let { fc, reqId, dataType, body } = message;
        let bodyBytes = body instanceof Buffer ? body.byteLength : typeof body == 'string' ? Buffer.byteLength(body, 'utf8') : body.length;
        let data = Buffer.alloc(this.headerLen + bodyBytes, 0);

        data.writeInt32BE(fc, 0);
        // write the high order bits (shifted over)
        data.writeInt32BE(0, 4);
        // write the low order bits
        data.writeInt32BE(reqId & 0xffff, 8);
        data.writeInt32BE(dataType, 12);
        data.writeInt32BE(bodyBytes, 16);

        if (body instanceof Buffer) {
            for(let idx = this.headerLen; idx < data.length; idx++) {
                data[idx] = body[idx - this.headerLen];
            }
        }
        else {
            data.write(body, this.headerLen, 'utf8');
        }

        let body_data = data.slice(this.headerLen);
        if (encryptMessageAllowed) {
            this.endecrypt(body_data);
        }

        return data;
    }

    decode(data_package) {

        let start_index = 0;
        let int32_bytes_len = 4;
        let msg_not_integrated = 'waiting for the next shall-come data package';

        if (this.messageSlice != null && this.messageSlice.length > 0) {
            data_package = Buffer.concat([this.messageSlice, data_package], data_package.length + this.messageSlice.length);
        }

        let messages = [];

        while (true) {

            let left_len = data_package.length - start_index;

            if (left_len < this.headerLen) {

                if (left_len <= 0) {
                    this.messageSlice = null;
                }
                else {
                    this.messageSlice = data_package.slice(start_index);
                }

                break;
            }

            let fc = data_package.readInt32BE(start_index);
            start_index += int32_bytes_len;

            let no_use = data_package.readInt32BE(start_index);
            start_index += int32_bytes_len;

            let req_id = data_package.readInt32BE(start_index);
            start_index += int32_bytes_len;

            let data_type = data_package.readInt32BE(start_index);
            start_index += int32_bytes_len;

            let body_len = data_package.readInt32BE(start_index);
            start_index += int32_bytes_len;

            if (data_package.length - start_index < body_len) {

                this.messageSlice = data_package.slice(start_index - this.headerLen);
                break;
            }
            else {

                let body = data_package.slice(start_index, body_len + start_index);
                start_index += body_len;
                if (encryptMessageAllowed) {
                    this.endecrypt(body);
                }
                messages.push({ fc: fc, reqId: req_id, dataType: data_type, body: body });
            }
        }

        return messages;
    }

    endecrypt(data) {

        let index = 0;
        while (index < data.length) {

            let next_index = index + 1;
            if (next_index < data.length) {

                let temp = (data[next_index] ^ 127);
                data[next_index] = (data[index] ^ 127);
                data[index] = temp;
            }

            index += 2;
        }
    }
}

module.exports = { MessageEncoder };