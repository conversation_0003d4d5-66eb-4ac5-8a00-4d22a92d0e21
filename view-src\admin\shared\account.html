<div class="account-manage-view xsplitter">

    <div class="part-upper" style="height: 300px;">

        <div class="toolbar">

            <template v-if="allow2CreateAccount">
                <el-button type="primary" size="mini" @click="create"><i class="el-icon-plus"></i> 创建账号</el-button>
            </template>

            <el-input v-model="searching.keywords" class="input-searching s-mgl-10" placeholder="输入关键词过滤" @change="filterRecords"
                clearable>
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>

            <span class="pagination-wrapper" style="display: block; float: right;">

                <el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
                    :current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
                    @current-change="handlePageChange"></el-pagination>

            </span>

        </div>

        <table>
            
            <tr>
                <th 
                    label="账号名称"
                    prop="accountName" 
                    watch="accountName, brokerId" 
                    min-width="200" 
                    formatter="formatNameCol"
                    searchable 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="ID"
                    prop="id" 
                    min-width="100" 
                    searchable 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="券商"
                    prop="brokerName" 
                    min-width="100" 
                    searchable 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="资金账号"
                    prop="financeAccount" 
                    min-width="110" 
                    searchable 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="账号类型"
                    prop="assetType" 
                    min-width="70" 
                    formatter="formatAssetType" 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="所属机构"
                    prop="orgName" 
                    min-width="120" 
                    searchable 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="连接状态"
                    prop="connectionStatus" 
                    min-width="70" 
                    formatter="formatConnectionStatus" 
                    export-formatter="formatConnectionStatusText" 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="状态"
                    prop="status" 
                    min-width="70" 
                    formatter="formatAccountStatus" 
                    export-formatter="formatAccountStatusText" 
                    overflowt>
                </th>
            
                <th 
                    label="盘前风控"
                    prop="preRiskControl" 
                    watch="preRiskControl,workFlowId,workFlowName"
                    min-width="70" 
                    formatter="formatPreRiskControlStatus" 
                    export-formatter="formatPreRiskControlStatusText" 
                    overflowt>
                </th>
            
                <th 
                    label="快速审核"
                    prop="autoApprove" 
                    watch="autoApprove,workFlowId,workFlowName"
                    min-width="70" 
                    formatter="formatAutoApproveStatus" 
                    export-formatter="formatAutoApproveStatusText" 
                    overflowt>
                </th>
            
                <th 
                    condition="manage-only"
                    label="交易终端"
                    prop="terminals" 
                    min-width="120" 
                    formatter="formatTerminal"
                    export-formatter="formatTerminalText" 
                    sorting-method="sortTerminal" 
                    searchable 
                    sortable 
                    overflowt>
                </th>
            
                <th 
                    label="绑定流程"
                    prop="workFlowId" 
                    watch="workFlowId,workFlowName" 
                    min-width="110" 
                    formatter="formatWorkflow" 
                    export-formatter="formatWorkflowText" 
                    overflowt>
                </th>
            
                <th 
                    condition="org-only" 
                    label="昨日权益"
                    prop="preBalance" 
                    min-width="100" 
                    align="right"
                    searchable 
                    sortable 
                    overflowt 
                    summarizable 
                    thousands>
                </th>
            
                <th 
                    condition="org-only"
                    label="权益"
                    prop="balance" 
                    min-width="100" 
                    align="right" 
                    sortable 
                    overflowt 
                    summarizable 
                    thousands>
                </th>
            
                <th 
                    condition="org-only"
                    label="市值"
                    prop="marketValue" 
                    min-width="100" 
                    align="right" 
                    sortable 
                    overflowt 
                    summarizable 
                    thousands>
                </th>
            
                <th 
                    condition="org-only"
                    label="可用"
                    prop="available" 
                    min-width="100" 
                    align="right" 
                    searchable 
                    sortable 
                    overflowt 
                    summarizable 
                    thousands>
                </th>
            
                <th 
                    condition="org-only"
                    label="冻结"
                    prop="frozenMargin" 
                    min-width="100" 
                    align="right" 
                    searchable 
                    sortable 
                    overflowt 
                    thousands>
                </th>
            
                <th 
                    label="操作"
                    fixed-width="80" 
                    align="center" 
                    fixed="right" 
                    exportable="false" 
                    formatter="formatActions">
                </th>
            </tr>

        </table>

    </div>

    <div class="splitter-bar"></div>

    <div class="part-lower">

        <div class="account-summary">
            <div class="summary-tabs">
                <!-- multiple tabs 123 -->
            </div>
            <div class="summary-content">
                <!-- multiple tabs content 123 -->
            </div>
        </div>

    </div>

    <div class="dialog-terminal">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="绑定终端" :show-close="false"
                width="540px" v-drag>
                <div class="dialog-body-inner s-pd-10">
                    <div class="single-row" style="line-height: 36px;">
                        <label>目标账号：</label>
                        <span class="s-bold">{{ dialog.accountName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择终端：</label>
                        <br>
                        <br>
                        <el-transfer filter-placeholder="关键字搜索"
                                     v-model="dialog.selected"
                                     :titles="['可选终端', '已选终端']"
                                     :data="typedTerminals"
                                     :props="{ key: 'id', label: 'terminalName' }" filterable></el-transfer>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveTerminal">确定</el-button>
                    <el-button size="small" @click="unsaveTerminal">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-workflow">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="绑定订单审核流程" :show-close="false"
                width="350px" v-drag>
                <div class="dialog-body-inner s-pdl-10">
                    <div class="single-row" style="line-height: 36px;">
                        <label>目标账号</label>
                        <span class="s-bold">{{ dialog.accountName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择流程</label>
                        <el-select v-model="dialog.workflowId" placeholder="请选择流程" :clearable="true">
                            <el-option v-for="(item, item_idx) in workflows" :key="item_idx" :label="item.workflowName"
                                :value="item.id"></el-option>
                        </el-select>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveWorkflow">确定</el-button>
                    <el-button size="small" @click="unsaveWorkflow">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-password">

        <template>

            <el-dialog width="400px" :title="dialog.title" :visible="dialog.visible" :close-on-click-modal="false"
                :show-close="false" v-drag>

                <el-form ref="passform" label-width="120px" :model="formd" :rules="rules">
                    
                    <el-form-item label="目标账号">
                        <span class="s-bold">{{ formd.accountName }}</span>
                    </el-form-item>

                    <el-form-item label="账号密码" prop="pwd">
                        <el-input type="password" placeholder="请填写" v-model.trim="formd.pwd" style="width: 250px;">
                            <i slot="suffix" class="iconfont icon-password"></i>
                        </el-input>
                    </el-form-item>

                    <template v-if="isStock || isFuture">
                        <el-form-item :label="isStock ? '通信密码' : 'AUTH CODE'">
                            <el-input type="password" placeholder="可选填" v-model.trim="formd.txPassword" style="width: 250px;">
                                <i slot="suffix" class="iconfont icon-password"></i>
                            </el-input>
                        </el-form-item>
                    </template>

                </el-form>

                <div slot="footer">
                    <el-button @click="savePass" type="primary" size="small">确定</el-button>
                    <el-button @click="unsavePass" size="small">取消</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

    <div class="dialog-editing">

        <template>

            <el-dialog width="630px" :visible="dialog.visible" :title="dialog.title" :close-on-click-modal="false"
                :close-on-press-escape="false" :show-close="false" v-drag>

                <el-form ref="editform" class="dialog-body-form" label-width="110px" :model="formd" :rules="rules"
                    :inline="true">

                    <el-form-item label="账号名称" prop="accountName">
                        <el-input type="text" placeholder="请填写" v-model="formd.accountName" :maxlength="25"></el-input>
                    </el-form-item>

                    <el-form-item label="所属机构" prop="orgId">
                        <el-select v-model="formd.orgId" @change="handleOrgChange" :disabled="isOrgFixed" filterable
                            clearable>
                            <el-option v-for="(item, item_idx) in orgs" :key="item_idx" :value="item.id"
                                :label="item.orgName"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="资产类型" prop="assetType">
                        <el-select @change="handleAssetTypeChange" v-model="formd.assetType" clearable>
                            <el-option v-for="(item, item_index) in assetTypes" :key="item_index" :value="item.code"
                                :label="item.mean"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="账号类型" prop="credit" v-if="isStock" clearable>
                        <el-select v-model="formd.credit">
                            <el-option v-for="(item, item_idx) in accountTypes" :key="item_idx" :value="item.code"
                                :label="item.mean"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="所属经纪商" prop="bkId">
                        <el-select v-model="formd.bkId" @change="handleBrokerChange" filterable clearable>
                            <el-option v-for="(item, item_idx) in brokers" :key="item_idx" :value="item.id" :label="item.brokerName"></el-option>
                        </el-select>
                    </el-form-item>

                    <template v-if="isStock">

                        <el-form-item label="营业部代码" prop="extInfo.yybId">
                            <el-input placeholder="请填写" type="text" v-model.trim="formd.extInfo.yybId"
                                :maxlength="30"></el-input>
                        </el-form-item>

                        <el-form-item label="沪市股东代码" prop="extInfo.shSecId">
                            <el-input placeholder="请填写" type="text" v-model="formd.extInfo.shSecId" :maxlength="20">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="深市股东代码" prop="extInfo.szSecId">
                            <el-input placeholder="请填写" type="text" v-model="formd.extInfo.szSecId" :maxlength="20">
                            </el-input>
                        </el-form-item>

                    </template>

                    <el-form-item label="资金账号" prop="financeAccount">
                        <el-input type="text" placeholder="请填写" v-model="formd.financeAccount" :maxlength="20">
                        </el-input>
                    </el-form-item>

                    <el-form-item v-if="isStock || isFuture" :label="isStock ? '交易账号' : 'APPID'"
                        prop="extInfo.tradeAccount">
                        <el-input type="text" placeholder="请填写" v-model="formd.extInfo.tradeAccount" :maxlength="35">
                        </el-input>
                    </el-form-item>

                    <el-form-item v-if="allow2ChangePasscode" label="账号密码" prop="pwd">
                        <el-input type="password" placeholder="请填写" v-model.trim="formd.pwd">
                            <i slot="suffix" class="iconfont icon-password"></i>
                        </el-input>
                    </el-form-item>

                </el-form>

                <div slot="footer">
                    <el-button @click="save" type="primary" size="small">确定</el-button>
                    <el-button @click="unsave" size="small">取消</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

    <div class="dialog-child-account">

        <template>

            <el-dialog 
                width="400px" 
                :title="dialog.title" 
                :visible="dialog.visible" 
                :close-on-click-modal="false"
                :show-close="false" 
                v-drag
                >

                <el-form ref="childform" label-width="120px" :model="formd" :rules="rules">
                    
                    <el-form-item label="从属于账号" prop="accountId">
                        <el-select v-model="formd.accountId" :disabled="isChildModify()" filterable clearable>
                            <el-option 
                                v-for="(item, item_idx) in accounts" 
                                :key="item_idx" 
                                :value="item.accountId" 
                                :label="`${item_idx + 1} / ${accounts.length} ${formatSelectAccountName(item)}`"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="账号别名" prop="accountName">
                        <el-input placeholder="请填写别名" v-model="formd.accountName" clearable></el-input>
                    </el-form-item>

                </el-form>

                <div slot="footer">
                    <el-button @click="saveChild" type="primary" size="small">确定</el-button>
                    <el-button @click="unsaveChild" size="small">取消</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

    <div class="dialog-pre-balance">

        <template>

            <el-dialog width="640px" :visible="dialog.visible" :title="dialog.title" :close-on-click-modal="false"
                :close-on-press-escape="false" :show-close="false" v-drag>

                <el-form ref="preform" class="dialog-body-form" label-width="130px" :model="form" :rules="rules"
                    :inline="true">

                    <el-form-item label="昨日权益" prop="pre_balance">
                        <el-input type="text" placeholder="请填写" v-model="form.pre_balance" :maxlength="25"></el-input>
                    </el-form-item>
                    <el-form-item label="可用" prop="available">
                        <el-input type="text" placeholder="请填写" v-model="form.available" :maxlength="25"></el-input>
                    </el-form-item>
                    <el-form-item label="冻结" prop="frozen">
                        <el-input type="text" placeholder="请填写" v-model="form.frozen" :maxlength="25"></el-input>
                    </el-form-item>
                    <el-form-item label="融资买入金额" prop="buy_balance">
                        <el-input type="text" placeholder="请填写" v-model="form.buy_balance" :maxlength="25"></el-input>
                    </el-form-item>
                    <el-form-item label="融资卖出金额" prop="sell_quota">
                        <el-input type="text" placeholder="请填写" v-model="form.sell_quota" :maxlength="25"></el-input>
                    </el-form-item>
                    <el-form-item label="可用融券卖出金额" prop="sell_balance">
                        <el-input type="text" placeholder="请填写" v-model="form.sell_balance" :maxlength="25"></el-input>
                    </el-form-item>

                </el-form>

                <div slot="footer">
                    <el-button @click="savePreBalance" type="primary" size="small">确定</el-button>
                    <el-button @click="closePreBalance" size="small">取消</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

    <div class="dialog-fee">

        <template>

            <el-dialog width="700px" :title="dialog.title" :visible="dialog.visible" :close-on-click-modal="false"
                :show-close="false" v-drag>

                <div class="dialog-body-inner">
                    <div class="single-row" style="line-height: 36px;">
                        <label style="display: inline-block; width: 120px; text-align: right;">目标账号</label>
                        <span class="s-bold">{{ feedata.accountName }}</span>
                    </div>
                </div>

                <el-form ref="feeform" class="dialog-body-form" label-width="120px" :disabled="isReadonly" :model="feedata.model"
                    :rules="rules" :inline="true">

                    <el-form-item label="过户费(上海)" prop="shTransferFee">
                        <el-input v-model="feedata.model.shTransferFee" placeholder="例如 0.001" :maxlength="10">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="过户费(深圳)" prop="szTransferFee">
                        <el-input v-model="feedata.model.szTransferFee" placeholder="例如 0.001" :maxlength="10">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="交易佣金" prop="commission">
                        <el-input v-model="feedata.model.commission" placeholder="例如 0.001" :maxlength="10"></el-input>
                    </el-form-item>

                    <el-form-item label="最低佣金(元)" prop="commissionLeast">
                        <el-input v-model="feedata.model.commissionLeast" placeholder="例如 5.0" :maxlength="10">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="其他费用(上海)" prop="shOther">
                        <el-input v-model="feedata.model.shOther" placeholder="例如 0.001" :maxlength="10"></el-input>
                    </el-form-item>

                    <el-form-item label="其他费用(深圳)" prop="szOther">
                        <el-input v-model="feedata.model.szOther" placeholder="例如 0.001" :maxlength="10"></el-input>
                    </el-form-item>

                    <el-form-item label="印花税" prop="stampDuty">
                        <el-input v-model="feedata.model.stampDuty" placeholder="例如 0.001" :maxlength="10"></el-input>
                    </el-form-item>

                </el-form>
                <div slot="footer">
                    <template v-if="isReadonly">
                        <el-button @click="unsaveFeeConfig" size="small">关闭</el-button>
                    </template>
                    <template v-else>
                        <el-button @click="saveFeeConfig" type="primary" size="small">确定</el-button>
                        <el-button @click="unsaveFeeConfig" size="small">取消</el-button>
                    </template>
                </div>

            </el-dialog>
        </template>
    </div>

</div>