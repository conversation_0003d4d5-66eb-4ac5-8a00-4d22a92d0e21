const { IView } = require('../../../../component/iview');
const { Tab } = require('../../../../component/tab');
const { TabList } = require('../../../../component/tab-list');

class View extends IView {

    /**
     * @param {*} view_name 
     * @param {*} is_standalone_window 
     */
    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '算法交易数据');
    }

    createDataViews() {

        var $tab = this.$container.querySelector('.category-tab');
        var $content = this.$container.querySelector('.data-views');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/fragment/belong-orders', '委托');
        tabs.openTab(true, '@2021/fragment/belong-exchanges', '成交');
        this.tabList = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {
        
        if (this.belongId !== undefined) {
            tab.viewEngine.trigger('set-context', this.belongId);
        }

        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    /**
     * @param {Tab} tab 
     */
     handleTabFocused(tab) {
        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    listen2Events() {

        /**
         * 监听窗口尺寸调整
         */
        this.lisen2WinSizeChange((width, height, isMaximized) => {

            this.tabList.fireEventOnFocusedTab('table-max-height', height - (isMaximized ? 564 : 549));
            this.tabList.fireEventOnFocusedTab('table-scroll-2-left');
        });

        /**
         * 监听上下文切换
         */
        this.registerEvent('set-context-algo-order', this.handleContextChange.bind(this));
    }

    handleContextChange(belongId) {

        this.belongId = belongId;
        this.tabList.fireEventOnAllTabs('set-context', belongId);
    }

    build($container) {

        super.build($container);
        this.createDataViews();
        this.listen2Events();
    }
}

module.exports = View;