import Utils from '../modules/utils';
import { GetLogger } from '../global-state';

const defaultLogger = GetLogger();

/**
 * 通用PUB/SUB中间件
 */
export class PubSubCapability {

    /**
     * 已注册回调函数
     */
    private handlers: Function[];

    /**
     * 已注册回调函数个数
     */
    get totalHandlers() {
        return this.handlers.length;
    }

    constructor() {
        this.handlers = [];
    }

    /**
     * 添加订阅函数
    */
    sub(handler: Function) {

        if (typeof handler == 'function' && !this.handlers.some(func => func === handler)) {
            this.handlers.push(handler);
        }
    }

    /**
     * 退订订阅函数
    */
    unsub(handler: Function) {

        if (typeof handler == 'function') {
            Utils.remove(this.handlers, func => func === handler);
        }
    }

    /**
     * 退订所有订阅
    */
    unsubAll() {
        this.handlers.length = 0;
    }

    /**
     * 触发回调
     */
    fire(...args: any) {

        this.handlers.forEach(func => {

            try {
                func(...args);
            } 
            catch (ex) {
                defaultLogger.error('PubSubCapability.fire', func.name || '[anonymous]', ex);
            }
        });
    }
}