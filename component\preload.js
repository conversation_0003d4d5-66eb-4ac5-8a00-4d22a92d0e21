localStorage.clear();
const { ipcRenderer, remote } = require('electron');
const userInfo = remote.app.contextData.userInfo;

let count1 = 0;

const waitforSingingIn = setInterval(() => {

    if (count1 > 30) {
        clearInterval(waitforSingingIn);
        ipcRenderer.sendToHost('登录超时，请稍后重试');
    }

    if (document.querySelector('.tabsTop') !== null) {
        clearInterval(waitforSingingIn);
        ipcRenderer.sendToHost('signed');
    }

}, 100);

const doSignIn = () => {
    let $labs = document.querySelectorAll('.lab-buy');
    $($labs[0].querySelector('input')).val(userInfo.userName)[0].dispatchEvent(new Event('input'));
    $($labs[1].querySelector('input')).val(userInfo.password)[0].dispatchEvent(new Event('input'));
    document.querySelector('.el-button').click();
    waitforSingingIn();
};


let count = 0;
let wait = setInterval(() => {
    if (count > 3) {
        clearInterval(wait);
    }

    if (document.querySelector('#register') !== null) {
        clearInterval(wait);
        doSignIn();
    }
    count++;
    console.log(count);
}, 1000);