.algo-design-index {
 
    .params-box {
        max-height: 320px;
        overflow-y: auto;
    }

    .creation-form {

        display: flex;
        flex-wrap: wrap;

        .el-form-item {

            flex: 1 1 30%;
            display: flex;

            .el-form-item__content {

                flex: 1 0 100px;
                .el-select {
                    width: 100%;
                }
            }
        }

        .el-input__inner {
            color: white;
        }

        .el-textarea__inner {

            background-color: #0C1016;
            border-color: #333;
            color: white;
        }
    }

    .param-row {

        padding: 5px 20px;
        display: flex;
        align-items: center;
        gap: 10px;

        &:not(:first-child) {

            margin-top: 5px;
            padding-top: 10px;
            border-top: 1px dotted #444;
        }

        .left-no,
        .right-btn {
            flex: 0 0 40px;
            display: flex;
            justify-content: center;
        }

        .center-area {

            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
    
            > span {
                flex: 0 0 40px;
            }

            > .el-input,
            > .el-select,
            > .el-input-number,
            > .el-date-editor,
            > .input-wrap {
                flex: 1 0 25%;
            }

            .select-user-options {

                display: flex;
                gap: 10px;
                align-items: center;
        
                .el-select {
                    flex: 1 1 50px;
                }
        
                .add-button {
                    flex: 0 0 20px;
                }
            }

            .option-item {

                display: flex;
                gap: 10px;
                align-items: center;
        
                .option-item-label {
                    flex: 1 1 50px;
                }
        
                .option-item-delete {
                    flex: 0 0 20px;
                }
            }

            .switch-controls {

                display: flex;
                gap: 10px;
                align-items: center;
            }
        }
    }
}

.algo-param-option-form {
    
    .option-row {

        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 0;

        span {
            flex: 0 0 50px;
        }

        .el-input {
            flex: 1 1 50px;
        }
    }
}