.algform {

    padding-right: 2px;

    .import-button {
        height: 20px;
        margin-top: 4px;
    }

    .xtform .xtinput .xtlabel {
        width: 29%;
    }

    .xtform .xtinput > .el-input,
    .xtform .xtinput > .el-autocomplete,
    .xtform .xtinput > .el-input-number,
    .xtform .xtinput > .el-select,
    .xtform .xtinput > .el-date-editor,
    .xtform .xtinput > .el-range-editor,
    .xtform .xtinput .trade-options {
        width: 69%;
    }

    .xtform .xtinput > .el-date-editor,
    .xtform .xtinput > .el-range-editor {
        
        position: relative;
        top: -5px;
    }

    .trade-options {
        display: inline-block;
    }

    .xtinput {

        box-sizing: border-box;

        &.shorten {

            .el-input-number {
                width: 68%;
            }
        }

        .xtlabel {
            text-align: left;
        }

        .el-checkbox {
            margin-right: 20px;
        }

        &.basket-button-row {
            
            margin-top: 20px !important;

            button {

                margin-left: 0 !important;
                margin-right: 10px !important;
                width: 45%;
            }
        }
    }

    .xtinput-algo {
        
        .el-select {
            width: 61% !important;
        }

        .algo-param-setting {

            position: absolute;
            right: 6px;
            display: inline-block;
            padding: 0;
            width: 24px;
            line-height: 22px;
            border-radius: 2px;
            text-align: center;
            background-color: #283A56;
            color: white;
            opacity: 0.8;

            &:hover {
                opacity: 1;
            }
        }
    }

    .xtinput-options {
        height: 30px;
    }

    .changed-icon {

        font-size: 30px;
        position: absolute;
        margin-top: -2px;
    }
}

.algo-param-popup {

    width: 300px;
            
    .alog-param-row {

        height: 24px;
        margin-top: 10px;

        .xtlabel {

            display: inline-block;
            height: 100%;
            width: 130px !important;
            padding-left: 10px;
            vertical-align: top;
            color: white;
        }

        .el-input {

            height: 100%;
            width: 155px;
            vertical-align: top;
        }

        &:last-child {

            margin-top: 15px;
            margin-bottom: 10px;
        }
    }
}

.algform-internal {

    .form-external {
        padding: 10px;
    }

    .direction-row {

        .el-radio-group {

            .el-radio-button {

                .el-radio-button__inner {
                    width: 100%;
                }
            }
        }
    }

    .limit-btn,
    .unit-txt {

        position: absolute;
        right: 5px;
        z-index: 1;
        width: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
    }
}