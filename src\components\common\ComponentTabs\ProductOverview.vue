<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, watch } from 'vue';
import {
  Repos,
  type AccountDetail,
  type LegacyFundInfo,
  type NavSerie,
} from '../../../../../xtrade-sdk/dist';
import type { ColumnDefinition } from '@/types';
import { formatNumber, thousands, thousandsCol } from '@/script';
import VirtualizedTable from '../VirtualizedTable.vue';
import VueEchart from '../VueEchart.vue';
import type { EChartsOption } from 'echarts';
import {
  availableCol,
  balanceCol,
  closeProfitCol,
  commissionCol,
  frozenMarginCol,
  marginCol,
  marketValueCol,
  navCol,
  positionProfitCol,
  preBalanceCol,
  risePercentCol,
} from './shared/columnDefinitions';

// 定义组件属性
const { activeItem } = defineProps<{
  activeItem?: LegacyFundInfo;
}>();

const repoInstance = new Repos.GovernanceRepo();
// 概览数据
const overviewData = shallowRef<AccountDetail[]>([]);
// 净值数据
const navData = shallowRef<NavSerie[]>([]);

// 净值走势图配置
const navChartOption = computed<EChartsOption>(() => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: (params: any) => {
        const point = params[0];
        if (!point) return '';
        const data = navData.value[point.dataIndex];
        return `
          <div>交易日：${data.tradingDay}</div>
          <div><span style="background-color:#F9BE1F;width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:5px;"></span>单位净值：${formatNumber(data.nav, { fix: 4 })}</div>
          <div><span style="background-color:#65A1FF;width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:5px;"></span>累计净值：${formatNumber(data.sumNav, { fix: 4 })}</div>
        `;
      },
    },
    legend: {
      data: ['单位净值', '累计净值'],
      textStyle: {
        color: '#fff',
      },
      top: 10,
    },
    grid: {
      left: 0,
      right: 20,
      bottom: 30,
      top: 50,
      outerBoundsMode: 'same',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: navData.value.map(item => item.tradingDay),
      axisLabel: {
        color: '#fff',
      },
      axisLine: {
        show: true,
      },
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        color: '#fff',
        formatter: (value: number) => formatNumber(value, { fix: 4 }),
      },
      axisLine: {
        show: true,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0.1)',
        },
      },
    },
    series: [
      {
        name: '单位净值',
        type: 'line',
        smooth: true,
        symbol: 'none',
        data: navData.value.map(item => item.nav),
        lineStyle: {
          color: '#F9BE1F',
        },
        itemStyle: {
          color: '#F9BE1F',
        },
      },
      {
        name: '累计净值',
        type: 'line',
        smooth: true,
        symbol: 'none',
        data: navData.value.map(item => item.sumNav),
        lineStyle: {
          color: '#65A1FF',
        },
        itemStyle: {
          color: '#65A1FF',
        },
      },
    ],
  };
});

const columns = [
  {
    key: 'identityName',
    title: '账号名称',
    width: 200,
    sortable: true,
    searchable: true,
  },
  balanceCol,
  marketValueCol,
  availableCol,
  {
    ...risePercentCol,
    title: '收益率',
  },
  {
    ...positionProfitCol,
    title: '持仓收益',
  },
  navCol,
  preBalanceCol,
  frozenMarginCol,
  closeProfitCol,
  {
    key: 'loanBuyBalance',
    title: '融资买入金额',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'loanSellBalance',
    title: '可用融券卖出资金',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'loanSellQuota',
    title: '融券卖出金额',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  commissionCol,
  marginCol,
] as ColumnDefinition<AccountDetail>;

// 获取概览数据
const fetchOverviewData = async () => {
  if (!activeItem) return;
  const { errorCode, errorMsg, data } = await repoInstance.QueryAccountDetails(activeItem.id);
  if (errorCode == 0) {
    overviewData.value = data?.list || [];
  } else {
    console.error(errorMsg);
    overviewData.value = [];
  }
};

// 获取净值数据
const fetchNavData = async () => {
  if (!activeItem) return;
  const { errorCode, errorMsg, data } = await repoInstance.QueryProductNav(activeItem.id);
  if (errorCode == 0) {
    navData.value = data?.series || [];
  } else {
    console.error(errorMsg);
    navData.value = [];
  }
};

onMounted(() => {
  if (activeItem) {
    fetchOverviewData();
    fetchNavData();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchOverviewData();
      fetchNavData();
    }
  },
  { deep: true },
);
</script>

<template>
  <div h-full v-if="!activeItem" flex jcc aic h-200 text-gray-500>请选择产品</div>
  <div h-full v-else flex gap-20>
    <VirtualizedTable flex-1 min-w-1 fixed :columns="columns" :data="overviewData" />
    <!-- 产品净值走势图 -->
    <div w-600 h-full>
      <VueEchart v-if="navData.length > 0" :option="navChartOption" />
      <div v-else flex jcc aic h-full text-gray-500>暂无净值数据</div>
    </div>
  </div>
</template>

<style scoped></style>
