
const httpRequest = require('../libs/http').http;
const helper = require('../libs/helper').helper;

class classificationRepository {

    constructor(){
        //
    }

    getStockPoolList(current_user_id) {

        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/classification/info?query_type=1').then(resp => {

                var all_items = resp.data.data;
                var own_items = all_items.filter(x => x.createUser === current_user_id);
                var group_map = own_items.groupBy(ins => ins.classificationName);
                var mapper = function(x) { return { instrument: x.instrument, instrumentName: x.instrumentName }; };

                var pool_list = [];
                for(let pool_name in group_map) {
                    pool_list.push({
                        id: `pool-${helper.makeToken()}`,
                        name: pool_name,
                        instruments: group_map[pool_name].map(mapper)
                    });
                }
                resolve({ errorCode: 0, data: pool_list }); }, err => { reject(err); });
        });
    }

    getClassificationDetail (classificationId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/classification/detail', {
                params: {
                    classification_id: classificationId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getClassificationList () {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/classification').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    createClassification(list) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/risk/classification', list).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    deleteClassification(className) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/risk/classification', {
                params: {
                    classification_name: className
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    updateClassification(classification) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/risk/classification', classification).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

}

module.exports = { repoClassification: new classificationRepository() };