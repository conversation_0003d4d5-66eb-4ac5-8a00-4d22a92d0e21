<script setup lang="tsx">
import VirtualizedTable from '../../common/VirtualizedTable.vue';
import { computed, ref, useTemplateRef, watch } from 'vue';
import {
  IdentityType,
  IdentityTypes,
  Repos,
  type SystemIdentityInfo,
} from '../../../../../xtrade-sdk/dist';
import type { ColumnDefinition, RowAction } from '@/types';
import { remove, renderLabel } from '@/script';
import { ElMessage } from 'element-plus';

interface CellRenderParam {
  rowData: SystemIdentityInfo;
  cellData: any;
}

const props = defineProps<{
  // 至少选择多少个
  least?: number;
  ison: boolean;
  selectedIds: string[];
}>();

const columns: ColumnDefinition<SystemIdentityInfo> = [
  { key: 'identityId', title: 'ID', width: 150, sortable: true },
  { key: 'identityName', title: '名称', width: 200, sortable: true },
  {
    key: 'identityType',
    title: '类型',
    width: 120,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      return <span>{renderLabel(params.cellData, IdentityTypes)}</span>;
    },
  },
];

const rowActions: RowAction<SystemIdentityInfo>[] = [
  {
    label: '移除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function deleteRow(row: SystemIdentityInfo) {
  remove(checkeds.value, x => x === row);
}

const $tableRef = useTemplateRef('$tableRef');
const repoInstance = new Repos.RiskControlRepo();
const targetIds = ref<string[]>([]);
const records = ref<SystemIdentityInfo[]>([]);
const checkeds = ref<SystemIdentityInfo[]>([]);

watch(
  () => props.ison,
  () => {
    if (props.ison) {
      targetIds.value = [...props.selectedIds];
      request();
    }
  },
  { immediate: true },
);

const typeGroups = [
  IdentityType.Fund,
  IdentityType.FundGroup,
  IdentityType.Account,
  IdentityType.AccountGroup,
];

const selectedType = ref(typeGroups[0].value);
const selectedId = ref<string>('');
const availables = computed(() => {
  return records.value.filter(x => !checkeds.value.some(y => y.identityId == x.identityId));
});

const typedAvailables = computed(() => {
  return availables.value.filter(x => x.identityType == selectedType.value);
});

function add2Table() {
  const matched = checkeds.value.find(x => x.identityId == selectedId.value);
  if (!matched) {
    const matched2 = records.value.find(x => x.identityId == selectedId.value);
    if (matched2) {
      checkeds.value.push(matched2);
    }
  }

  // reset to empty
  selectedId.value = '';
}

function deleteChecks() {
  const selectedRows = $tableRef.value?.selectedRows || [];

  if (selectedRows.length === 0) {
    ElMessage.warning('请选择');
    return;
  }

  selectedRows.forEach(row => {
    deleteRow(row);
  });
}

async function request() {
  const list = (await repoInstance.QueryAllIdentities()).data || [];
  list.sort((a, b) => a.identityType - b.identityType);
  list.forEach(x => {
    x.identityName = `${renderLabel(x.identityType, IdentityTypes)} - ${x.identityName}`;
  });
  records.value = list;
  checkeds.value = list.filter(x => targetIds.value.some(id => id == x.identityId));
}

function getSelectedRows() {
  return checkeds.value;
}

defineExpose({
  getSelectedRows,
});
</script>

<template>
  <div class="choose-panel" h-full flex flex-col>
    <div class="waiting-list" h-40 flex aic gap-10>
      <el-select v-model="selectedType" style="width: 100px">
        <el-option
          v-for="item in typeGroups"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="selectedId"
        @change="add2Table"
        placeholder="请选择要添加的产品或账号"
        style="width: 300px; margin-left: 10px"
        filterable
      >
        <el-option
          v-for="item in typedAvailables"
          :key="item.identityId"
          :label="item.identityName"
          :value="item.identityId"
        ></el-option>
      </el-select>
      <span class="c-[var(--g-text-color-1)]">剩余可选数量 = {{ typedAvailables.length }}</span>
    </div>
    <div flex-1 of-y-hidden>
      <VirtualizedTable
        ref="$tableRef"
        identity="identityId"
        style="height: 100%"
        :columns="columns"
        :data="checkeds"
        :row-actions="rowActions"
        :row-action-width="80"
        select
        fixed
      >
        <template #actions>
          <el-button
            type="primary"
            @click="deleteChecks"
            size="small"
            :disabled="!$tableRef?.selectedRows.length"
          >
            <i class="iconfont icon-remove"></i>
            <span pl-5>删除勾选</span>
          </el-button>
        </template>
      </VirtualizedTable>
    </div>
  </div>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
