<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { deepClone, getUser } from '@/script';

import {
  Repos,
  type TradeClassification,
  TradeClassificationType,
} from '../../../../xtrade-sdk/dist';

const { classType, classification } = defineProps<{
  classType: TradeClassificationType;
  classification?: TradeClassification;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 30, message: '分类名称长度在 1 到 30 个字符', trigger: 'blur' },
  ],
  description: [{ required: false, message: '请输入分类描述', trigger: 'blur' }],
};

const usr = getUser()!;
const formRef = useTemplateRef('formRef');

const form = ref<TradeClassification>(createEmpty());

function createEmpty(): TradeClassification {
  const item = {
    id: null as any,
    name: '',
    type: TradeClassificationType.Asset,
    description: '',
    parentId: 0,
    level: 2,
    createUserId: usr.userId,
    createUserName: usr.username,
    orgId: usr.orgId,
  };

  item.type = classType;
  return item;
}

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (classification) {
      form.value = deepClone(classification);
    } else {
      resetForm();
    }
  }
});

// 重置表单
const resetForm = () => {
  form.value = createEmpty();
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      handleSave();
    }
  });
};

const repoInstance = new Repos.ClassificationRepo();

async function handleSave() {
  const resp = classification
    ? await repoInstance.updateTradeClassification(form.value)
    : await repoInstance.createTradeClassification(form.value);

  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success(`已保存`);
    emit('success');
    handleClose();
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="classification ? '编辑分类' : '新建分类'"
    width="500px"
    @close="handleClose"
    draggable
  >
    <el-form
      ref="formRef"
      class="typical-form"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="80px"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入分类名称"
          maxlength="30"
          clearable
          show-word-limit
        >
          <template #prefix>
            <i class="iconfont icon-block"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="分类描述" prop="description">
        <el-input
          v-model="form.description"
          placeholder="请输入分类描述"
          maxlength="200"
          clearable
          show-word-limit
        >
          <template #prefix>
            <i class="iconfont icon-block"></i>
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped></style>
