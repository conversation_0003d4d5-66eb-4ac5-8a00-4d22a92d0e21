const ApproveController = require('./approve').ApproveController;
const electronLocalshortcut = require('../../libs/3rd/electron-localshortcut');

class View extends ApproveController {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '待审核订单');
        this.approveShortcutKey = 'F10';
    }

    createRowAction(order) {

        let as = this.statuses;
        if (order.instructionStatus != as.waiting.code) {
            return '';
        }

        return `<button event.onclick="approveSingle">${as.passed.mean}</button>
                <button event.onclick="rejectSingle" class="danger">${as.rejected.mean}</button>`;
    }

    approveSingle(order) {
        this.makeOrdersGo(this.statuses.passed.code, [order]);
    }

    rejectSingle(order) {
        this.makeOrdersGo(this.statuses.rejected.code, [order]);
    }

    handleShortcutApproveAll() {
        this.approveAll();
    }

    approveAll() {

        this.tableObj.checkAll();
        const all = this.tableObj.extractCheckedRecords();
        this.makeOrdersGo(this.statuses.passed.code, all);
    }

    rejectAll() {

        this.tableObj.checkAll();
        const all = this.tableObj.extractCheckedRecords();
        this.makeOrdersGo(this.statuses.rejected.code, all);
    }

    approveSelected() {
        
        const checkeds = this.tableObj.extractCheckedRecords();
        this.makeOrdersGo(this.statuses.passed.code, checkeds);
    }

    rejectSelected() {
        
        const checkeds = this.tableObj.extractCheckedRecords();
        this.makeOrdersGo(this.statuses.rejected.code, checkeds);
    }

    /**
     * @param {Number} status
     * @param {Array<Object>} orders
     */
    makeOrdersGo(status, orders) {

        if (!Array.isArray(orders) || orders.length == 0) {

            this.interaction.showError('未选择订单');
            return;
        }

        this.renderProcess.send(this.systemEvent.auditOrder, status, orders.map(x => x.instructionId));
        orders.forEach(rowd => {

            let rkey = this.identifyRecord(rowd);
            if (this.tableObj.isRowChecked(rkey)) {
                this.tableObj.checkRow(rkey, false);
            }

            this.tableObj.deleteRow(rkey);
        });
    }

    handleInstruction(event, instruction) {

        const rowd = this.reshapeOrder(instruction);
        if (rowd.instructionStatus == this.statuses.waiting.code) {
            this.tableObj.putRow(rowd);
        }
        else {

            let rkey = this.identifyRecord(rowd);
            if (this.tableObj.hasRow(rkey)) {
                this.tableObj.deleteRow(rkey);
            }
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestInstructions(true, '待审核');
    }

    exportSome() {
        this.tableObj.exportAllRecords(`待审核订单-${new Date().format('yyyyMMdd')}`);
    }

    dispose() {
        electronLocalshortcut.unregister(this.approveShortcutKey);
    }

    registerShortcutListener() {
        electronLocalshortcut.register(this.approveShortcutKey, () => { this.handleShortcutApproveAll(); });
    }

    build($container) {

        super.build($container);
        this.setupTable('smt-aap');
        this.requestInstructions(true, '待审核');
        this.createToolbarApp([this.approveAll, this.approveSelected, this.rejectAll, this.rejectSelected]);
        this.registerShortcutListener();
        this.lisen2WinSizeChange(this.handleWinSizeChangeProxy);
        this.simulateWinSizeChange();
    }
}

module.exports = View;
