<script setup lang="ts">
import { ref, provide, computed, shallowRef, watch } from 'vue';
import AccountList from '@/components/common/ComponentTabs/AccountList.vue';
import TradingPanel from '@/components/NewTradingView/TradingPanel.vue';
import TradeLevels from '@/components/NormalTradeView/TradeTabs/TradeLevels.vue';
import AccountDetails from '@/components/NewTradingView/AccountDetails.vue';
import type { AccountInfo, InstrumentInfo, StandardTick } from '@/types';
import { INSTRUMENT_SELECT_KEY, TRADE_LEVEL_CLICK_KEY } from '@/keys';
import { TickType } from '../../../../../xtrade-sdk';
import { TickService } from '@/api';

// 选中合约，依赖注入到InstrumentInput组件中
const instrumentSelect = ref<string>('');
// 盘口点击价格，依赖注入到TradePanel组件中
const levelPrice = ref<number>(0);
provide(INSTRUMENT_SELECT_KEY, instrumentSelect);
provide(TRADE_LEVEL_CLICK_KEY, levelPrice);

// 当前选中的账号
const selectedAccount = ref<AccountInfo>();

// 当前选中的合约
const selectedInstrument = ref<InstrumentInfo>();

// 最新tick
const lastTick = shallowRef<StandardTick>();

// 交易类型：普通交易、算法交易、篮子交易
const tradeType = ref<'normal' | 'algo' | 'basket'>('normal');

// 是否为单合约交易模式
const is4SingleInstrument = computed(() => tradeType.value != 'basket');

// 先取消订阅旧合约tick行情，再订阅新合约行情
watch(selectedInstrument, async (ins, oldIns) => {
  if (oldIns) {
    await TickService.unsubscribeTick(oldIns!.instrument, TickType.tick, updateTick);
    lastTick.value = undefined;
  }
  if (ins) {
    TickService.subscribeTick(ins.instrument, TickType.tick, updateTick);
  }
});

// 更新tick数据
const updateTick = (data: StandardTick) => {
  lastTick.value = data;
};

// 处理账号选择
const handleAccountSelect = (account: AccountInfo) => {
  selectedAccount.value = account;
};

// 处理点击价格档位事件
const handleClickLevel = (price: number) => {
  levelPrice.value = price;
};
</script>

<template>
  <div class="new-trading-view" flex="~ col" h-full>
    <!-- 上半部分：账号列表 -->
    <div class="trader-overview-section" h-300>
      <AccountList
        trade
        @row-click="handleAccountSelect"
        @account-ids-change="$emit('account-ids-change', $event)"
      />
    </div>

    <!-- 下半部分：三个面板 -->
    <div class="trading-panels" bg="#000" flex flex-1 min-h-1 gap-6>
      <!-- 左侧：下单界面 -->
      <div class="trading-panel-section" w-350>
        <TradingPanel
          :selected-account
          :last-tick="lastTick"
          v-model:instrument="selectedInstrument"
          v-model:tradeType="tradeType"
          ref="TradingPanelRef"
        />
      </div>

      <!-- 中间：盘口 -->
      <div v-if="is4SingleInstrument" class="market-depth-section" w-300>
        <TradeLevels :selected-account :last-tick="lastTick" @click-level="handleClickLevel" />
      </div>

      <!-- 右侧：账号详情 -->
      <div class="account-details-section" flex-1 min-w-1>
        <AccountDetails :selected-account :trade-type="tradeType" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.new-trading-view {
  background-color: var(--g-bg);
}
</style>
