const { IView } = require('../../../component/iview');
const { NumberMixin } = require('../../../mixin/number');
const { DatetimeMixin } = require('../../../mixin/date-time');
const { helperUi } = require('../../../libs/helper-ui');
const { AlgoTaskPackage, AlgoTaskInfo, AlgoEntrustInfo, TaskEntrustStatus } = require('../../../model/algot');
const { GaoyuAlgo, GaoyuAlgoGroup } = require('../../../model/algo-vendor');
const { AccountDetail } = require('../../../model/account');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repoAccount } = require('../../../repository/account');
const { repoAlgo, AlgorithmClasses } = require('../../../repository/algorithm');

/**
 * 算法交易限制
 */
const AlgoLimits = [

    { label: '跌停不买涨停不卖', value: '0' },
    { label: '涨跌停继续交易', value: '1' },
    { label: '涨跌停不交易', value: '2' },
    { label: '跌停不卖涨停不买', value: '3' },
];

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '算法交易');
        this.directions = Object.values(this.systemTrdEnum.algoTradingDirection);
        this.status = TaskEntrustStatus;
        this.tasks = [new AlgoTaskInfo({})].splice(1);
    }

    /**
     * @param {AlgoTaskInfo} task 
     * @returns {AlgoTaskInfo}
     */
    asTask(task) {
        return task;
    }

    create() {

        if (this.dialog == undefined) {

            const CreationView = require('./creation');
            let dialog = this.dialog = new CreationView('@2023/algot/creation', false);
            dialog.registerEvent('save-success', this.handleEditing.bind(this));
            dialog.loadBuild(this.$dialog, null, () => { setTimeout(() => { this.raiseCreation(); }, 500); });
        }
        else {
            this.raiseCreation();
        }
    }

    raiseCreation() {
        this.dialog.openCreation();
    }

    handleEditing() {

        console.log('to reload task list / cause = saving successful');
        this.requestTasks(true);
    }

    setupTop() {

        this.top = {

            strategyCount: 0,
            runningStock: 0,
            runningCount: 0,
            stoppedCount: 0,
            totalTarget: 0,
            totalBuyTarget: 0,
            totalSellTarget: 0,
            totalTraded: 0,
        };
        
        this.vtop = new Vue({

            el: this.$top,
            data: this.top,
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [
                this.create,
                this.formatSelectAccountName,
            ]),
        });
    }

    handleAccountChange() {
        this.requestAlgoes();
    }

    toogleShowEntrusts() {

        let shows = this.tasks.filter(x => this.meetLocal(x));
        let display = shows.length == 0 ? 'none' : 'block';
        let $nodes = this.$container.querySelectorAll('.child-task-list, .child-task-title');
        $nodes.forEach($ele => $ele.style.display = display);
    }

    handleAlgoChange() {
        
        this.states.local.algoId = this.filter.algoId;
        this.toogleShowEntrusts();
    }
    
    handleInstanceChange() {

        this.states.local.taskId = this.filter.taskId;
        this.toogleShowEntrusts();
    }

    formatAlgoDirection(row_data, direction) {

        let matched = this.directions.find(x => x.code == direction);
        return matched ? matched.mean : direction;
    }
    
    filterTasks() {
        this.requestTasks(true);
    }

    /**
     * @param {AlgoTaskInfo} record 
     */
    identifyTask(record) {
        return record.taskId;
    }

    exportTasks() {
        
        let table = this.table4TaskExport;
        if (table == undefined) {

            let $table = this.$container.querySelector('.table-all-task');
            table = this.table4TaskExport = new SmartTable($table, this.identifyTask, this);
            table.setPageSize(9999);
        }
        
        table.refill(this.helper.deepClone(this.tasks));
        table.exportAllRecords(`算法母单-${new Date().format('yyyy-MM-dd')}`);
    }

    exportEntrusts() {
        
        let table = this.table4EntrustExport;
        if (table == undefined) {

            let $table = this.$container.querySelector('.table-all-entrust');
            table = this.table4EntrustExport = new SmartTable($table, this.identify, this);
            table.setPageSize(9999);
        }
        
        let entrusts = [];
        this.tasks.forEach(task => {
            task.entrusts.forEach(ent => {
                let { taskId, taskName } = task;
                entrusts.push(Object.assign({ taskId, taskName }, ent));
            });
        });

        table.refill(this.helper.deepClone(entrusts));
        table.exportAllRecords(`算法委托-${new Date().format('yyyy-MM-dd')}`);
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    exportUnclosedEntrusts(task) {
        
        let table = this.table4UnclosedEntrust;
        if (table == undefined) {

            let $table = this.$container.querySelector('.table-unclosed-entrust');
            table = this.table4UnclosedEntrust = new SmartTable($table, this.identify, this);
            table.setPageSize(9999);
        }
        
        let entrusts = task.entrusts.map(ent => this.helper.deepClone(ent));
        table.refill(entrusts);
        table.exportAllRecords(`剩余委托-${task.taskName}-${new Date().format('yyyy-MM-dd')}`);
    }
    
    cancelAll() {

        let runnings = this.tasks.filter(x => this.isTaskRunning(x));
        if (runnings.length == 0) {
            this.interaction.showInfo('没有运行中的母单');
            return;
        }

        let taskIds = runnings.map(x => x.taskId);
        this.interaction.showConfirm({

            title: '操作确认',
            message: '是否确认撤销全部母单？',
            confirmed: async () => {
                
                let resp = await repoAlgo.cancelOrder(taskIds);
                let { errorCode, errorMsg } = resp;

                if (errorCode == 0) {

                    this.interaction.showSuccess(`已批量撤单`);
                    this.tasks.forEach(task => { task.status = this.status.canceled; });
                    this.requestTasks(false);
                }
                else {
                    this.interaction.showError(`批量撤单错误：${errorCode}/${errorMsg}`);
                }
            },
        });
    }

    setupFilterRow() {

        this.accounts = [new AccountDetail({})].splice(1);
        this.algoGrps = [new GaoyuAlgoGroup()].splice(1);
        this.algoes = [new GaoyuAlgo({})].splice(1);
        this.instances = [{ id: null, name: null }].splice(1);

        this.filter = {

            accounts: this.accounts,
            instances: this.instances,

            recordId: null,
            algoId: null,
            algoGrps: this.algoGrps,
            taskId: null,
        };

        this.vtoolbar = new Vue({

            el: this.$control,
            data: this.filter,
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleAccountChange,
                this.handleAlgoChange, 
                this.handleInstanceChange,
                this.filterTasks,
                this.exportTasks,
                this.exportEntrusts,
                this.formatSelectAccountName,
                this.getProperAccountId,
                this.cancelAll,
            ]),
        });
    }

    /**
     * @param {AccountDetail} account 
     */
    getProperAccountId(account) {
        return this.helperUi.getProperAccountId(account);
    }

    /**
     * @param {AlgoTaskInfo} data 
     */
    isTaskRunning(data) {
        return data.status == this.status.running;
    }

    /**
     * @param {AlgoTaskInfo} data 
     */
    isTaskStopped(data) {
        return data.status == this.status.completed || data.status == this.status.canceled;
    }

    /**
     * @param {AlgoTaskInfo} data 
     */
    hasNotCompleted(data) {
        return data.totalTarget > 0 && data.totalTraded < data.totalTarget;
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    meetLocal(task) {

        let { algoId, taskId } = this.states.local;
        let is_ok1 = this.helper.isNone(algoId) || algoId == task.algoId;
        let is_ok2 = this.helper.isNone(taskId) || taskId == task.taskId;
        return is_ok1 && is_ok2;
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    isCurrentChoosed(task) {

        let ref = this.states.selected;
        return ref && ref.taskId == task.taskId;
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    handleTaskCheck(task) {
        this.setAsChoosed(task);
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    async stopm(task) {

        let resp = await repoAlgo.cancelOrder([task.taskId]);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess(`${task.taskName}，母单已撤单`);
            task.status = this.status.canceled;
            this.requestTasks(false);
        }
        else {
            this.interaction.showError(`任务撤单错误：${errorCode}/${errorMsg}`);
        }
    }

    setupTaskView() {

        this.states = {

            selected: this.asTask(null),
            /** 映射的本来筛选条件 */
            local: { algoId: null, taskId: null },
        };

        this.vtask = new Vue({

            el: this.$task,
            data: {
                tasks: this.tasks,
                states: this.states,
            },
            mixins: [NumberMixin, DatetimeMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.isTaskRunning,
                this.isTaskStopped,
                this.isCurrentChoosed,
                this.hasNotCompleted,
                this.meetLocal,
                this.stopm,
                this.handleTaskCheck,
                this.formatTaskStatus,
                this.formatTaskProgress,
                this.formatTaskMoney,
                this.formatActionLimit,
                this.formatAlgoType,
                this.formatTaskTime,
                this.formatSelectAccountName,
                this.exportUnclosedEntrusts,
            ]),
        });
    }

    setupTitleView() {

        this.caption = { title: '任务委托列表', keywords: null }
        this.vtask = new Vue({

            el: this.$title,
            data: this.caption,
            methods: this.helper.fakeVueInsMethod(this, [
                this.filterSubTasks,
            ]),
        });
    }

    filterSubTasks() {
        this.tentrust.setKeywords(this.caption.keywords);
    }

    listen2Events() {
        this.registerEvent('on-view-visible', () => { this.tentrust.fitColumnWidth(); });
    }

    async requestAccounts() {

        var resp = await repoAccount.batchGetAccountCash();
        var { errorCode, errorMsg, data } = resp;
        
        if (errorCode == 0) {

            let accounts = data instanceof Array ? data.map(x => new AccountDetail(x)) : [];
            this.accounts.refill(accounts);
        }
        else {
            this.interaction.showError('账号获取异常：' + errorMsg);
        }
    }

    async requestAlgoes() {

        var account = this.accounts.find(x => this.getProperAccountId(x) == this.filter.recordId);
        if (!account) {

            this.algoGrps.clear();
            this.algoes.clear();
            return;
        }

        var resp = await repoAlgo.queryAlgoesV2403(AlgorithmClasses.normal, account.accountId);
        var { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {
            return this.interaction.showError(`算法加载失败：${errorCode}/${errorMsg}`);
        }

        var algoes = Array.isArray(data) ? data.map(x => new GaoyuAlgo(x)) : [];
        var map = algoes.groupBy(x => x.brokerId);
        var flattends = [];
        this.algoGrps.clear();
        
        for (let key in map) {

            let gmembers = map[key];
            this.algoGrps.push(new GaoyuAlgoGroup(key, gmembers));
            flattends.merge(gmembers);
        }

        this.algoes.refill(flattends);
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    identify(record) {
        return record.id;
    }

    setupEntrustTable() {

        this.helper.extend(this, ColumnCommonFunc);
        let tentrust = new SmartTable(this.$table, this.identify, this, { tableName: 'smt-vb28', displayName: '算法单详情', headerHeight: 32, rowHeight: 32 });
        tentrust.setPageSize(9999);
        tentrust.hideColumns(['名称']);
        this.tentrust = tentrust;
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    formatCodeName(record) {
        return `${record.instrument} / ${record.instrumentName}`;
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    formatProgress(record) {

        let method = NumberMixin.methods.thousandsInt;
        return `${method(record.tradedVolume)} / ${method(record.volume)}`;
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    formatTaskStatus(task) {
        return this.isTaskRunning(task) ? '运行中' : '已停止';
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    formatTaskProgress(task) {

        let { totalTarget, totalTraded } = task;
        return (totalTarget > 0 ? totalTraded * 100 / totalTarget : 0).toFixed(2) + '%';
    }

    formatTaskTime(time) {

        if (!time) {
            return '--';
        }
        else  if (typeof time == 'string' && time.length == 6) {
            return time;
        }
        else {
            return time ? ColumnCommonFunc.formatTime({}, time, null) : '--';
        }
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    formatTaskMoney(task) {
        return (task.algoParam || {}).money || '--';
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    formatActionLimit(task) {

        let limit = (task.algoParam || {}).limitAction;
        let matched = AlgoLimits.find(x => x.value == limit);
        return matched ? matched.label : (limit || '--');
    }

    /**
     * @param {AlgoTaskInfo} task 
     */
    formatAlgoType(task) {

        let atype = (task.algoParam || {}).AlgoType;
        return atype || '--';
    }

    /**
     * @param {AlgoEntrustInfo} data 
     */
    isEntrustStopped(data) {
        return data.algorithmStatus == this.status.completed || data.algorithmStatus == this.status.canceled;
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    formatActions(record) {
        return this.isEntrustStopped(record) ? '<span>已停止</span>' : '<button class="danger" event.onclick="stopEntrust">停止</button>';
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    async stopEntrust(record) {

        let resp = await repoAlgo.cancelOrder([record.id]);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.tentrust.updateRow({ id: record.id, algorithmStatus: this.status.canceled });
            this.interaction.showSuccess(`${record.instrument} / ${record.instrumentName}，委托已撤单`);
        }
        else {
            this.interaction.showError(`任务撤单错误：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {AlgoTaskPackage} datap 
     */
    summarize(datap) {

        const top_ref = this.top;

        top_ref.runningStock = datap.runningStock;
        top_ref.strategyCount = datap.tasks.length;
        top_ref.runningCount = datap.tasks.filter(x => this.isTaskRunning(x)).length;
        top_ref.stoppedCount = datap.tasks.filter(x => this.isTaskStopped(x)).length;
        top_ref.totalTarget = datap.totalTarget;
        top_ref.totalBuyTarget = datap.totalBuyTarget;
        top_ref.totalSellTarget = datap.totalSellTarget;
        top_ref.totalTraded = datap.totalTraded;
    }

    /**
     * @param {AlgoTaskInfo | undefined} task 
     */
    setAsChoosed(task) {

        this.states.selected = task;
        this.tentrust.refill(task ? task.entrusts : []);
        this.caption.title = task ? `${task.taskName} / 任务委托列表` : '任务委托列表';

        if (this.caption.keywords) {
            this.filterSubTasks();
        }
    }

    /**
     * @param {Array<AlgoTaskInfo>} tasks 
     */
    updateTaks(tasks) {

        let sorteds = tasks.map(x => ({ id: x.taskId, name: x.taskName })).sort((x, y) => this.helper.compare(x.name, y.name, false));
        let sorted_algos = tasks.sort((x, y) => this.helper.compare(y.createTime, x.createTime));
        this.instances.refill(sorteds);
        this.tasks.refill(sorted_algos);
        let ref = this.states.selected;
        this.setAsChoosed(ref ? tasks.find(x => x.taskId == ref.taskId) : tasks[0]);
    }

    async requestTasks(show_loading) {
        
        let record_id = this.filter.recordId;
        let account = this.accounts.find(x => this.getProperAccountId(x) == record_id);
        let params = {

            algorithm_class: 0, 
            fund_id: account ? account.fundId : null,
            strategy_id: account ? account.strategyId : null,
            account_id: account ? account.accountId : null,
        };

        let $loading = show_loading ? this.interaction.showLoading({ text: `正在查询任务列表...` }) : null;

        try {

            let resp = await repoAlgo.qtasks(params);
            let { data, errorCode, errorMsg } = resp;
            let key = 'taskMap';
            let map = data[key] || {};
            let members = [];
            for (let key in map) {

                map[key].taskId = key;
                members.push(map[key]);
            }
            data[key] = members;

            if (errorCode == 0) {

                let datap = new AlgoTaskPackage(data);
                this.summarize(datap);
                this.updateTaks(datap.tasks);
            }
            else {
                this.interaction.showError(`母单任务查询错误：${errorCode}/${errorMsg}`);
            }            
        }
        catch(ex) {
            console.error(ex);
        }
        finally {
            $loading && $loading.close();
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.requestTasks(true);
    }
    
    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    keepRefreshed() {

        this.refreshJob = setInterval(async () => {
            
            if (this._isJobRunning) {
                return;
            }

            this._isJobRunning = true;
            try { await this.requestTasks(false); } catch(ex) {}
            this._isJobRunning = false;

        }, 5 * 1000);
    }

    build($container) {

        super.build($container);
        this.$top = this.$container.querySelector('.top-box');
        this.$control = this.$container.querySelector('.control-box');
        this.$task = this.$container.querySelector('.morder-list');
        this.$title = this.$container.querySelector('.child-task-title');
        this.$table = this.$container.querySelector('.child-task-list');
        this.$dialog = this.$container.querySelector('.creation-dialog-external');

        this.setupTop();
        this.setupFilterRow();
        this.setupTaskView();
        this.setupTitleView();
        this.setupEntrustTable();
        this.listen2Events();

        this.requestAccounts();
        this.requestAlgoes();
        this.requestTasks(true);
        this.keepRefreshed();
    }
}

module.exports = View;
