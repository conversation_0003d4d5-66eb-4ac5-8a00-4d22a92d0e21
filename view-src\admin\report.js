const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');
const TemplateDesgin = require('./template-design');

class View extends BaseAdminView {
    
    get repoIndicator() {
        return require('../../repository/indicator').repoIndicator;
    }

    get repoUser() {
        return require('../../repository/user').repoUser;
    }

    constructor(view_name) {
        super(view_name, '报告管理');
        this.$container = null;
        this.list = [];
        this.listHash = {};
        this.context = {
            currentIndicator: null,
            currentDocument: null,
        };
        this.reportList = [];
        this.reportListHash = {};
        this.documentList = [];
        this.documentListHash = {};
        this.users = {
            //绑定的用户候选项
            data: [],
            //所有的用户列表
            full: [],
            loaded: false,
        };
        this.userListHash = {};
        let SOURCE = {
            system: {
                value: 0,
                text: '系统',
            },
            self: {
                value: 1,
                text: '自定',
            },
        };
        let STATUS = {
            open: {
                value: 0,
                text: '公开',
            },
            close: {
                value: 1,
                text: '不公开',
            },
        };

        this.tabs = {
            indicator: {
                loaded: false,
            },
            template: {
                loaded: false,
                query: {
                    keyword: '',
                    pageSize: 30,
                    pageNo: 1,
                },
            },
            report: {
                loaded: false,
            },
        };

        this.constant = {
            SOURCE: SOURCE,
            SOURCE_OPTIONS: this.helper.dict2Array(SOURCE),
            STATUS: STATUS,
            STATUS_OPTIONS: this.helper.dict2Array(STATUS),
            INDICATOR_VALUE_TYPES_OPTIONS: [
                { value: 1, text: '单值序列' },
                { value: 2, text: '多值序列' },
            ],
        };

        this.dialog = {
            visible: false,
            data: this.createEmptyIndicator(),
            rules: {
                name: { required: true, message: '请输入指标名称', trigger: 'blur' },
                valueType: { required: true, message: '请选择指标值类型', trigger: 'blur' },
                sourceType: { required: true, message: '请选择来源', trigger: 'blur' },
            },
        };

        this.reportDialog = {
            visible: false,
            data: {
                mergedIds: [],
            },
            rules: {
                mergedIds: { required: true, message: '请至少选择一个模板', trigger: 'blur' },
            },
        };

        this.documentDialog = {
            visible: false,
            data: this.createEmptyDocument(),
            rules: {
                reportName: { required: true, message: '请填写报告名称', trigger: 'blur' },
            },
        };

        this.reportViewDialog = {
            visible: false,
            title: '',
        };

        this.shareDialog = {
            visible: false,
            data: {
                shareUser: [],
            },
            rules: {
                shareUser: { type: 'array', required: true, message: '请至少选择一个用户', trigger: 'change' },
            },
        };

        this.templateDesign = null;
    }

    createApp() {
        let controller = this;
        this.tableProps = { maxHeight: 500, ...this.systemSetting.tableProps };
        this.vueApp = new Vue({
            el: this.$container.querySelector('.report-view-root'),
            data: {
                tab: 'indicator',
                tabs: this.tabs,
                list: this.list,
                users: this.users,
                reportList: this.reportList,
                documentList: this.documentList,
                context: this.context,
                dialog: this.dialog,
                reportDialog: this.reportDialog,
                documentDialog: this.documentDialog,
                shareDialog: this.shareDialog,
                reportViewDialog: this.reportViewDialog,
                constant: this.constant,
                tableProps: this.tableProps,
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'el-icon-search',
                    },
                },
                templateDialog: {
                    visible: false,
                },
            },
            components: {
                DataTables: DataTables.DataTables,
                DataTablesServer: DataTables.DataTablesServer,
            },
            directives: {
                drag,
            },
            mixins: [],
            methods: {
                handleClose() {
                    controller.destroyReportTemplate();
                },
                tabChange: (tab) => {
                    let name = tab.name;
                    switch (name) {
                        case 'indicator':
                            this.loadIndicatorList();
                            break;
                        case 'template':
                            this.loadTemplateReportList();
                            break;
                        case 'report':
                            this.loadReportDocumentList();
                            break;
                        default:
                            console.log('default');
                            break;
                    }
                },
                saveDocumentReport: () => {
                    this.vueApp.$refs.documentDialog.validate((valid) => {
                        if (valid) {
                            this.saveDocumentReport(this.helper.deepClone(this.documentDialog.data)).then(() => {
                                this.vueApp.closeDocumentDialog();
                            });
                        }
                    });
                },
                setCurrentDocument: (this_document) => {
                    this.context.currentDocument = this_document;
                },
                closeDocumentDialog: () => {
                    this.documentDialog.data = this.createEmptyDocument();
                    this.vueApp.$refs.documentDialog.resetFields();
                    this.vueApp.$refs.documentDialog.clearValidate();
                    this.documentDialog.visible = false;
                },
                createDocument: () => {
                    if (!this.tabs.template.loaded) {
                        this.loadTemplateReportList().then(() => {
                            this.documentDialog.visible = true;
                        });
                    } else {
                        this.documentDialog.visible = true;
                    }
                },
                viewDocument: (this_document) => {
                    this.want2ViewReport(this_document);
                },
                editDocument: (this_document) => {
                    this.helper.extend(this.documentDialog.data, this_document);
                    if (!this.tabs.template.loaded) {
                        this.loadTemplateReportList().then(() => {
                            this.documentDialog.visible = true;
                        });
                    } else {
                        this.documentDialog.visible = true;
                    }
                },
                closeShare: () => {
                    this.shareDialog.data.shareUser = [];
                    this.vueApp.$refs.shareDialog.resetFields();
                    this.vueApp.$refs.shareDialog.clearValidate();
                    this.shareDialog.visible = false;
                },
                shareToUser: () => {
                    this.vueApp.$refs.shareDialog.validate((valid) => {
                        if (valid) {
                            this.interaction.showConfirm({
                                title: '提示',
                                message: '确定要将当前报告分享给该用户吗?',
                                confirmed: () => {
                                    this.shareToUser(this.context.currentDocument.id, this.shareDialog.data.shareUser).then((users) => {
                                        this.context.currentDocument.shareUsers = users.map((item) => {
                                            let this_user = this.userListHash[item.userId || item.id] || {};
                                            return Object.assign(item, {
                                                fullName: this_user.fullName,
                                            });
                                        });
                                        this.shareDialog.data.shareUser = [];
                                        this.vueApp.$refs.shareDialog.resetFields();
                                        this.vueApp.$refs.shareDialog.clearValidate();
                                        this.shareDialog.visible = false;
                                    });
                                },
                            });
                        }
                    });
                },
                shareDocument: (this_document) => {
                    this.context.currentDocument = this_document;
                    if (!this.users.loaded) {
                        this.getUsers().then(() => {
                            this.shareDialog.visible = true;
                            this.shareDialog.data.shareUser.clear();
                            this.shareDialog.data.shareUser.merge(this_document.shareUsers.map((x) => x.userId) || []);
                            this.users.loaded = true;
                            this.vueApp.$refs.shareDialog ? this.vueApp.$refs.shareDialog.clearValidate() : null;
                        });
                    } else {
                        this.shareDialog.visible = true;
                        this.shareDialog.data.shareUser.clear();
                        this.shareDialog.data.shareUser.merge(this_document.shareUsers.map((x) => x.userId) || []);
                        this.vueApp.$refs.shareDialog ? this.vueApp.$refs.shareDialog.clearValidate() : null;
                    }
                },
                refreshDocument: () => {
                    this.tabs.report.loaded = false;
                    this.loadReportDocumentList();
                },
                removeDocument: (this_document) => {
                    let id = this_document.id;
                    this.interaction.showConfirm({
                        title: '警告',
                        message: '确定要删除当前报告吗?',
                        confirmed: () => {
                            this.removeDocument(id);
                        },
                    });
                },
                formatValueType: (indicator) => {
                    let value_type = this.constant.INDICATOR_VALUE_TYPES_OPTIONS.find((x) => x.value === indicator.valueType) || {};
                    return value_type.text || '未知';
                },
                formatSource: (indicator) => {
                    let source_type = this.constant.SOURCE_OPTIONS.find((x) => x.value == indicator.sourceType) || {};
                    return source_type.text || '未知';
                },
                refreshIndicator: () => {
                    this.tabs.indicator.loaded = false;
                    this.loadIndicatorList();
                },
                removeIndicator: (indicator) => {
                    this.interaction.showConfirm({
                        title: '提示',
                        message: '确定要删除当前选择的指标吗？',
                        confirmed: () => {
                            this.removeIndicator(indicator);
                        },
                    });
                },
                handleQueryOrderChange: (payload) => {
                    this.tabs.template.loaded = false;
                    let keyword = null;
                    try {
                        keyword = payload.filters[0].vals[0];
                    } catch (e) {
                        keyword = '';
                    }
                    this.loadTemplateReportList({
                        pageNo: payload.page,
                        pageSize: payload.pageSize,
                        keyword: keyword,
                    });
                },
                updateIndicator: (indicator) => {
                    this.saveIndicator(indicator);
                },
                editIndicator: (indicator) => {
                    this.context.currentIndicator = indicator;
                    this.dialog.visible = true;
                    this.helper.extend(this.dialog.data, indicator);
                },
                createIndicator: () => {
                    this.dialog.visible = true;
                    this.dialog.data = this.createEmptyIndicator();
                },
                submitIndicator: function () {
                    let exist = false;
                    let model = this.dialog.data;
                    if (!model.id) {
                        exist = !!this.list.find((x) => x.name === model.name);
                    } else {
                        exist = !!this.list.filter((x) => x.id !== model.id).find((x) => x.name === model.name);
                    }

                    if (exist) {
                        controller.interaction.showWarning('已经存在同名指标，无法继续创建!');
                        return false;
                    }

                    this.$refs.indicator.validate((valid) => {
                        if (valid) {
                            controller.interaction.showConfirm({
                                title: '提示',
                                message: '确定要提交吗？',
                                confirmed: async () => {
                                    let model = controller.helper.deepClone(this.dialog.data);
                                    let result = await controller.saveIndicator(model);
                                    if (result) {
                                        this.closeIndicatorEdit();
                                    }
                                },
                            });
                        }
                    });
                },
                closeIndicatorEdit: function () {
                    this.dialog.data = controller.createEmptyIndicator();
                    controller.vueApp.$refs.indicator.resetFields();
                    this.dialog.visible = false;
                },
                createReportTemplate() {
                    this.templateDialog.visible = !this.templateDialog.visible;
                    this.$nextTick(() => {
                        if (!this.templateDialog.loaded) {
                            controller.buildReportTemplate();
                        }
                    });
                },
                copyReportTemplate: () => {
                    this.interaction.showAlert('暂未实现!');
                },
                editReportTemplate(template) {
                    let tId = template.id;
                    // this.editReportTemplate(tId || null);
                    this.templateDialog.visible = !this.templateDialog.visible;
                    this.$nextTick(() => {
                        if (!this.templateDialog.loaded) {
                            controller.buildReportTemplate(tId || null);
                        }
                    });
                },
                refreshTemplate: () => {
                    this.tabs.template.loaded = false;
                    this.loadTemplateReportList(this.tabs.template.query);
                },
                removeReportTemplate: (template) => {
                    this.interaction.showConfirm({
                        title: '提示',
                        message: '确定要删除当前报告模板吗？',
                        confirmed: () => {
                            this.removeReportTemplate(template);
                        },
                    });
                },
                closeReportDialog() {
                    this.reportDialog.visible = false;
                    this.reportDialog.data.mergedIds = [];
                    this.$refs.model.resetFields();
                },
            },
        });
        this.vueApp.$nextTick(() => {
            this.resizeWindow();
        });
    }

    want2ViewReport(item) {
        var identity = item.id;
        var identity_name = item.reportName;
        var template_id = item.templateId;

        this.reportViewDialog.visible = true;
        this.reportViewDialog.title = '报告 > ' + identity_name;

        var param = {
            identity: identity,
            identityName: identity_name,
            templateId: template_id,
        };

        setTimeout(() => {
            this.buildReport(param);
        }, 200);
    }

    buildReport(param) {
        const NewReport = require('./new-report');

        if (this.newReport) {
            this.newReport.dispose();
            delete this.newReport;
        }

        this.newReport = new NewReport('@admin/new-report', true, { title: `报告 > ${param.identityName}` });
        this.newReport.trigger('setContextData', param);
        this.newReport.loadBuild(this.vueApp.$refs.reportViewDialog.$el.querySelector('.container-content'));
    }

    constructIndicator(model) {
        return {
            id: model.id,
            name: model.name,
            valueType: model.valueType,
            sourceType: model.sourceType,
            description: model.description || '暂无介绍',
            orgId: this.userInfo.orgId,
            createUser: this.userInfo.userId,
        };
    }

    async saveIndicator(model) {
        let flag = model.id !== 0 && !model.id;
        let loading = this.interaction.showLoading({
            text: '正在保存，请稍后...',
        });

        let result = false;
        try {
            let decorated = this.constructIndicator(model);
            let resp = flag ? await this.repoIndicator.saveIndicatorList(decorated) : await this.repoIndicator.updateIndicatorList(decorated);
            if (resp.errorCode === 0) {
                let respData = this.formatTemplateElement(resp.data || this.helper.deepClone(model));
                if (flag) {
                    //将数据插入到列表里面去
                    this.listHash[respData.id] = respData;
                    this.list.unshift(respData);
                } else {
                    //将数据更新到列表里面去
                    let updateModel = this.list.find((x) => x.id === respData.id) || {};
                    this.helper.extend(updateModel, respData);
                    this.listHash[respData.id] = respData;
                }
                result = true;
                this.interaction.showSuccess('保存指标信息成功!');
            } else {
                this.interaction.showError(`保存指标信息失败，详细信息:${resp.errorCode}/${resp.errorCode}`);
            }
        } catch (e) {
            this.interaction.showError('保存指标信息失败!');
        } finally {
            loading.close();
        }
        return result;
    }

    async removeIndicator(indicator) {
        let tId = indicator.id;
        let loading = this.interaction.showLoading({
            text: '正在删除，请稍后...',
        });
        try {
            let resp = await this.repoIndicator.removeIndicatorList(tId);
            if (resp.errorCode === 0) {
                this.list.remove((x) => x.id === tId && x.id !== undefined);
                delete this.listHash[tId];
                this.interaction.showSuccess('删除指标成功!');
            } else {
                this.interaction.showError(`删除指标失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('删除指标失败!');
        } finally {
            loading.close();
        }
    }

    createEmptyIndicator() {
        return {
            id: null,
            name: null,
            valueType: null,
            sourceType: null,
            status: this.constant.STATUS.open.value,
        };
    }

    formatTemplateElement(element) {
        let userDetail = this.userListHash[element.createUser];
        return {
            id: element.id,
            name: element.name,
            valueType: element.valueType,
            createUser: element.createUser,
            createUserName: userDetail ? userDetail.fullName : '---',
            sourceType: element.sourceType,
        };
    }

    async loadIndicatorList() {
        if (this.tabs.indicator.loaded) {
            return;
        }
        let loading = this.interaction.showLoading({
            text: '获取列表信息中...',
        });
        try {
            await this.getUsers();
            this.list.clear();
            this.tabs.indicator.loaded = true;
            let resp = await this.repoIndicator.getIndicatorList();
            if (resp.errorCode == 0) {
                let dataSrc = resp.data || [];
                let decoratedDataSrc = dataSrc
                    .map((meta) => {
                        let tId = meta.id;
                        let correctElement = this.formatTemplateElement(meta);
                        this.listHash[tId] = correctElement;
                        return correctElement;
                    })
                    .orderByDesc((x) => x.id);
                this.list.merge(decoratedDataSrc);
            } else {
                this.interaction.showError(`获取列表信息失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('获取列表信息失败!');
        } finally {
            loading.close();
        }
    }
    ///

    async removeReportTemplate(template) {
        let tId = template.id;
        let loading = this.interaction.showLoading({
            text: '正在删除，请稍后...',
        });
        try {
            let resp = await this.repoIndicator.removeReportTemplate(tId);
            if (resp.errorCode === 0) {
                this.reportList.remove((x) => x.id === tId && x.id !== undefined);
                delete this.reportListHash[tId];
                this.interaction.showSuccess('删除报告模板成功!');
            } else {
                this.interaction.showError(`删除报告模板失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('删除报告模板失败!');
        } finally {
            loading.close();
        }
    }

    formatReportElement(element) {
        return element;
    }

    formatDocument(element) {
        element.shareUsers = (element.shareUsers || []).map((user) => {
            let hashUser = this.userListHash[user.userId] || {};
            user.fullName = hashUser.fullName;
            return user;
        });
        return this.createEmptyDocument(element);
    }

    createEmptyDocument(input_document) {
        input_document = input_document || {};
        return {
            id: input_document.id || null,
            reportName: input_document.reportName || '',
            templateId: input_document.templateId || null,
            templateName: (input_document.templateId && input_document.templateName) || '',
            description: input_document.description || '',
            shareUsers: input_document.shareUsers || [],
        };
    }

    async saveDocumentReport(model) {
        let loading = this.interaction.showLoading({
            text: '正在保存，请稍后...',
        });
        let result = false;
        try {
            let resp = model.id ? await this.repoIndicator.updateReportDocument(model) : await this.repoIndicator.createReportDocument(model);
            if (resp.errorCode === 0) {
                result = true;
                let saved_document = !model.id ? resp.data || {} : model;
                let id = saved_document.id;
                if (saved_document.templateId) {
                    let template = this.reportListHash[saved_document.templateId] || {};
                    saved_document.templateName = template.report_info ? template.report_info.title_format : '';
                } else {
                    saved_document.templateName = '';
                }
                this.documentListHash[id] = this.formatDocument(saved_document);
                if (!model.id) {
                    this.documentList.unshift(this.formatDocument(saved_document));
                } else {
                    let update_document = this.documentList.find((doc) => doc.id === model.id) || {};
                    this.helper.extend(update_document, model);
                }
                this.interaction.showSuccess('保存报告信息成功!');
            } else {
                this.interaction.showError(`保存报告信息失败，错误信息：${resp.errorCode}/${resp.errorMsg}!`);
            }
        } catch (e) {
            this.interaction.showError('保存报告信息失败!');
        } finally {
            loading.close();
        }
        return Promise.resolve(result);
    }

    async removeDocument(id) {
        let loading = this.interaction.showLoading({
            text: '正在删除，请稍后...',
        });
        try {
            let resp = await this.repoIndicator.removeReportDocument(id);
            if (resp.errorCode === 0) {
                this.documentList.remove((x) => x.id === id);
                delete this.documentListHash[id];
                this.interaction.showSuccess('删除报告成功!');
            } else {
                this.interaction.showError(`删除报告失败,详细信息:${resp.errorCode}/${resp.errorMsg}!`);
            }
        } catch (e) {
            this.interaction.showError('删除报告失败!');
        } finally {
            loading.close();
        }
    }

    async getUsers() {
        let loading = this.interaction.showLoading({
            text: '获取用户信息中...',
        });
        try {
            this.users.data.clear();
            this.users.full.clear();
            let resp = await this.repoUser.getAll();
            if (resp.errorCode === 0) {
                let result = (resp.data || []).filter((x) => x.roleId === this.systemUserEnum.userRole.tradingMan.code || x.roleId === this.systemUserEnum.userRole.riskProtector.code);
                (resp.data || []).forEach((user) => {
                    this.userListHash[user.id] = user;
                });
                this.users.data.merge(result);
                this.users.full.merge(resp.data || []);
            } else {
                this.interaction.showError(`获取用户列表失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('获取用户列表失败!');
        } finally {
            loading.close();
        }
    }

    async loadReportDocumentList(params) {
        params = params || {};
        if (this.tabs.report.loaded) {
            return;
        }
        let loading = this.interaction.showLoading({
            text: '获取列表信息中...',
        });
        try {
            this.tabs.report.loaded = true;
            this.documentList.clear();
            await this.getUsers();
            let resp = await this.repoIndicator.getReportDocumentList(params);
            if (resp.errorCode === 0) {
                if (!this.tabs.template.loaded) {
                    await this.loadTemplateReportList();
                }
                let dataSrc = resp.data || [];
                let decoratedDataSrc = dataSrc.map((meta) => {
                    let dId = meta.id;
                    if (meta.templateId) {
                        let template = this.reportListHash[meta.templateId] || {};
                        meta.templateName = template.report_info ? template.report_info.title_format : '';
                    } else {
                        meta.templateName = '';
                    }
                    this.documentListHash[dId] = this.formatDocument(meta);
                    return this.formatDocument(meta);
                });
                this.documentList.merge(decoratedDataSrc.orderByDesc((x) => x.id));
                console.log(this.documentList, this.reportList);
            } else {
                this.interaction.showError(`获取列表信息失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('获取列表信息失败!');
        } finally {
            loading.close();
        }
    }

    async loadTemplateReportList(params) {
        params = params || {};
        if (this.tabs.template.loaded) {
            return;
        }
        let loading = this.interaction.showLoading({
            text: '获取列表信息中...',
        });
        try {
            this.tabs.template.loaded = true;
            this.helper.extend(this.tabs.template.query, params);
            this.reportList.clear();
            let resp = await this.repoIndicator.getReportTemplateList({
                key_word: params.keyword || '',
                page_size: params.pageSize || 20,
                page_no: params.pageNo || 1,
            });
            if (resp.errorCode == 0) {
                let dataSrc = resp.data || [];
                let decoratedDataSrc = dataSrc.map((meta) => {
                    let tId = meta.id;
                    let correctElement = this.formatReportElement(meta);
                    this.reportListHash[tId] = correctElement;
                    return correctElement;
                });
                this.reportList.merge(decoratedDataSrc.orderByDesc((x) => x.id));
            } else {
                this.interaction.showError(`获取列表信息失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('获取列表信息失败!');
        } finally {
            loading.close();
        }
    }

    //build报告模板
    buildReportTemplate(tId) {
        this.templateDesign = new TemplateDesgin('@admin/template-design', true, { title: '模板设计' });
        let el = this.vueApp.$refs.templateDesign.$el;
        this.templateDesign.registerEvent('save', this.vueApp.refreshTemplate);
        this.templateDesign.trigger('setContextData', { template_id: tId || '' });
        this.templateDesign.loadBuild(el.querySelector('.container-content'));
    }

    updateReportList(params) {
        let tid = params.id;
        let exist = this.reportList.find((x) => x.id === tid && tid !== undefined);
        if (!exist) {
            let element = {
                id: params.id,
                create_user_name: params.create_user_name,
                org_id: params.org_id,
                report_info: {
                    title_format: params.template_name,
                },
            };
            this.reportList.unshift(element);
            this.reportListHash[tid] = element;
        } else {
            exist.report_info.title_format = params.template_name;
            this.reportListHash[tid] = exist;
        }
    }

    //删除报告模板
    destroyReportTemplate() {
        if (this.templateDesign != null) {
            this.templateDesign.dispose();
            this.templateDesign = null;
            let el = this.vueApp.$refs.templateDesign.$el;
            let $container = el.querySelector('.container-content');
            while ($container.hasChildNodes()) {
                $container.removeChild($container.firstChild);
            }
            this.vueApp.templateDialog.visible = false;
        }
    }

    async shareToUser(reportId, shareUserIds) {
        let loading = this.interaction.showLoading({
            text: '正在分享报告中...',
        });
        let result = [];
        try {
            let resp = await this.repoIndicator.shareReportDocument(reportId, shareUserIds, 1);
            if (resp.errorCode === 0) {
                this.interaction.showSuccess('报告已分享成功!');
                result = resp.data || [];
            } else {
                this.interaction.showError(`分享报告失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            console.log(e);
            this.interaction.showError('分享报告失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(result);
    }

    resizeWindow() {
        
        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.maxHeight = winHeight - 166;
    }

    build($container) {
        this.$container = $container;
        this.loadIndicatorList();
        this.createApp();
    }
}

module.exports = View;
