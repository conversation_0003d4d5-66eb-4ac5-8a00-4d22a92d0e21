<script setup lang="ts">
// 内部日期范围状态
const dateRange = defineModel<[string, string]>({ default: ['', ''] });

// 快捷选项
const shortcuts = [
  {
    text: '近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 7);
      return [start, end] as [Date, Date];
    },
  },
  {
    text: '近一月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 1);
      return [start, end] as [Date, Date];
    },
  },
  {
    text: '近三月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 3);
      return [start, end] as [Date, Date];
    },
  },
  {
    text: '近半年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 6);
      return [start, end] as [Date, Date];
    },
  },
  {
    text: '近一年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setFullYear(start.getFullYear() - 1);
      return [start, end] as [Date, Date];
    },
  },
  {
    text: '近三年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setFullYear(start.getFullYear() - 3);
      return [start, end] as [Date, Date];
    },
  },
  {
    text: '近五年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setFullYear(start.getFullYear() - 5);
      return [start, end] as [Date, Date];
    },
  },
];

// 暴露方法给父组件
defineExpose({
  dateRange,
});
</script>

<template>
  <div flex aic>
    <span mr-8>日期范围:</span>
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      size="small"
      value-format="YYYYMMDD"
      w-240
      mr-16
      :shortcuts="shortcuts"
      :unlink-panels="true"
      :clearable="false"
    />
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
