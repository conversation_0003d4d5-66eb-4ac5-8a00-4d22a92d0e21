import { LogLevel } from '../config/architecture';

/**
 * 默认控制台日志名称
 */
const ConsoleLoggerName = 'Default';

/**
 * console输出文本颜色
 */
const colors: { [level: number]: string } = {
    
	[LogLevel.ALL]: '\x1b[37m',   // 白色
	[LogLevel.TRACE]: '\x1b[36m', // 青色
	[LogLevel.DEBUG]: '\x1b[34m', // 蓝色
	[LogLevel.INFO]: '\x1b[32m',  // 绿色
	[LogLevel.WARN]: '\x1b[33m',  // 黄色
	[LogLevel.ERROR]: '\x1b[31m', // 红色
	[LogLevel.FATAL]: '\x1b[35m', // 洋红色
	[LogLevel.OFF]: '\x1b[30m',   // 黑色
	[-1]: '\x1b[0m', // 重置样式
};

/**
 * 打印日志
 */
function outputLog(level: LogLevel, message: string, ...args: any) {

    const datetime = new Date().toISOString();
    const levelName = LogLevel[level];
	const color = colors[level] || colors[-1];
    const content = `${color}[${datetime}] [${levelName}] [${ConsoleLoggerName}] ${message}${colors[-1]}`;

    if (level === LogLevel.ERROR || level === LogLevel.FATAL) {
        console.error(content, ...args);
    } else if (level === LogLevel.WARN) {
        console.warn(content, ...args);
    } else if (level === LogLevel.DEBUG) {
        console.debug(content, ...args);
    } else {
        console.log(content, ...args);
    }
}

/**
 * 日志接口
 */
export interface ILogger {

    /**
     * 日志名称
     */
    get name(): string;

    /**
     * 设置日志级别
     */
    setLogLevel(level: LogLevel): void;

    /**
     * 打印所有日志
     */
    log(message: string, ...args: any): void;

    /**
     * 比 DEBUG 更详细的调试信息，通常用于追踪程序执行的每一步
     */
    trace(message: string, ...args: any): void;

    /**
     * 调试信息，主要用于开发阶段排查问题
     */
    debug(message: string, ...args: any): void;

    /**
     * 一般信息，表示应用程序正常运行时的关键事件
     */
    info(message: string, ...args: any): void;

    /**
     * 警告信息，表示可能有问题但不会影响程序继续运行
     */
    warn(message: string, ...args: any): void;

    /**
     * 错误信息，表示发生了错误，但程序仍然可以继续运行
     */
    error(message: string, ...args: any): void;

    /**
     * 致命错误信息，表示非常严重的错误，可能导致程序崩溃
     */
    fatal(message: string, ...args: any): void;
}

/**
 * 控制台日志类
 */
export class ConsoleLogger implements ILogger {

    /** 日志级别 */
    private currentLogLevel: LogLevel;

    get name() {
        return ConsoleLoggerName;
    }

    constructor() {
        this.currentLogLevel = LogLevel.ALL;
    }
    
    setLogLevel(level: LogLevel): void {

        const levels = Object.values(LogLevel);
        const index = levels.indexOf(level);
        
        if (index === -1) {
            throw new Error('Invalid log level');
        }

        this.currentLogLevel = level;
    }

    log(message: string, ...args: any): void {

        if (this.currentLogLevel === LogLevel.ALL) {
            outputLog(LogLevel.ALL, message, ...args);
        }
    }

    trace(message: string, ...args: any): void {

        if (this.currentLogLevel <= LogLevel.TRACE) {
            outputLog(LogLevel.TRACE, message, ...args);
        }
    }

    debug(message: string, ...args: any): void {

        if (this.currentLogLevel <= LogLevel.DEBUG) {
            outputLog(LogLevel.DEBUG, message, ...args);
        }
    }

    info(message: string, ...args: any): void {

        if (this.currentLogLevel <= LogLevel.INFO) {
            outputLog(LogLevel.INFO, message, ...args);
        }
    }

    warn(message: string, ...args: any): void {

        if (this.currentLogLevel <= LogLevel.WARN) {
            outputLog(LogLevel.WARN, message, ...args);
        }
    }

    error(message: string, ...args: any): void {

        if (this.currentLogLevel <= LogLevel.ERROR) {
            outputLog(LogLevel.ERROR, message, ...args);
        }
    }

    fatal(message: string, ...args: any): void {

        if (this.currentLogLevel <= LogLevel.FATAL) {
            outputLog(LogLevel.FATAL, message, ...args);
        }
    }
}

/**
 * 广播日志终端（日志内容会广播到所有日志终端）
 */
export class BrocastLogger implements ILogger {

    private loggers: ILogger[];

    get name() {
        return 'BrocastLogger';
    }

    constructor(loggers: ILogger[]) {

        if (!Array.isArray(loggers) || loggers.length === 0) {
            throw new Error('atleast one logger is required');
        }

        this.loggers = loggers;
    }
    
    setLogLevel(level: LogLevel): void {
        // 广播日志终端，无实际日志级别，根据所涵盖的各个日志终端的日志级别
    }

    log(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.log(message, ...args); });
    }

    trace(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.trace(message, ...args); });
    }

    debug(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.debug(message, ...args); });
    }

    info(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.info(message, ...args); });
    }

    warn(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.warn(message, ...args); });
    }

    error(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.error(message, ...args); });
    }

    fatal(message: string, ...args: any): void {
        this.loggers.forEach(x => { x.fatal(message, ...args); });
    }
}