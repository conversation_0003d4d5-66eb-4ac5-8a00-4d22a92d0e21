const { IdentityRelationView } = require('../../2021/classcial/identity-relation-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repoRisk } = require('../../../repository/risk');
const { RiskMessage } = require('../../../model/risk-message');

class View extends IdentityRelationView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '风控消息查询');
        this.identityTypes = Object.values(this.systemEnum.identityType);
        this.warningTypes = Object.values(this.systemEnum.warningType);
    }

    createToolbarApp() {

        const now = new Date();
        this.states = {

            date: [now.addDays(-3).format('yyyy-MM-dd'), now.addDays(0).format('yyyy-MM-dd')],
            pickerOptions: {

                disabledDate(time) {
                    return time.getTime() > Date.now() - 8.64e6;
                },
            },
        };

        this.toolbarApp = new Vue({

            el: this.$toolbar,
            data: {

                istates: this.istates,
                funds: this.products,
                strategies:  this.strategies,
                accounts: this.accounts,
                states: this.states,
            },
                
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleProductChange,
                this.handleStrategyChange,
                this.handleAccountChange,
                this.dateTimechange,
                this.search,
                this.formatSelectAccountName,
            ]),
        });
    }

    dateTimechange() {
        //
    }

    search() {

        this.paging.pageNo = 1;
        this.turn2Request();
    }

    createFooterRowApp() {
        
        const paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            pageNo: 0,
            total: 0,
            layout: paging.layout,
        };

        this.footerApp = new Vue({

            el: this.$paging,
            data: this.paging,
            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize);
        this.turn2Request();
    }

    handlePageChange() {
        this.turn2Request();
    }

    refresh() {

        this.interaction.showSuccess('已发送刷新请求');
        this.search();
    }

    exportSome() {
        this.tableObj.exportAllRecords(`风控消息记录-${new Date().format('yyyyMMdd')}`);
    }

    async turn2Request() {

        var $loading = this.interaction.showLoading({ text: '正在请求数据...' });
        try {

            let date = this.states.date || [];
            let start = date[0] || null;
            let end = date[1] || null;
            let { productId, strategyId, accountId } = this.istates;
            let { pageSize, pageNo } = this.paging;
            let condition = { 
                fundId: productId, 
                strategyId, 
                accountId,
                start,
                end,
                pageSize, 
                pageNo, 
                sort: 'createTime desc',
            };
            console.log(condition);
            let resp = await repoRisk.searchRiskMessage(condition);
            let { errorCode, errorMsg, data } = resp;
            let { 
                /** 返回包含当前页数据的列表 */
                content, 
                /** 返回当前页的页码《从 0 开始计数) */
                number,
                /** 当前页的实际元素数量 */
                numberOfElements,
                /** 返回每页的数据大小，即每页最多包含多少条数据，每页的固定大小 */
                size,
                /** 返回总记录数，表示符合查询条件的数据总数 */
                totalElements,
                /** 返回总页数，表示分页查询的总页数 */
                totalPages,
            } = data;

            $loading.close();
            this.paging.total = totalElements;
            let alerts = (content || []).map(x => new RiskMessage(x));
            this.tableObj.refill(alerts);
            this.tableObj.setPageSize(size);
        } 
        catch (ex) {
            $loading.close();
        }
    }

    /**
     * @param {RiskMessage} message 
     */
    formatIdentityType(message) {

        let matched = this.identityTypes.find(x => x.code == message.identityType);
        return matched ? matched.mean : message.identityType;
    }

    /**
     * @param {RiskMessage} message 
     */
    formatWarningType(message) {
        
        let matched = this.warningTypes.find(x => x.code == message.warningType);
        return matched ? matched.mean : message.warningType;
    }

    /**
     * @param {RiskMessage} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    createTable() {
        
        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable(this.$table, this.identifyRecord, this, { tableName: 'smt-amx2', displayName: this.title });
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    build($container) {

        super.build($container);
        this.$toolbar = $container.querySelector('.query-toolbar');
        this.$table = $container.querySelector('.table-alert');
        this.$paging = $container.querySelector('.paging-row');
        this.createToolbarApp();
        this.createFooterRowApp();
        this.createTable();
        this.search();
    }
}

module.exports = View;
