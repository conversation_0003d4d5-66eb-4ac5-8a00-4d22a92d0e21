
/**
 * 系统级产品
 */
class SysProduct {

    /**
     * @param {*} struc 
     */
    constructor(struc) {

        this.amacCode = struc.amacCode;
        this.basisReference = struc.basisReference;
        this.closedFlag = struc.closedFlag;
        this.establishedDay = struc.establishedDay;
        this.fundManager = struc.fundManager;
        this.fundName = struc.fundName;
        this.fundOrganization = struc.fundOrganization;
        this.fundType = struc.fundType;
        this.id = struc.id;
        this.nav = struc.nav;
        this.orgId = struc.orgId;
        this.orgName = struc.orgName;
        this.reportTemplates = struc.reportTemplates;
        this.riskEnable = struc.riskEnable;
        this.strategyType = struc.strategyType;
        this.valuation = struc.valuation;
    }
}

/**
 * 系统级产品详情
 */
class SysProductDetail {

    /**
     * @param {*} struc 
     */
    constructor(struc, amacCode = undefined) {

        this.available = struc.available;
        this.balance = struc.balance;
        this.closeProfit = struc.closeProfit;
        this.commission = struc.commission;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.inMoney = struc.inMoney;
        this.loanBuyBalance = struc.loanBuyBalance;
        this.loanSellBalance = struc.loanSellBalance;
        this.loanSellQuota = struc.loanSellQuota;
        this.margin = struc.margin;
        this.marketValue = struc.marketValue;
        this.nav = struc.nav;
        this.navRealTime = struc.navRealTime;
        this.outMoney = struc.outMoney;
        this.positionProfit = struc.positionProfit;
        this.preBalance = struc.preBalance;
        this.risePercent = struc.risePercent;
        this.withdrawQuota = struc.withdrawQuota;
        this.amacCode = amacCode;
    }
}

module.exports = { SysProduct, SysProductDetail };