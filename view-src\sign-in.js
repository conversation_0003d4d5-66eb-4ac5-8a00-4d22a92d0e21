const IView = require('../component/iview').IView;
const path = require('path');
const fs = require('fs');
const IpLib = require('ip');
const OS = require('os');
const md5 = require('md5');
const DiskInfo = require('node-disk-info');
const { exec } = require('child_process');
const svgCaptcha = require('../libs/3rd/svg-captcha');
const { app } = require('@electron/remote');
const encryptPasscodeAllowed = app.encryptionOptions.encryptPasscode;
const isDarwin = process.platform.toLowerCase() == 'darwin';
const { isDev } = require('../config/environment');
const appDir = isDarwin && !isDev ? path.join(__dirname, '../../../../../') : process.cwd();

class View extends IView {

    get logR() {
        return this.systemUserEnum.loginResult;
    }

    get isHttps() {

        try {
            return this.app.GeneralSettings.https;
        }
        catch(ex) {
            return false;
        }
    }

    constructor(view_name) {

        super(view_name, true, '用户登录');

        this.vueApp = null;

        this.states = {

            trdPassed: null,
            quotePassed: null,
        };

        this.servers = [];

        this.captchaOption = {

            size: 4, // 验证码长度为 4 个字符
            ignoreChars: '0o1iIlL', // 忽略容易混淆的字符
            noise: 0, // 添加 0 条噪声线
            color: true, // 启用彩色字符
            background: '#ffffff', // 设置较淡的背景色
            width: 150, // 验证码图片宽度
            height: 50, // 验证码图片高度
            fontSize: 50, // 字体大小
            charPreset: 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnopqrstuvwxyz23456789' // 自定义字符集（排除容易混淆的字符）
        };

        this.chgPwdForm = { old_password: '', new_password: '', confirm_password: '' };
        this.chgPwdRules = {

            old_password: [{ required: true, message: '请输入原始密码', trigger: 'blur' }],
            new_password: [
                { required: true, message: '请输入新密码', trigger: 'blur' },
                { validator: this.checkNewPassword.bind(this), trigger: 'blur' },
            ],
            confirm_password: [
                { required: true, message: '请再次输入新密码', trigger: 'blur' },
                { validator: this.checkPasswordConfirm.bind(this), trigger: 'blur' }
            ]
        };

        this.uidata = {

            userName: null,
            userNameError: null,
            passcode: null,
            passcodeError: null,
            captcha: null,
            captchaError: null,
            rememberUserName: this.getRememberUserNameOption(),
            isSigningIn: false,
            servers: this.servers,
            selectedServer: this.getRecentServerProfileId(),
            changePasswordRequired: false,
            chgPwdForm: this.chgPwdForm,
            chgPwdRules: this.chgPwdRules,
        };

        if (this.uidata.rememberUserName) {
            this.uidata.userName = this.getRecentUser();
        }
    }

    getRememberUserNameOption() {
        return parseInt(localStorage.rem_usr_name) === 1;
    }

    setRememberUserNameOption(yes) {
        localStorage.rem_usr_name = !!yes ? 1 : 0;
    }

    getRecentServerProfileId() {
        var recent_id = parseInt(localStorage.recent_server);
        var matched = this.servers.first(x => {
            return x.id === recent_id;
        });
        return matched ? matched.id : null;
    }

    setRecentServerProfileId(server_profile_id) {
        localStorage.recent_server = server_profile_id;
    }

    getRecentUser() {
        return typeof localStorage.recent_user_name == 'string' ? localStorage.recent_user_name.trim() : null;
    }

    setRecentUser(user_name) {
        user_name ? (localStorage.recent_user_name = user_name) : delete localStorage.recent_user_name;
    }

    setUserNameError() {
        this.uidata.userNameError = !this.uidata.userName ? '用户名未输入' : null;
    }

    setPasscodeError() {
        this.uidata.passcodeError = !this.uidata.passcode ? '密码未输入' : null;
    }

    setCaptchaError() {
        this.uidata.captchaError = !this.uidata.captcha ? '验证码未输入' : (this.uidata.captcha || '').toLowerCase() !== this.validation.text.toLowerCase() ? '验证码错误' : null;
    }

    createApp($container) {
        this.vueApp = new Vue({
            el: $container,
            data: this.uidata,
            watch: {
                rememberUserName: yes => {
                    this.setRememberUserNameOption(yes);
                },
            },
            methods: {
                closeWindow: () => {
                    this.thisWindow.close();
                },
                checkUserNameInput: () => {
                    this.setUserNameError();
                },
                checkPasscodeInput: () => {
                    this.setPasscodeError();
                },
                checkCaptchaInput: () => {
                    this.setCaptchaError();
                },
                refreshCaptcha: () => {
                    this.resetCaptcha();
                },
                toSignIn: () => {
                    this.checkAndSignIn();
                },
                move2Next: e => {
                    e.keyCode === 13 ? document.getElementById('input-user-passcode').focus() : null;
                },
                finishInput: e => {
                    e.keyCode === 13 ? document.getElementById('btn-to-sign-in').click() : null;
                },
                changePassword: () => {
                    this.changePassword();
                },
                decline2Exit: () => {
                    this.decline2Exit();
                },
            },
        });

        this.vueApp.$nextTick(() => {
            this.resetCaptcha();
        });
    }

    resetCaptcha() {
        this.validation = svgCaptcha.create(this.captchaOption);
        document.getElementById('captcha-img').innerHTML = this.validation.data;
    }

    getIpAddress() {

        try {
            return IpLib.address();
        }
        catch(ex) {
            return null;
        }
    }

    async getDiskSN() {

        if (this._diskSN !== undefined) {
            return this._diskSN;
        }
        
        try {
            let si = require('systeminformation');
            let results = await si.diskLayout();
            this._diskSN = results.length > 0 ? results[0].serialNum.replaceAll('-', '').replaceAll('_', '').replaceAll(' ', '').replaceAll('.', '') : null;
            // console.log('disk sn = ', this._diskSN);
        }
        catch(ex) {
            this._diskSN = null;
        }
        
        return this._diskSN;
    }

    async getDiskPartition() {

        if (this._diskPart !== undefined) {
            return this._diskPart;
        }

        return new Promise((resolve, reject) => {
            try {
                exec('wmic logicaldisk get FileSystem, Name, Size', (error, stdout) => {
                    if (error) {
                        resolve(this._diskPart = null);
                    }
                    else {

                        const rows = this.extractConsoleOutputTable(stdout);
                        this._diskPart = rows.length == 0 ? null : 1;

                        if (rows.length > 0) {
                            
                            let first_vol = rows[0];
                            let { FileSystem, Name, Size } = first_vol;
                            typeof Name == 'string' && (Name = Name.replace(':', ''));
                            /^\d+$/g.test(Size) && (Size = parseInt(Size));
                            this._diskPart = `${Name}^${FileSystem}^${Size > 0 ? Math.ceil(Size / 1024 / 1024 / 1024) + 'G' : 0}`;
                        }
                        else {
                            this._diskPart = null;
                        }

                        // console.log('disk partition = ', this._diskPart);
                        resolve(this._diskPart);
                    }
                });
            }
            catch(ex) {
                resolve(this._diskPart = null);
            }
        });
    }

    getSysVol() {

        if (this._sysVol !== undefined) {
            return this._sysVol;
        }

        return new Promise((resolve, reject) => {
            try {
                exec('vol', (error, stdout) => {
                    if (error) {
                        resolve(this._sysVol = null);
                    }
                    else {

                        let lines = stdout.replaceAll('\r', '').split('\n').map(x => x.trim()).filter(x => x.length > 0);
                        let matrix = lines.map(x => x.split(' ').map(x => x.trim()).filter(x => x.length > 0));

                        if (matrix.length >= 2) {
                            
                            let line2_values = matrix[1];
                            this._sysVol = line2_values[line2_values.length - 1] || null;
                        }
                        else {
                            this._sysVol = null;
                        }
                        
                        // console.log('system vol = ', this._sysVol);
                        resolve(this._sysVol);
                    }
                });
            }
            catch(ex) {
                resolve(this._sysVol = null);
            }
        });
    }

    getCpuSN() {

        if (this._cpuSN !== undefined) {
            return this._cpuSN;
        }

        return new Promise((resolve, reject) => {
            try {
                exec('wmic cpu get ProcessorId', (error, stdout) => {
                    
                    if (error) {
                        resolve(this._cpuSN = null);
                    }
                    else {

                        const rows = this.extractConsoleOutputTable(stdout);
                        this._cpuSN = rows.length > 0 ? rows[0].ProcessorId : null;
                        // console.log('cpu sn = ', this._cpuSN);
                        resolve(this._cpuSN);
                    }
                });
            }
            catch(ex) {
                resolve(this._cpuSN = null);
            }
        });
    }

    /**
     * 从控制台输出中提取表格
     * @param {string} stdout 
     */
    extractConsoleOutputTable(stdout) {

        let lines = stdout.replaceAll('\r', '').split('\n').map(x => x.trim()).filter(x => x.length > 0);
        let matrix = lines.map(x => x.split(' ').map(x => x.trim()).filter(x => x.length > 0));
        let titles = matrix.shift();

        if (matrix.length == 0) {
            return [];
        }

        return matrix.map(x => {
            let obj = {};
            for (let i = 0; i < titles.length; i++) {
                obj[titles[i]] = x[i];
            }
            return obj;
        });
    }

    checkAndSignIn() {

        if (isDev) {
            this.uidata.captcha = this.validation.text;
        }

        if (!this.uidata.userName || !this.uidata.passcode || (this.uidata.captcha || '').toLowerCase() !== this.validation.text.toLowerCase()) {

            this.setUserNameError();
            this.setPasscodeError();
            this.setCaptchaError();
            return;
        }
        else if (this.uidata.selectedServer == null || this.uidata.selectedServer == '') {
            
            this.interaction.showError('请选择服务器');
            return;
        }

        if (this.uidata.rememberUserName) {
            this.setRecentUser(this.uidata.userName);
        }

        this.resetUIStatus(null);
        this.resetDisconnectionReason();
        this.setSelectedServer(
            this.servers.first(x => {
                return x.id == this.uidata.selectedServer;
            }),
        );
        this.setRecentServerProfileId(this.uidata.selectedServer);
        this.resetCaptcha();
        this.uidata.isSigningIn = true;
        this.executeSignIn();
    }

    async executeSignIn() {

        /**
         * @param {string} str 
         */
        function reverseString(str) {

            if (typeof str != 'string') {
                return 'N/A';
            }

            return str.replace(/./g, (_, i, arr) => arr[arr.length - 1 - i]).toUpperCase();
        }

        var allMacAddrs = this.getAllMACAddresses();
        var mac_addr = allMacAddrs.map(x => x.mac).join(',');
        var internal_ip = this.getIpAddress();
        var disk_sn = reverseString(reverseString(await this.getDiskSN()).substring(0, 32));
        var pc_name = reverseString(reverseString(OS.hostname()).substring(0, 20));
        var cpu_sn = await this.getCpuSN();
        var disk_pi = await this.getDiskPartition();
        var sys_vol = await this.getSysVol();
        var local_infos = [internal_ip, disk_sn, pc_name, cpu_sn, disk_pi, sys_vol];
        
        var user_input = {

            userName: this.uidata.userName,
            passCode: encryptPasscodeAllowed ? this.helper.aesEncrypt(this.uidata.passcode) : this.uidata.passcode,
            os: local_infos.join('|'),
            macAddr: localStorage.getItem('dev-pass-mac') || mac_addr,
        };

        if (!user_input.macAddr) {
            this.loggerSys.error('MAC addr not fetched');
        }

        if (user_input.macAddr && typeof user_input.macAddr != 'string') {
            user_input.macAddr = user_input.macAddr.toUpperCase();
        }

        /**
         * 从源代码压缩档提取该版本KEY值
         */

        this.readClientKey(user_input, disk_sn);
    }

    readClientKey(user_input, disk_sn) { 

        const client_key_file_path = path.join(process.cwd(), isDev ? '' : 'resources/app.asar', 'client-key.txt');

        if (!fs.existsSync(client_key_file_path)) {

            this.loggerSys.error('client-key > file not found');
            this.finish2Login(user_input, disk_sn);
            return;
        }
        
        const content = fs.readFileSync(client_key_file_path, { encoding: 'utf-8' });
        const secret = md5(content);
        this.loggerSys.debug(`client-key > read content = ${JSON.stringify({ content, secret })}`);

        if (secret) {
            user_input.md5 = secret;
        }

        this.finish2Login(user_input, disk_sn);
    }

    finish2Login(user_input, disk_sn) {
        
        this.setLoginInput(user_input, disk_sn);
        const input = Object.assign({}, user_input);
        delete input.passCode;
        this.loggerSys.debug(`sign-in window > to login to server: ${JSON.stringify(this.app.contextData.serverInfo)} with input: ${JSON.stringify(input)}`);
        this.doSignIn();
    }

    getAllMACAddresses() {
        
        try {

            const interfaces = OS.networkInterfaces();
            const macAddresses = [{ interface: '', mac: '' }].splice(1);
        
            for (const ifname in interfaces) {
        
                const devices = interfaces[ifname];

                for (const alias of devices) {

                    if (alias.mac && alias.mac !== '00:00:00:00:00:00') {
                        macAddresses.push({ interface: ifname, mac: alias.mac.replaceAll(':', '-').toUpperCase() });
                    }
                }
            }
        
            return macAddresses;
        }
        catch(ex) {

            console.error(ex);
            return [];
        }
    }

    resetUIStatus(error_msg) {

        this.states.trdPassed = null;
        this.states.quotePassed = null;

        this.uidata.userNameError = error_msg;
        this.uidata.passcodeError = null;
        this.uidata.isSigningIn = false;
    }

    resetDisconnectionReason() {

        this.app.contextData.disconnectedByLogout = null;
        this.app.contextData.disconnectedTradingServerAccidently = null;
        this.app.contextData.disconnectedQuoteServerAccidently = null;
    }

    setSelectedServer(server_info) {

        localStorage.serverInfo = JSON.stringify(server_info);
        this.app.contextData.serverInfo = server_info;
    }

    setLoginInput(user_input, disk_sn) {
        
        this.app.contextData.logInInput = user_input;
        this.app.contextData.diskSN = disk_sn;
    }

    doSignIn() {
        // only login successfully onto [trading] server, and then can quote server tries to login <if required depending on user role>
        this.renderProcess.send(this.systemEvent.toLoginTradingServer);
    }

    quoteServerNotRequired(signin_response) {
        // return signin_response.roleId === this.systemUserEnum.userRole.superAdmin.code;
        return true;
    }

    listen2TradingServerLoginCompletement() {

        this.renderProcess.on(this.systemEvent.loginTradingServerCompleted, (event, signin_response) => {

            this.states.trdPassed = signin_response.errorCode === this.logR.ok.code;
            if (!this.states.trdPassed) {
                this.uidata.isSigningIn = false;
            }

            // only when login onto trading server is validated ok, then can try to login onto quote server (if required for this user type)
            if (this.states.trdPassed && this.quoteServerNotRequired(signin_response)) {
                let { userName, username, fullName } = signin_response;
                this.loggerSys.debug(`sign-in window > user with username = ${userName || username}, fullname = ${fullName} does not require quote server`);
                // bypass login onto quote server
                this.states.quotePassed = true;
                this.handleSignInResponse(signin_response, '交易服务器');
                return;
            }

            /*
			    set trading server login feedback,
			    no matter [ok] or [error]
			    and it will be 100% replaced with latest quote server login feeback very quickly (if with error).
			*/
            this.handleSignInResponse(signin_response, '交易服务器');

            if (this.states.trdPassed) {
                this.renderProcess.send(this.systemEvent.toLoginQuoteServer);
            }
        });
    }

    listen2QuoteServerLoginCompletement() {

        this.renderProcess.on(this.systemEvent.loginQuoteServerCompleted, (event, signin_response) => {
            this.states.quotePassed = signin_response.errorCode === this.logR.ok.code;
            this.handleSignInResponse(signin_response, '行情服务器');
        });
    }

    listen2NetworkEvents() {
        /*
		    the followed 3 methods will be fired by [trading] server or [quote] server for aligned event
		    no matter which connection happens to be [FAILED],
            the whole login process is treated as [FAILED]
		*/

        this.renderProcess.on(this.systemEvent.connTimedOut, (event, error_msg) => {
            this.resetUIStatus(error_msg);
        });
        this.renderProcess.on(this.systemEvent.connError, (event, error_msg) => {
            this.resetUIStatus(error_msg);
        });
        this.renderProcess.on(this.systemEvent.connClosed, (event, error_msg) => {
            this.resetUIStatus(error_msg);
        });
    }

    listen2AdminKicksMeOut() {
        this.renderProcess.on(this.serverEvent.forcedKickOut, () => {
            alert('您已被管理员强制下线');
        });
    }

    handleSignInResponse(signin_response, server_type_name) {

        const { trdPassed, quotePassed } = this.states;

        if (!this.thisWindow.isVisible()) {
            this.loggerSys.info(`sign-in window > server login response comes, but window is not visible / ${JSON.stringify({ server_type_name, trdPassed, quotePassed })}`);
            return;
        }

        const { errorCode, errorMsg } = signin_response;

        // both [trading] & [quote] server login finished(ok / failed)
        if (trdPassed != null && quotePassed != null) {
            this.uidata.isSigningIn = false;
        }

        // both [trading] & [quote] servers login successfully
        if (trdPassed === true && quotePassed === true) {

            this.uidata.userNameError = null;
            this.uidata.passcodeError = null;
            const { firstLogin } = this.userInfo;

            // 检查是否是首次登录
            if (firstLogin === true) {

                // 展示强制修改密码弹窗
                this.promptupChgPwdForm();
            }
            else {
                this.renderProcess.send(this.systemEvent.loginRequestCompleted);
            }
            
            this.loggerSys.info(`sign-in window > both [trading] and [quote] servers are validated ok`);
            // 通知主进程，开始后续相关的工作
            this.renderProcess.send(this.systemEvent.loginRequestValidatedOk);
            return;
        }

        // 交易服务器，或行情服务器，其中一方已经完成，另外一方登录尚未返回
        if (errorCode === this.logR.ok.code) {
            
            this.loggerSys.info(`sign-in window > only one server login finished / ${server_type_name}`);
            return;
        }

        switch (errorCode) {

            case this.logR.userNameOrPasscodeError.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.userNameOrPasscodeError.mean}`;
                break;

            case this.logR.userNameNonExist.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.userNameNonExist.mean}`;
                break;

            case this.logR.passcodeError.code:
                this.uidata.passcodeError = `(${server_type_name}) ${this.logR.passcodeError.mean}`;
                break;

            case this.logR.userDisabled.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.userDisabled.mean}`;
                break;

            case this.logR.alreadySignedIn.code:
                this.uidata.userNameError = `(${server_type_name}) ${this.logR.alreadySignedIn.mean}`;
                break;

            default:
                this.uidata.userNameError = `(${server_type_name}) ${errorMsg || '未知错误'}`;
                this.uidata.passcodeError = null;
                break;
        }
    }

    promptupChgPwdForm() {
        
        this.uidata.changePasswordRequired = true;
        this.chgPwdForm.old_password = '';
        this.chgPwdForm.new_password = '';
        this.chgPwdForm.confirm_password = '';
    }

    checkNewPassword(rule, value, callback) {

        if (value == this.chgPwdForm.old_password) {
            callback(new Error('新密码不能与旧密码相同!'));
            return;
        }

        const regExp = /^(?=.*[A-Za-z])(?=.*\d).{8,12}$/;
        if (!regExp.test(value)) {
            callback(new Error('密码必须包含字母和数字，可包含中下划线，长度为8-12位'));
        } 
        else {
            callback();
        }
    }

    checkPasswordConfirm(rule, value, callback) {

        if (value !== this.chgPwdForm.new_password) {
            callback(new Error('两次输入的新密码不一致!'));
        } 
        else {
            callback();
        }
    }

    changePassword() {
        
        this.vueApp.$refs.$chgPwdForm.validate((valid) => {

            if (valid) {
                this.sendPwdChange();
            } 
            else {
                return false;
            }
        });
    }

    async sendPwdChange() {
        
        const { repoUser } = require('../repository/user');
        const resp = await repoUser.changePwd(this.userInfo.userName, this.chgPwdForm.old_password, this.chgPwdForm.new_password);
        const { errorCode, errorMsg, data } = resp;
        
        if (errorCode == 0) {

            const usrInfo = this.getContextDataItem(this.dataKey.userInfo);
            usrInfo.password = this.helper.aesEncrypt(this.chgPwdForm.new_password);
            this.setContextDataItem(this.dataKey.userInfo, usrInfo);
            this.uidata.changePasswordRequired = false;

            this.vueApp.$nextTick(() => {

                alert('恭喜，初始密码已修改!');
                this.renderProcess.send(this.systemEvent.loginRequestCompleted);
            });
        }
        else {
            this.interaction.showError(`初始密码更改异常：${errorCode}/${errorMsg}`);
        }
    }
    
    decline2Exit() {
        this.toExitApp();
    }

    toExitApp() {

        this.renderProcess.send(this.systemEvent.exitApp);
        this.thisWindow.close();
    }

    bindServers() {

        try {

            var config_file = path.join(appDir, 'servers');

            if (!fs.existsSync(config_file)) {

                alert('服务器配置文件缺失，请确认：' + config_file);
                this.toExitApp();
                return;
            }

            var server_cfgs = [];
            try {

                const list = require(config_file);
                if (list instanceof Array && list.length > 0) {
                    server_cfgs.merge(list);
                }
            }
            catch (ex) {

                alert('导入服务器配置产生异常，终止运行。');
                this.toExitApp();
                return;
            }

            if (server_cfgs.length == 0) {

                alert('服务器配置，未指定任何可用服务器列表，终止运行。');
                this.toExitApp();
                return;
            }
            else {

                let invalid = server_cfgs.find(x => {

                    let server_name = x.serverName;
                    if (typeof server_name != 'string' || server_name.trim().length == 0) {
                        return true;
                    }

                    let servers = x.servers;            
                    return typeof servers.quoteRestfulServer != 'string' || servers.quoteRestfulServer.indexOf(':') <= 0
                        || typeof servers.restServer != 'string' || servers.restServer.indexOf(':') <= 0
                        || typeof servers.tradeServer != 'string' || servers.tradeServer.indexOf(':') <= 0;
                });

                if (invalid) {

                    alert('服务器配置，非预期数据结构，终止运行。');
                    this.toExitApp();
                    return;
                }
            }

            let prefix = this.isHttps ? 'https://' : 'http://';
            let counter = 1;
            let dto_servers = server_cfgs.map(cfg => {

                let server_name = cfg.serverName;
                let servers = cfg.servers;
                let trade_server = servers.tradeServer.split(':');

                return {

                    id: ++counter,
                    name: server_name,
                    tradingServer: { ip: trade_server[0], port: trade_server[1] },
                    quoteRestfulServer: `http://${ servers.quoteRestfulServer }`,
                    restfulServer: `${ prefix }${ servers.restServer }/quant/v3`,
                    indayServer: 'http://local.gaoyusoft.com:8181',
                };
            });

            this.servers.clear();
            this.servers.merge(dto_servers);

            if (this.servers.length > 0) {
                this.uidata.selectedServer = this.getRecentServerProfileId();
            }
        }
        catch (e) {
            this.interaction.showError('获取服务器列表异常!');
        }
    }

    build($container) {

        this.listen2TradingServerLoginCompletement();
        this.listen2QuoteServerLoginCompletement();
        this.listen2NetworkEvents();
        this.listen2AdminKicksMeOut();
        this.createApp($container);
        this.bindServers();
    }
}

module.exports = View;
