/**
 * 工作流单步角色与人员配置
 */
export interface WorkflowStepConfig {
  /**
   * 角色ID
   */
  roleId: number;

  /**
   * 角色名称
   */
  roleName: string;

  /**
   * 成员
   */
  members: { userId: number; userName: string }[];
}

/**
 * 工作流配置
 */
export interface WorkflowConfig {
  /**
   * 流程ID
   */
  id: number;

  /**
   * 流程名称
   */
  name: string;

  /**
   * 工作流类型
   */
  type: number;

  /**
   * 流程节点
   */
  steps: WorkflowStepConfig[];

  /**
   * 绑定账号
   */
  accounts: { accountId: string; accountName: string }[];
}
