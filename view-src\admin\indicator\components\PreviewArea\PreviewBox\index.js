const BaseComponent = require('../../BaseComponent');
const helper = require('../../../../../../libs/helper').helper;
const xlsx = require('node-xlsx').default;
const fs = require('fs');
const remote = require('@electron/remote');
const DataTables = require('../../../../../../libs/3rd/vue-data-tables.min.3.4.2');
module.exports = class PreviewBox extends BaseComponent {
    constructor() {
        super(__dirname);
        this.options = {
            components: {
                DataTables: DataTables.DataTables,
            },
            props: {
                item: {
                    type: Object,
                    required: true,
                },
                rawData: {
                    type: Array,
                },
                identityId: {},
                controller: {},
                designMode: {},
                interval: {},
                destroy: {},
            },
            data() {
                return {
                    itemInterval: 0,
                    obj: null,
                };
            },
            computed: {
                empty() {
                    return this.option.series.length == 0;
                },
                chartStyle() {
                    return {
                        height: `${this.item.height}px`,
                    };
                },
                single() {
                    return this.item.valueType == '1';
                },
                multi() {
                    return this.item.valueType == '2';
                },
                option() {
                    return {
                        tooltip: {
                            confine: true,
                            trigger: 'axis',
                            axisPointer: {
                                type: 'none',
                            },
                            formatter: this.item.percent
                                ? (params) => {
                                      let tpl = [];
                                      const { name, axisValueLabel } = params[0];
                                      const title = name || axisValueLabel;
                                      tpl.push(`${title}<br>`);
                                      params.forEach(({ seriesName, data, marker }) => {
                                          tpl.push(marker);
                                          tpl.push(`${seriesName}: ${this.formatValue(data, 2, true)}`);
                                          tpl.push('<br>');
                                      });
                                      return tpl.join('');
                                  }
                                : null,
                        },
                        legend: {
                            data: this.getLegend(),
                            textStyle: {
                                color: '#9e9e9e',
                            },
                            inactiveColor: 'rgba(120,120,120, 0.8)',
                            top: 'bottom',
                            type: 'scroll',
                        },
                        xAxis: {
                            data: this.getXaxis(),
                            axisLine: {
                                lineStyle: {
                                    color: '#555',
                                },
                            },
                            axisTick: {
                                alignWithLabel: true,
                                lineStyle: {
                                    color: '#555',
                                },
                            },
                            axisLabel: {
                                color: '#9e9e9e',
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    type: 'dotted',
                                    color: '#555',
                                },
                            },
                            splitArea: {
                                show: true,
                                areaStyle: {
                                    color: ['rgba(50,50,50,0.1)', 'rgba(100,100,100,0.1)'],
                                },
                            },
                            boundaryGap: this.chartType == 'bar',
                        },
                        yAxis: {
                            min: this.chartType == 'bar' || this.item.type == 'area' ? null : 'dataMin',
                            // max: val => {
                            //     return val.max + 0.01;
                            // },
                            name: this.item.yAxisName,
                            nameLocation: 'center',
                            nameGap: 35,
                            axisLine: {
                                lineStyle: {
                                    color: '#555',
                                },
                            },
                            axisTick: {
                                alignWithLabel: true,
                                lineStyle: {
                                    color: '#555',
                                },
                            },
                            axisLabel: {
                                color: '#9e9e9e',
                                formatter: this.item.percent ? (val) => (!isNaN(val) ? `${(val * 100).toFixed(0)}%` : val) : '{value}',
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dotted',
                                    color: '#555',
                                },
                            },
                        },
                        grid: {
                            top: 45,
                            bottom: 60,
                        },
                        series: this.getSeries(),
                        backgroundColor: 'rgba(70,70,70, 0.1)',
                        textStyle: {
                            color: '#9e9e9e',
                        },
                        toolbox: {
                            show: false,
                            feature: {
                                dataZoom: {
                                    yAxisIndex: 'none',
                                },
                                // dataView: { readOnly: false },
                                saveAsImage: {
                                    name: this.item.name,
                                },
                            },
                        },
                    };
                },
                chartType() {
                    return this.item.type != 'area' ? this.item.type : 'line';
                },
                areaStyle() {
                    return this.item.type == 'area' ? {} : undefined;
                },
            },
            watch: {
                'item.height'() {
                    this.resizeChart();
                },
                'item.inverse'() {
                    this.shouldRenderTable();
                },
                'item.float'() {
                    this.shouldRenderTable();
                },
                'item.percent'() {
                    this.shouldRenderTable();
                },
                'item.widget'() {
                    this.shouldRenderTable();
                },
                'item.style'() {
                    this.resizeChart();
                },
                destroy() {
                    if (this.destroy) {
                        clearInterval(this.obj);
                    }
                },
            },
            async mounted() {
                this.onResize();
                await this.getIndicatorData();
                this.shouldRenderTable();
                this.shouldAutoRefresh();
            },
            beforeDestroy() {
                clearInterval(this.obj);
            },
            methods: {
                shouldAutoRefresh() {
                    clearInterval(this.obj);
                    this.itemInterval = this.item.interval || this.interval;
                    if (this.itemInterval > 0) {
                        this.obj = setInterval(async () => {
                            this.itemInterval--;
                            if (this.itemInterval == 0) {
                                this.itemInterval = this.item.interval || this.interval;
                                await this.getIndicatorData();
                                this.shouldRenderTable();
                            }
                        }, 1000);
                    }
                },
                getValue(item) {
                    let value,
                        float = item.float;
                    const rawData = this.item.rawData;
                    value = rawData.length > 0 ? rawData[1][0] : this.item.value;
                    return this.formatValue(value, float, this.item.percent);
                },
                formatValue(value, float, percent) {
                    if (percent) return this.renderValue(value, 2, true);
                    if (float < 0) return value;
                    return this.renderValue(value, float, false);
                },
                renderValue(value, fix, percent) {
                    if (!isNaN(value)) {
                        if (percent) return `${String(value).indexOf('.') > 0 ? (value * 100).toFixed(fix) : value * 100}%`;
                        return String(value).indexOf('.') > 0 ? Number(value).toFixed(fix) : value;
                    } else {
                        return value;
                    }
                },
                async getIndicatorData() {
                    if (this.identityId && this.item.name) {
                        let resp = await this.controller.indicatorRepo.getIndicatorData(this.identityId, this.item.name);
                        if (resp.errorCode == 0) {
                            const data = resp.data;
                            if (typeof data == 'object' && Object.keys(data).length > 0) {
                                const key = Object.keys(data)[0];
                                if (Array.isArray(data[key].data)) {
                                    this.item.rawData = data[key].data;
                                }
                            }
                        }
                    } else {
                        if (this.multi) {
                            this.item.rawData = helper.deepClone(this.rawData);
                        }
                    }
                },
                shouldRenderTable() {
                    if (this.multi && this.item.rawData.length > 0 && this.item.widget == 'table') {
                        this.renderTable();
                    }
                },
                renderTable() {
                    this.item.data = this.renderData(this.item.rawData, this.item.inverse, this.item.float, this.item.percent);
                    this.item.columns = this.renderColumns(this.item.rawData, this.item.inverse);
                },
                renderData(rawData, inverse, float, percent) {
                    const data = inverse ? this.inverseData(rawData) : rawData;
                    const keys = data[0];
                    return data.slice(1).map((item) => {
                        let row = {};
                        keys.forEach((key, index) => {
                            if (index == 0) row[key] = item[index];
                            else {
                                row[key] = this.formatValue(item[index], float, percent);
                            }
                        });
                        return row;
                    });
                },
                renderColumns(rawData, inverse) {
                    const data = inverse ? this.inverseData(rawData) : rawData;
                    const keys = data[0];
                    return keys.map((key) => ({ label: key, prop: key }));
                },
                inverseData(rawData) {
                    return rawData[0].map((col, i) => rawData.map((row) => row[i]));
                },
                getLegend() {
                    const rawData = this.item.rawData;
                    if (rawData.length > 0) {
                        return rawData[0].slice(1);
                    }
                },
                getXaxis() {
                    const rawData = this.item.rawData;
                    if (rawData.length > 0) {
                        return rawData.slice(1).map((row) => this.formatValue(row[0], this.item.float));
                    }
                },
                getSeries() {
                    const rawData = helper.deepClone(this.item.rawData);
                    if (rawData.length > 0) {
                        const keys = rawData[0].slice(1);
                        return keys.map((key, index) => {
                            const serie = {
                                name: key,
                                type: this.chartType,
                                areaStyle: this.areaStyle,
                                data: this.getData(index),
                                smooth: this.item.smooth,
                                showSymbol: false,
                                stack: this.item.stack,
                            };
                            return serie;
                        });
                    } else {
                        return [];
                    }
                },
                getData(index) {
                    return this.item.rawData.slice(1).map((row) => this.formatValue(row[index + 1], this.item.float));
                },
                onResize() {
                    window.addEventListener('resize', (e) => {
                        this.resizeChart();
                    });
                },
                resizeChart() {
                    if (this.$refs.chart) {
                        this.$refs.chart.resize();
                    }
                },
                handleExport() {
                    const buf = xlsx.build([
                        {
                            name: this.item.name,
                            data: this.item.rawData.map((row, index) => {
                                if (index == 0) return row;
                                return row.map((val, valIndex) => {
                                    if (valIndex == 0) return val;
                                    return this.formatValue(val, this.item.float, this.item.percent);
                                });
                            }),
                        },
                    ]);
                    
                    const dialog_opts = {

                        title: '导出数据至表格',
                        defaultPath: this.item.name,
                        filters: [{ name: 'Excels', extensions: ['xlsx'] }],
                    };

                    const filename = remote.dialog.showSaveDialogSync(remote.getCurrentWindow(), dialog_opts);
                    if (filename) {
                        fs.writeFile(filename, buf, (err) => {
                            if (err) {
                                console.error(err);
                            }
                        });
                    }
                },
            },
        };
    }
};
