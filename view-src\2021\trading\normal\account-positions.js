const { ContextualTradeRecordView } = require('../module/trade-record-view-contextual');
const { Position } = require('../../../../model/position');
const { repoPosition } = require('../../../../repository/position');
const { repoTrading } = require('../../../../repository/trading');

class View extends ContextualTradeRecordView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '账号持仓');

        /**
         * 页面状态对象
         */
         this.states = {
            keywords: null,
        };

        this.priceMap = {};
    }

    handleChannelChange() {

        super.handleChannelChange();

        if (this.isFuture || this.isOption) {
            this.tableObj.showColumns(['保证金']);
        }
        else {
            this.tableObj.hideColumns(['保证金']);
        }
    }

    async queryFirstScreen() {
        return await repoPosition.quickMemQuery({ identityIds: this.properId, pageSize: this.paging.pageSize, pageNo: 1 });
    }

    async queryAll() {
        return await repoPosition.batchMemQuery({ identityIds: this.properId });
    }

    /**
     * @param {Array<Position>} records 
     */
    typedsPositions(records) {
        return records;
    }

    async updatePrice() {

        var map = await repoTrading.getAssetsLatestPrice();

        for (let key in map) {
            this.priceMap[key] = map[key] || 0;
        }

        const positions = this.typeRecords(this.tableObj.extractAllRecords());
        positions.forEach(pos => {
            
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let scale = pos.totalPosition * pos.direction * pos.volumeMultiple;
            let floatProfit = (lastPrice - pos.avgPrice) * scale;

            this.tableObj.updateRow({

                id: pos.id,
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
                marketValue: Math.abs(lastPrice * scale),
            });
        });
    }

    schedulePriceUpdate() {

        this.priceUpdateTimer = setInterval(async () => {
            
            if (this.isRequestingPrice) {
                return;
            }

            try {
                
                this.isRequestingPrice = true;
                await this.updatePrice();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isRequestingPrice = false;
            }

        }, 1000 * 10);
    }

    listen2DataChange() {
        this.standardListen(this.serverEvent.positionChanged, this.handlePositionChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, this.properId, [this.serverEvent.positionChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, this.properId, [this.serverEvent.positionChanged]);
    }

    handleBeforeContextChange(lastId) {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, lastId, [this.serverEvent.positionChanged]);
    }

    consumeBatchPush(titles, contents, totalSize) {

        super.consumeBatchPush(titles, contents, totalSize);
        var records = View.ModelConverter.formalizePositions(titles, contents);
        this.tableObj.refill(records);
        this.filterPositions();
        this.updatePrice();
    }

    /**
     * @param {*} struc
     */
    handlePositionChange(struc) {
        
        var pos = new Position(struc);

        if (this.isRecordAssetQualified(pos.assetType)) {

            this.tableObj.putRow(pos);
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let scale = pos.totalPosition * pos.direction * pos.volumeMultiple
            let floatProfit = (lastPrice - pos.avgPrice) * scale;

            this.tableObj.updateRow({ 
                
                id: pos.id, 
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
                marketValue: Math.abs(lastPrice * scale),
            });
        }
    }

    // resetControls() {

    //     super.resetControls();
    //     this.states.keywords = null;
    // }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterPositions,
                this.hope2CloseCheckeds,
                this.createAsBasket,
                this.refresh,
            ]),
        });
    }

    /**
     * @param {Position} record 
     */
    formatActions(record) {
        return '';
    }

    filterPositions() {

        var thisObj = this;
        var keywords = this.states.keywords;

        /**
         * @param {Position} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Position} record 
         */
        function testRecords(record) {
            return thisObj.tableObj.matchKeywords(record) || filterByPinyin(record);
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    /**
     * @param {Array<Position>} records
     * @returns {Array<Position>}
     */
    typeRecords(records) {
        return records;
    }

    /**
     * @param {Position} record 
     */
    handleRowDbClick(record) {
        this.trigger('account-position-item-double-clicked', record);
    }

    hope2CloseCheckeds() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无持仓');
            return;
        }

        if (this.tableObj.filteredRowCount == 0) {

            this.interaction.showMessage('筛选结果无持仓');
            return;
        }

        var checkeds = this.typeRecords(this.tableObj.extractCheckedRecords());
        if (checkeds.length == 0) {

            this.interaction.showMessage('请选择要平仓的合约');
            return;
        }

        var filtereds = this.typeRecords(this.tableObj.extractFilteredRecords());
        var intersecs = checkeds.filter(item => filtereds.some(item2 => this.identifyRecord(item2) == this.identifyRecord(item)));
        var closables = intersecs.filter(item => item.closableVolume > 0);
        if (closables.length == 0) {

            this.interaction.showError(`勾选持仓 = ${intersecs.length}，可平持仓 = 0`);
            return;
        }

        this.closePosition(closables);
    }

    /**
     * 平仓
     * @param {Array<Position>} positions 
     */
    closePosition(positions) {
        
        if (this.closeDialog === undefined) {
            
            const DialogClosePosition = require('../../fragment/dialog-close-positions');
            const dialog = new DialogClosePosition('@2021/fragment/dialog-close-positions', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', positions); });
            this.closeDialog = dialog;
        }
        else {
            this.closeDialog.trigger('showup', positions);
        }
    }

    createAsBasket() {
        this.interaction.showAlert('暂未实现篮子导入功能');
    }

    dispose() {

        super.dispose();
        clearInterval(this.priceUpdateTimer);
        delete this.priceUpdateTimer;
    }

    build($container) {

        super.build($container, 'smt-tnap');
        this.registerEvent('reload-account-positions', _ => {

            /**
             * 延迟些许刷新持仓，获得完整度更高的，最新持仓数据
             */
            setTimeout(() => { this.reloadRecords(); }, 1000);
        });

        this.schedulePriceUpdate();
    }
}

module.exports = View;