@import "../../../../node_modules/vxe-table/styles/variable";

//$vxe-table-body-background-color: rgb(40, 50, 63);
//$vxe-table-header-background-color: rgb(40, 50, 63);
$vxe-table-body-background-color: rgb(31, 36, 45);
$vxe-table-header-background-color: rgb(31, 36, 45);
$vxe-table-border-color: black;
$vxe-table-font-color: white;
//$vxe-font-family: 'Microsoft YaHei';
$vxe-table-row-hover-background-color: rgb(55, 76, 107);
$vxe-table-row-current-background-color: #2C79F2;
$vxe-table-row-hover-current-background-color: rgb(55, 76, 107);
$vxe-table-row-striped-background-color: rgb(38,47, 60);
$vxe-table-row-hover-striped-background-color: rgb(55, 76, 107);

.vxe-header--column{
  color: rgb(140,181,237);
}

/*滚动条整体部分*/
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background-color: #262f3c;
}
/*滚动条里面的小方块，能向上向下移动*/
::-webkit-scrollbar-thumb {
  background-color: #65A1FF;
  border-radius: 5px;
  //border: 1px solid #F1F1F1;
  box-shadow: inset 0 0 6px rgba(0,0,0,.3);
}
::-webkit-scrollbar-thumb:hover {
  background-color: #65A1FF;
  box-shadow: inset 0 0 2px 0 #8CB5ED;
}
::-webkit-scrollbar-thumb:active {
  background-color: #65A1FF;
  box-shadow: inset 0 0 2px 0 #8CB5ED;
}
/*边角，即两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
  //background-color: #262f3c;
}

@import "../../../../node_modules/vxe-table/styles/modules";