const NormalTradeView = require('./trade-view-normal');

class View extends NormalTradeView {

    constructor(view_name) {
        super(view_name, { isCredit: false });
    }

    sendOutOrder() {

        var uistates = this.uistates;
        var { fundId, strategyId, accountId } = this.account;
        var dict = this.systemTrdEnum;

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

            strategyId: strategyId || fundId,
            accountId: accountId,
            userId: this.userInfo.userId,
            price: uistates.price,
            volume: uistates.scale,
            instrument: this.states.instrument,
            priceType: dict.pricingType.fixedPrice.code,
            bsFlag: uistates.direction,
            businessFlag: 0,
            positionEffect: this.isSpot ? 0 : this.uistates.effect,
            customId: 'normal-spot-' + this.helper.makeToken(),
            orderTime: null,
            hedgeFlag: uistates.checkOpt.hedgeFlag,
        });
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                channel: this.channel,
                directions: this.sdirections,
                accounts: this.saccounts,
                effects: this.effects,
                hedgeFlags: this.hedgeFlags,
                shortcuts: this.shortcuts,
                states: this.states,
                uistates: this.uistates,
                localStates: this.localStates,
            },

            computed: {

                isFuture: () => { return this.channel.options.isFuture; },
                isEffectApplicable: () => { return this.isEffectApplicable(); },
                isCloseTodayNotApplicable: () => { return this.isCloseTodayNotApplicable(); },
                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                scaleStep: () => { return this.decideScaleStep(); },
                maxScale: () => { return this.decideMaxScale(); },

                computedFloorPrice: () => {
                    return this.states.isBuyBack ? 0.001 : this.uistates.floor;
                },

                computedCeilingPrice: () => {
                    return this.states.isBuyBack ? *********.99 : (this.uistates.ceiling > 0 ? this.uistates.ceiling : *********);
                },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.setAsPrice,
                this.precisePrice,
                this.handleUserInput,
                this.handleSelect,
                this.suggest,
                this.handleClearIns,
                this.handleDirectionChange,
                this.handlePositionEffectChange,
                this.handleAccountChange,
                this.handlePriceChange,
                this.handleScaleChange,
                this.hope2Entrust,
                this.handleShortcutClick,
                this.makeDisplayUnit,
                this.makeBuyText,
                this.makeSellText,
                this.formatSelectAccountName,
                this.getProperAccountId,
            ]),
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;