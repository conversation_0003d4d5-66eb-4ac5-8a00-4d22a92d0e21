<script setup lang="ts">
import { ref, reactive, watch, onMounted, useTemplateRef, nextTick } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import DraggableComponent from 'vuedraggable';
import AdminService from '@/api/admin';
import {
  type MomWorkflow,
  type FormWorkflow,
  type MomRole,
  WorkFlowAuditTypeEnum,
} from '../../../../xtrade-sdk/dist';
import { enumToArray } from '@/script';

// Props
interface Props {
  visible: boolean;
  title: string;
  procedure: MomWorkflow | null;
}

const props = defineProps<Props>();

// Emits
const emits = defineEmits<{
  'update:visible': [value: boolean];
  save: [];
  cancel: [];
}>();

// 表单引用
const formRef = useTemplateRef<FormInstance>('formRef');

// 数据状态
const roles = ref<MomRole[]>([]);
const loading = ref(false);

// 表单数据
const formData = reactive<FormWorkflow>({
  workFlowName: '',
  orgId: 0,
  content: [
    {
      defaultOffLineSetting: WorkFlowAuditTypeEnum.自动审核,
      roleName: '起始',
      roleType: null,
    },
  ],
});

// 表单验证规则
const rules: FormRules = {
  workFlowName: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
};

// 工作流类型选项
const workflowTypeOptions = enumToArray(WorkFlowAuditTypeEnum);

// 获取可用角色选项（排除已选择的角色）
const getAvailableRoles = (currentIndex: number) => {
  const selectedRoleTypes = formData.content
    .map((item, index) => (index !== currentIndex ? item.roleType : null))
    .filter(roleType => roleType !== null && roleType !== 0);

  return roles.value.filter(role => !selectedRoleTypes.includes(role.id));
};

// 加载角色列表
const loadRoles = async () => {
  try {
    roles.value = await AdminService.getRoles();
  } catch (error) {
    console.error('加载角色列表失败:', error);
    ElMessage.error('加载角色列表失败');
  }
};

// 添加流程环节
const addStep = () => {
  formData.content.push({
    defaultOffLineSetting: WorkFlowAuditTypeEnum.人工审核,
    roleName: '',
    roleType: null,
  });
};

// 删除流程环节
const removeStep = (index: number) => {
  if (index === 0) {
    ElMessage.warning('起始环节不能删除');
    return;
  }
  formData.content.splice(index, 1);
};

// 角色选择变化处理
const handleRoleChange = (roleType: number, index: number) => {
  const role = roles.value.find(r => r.id === roleType);
  if (role) {
    formData.content[index]!.roleName = role.roleName;
  }
};

// 保存流程
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 验证每个环节都必须选择角色（除起始环节外）
    for (let i = 1; i < formData.content.length; i++) {
      if (!formData.content[i]?.roleType) {
        ElMessage.error(`环节${i + 1}必须选择角色`);
        return;
      }
    }

    loading.value = true;

    let result;
    if (props.procedure) {
      // 编辑模式
      result = await AdminService.updateProcedure({
        ...formData,
        id: props.procedure.id,
      } as MomWorkflow);
    } else {
      // 创建模式
      result = await AdminService.createProcedure(formData);
    }

    if (result.errorCode === 0) {
      ElMessage.success(props.procedure ? '更新成功' : '创建成功');
      emits('save');
    } else {
      ElMessage.error(result.errorMsg || '操作失败');
    }
  } catch (error) {
    console.error('保存流程失败:', error);
    ElMessage.error('保存失败');
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emits('cancel');
};

// 重置表单
const resetForm = () => {
  if (props.procedure) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      workFlowName: props.procedure.workFlowName,
      orgId: props.procedure.orgId,
      content: [...props.procedure.content],
    });
  } else {
    // 创建模式，重置为默认值
    Object.assign(formData, {
      workFlowName: '',
      orgId: 0,
      content: [
        {
          defaultOffLineSetting: WorkFlowAuditTypeEnum.自动审核,
          roleName: '起始',
          roleType: null,
        },
      ],
    });
  }

  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      resetForm();
    }
  },
);

// 组件挂载时加载角色列表
onMounted(() => {
  loadRoles();
});
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item pl-10 label="流程名称" prop="workFlowName" label-width="90px">
        <div w-225>
          <el-input v-model="formData.workFlowName" placeholder="请设置流程名称" maxlength="50" />
        </div>
      </el-form-item>

      <div mx-10>
        <div flex aic py-3 px-15 mb-10 h-44 gap-8 bg="[--g-block-bg-2]">
          <!-- 环节名称 -->
          <div class="step-name" w-68 pl-28>起始</div>

          <!-- 角色选择 -->
          <div w-225>
            <el-select
              v-model="formData.content[0]!.roleType"
              placeholder="请选择角色"
              w-100
              @change="(value: number) => handleRoleChange(value, 0)"
            >
              <el-option
                v-for="role in getAvailableRoles(0)"
                :key="role.id"
                :label="role.roleName"
                :value="role.id"
              />
            </el-select>
          </div>
        </div>
        <DraggableComponent v-model="formData.content" item-key="index" handle=".drag">
          <template #item="{ element, index }">
            <div v-if="index == 0"></div>
            <div v-else flex aic py-3 px-15 mb-10 h-44 gap-8 bg="[--g-block-bg-2]">
              <!-- 拖拽图标 -->
              <div class="drag" cursor-move>
                <i fs-20 block i-mdi-drag-vertical></i>
              </div>

              <!-- 环节名称 -->
              <div class="step-name" w-40>
                {{ `环节${index + 1}` }}
              </div>

              <!-- 角色选择 -->
              <div w-225>
                <el-select
                  v-model="element.roleType"
                  placeholder="请选择角色"
                  w-100
                  @change="(value: number) => handleRoleChange(value, index)"
                >
                  <el-option
                    v-for="role in getAvailableRoles(index)"
                    :key="role.id"
                    :label="role.roleName"
                    :value="role.id"
                  />
                </el-select>
              </div>

              <!-- 类型选择 -->
              <div flex-1 min-w-1>
                <el-select v-model="element.defaultOffLineSetting" class="w-32">
                  <el-option
                    v-for="option in workflowTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>

              <el-button size="small" link @click="removeStep(index)">
                <i class="iconfont icon-remove"></i>
              </el-button>
            </div>
          </template>
        </DraggableComponent>

        <!-- 添加环节按钮 -->
        <el-button type="primary" text @click="addStep">
          <i class="iconfont icon-add"></i>
          <span>添加流程</span>
        </el-button>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :disabled="loading" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
