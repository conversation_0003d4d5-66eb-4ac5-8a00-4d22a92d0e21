<div class="summary-history-inout">

    <div class="user-toolbar themed-box">

        <el-date-picker size="mini" v-model="condition.date" placeholder="归属交易日"></el-date-picker>
        <el-button size="mini" type="primary" size="small" @click="search" class="s-mgl-10">查询</el-button>

        <el-button v-show="!condition.isShowCreating" size="mini" type="primary" size="small" @click="toggleAddIORecord">
            <i class="el-icon-plus"></i> 添加</el-button>

        <span v-show="condition.isShowCreating" class="cash-change-creation-panel">

            <el-date-picker size="mini" v-model="condition.happenDate" placeholder="归属交易日"></el-date-picker>
            <span class="s-pdl-10 s-pdr-10">入金额</span>
            <el-input-number size="mini" :controls="false" :precision="2" :step="1000" v-model="condition.cashIn" placeholder="入金" class="cash-scale"></el-input-number>
            <span class="s-pdl-10 s-pdr-10">出金额</span>
            <el-input-number size="mini" :controls="false" :precision="2" :step="1000" v-model="condition.cashOut" placeholder="出金" class="cash-scale"></el-input-number>
            <el-button size="mini" type="primary" size="small" @click="addIORecord"><i class="el-icon-plus"></i> 添加</el-button>
            <el-button size="mini" type="default" size="small" @click="toggleAddIORecord">取消</el-button>
        </span>

        <el-pagination :page-sizes="paging.pageSizes" 
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total" 
					   :current-page.sync="paging.page"
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange" 
					   @current-change="handlePageChange"></el-pagination>
    </div>

    <div class="table-control">
        <table>
            <tr>
                <th label="交易日" prop="tradingDay" min-width="80" sortable overflowt></th>
                <th label="账号名称" prop="identityName" min-width="202.0" formatter="formatAccountName" sortable overflowt></th>
                <th label="入金" min-width="100" prop="inMoney" align="right" thousands overflowt></th>
                <th label="出金" min-width="100" prop="outMoney" align="right" thousands overflowt></th>
                <th label="操作员" prop="operatorUserName" min-width="80" sortable overflowt></th>
                <th label="记录时间" prop="createTime" formatter="formatDateTime" min-width="80" sortable overflowt></th>
                <!-- <th label="合约" prop="remark" min-width="80" sortable overflowt></th> -->
                <th label="备注" prop="type" formatter="formatMoneyType" min-width="80" sortable overflowt></th>
            </tr>
        </table>
    </div>
</div>
