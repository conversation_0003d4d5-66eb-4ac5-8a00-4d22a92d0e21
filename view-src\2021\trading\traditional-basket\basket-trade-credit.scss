.bktform {

    padding-right: 2px;

    .xtform .xtinput .xtlabel {
        width: 25%;
    }

    .xtform .xtinput > .el-input,
    .xtform .xtinput > .el-autocomplete,
    .xtform .xtinput > .el-input-number,
    .xtform .xtinput > .el-select,
    .xtform .xtinput .trade-options {
        width: 73%;
    }

    .effect-box {
        &.minified {
            .el-radio {
                margin-right: 5px;
            }
        }
    }

    .xtinput {

        &.shorten {

            .el-input-number {
                width: 68%;
            }
        }

        .xtlabel {
            text-align: left;
        }

        .el-checkbox {
            margin-right: 20px;
        }
        
        &.basket-button-row {
            
            margin-top: 20px !important;

            button {

                margin-left: 0 !important;
                margin-right: 10px !important;
                width: 45%;
            }
        }
    }
}

.bktform-internal {

    overflow: hidden;

    .form-external {
        padding: 10px;
    }

    .direction-row {

        .el-radio-group {

            .el-radio-button {

                .el-radio-button__inner {
                    width: 100%;
                }
            }
        }
    }

    .limit-btn,
    .unit-txt {

        position: absolute;
        right: 5px;
        z-index: 1;
        width: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
    }

    .effect-box {
        
        display: inline-block;
        line-height: 24px;
        position: relative;
        top: -7px;
    }
}