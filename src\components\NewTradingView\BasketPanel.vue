<script setup lang="ts">
import { computed, onMounted, reactive, ref, useTemplateRef } from 'vue';
import TradeDirection from '@/components/NormalTradeView/TradeTabs/TradeDirection.vue';
import BasketEntrance from '@/components/BasketManage/BasketEntrance.vue';
import { TradeDirectionEnum } from '@/enum';
import type { BasketOrderPreview, BasketTradeInfo } from '@/types/basket';
import { thousands } from '@/script';
import { ElMessage } from 'element-plus';
import type { AccountInfo, AnyObject } from '@/types';
import { usePreviewDataStore } from '@/stores/counter';

import {
  Repos,
  type BasketInstrumentItem,
  type BasketItem,
  type TradingAlgorithm,
} from '../../../../xtrade-sdk/dist';

import {
  BasketTradeModeEnum,
  BasketTradeModes,
  AlgorithmExecuteTimeEnum,
  AlgorithmExecuteTimes,
  ExpirationUnfinishedTreatmentEnum,
  ExpirationUnfinishedTreatments,
  TradeStyleEnum,
  TradeStyles,
  AssetTypeEnum,
} from '@/enum/trade';

const { selectedAccount } = defineProps<{
  selectedAccount?: AccountInfo;
}>();

const direction = ref<TradeDirectionEnum>(TradeDirectionEnum.买入);
const isBuy = computed(() => direction.value === TradeDirectionEnum.买入);
const isByBaskets = computed(() => basketForm.tradeMode === BasketTradeModeEnum.按数量);
const isByWeight = computed(() => !isByBaskets.value);

// 添加表单验证规则
const rules = {
  basketId: [{ required: true, message: '请选择篮子', trigger: 'change' }],
  tradeMode: [{ required: true, message: '请选择交易模式', trigger: 'change' }],
  targetBaskets: [{ required: true, message: '请输入篮子数量', trigger: 'blur' }],
  targetAmount: [{ required: true, message: '请输入目标金额', trigger: 'blur' }],
  algorithmId: [{ required: true, message: '请选择算法', trigger: 'change' }],
  executionTime: [{ required: true, message: '请选择时间类型', trigger: 'change' }],
  timeRange: [{ required: true, message: '请选择起止时间', trigger: 'change' }],
  volumeRatio: [{ required: true, message: '请输入量比比例', trigger: 'blur' }],
  openingCallAuctionParticipation: [
    { required: true, message: '请输入开盘集合竞价参与比例', trigger: 'blur' },
  ],
  openingCallAuctionPriceOffset: [
    { required: true, message: '请输入开盘集合竞价价格偏移', trigger: 'blur' },
  ],
  maxPrice: [{ required: true, message: '请输入触价设置', trigger: 'blur' }],
  unfinishedTreatment: [{ required: true, message: '请选择处理方式', trigger: 'change' }],
  tradingStyle: [{ required: true, message: '请选择交易风格', trigger: 'change' }],
  cancellationRate: [{ required: true, message: '请输入撤单率', trigger: 'blur' }],
  minSingleVolume: [{ required: true, message: '请输入单笔最小量', trigger: 'blur' }],
  investmentNotes: [{ required: true, message: '请输入投资备注', trigger: 'blur' }],
};

const basketForm = reactive<BasketTradeInfo>({
  basketId: null as any,
  tradeMode: BasketTradeModeEnum.按数量,
  targetAmount: 0,
  targetBaskets: 0,
  algorithmId: null as any,
  executionTime: AlgorithmExecuteTimeEnum.立即执行,
  timeRange: [new Date(), new Date()],
  volumeRatio: 1,
  openingCallAuction: false,
  openingCallAuctionParticipation: 0,
  openingCallAuctionPriceOffset: 0,
  maxPrice: 0,
  unfinishedTreatment: ExpirationUnfinishedTreatmentEnum.自动撤销,
  tradingStyle: TradeStyleEnum.保守型,
  cancellationRate: 0,
  minSingleVolume: 0,
  investmentNotes: '',
});

const baskets = ref<BasketItem[]>([]);
const algorithms = ref<TradingAlgorithm[]>([]);

const volumeStep = computed(() => {
  return 100;
});

const isExecutedByTimeRange = computed(() => {
  return basketForm.executionTime == AlgorithmExecuteTimeEnum.指定时间;
});

/**
 * 预估金额
 */
const estimatedAmount = computed(() => {
  return ********.99;
});

const formRef = useTemplateRef('formRef');

const precheck = async () => {
  try {
    formRef.value?.validate();
  } catch (ex) {
    console.error(ex);
    ElMessage.error('请完善下单条件');
    return;
  }

  const acnt = selectedAccount;
  if (!acnt) {
    ElMessage.warning('请选择账号');
    return;
  }

  return true;
};

const store = usePreviewDataStore();

const calculate = async () => {
  if (!precheck()) {
    return;
  }

  const members = (await repoBasket.QueryBasketInstruments(basketForm.basketId)).data || [];
  const priceMap = (await repoTick.QuerySimpleTick(selectedAccount!.assetType)).data || {};
  const previews = makePreview(members, priceMap);
  store.updatePreviews(previews);
};

const trade = async () => {
  if (!precheck()) {
    return;
  }

  // todo
};

const makePreview = (members: BasketInstrumentItem[] = [], priceMap: AnyObject) => {
  const { tradeMode, targetBaskets, targetAmount } = basketForm;
  // 处理数据，生成预览信息
  const previews = members.map((item, index) => {
    // 获取价格
    const price = priceMap[item.instrument] || 0;
    // 根据交易模式计算委托数量
    let volume = 0;
    if (tradeMode === BasketTradeModeEnum.按数量) {
      // 按数量
      volume = item.volume * targetBaskets;
    } else {
      // 按权重
      volume = price <= 0 ? 0 : Math.round((targetAmount * item.weight) / 100 / price / 100) * 100;
    }

    const preview: BasketOrderPreview = {
      id: `preview-${index + 1}` as any,
      instrumentName: item.instrumentName,
      instrument: item.instrument,
      assetType: item.assetType,
      direction: TradeDirectionEnum.买入,
      volume: volume,
      price: price,
      amount: volume * price,
      ceilingPrice: 9999.9999,
      floorPrice: 0,
      yesterdayPosition: 0, // 需要从账户信息中获取
      todayPosition: 0, // 需要从账户信息中获取
      closablePosition: 0, // 需要从账户信息中获取
    };

    return preview;
  });

  return previews;
};

const repoBasket = new Repos.BasketRepo();
const repoTick = new Repos.TickRepo();
async function requestBaskets() {
  baskets.value = ((await repoBasket.QueryBaskets()) || {}).data || [];
}

onMounted(() => {
  requestBaskets();
});
</script>

<template>
  <div class="trading-panel" h-full flex flex-col of-y-hidden>
    <div h-500 flex-1 p-l-20 p-r-20 of-y-auto>
      <!-- 买卖方向 -->
      <div class="direction-section" p-16>
        <TradeDirection v-model="direction" />
      </div>

      <!-- 篮子交易表单 -->
      <el-form
        :model="basketForm"
        :rules="rules"
        ref="formRef"
        label-width="110px"
        label-position="left"
        class="basket-form"
      >
        <el-form-item label="篮子选择" prop="basketId">
          <div w-full flex aic gap-5>
            <el-select v-model="basketForm.basketId" placeholder="请选择篮子">
              <el-option
                v-for="item in baskets"
                :key="item.basketId"
                :label="item.basketName"
                :value="item.basketId"
              />
            </el-select>
            <div w-20 text-center>
              <BasketEntrance :asset-type="AssetTypeEnum.股票" :target-id="basketForm.basketId" />
            </div>
          </div>
        </el-form-item>

        <el-form-item label="交易模式" prop="tradeMode">
          <el-select v-model="basketForm.tradeMode" placeholder="请选择交易模式">
            <el-option
              v-for="item in BasketTradeModes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="isByBaskets" label="篮子数量" prop="targetBaskets">
          <el-input-number
            :controls="false"
            v-model="basketForm.targetBaskets"
            :min="1"
            :precision="0"
            :step="1"
          >
            <template #suffix>
              <span>篮</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item v-if="isByWeight" label="目标金额" prop="targetAmount">
          <el-input-number
            :controls="false"
            v-model="basketForm.targetAmount"
            :min="0"
            :precision="0"
          >
            <template #suffix>
              <span>元</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="交易算法" prop="algorithmId">
          <el-select v-model="basketForm.algorithmId" placeholder="请选择算法">
            <el-option
              v-for="item in algorithms"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间类型" prop="executionTime">
          <el-select v-model="basketForm.executionTime" placeholder="请选择时间类型">
            <el-option
              v-for="item in AlgorithmExecuteTimes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="isExecutedByTimeRange" label="起止时间" prop="timeRange">
          <el-time-picker
            v-model="basketForm.timeRange"
            range-separator="至"
            start-placeholder="开始"
            end-placeholder="结束"
            format="HH:mm:ss"
            is-range
          />
        </el-form-item>

        <el-form-item label="量比比例" prop="volumeRatio">
          <el-input-number
            :controls="false"
            v-model="basketForm.volumeRatio"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="开盘集合竞价" prop="openingCallAuction">
          <el-switch v-model="basketForm.openingCallAuction" />
        </el-form-item>

        <el-form-item
          v-if="basketForm.openingCallAuction"
          label="开盘集合竞价参与比例"
          prop="openingCallAuctionParticipation"
        >
          <el-input-number
            :controls="false"
            v-model="basketForm.openingCallAuctionParticipation"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item
          v-if="basketForm.openingCallAuction"
          label="开盘集合竞价价格偏移"
          prop="openingCallAuctionPriceOffset"
        >
          <el-input-number
            :controls="false"
            v-model="basketForm.openingCallAuctionPriceOffset"
            :precision="2"
            :step="0.01"
            :min="0"
          >
            <template #suffix>
              <span>元</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="触价设置" prop="maxPrice">
          <el-input-number
            :controls="false"
            v-model="basketForm.maxPrice"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #prefix>
              <span>最新价&lt;=</span>
            </template>
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="到期未成处理" prop="unfinishedTreatment">
          <el-select v-model="basketForm.unfinishedTreatment" placeholder="请选择处理方式">
            <el-option
              v-for="item in ExpirationUnfinishedTreatments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交易风格" prop="tradingStyle">
          <el-select v-model="basketForm.tradingStyle" placeholder="请选择交易风格">
            <el-option
              v-for="item in TradeStyles"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="撤单率" prop="cancellationRate">
          <el-input-number
            :controls="false"
            v-model="basketForm.cancellationRate"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="单笔最小量" prop="minSingleVolume">
          <el-input-number
            :controls="false"
            v-model="basketForm.minSingleVolume"
            :min="0"
            :precision="0"
            :step="volumeStep"
          >
            <template #suffix>
              <span>股</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="投资备注" prop="investmentNotes">
          <el-input
            v-model.trim="basketForm.investmentNotes"
            :maxlength="100"
            placeholder="请输入投资备注"
            clearable
          />
        </el-form-item>

        <el-form-item label="预估金额">
          <div w-full text-right>{{ thousands(estimatedAmount) }}</div>
        </el-form-item>
      </el-form>
    </div>
    <div class="action-button" h-47 lh-47 fs-14 fw-500 text-center flex gap-10>
      <div
        @click="calculate"
        class="button-inner"
        :class="{ buy: isBuy, sell: !isBuy }"
        w-100
        flex-1
        h-full
      >
        {{ isBuy ? '买入试算' : '卖出试算' }}
      </div>
      <div
        @click="trade"
        class="button-inner"
        :class="{ buy: isBuy, sell: !isBuy }"
        w-100
        flex-1
        h-full
      >
        {{ isBuy ? '买入' : '卖出' }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.trading-panel {
  :deep() {
    .el-input-number,
    .el-select {
      width: 100%;
    }

    .el-input__inner {
      text-align: left;
    }
  }

  .action-button {
    .button-inner {
      &.buy {
        background-color: var(--g-red-2);
      }

      &.sell {
        background-color: var(--g-bg-green);
      }
    }
  }
}
</style>
