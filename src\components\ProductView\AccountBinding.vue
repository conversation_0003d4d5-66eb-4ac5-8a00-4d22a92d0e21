<script setup lang="tsx">
import { ref, computed, shallowRef, watch, onMounted, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import { Repos, type LegacyAccountInfo } from '../../../../xtrade-sdk/dist';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import AccountSelect from '../common/AccountSelect.vue';
import type { ColumnDefinition, RowAction, ProductInfo } from '@/types';
import { thousands, thousandsCol } from '@/script';
import {
  accountNameCol,
  assetTypeCol,
  orgNameCol,
  brokerNameCol,
  balanceCol,
  marketValueCol,
} from '../common/ComponentTabs/shared/columnDefinitions';
import { FundTypeEnum } from '@/enum';

const contextProduct = defineModel<ProductInfo | null>({});

// 仓库实例
const governanceRepo = new Repos.GovernanceRepo();

const allAccounts = shallowRef<LegacyAccountInfo[]>([]);
const selectedAccountForBind = ref<string>('');
const accountSelectRef = useTemplateRef('accountSelectRef');

// 已绑定账号
const boundAccounts = computed(() => {
  if (!contextProduct.value) return [];
  return allAccounts.value.filter(x =>
    contextProduct.value!.accounts.some(y => y.accountId == x.id),
  );
});

// 已绑定账号表格列定义
const boundAccountColumns = [
  accountNameCol,
  assetTypeCol,
  orgNameCol,
  brokerNameCol,
  balanceCol,
  marketValueCol,
  {
    key: 'available',
    title: '可用资金',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  { key: 'accountId', title: 'ID', width: 150, sortable: true },
] as ColumnDefinition<LegacyAccountInfo>;

// 已绑定账号行操作
const boundAccountRowActions: RowAction<LegacyAccountInfo>[] = [
  {
    label: '解除绑定',
    type: 'danger',
    onClick: (row: LegacyAccountInfo) => {
      handleUnbindAccount(row);
    },
  },
];

// 加载所有账号列表
const loadAllAccounts = async () => {
  const { errorCode, errorMsg, data } = await governanceRepo.QueryAccounts();
  if (errorCode === 0) {
    allAccounts.value = data || [];
  } else {
    ElMessage.error(errorMsg || '加载账号列表失败');
  }
};

// 处理账号选择变化
const handleAccountChange = (accountId: string | undefined) => {
  console.log('handleAccountChange', accountId);
  if (accountId) {
    handleBindAccount(accountId);
  }
};

// 绑定账号到产品
const handleBindAccount = async (accountId: string) => {
  const { errorCode, errorMsg } = await governanceRepo.BindProductAccounts(
    contextProduct.value!.id,
    contextProduct.value!.accounts.map(x => x.accountId as string).concat(accountId),
  );
  if (errorCode === 0) {
    await loadAllAccounts();
    const account = allAccounts.value.find(item => item.id == accountId)!;
    ElMessage.success('绑定成功');
    contextProduct.value!.accounts.push({
      accountId: account.id,
      accountName: account.accountName,
      assetType: account.assetType,
    } as any);
    accountSelectRef.value?.loadAccounts();
  } else {
    ElMessage.error(errorMsg || '绑定失败');
  }
  // 清空选择
  selectedAccountForBind.value = '';
};

// 解除账号绑定
const handleUnbindAccount = async (account: LegacyAccountInfo) => {
  const { errorCode, errorMsg } = await governanceRepo.BindProductAccounts(
    contextProduct.value!.id,
    contextProduct
      .value!.accounts.filter(x => x.accountId != account.id)
      .map(x => x.accountId as string),
  );
  if (errorCode === 0) {
    ElMessage.success('解绑成功');
    contextProduct.value!.accounts = contextProduct.value!.accounts.filter(
      x => x.accountId != account.id,
    );
    accountSelectRef.value?.loadAccounts();
  } else {
    ElMessage.error(errorMsg || '解绑失败');
  }
};

// 监听产品变化（AccountSelect组件会自动处理账号加载）
watch(
  () => contextProduct.value?.id,
  () => {
    // AccountSelect组件会自动根据orgId变化重新加载账号
  },
  { immediate: true },
);

onMounted(() => {
  loadAllAccounts();
});
</script>

<template>
  <div class="account-binding-view product-guide-slide" pr-10 flex flex-col>
    <!-- 账号选择和绑定 -->
    <div w-200 mt-10>
      <AccountSelect
        ref="accountSelectRef"
        v-model="selectedAccountForBind"
        :org-id="contextProduct?.orgId"
        :use-unbound-only="contextProduct?.fundType !== FundTypeEnum.MOM子基金"
        from="product"
        :context-product="contextProduct"
        @change="handleAccountChange"
      />
    </div>

    <!-- 已绑定账号列表 -->
    <div class="bound-accounts-section" mb-6>
      <h3 class="section-title" mb-3>已绑定账号</h3>
      <div h-400>
        <VirtualizedTable
          :columns="boundAccountColumns"
          :data="boundAccounts"
          :row-actions="boundAccountRowActions"
          :row-action-width="120"
          :show-toolbar="false"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.account-name-cell {
  display: flex;
  flex-direction: column;
}

.account-name {
  font-weight: 500;
}

.finance-account {
  font-size: 12px;
  opacity: 0.7;
}
</style>
