const { TypicalDataView } = require('../../classcial/typical-data-view');
const { SysProduct, SysProductDetail } = require('../../../../model/sys-product');
const { repoFund } = require('../../../../repository/fund');

class ProductsView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '产品列表');
        this.registerEvent('refresh', this.refresh.bind(this));
    }

    createTable(options) {

        if (this.userInfo.isCounselor) {

            let $manage_cols = this.$table.querySelectorAll('th[condition="manage-only"]');
            $manage_cols.forEach($col => { $col.remove(); });
        }
        
        super.createTable(options);
    }

    /**
     * @param {SysProductDetail} record
     */
    identifyRecord(record) {
        return record.fundId;
    }

    /**
     * @param {SysProductDetail} record
     */
    testRecords(record) {
        return this.tableObj.matchKeywords(record) || this.testPy(record.fundName, this.states.keywords);
    }

    async requestRecords() {
        var resp = await repoFund.getAll();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`产品列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var simples = resp.data;
        var sproducts = Array.isArray(simples) ? simples.map(item => new SysProduct(item)) : [];
        var ids = sproducts.map(x => x.id);
        var resp2 = await repoFund.getFundDetail(ids);
        if (resp2.errorCode != 0) {
            this.interaction.showError(`产品详情加载错误：${resp2.errorCode}/${resp2.errorMsg}`);
        }

        var fdetails = resp2.data;
        if (fdetails instanceof Array) {
            fdetails.forEach(item => {
                delete item.fundAccounts;
            });
        }

        var details = fdetails.map(x => {
            let matched = sproducts.find(y => y.id == x.fundId);
            let amac_code = matched ? matched.amacCode : 0;
            return new SysProductDetail(x, amac_code);
        });

        this.tableObj.refill(details);

        if (details.length > 0) {
            this.tableObj.selectRow(this.identifyRecord(details[0]));
        } else {
            this.trigger('selected-one-product', null);
        }
    }

    /**
     * @param {SysProductDetail} record
     * @param {Boolean} close_flag
     */
    formatFundStatus(record, close_flag) {
        return this.helperUi.makeYesNoLabelHtml(!close_flag, { yesLabel: '运行中', noLabel: '已清盘' });
    }

    /**
     * @param {SysProductDetail} record
     * @param {Boolean} closeFlag
     */
    formatFundStatusText(record, closeFlag) {
        return !closeFlag ? '运行中' : '已清盘';
    }

    /**
     * @param {SysProductDetail} record
     * @param {Boolean} close_flag
     */
    formatClosePosition(record, close_flag) {
        return '<button class="danger" event.onclick="confirmCloseAll">一键平仓</button>';
    }

    /**
     * @param {SysProductDetail} record
     */
    confirmCloseAll(record) {
        this.interaction.showConfirm({
            title: '一键平仓确认',
            message: '一键平仓产品：' + record.fundName,
            confirmed: () => {
                console.log({ fundId: record.fundId }, record);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.closePosition, { fundId: record.fundId });
                this.interaction.showSuccess(`平仓请求已发出，目标：${record.fundName}`);
            },
        });
    }

    /**
     * @param {SysProductDetail} record
     */
    handleRowSelected(record) {
        this.trigger('selected-one-product', record);
    }

    build($container, options) {
        super.build($container, options, { tableName: 'smt-op', heightOffset: 92 });
        this.requestRecords();
    }
}

module.exports = { ProductsView };
