const { IView } = require('../../../../component/iview');
const { AccountsView } = require('./accounts');
const IdentityNavView = require('./nav');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '概览');
        this.events = {
            contextChange: 'set-context-identity',
        };
    }

    createAccountsView() {

        var $root = this.$tr.querySelector('td > .block-accounts');
        var view = new AccountsView('@2021/overview/components/accounts', false);
        view.loadBuild($root, this.voptions, () => {
            this.helper.isNotNone(this.identityId) && view.trigger(this.events.contextChange, this.identityId);
        });
        this.accountView = view;
    }

    createNavView() {

        var $root = this.$tr.querySelector('td > .block-nav-chart');
        var view = new IdentityNavView('@2021/overview/components/nav', false);
        view.loadBuild($root, this.voptions, () => {
            this.helper.isNotNone(this.identityId) && view.trigger(this.events.contextChange, this.identityId);
        });
        this.chartView = view;
    }

    handleIdentityChange(identityId, forced = false) {

        if (identityId === this.identityId && !forced) {
            return;
        }

        this.identityId = identityId;
        this.accountView.trigger(this.events.contextChange, identityId);
        this.chartView.trigger(this.events.contextChange, identityId);
    }

    handleHeightChange(height) {

        this.$table.style.height = height - 100 + 'px';
        this.accountView.trigger('table-max-height', height);
        this.chartView.trigger('set-chart-height', height);
    }

    refresh() {

        this.interaction.showSuccess('刷新已执行');
        this.handleIdentityChange(this.identityId, true);
    }

    exportSome() {
        this.accountView.exportSome();
    }

    build($container, options) {

        super.build($container);
        this.voptions = options;
        this.$table = this.$container.querySelector('.identity-composition > table');
        this.$tr = this.$table.querySelector('tbody > tr');
        this.createAccountsView();
        this.createNavView();
        this.registerEvent(this.events.contextChange, this.handleIdentityChange.bind(this));
        this.registerEvent('table-max-height', this.handleHeightChange.bind(this));
    }
}

module.exports = View;