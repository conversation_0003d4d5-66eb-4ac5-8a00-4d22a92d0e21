<div class="role-view-root">
    <template>
        <div class="s-scroll-bar" style="overflow: auto;">

            <div class="s-typical-toolbar">
                <el-button type="primary" @click="showCreationDialog" type="primary" size="mini">
                    <i class="el-icon-plus"></i> 创建新角色
                </el-button>
                <el-input v-model="searching.value" class="s-mgl-10" style="width: 160px;" placeholder="请输入关键词" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
                <el-button class="s-pull-right" size="mini" @click="refresh">
                    <span class="el-icon-refresh"></span> 刷新
                </el-button>
            </div>

            <data-tables layout="pagination,table" :filters="filters" table-label="角色管理"
                class="s-searchable-table" v-bind:data="roleList" v-bind:table-props="tableProps"
                v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef">
                <el-table-column key="roleManagementIndex" label="序号" prop="index" type="index" width="60"
                    align="center"></el-table-column>
                <el-table-column key="roleManagementRoleName" sortable label="角色名称" prop="roleName" min-width="120"
                    show-overflow-tooltip></el-table-column>
                <el-table-column key="roleManagementRoleId" sortable label="角色ID" prop="id" min-width="80"></el-table-column>
                <el-table-column key="roleManagementStatus" label="启用/禁用" prop="status" width="75" align="center">
                    <template slot-scope="scope">
                        <el-switch @change="toggleEnableStatus(scope.row)" v-model="scope.row.activeFlag"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column key="roleManagementOperation" label="操作" prop="operation" width="100"
                    class-name="s-col-oper" fixed="right">
                    <template slot-scope="props">
                        <el-tooltip content="配置权限" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button iconfont icon-shezhi11" @click="configRights(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button el-icon-edit" @click="showEditDialog(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button el-icon-delete s-color-red" @click="removeRole(props.row)"></a>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </data-tables>

            <div class="table-4-role-list-export" style="display: none;">
                <table>
                    <tr>
                        <th label="角色名称" prop="roleName"></th>
                        <th label="角色ID" prop="id"></th>
                        <th label="状态" prop="activeFlag" formatter="formatActivation"></th>
                    </tr>
                </table>
            </div>

            <el-dialog v-drag width="380px" class="dialog-role-editing role-dialog"
                v-bind:title="dialog.role.isCreation ? '创建角色' : '修改角色信息'" v-bind:visible="dialog.role.visible"
                :show-close="false" v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false">

                <el-form class="role-form" ref="form-editing-role" size="mini" v-bind:inline="true"
                    v-bind:model="dialog.role.form" v-bind:rules="dialog.role.rules" label-width="80px">

                    <el-form-item label="角色名称" prop="roleName">
                        <el-input placeholder="角色名称" v-model.trim="dialog.role.form.roleName"></el-input>
                    </el-form-item>

                    <el-form-item label="状态" prop="activeFlag">
                        <el-radio-group v-model="dialog.role.form.activeFlag">
                            <el-radio v-for="(item, item_idx) in roleStatusList" :key="item_idx"
                                :label="item.status">
                                {{item.mean}}</el-radio>
                        </el-radio-group>
                    </el-form-item>

                </el-form>
                <div slot="footer">
                    <el-button type="primary" @click="confirmSaveRole" type="primary" size="small">确定</el-button>
                    <el-button @click="() => { dialog.role.visible = false; }" size="small">取消</el-button>
                </div>
            </el-dialog>

            <el-dialog v-drag class="role-dialog" :title="dialog.roleRights.title" :visible="dialog.roleRights.visible"
                :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
                <div class="role-box">
                    <!-- <div class="tab-box" v-for="tab in rightTabs" :key="tab.name">
                        <div class="tab-title">
                            <el-checkbox v-model="tab.selected" @change="handleTabChange(tab.selected, tab.id)">
                                {{tab.name}}</el-checkbox>
                        </div>
                        <div>
                            <el-checkbox-group v-model="tab.selectedRights" @change="handleRightChange(tab.id)">
                                <el-checkbox :disabled="right.defaultPermission" v-for="right in tab.rights"
                                    :key="right.id" :label="right.id">{{right.permissionName}}</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div> -->
                    <el-table ref="roleTable" :data="menuData" max-height="650px" row-key="id" border stripe default-expand-all
                        @selection-change="handleSelect">
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column label="菜单名称" prop="menuName" show-overflow-tooltip></el-table-column>
                        <el-table-column label="备注" prop="menuIcon" show-overflow-tooltip></el-table-column>
                    </el-table>
                </div>
                <div class="s-center" slot="footer">
                    <el-button type="primary" @click="confirmSaveRoleRights" type="primary" size="small">确定</el-button>
                    <el-button @click="() => { dialog.roleRights.visible = false; }" size="small">取消</el-button>
                </div>
            </el-dialog>
        </div>
    </template>
</div>