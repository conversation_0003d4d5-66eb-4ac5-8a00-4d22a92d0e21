const { IdentityRelationView } = require('../classcial/identity-relation-view');
const { SpecialTradeRecordRemark, TradeDataQueryCondition } = require('../../../model/trading');

class QueryToolbarView extends IdentityRelationView {

    constructor(view_name, { isEntrust, isPosition, isTrade } = {}) {

        super(view_name, false);
        this.total = 100;
        this.pageSize = 30;

        this.source = {

            isEntrust: !!isEntrust,
            isPosition: !!isPosition,
            isTrade: !!isTrade,
        };

        this.tbdata = {

            source: this.source,
            istates: this.istates,
            funds: this.products,
            strategies:  this.strategies,
            accounts: this.accounts,

            date: [],
            checked: true,
            special: false,
            searchValue: '',
        };
    }

    createApp() {
        
        this.toolbarVueIns = new Vue({

            el: this.$container.firstElementChild,
            data: this.tbdata,
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleSearch,
                this.handleProductChange,
                this.handleStrategyChange,
                this.handleAccountChange,
                this.dateTimechange,
                this.formatSelectAccountName,
            ]),
        });

    }

    isDateRangeOk() {
        return this.tbdata.date instanceof Array && this.tbdata.date.length == 2;
    }

    /**
     * @returns {TradeDataQueryCondition}
     */
    collectInput() {
        
        let { date, checked, special, searchValue } = this.tbdata;
        let { productId, strategyId, accountId } = this.istates;

        let searchData = new TradeDataQueryCondition({

            date: date,
            checked: checked,
            remark: special ? Object.values(SpecialTradeRecordRemark).join(',') : null,
            business_flag: special ? this.systemTrdEnum.businessFlag.settleAndSangu.code : null,
            fund: productId,
            strategy: strategyId,
            account: accountId,
            searchValue: searchValue,
            pageNo: 1,
            pageSize: 30,
            userId: this.userInfo.userId,
            token: this.userInfo.token,
        });

        if (!this.isDateRangeOk()) {
            
            /**
             * 未选择有效的日期区间时，自动勾选【当日】选项
             */

            searchData.checked = true;
            this.tbdata.checked = true;
        }
        
        return searchData;
    }

    handleSearch() {

        this.interaction.showSuccess('刷新请求已发送');
        this.trigger('search-btn', this.collectInput());
    }

    dateTimechange() {
        //
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.handleSearch();
    }
}

module.exports = { QueryToolbarView };