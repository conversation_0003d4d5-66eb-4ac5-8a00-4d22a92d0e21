.creation-view {
    color: white;
}

.creation-body {

    .header {
        
        height: 40px;
        padding-right: 10px;
        border-radius: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .input-area {
            display: flex;
            align-items: center;
        }

        .input-sth {
            display: flex;
            align-items: center;
        }

        .ctr-label {
            display: inline-block;
            margin-left: 10px;
            margin-right: 10px;
        }
    }

    .param-body {

        margin-top: 10px;
        display: flex;

        .part-left {

            width: 35%;
            box-sizing: border-box;
            padding-right: 10px;
            display: flex;
            flex-direction: column;

            .el-input,
            .el-input-number,
            .el-input__inner,
            .el-select {
                width: 100%;
            }
        }

        .part-right {

            width: 65%;
            box-sizing: border-box;
            padding-left: 10px;
            display: flex;
            flex-direction: column;
        }

        .part-title {

            padding-left: 10px;
            line-height: 38px;
            font-size: 14px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }

        .left-part-title {

            display: flex;
            justify-content: space-between;
            padding-right: 10px;
        }

        .part-body {

            height: 500px;
            flex-grow: 1;
            background-color: #2A2929;

            .param-row {

                line-height: 38px;
                padding-left: 10px;
                display: flex;
            }

            .row-control {
                flex-grow: 1;
                padding-right: 10px;
            }

            .el-input__inner {
                text-align: left;
            }
        }
    }

    .ctr-label,
    .row-label {
        color: #aaa;
    }

    .row-label {

        display: inline-block;
        width: 100px;
    }

    .stocks-list {

        .el-input {

            margin-left: 0 !important;
            width: 100%;
        }
    }

    .dir-select {

        height: 24px;
        border-radius: 3px;
        background-color: #0C1016;
        color: white;
    }
}

.import-table-sample {
    
    position: relative;
    display: inline-block;
    width: 488px;
    height: 180px;
    background-image: url(../asset/image/template-sample/buy-sell-stock.png);

    .buttons-row {

        position: absolute;
        z-index: 1;
        right: 5px;
        top: 0;
        margin-top: -23px;

        .el-button {
            height: 20px !important;
        }
    }
}