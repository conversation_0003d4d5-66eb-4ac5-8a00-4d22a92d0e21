/**
 * 风控消息结构
 */
class RiskMessage {

    constructor({ 
        id,
        configurationId, 
        identity, 
        identityType, 
        identityName, 
        warningType, 
        content, 
        createTime,
    }) {

        this.id = id;
        this.configurationId = configurationId;
        this.identity = identity;
        this.identityType = identityType;
        this.identityName = identityName;
        this.warningType = warningType;
        this.content = content;
        this.createTime = createTime;
    }
}

module.exports = {
    RiskMessage,
};