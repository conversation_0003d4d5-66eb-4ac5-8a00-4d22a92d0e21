import { IdentityType } from '../../config/trading';

/**
 * 交易分类信息接口
 * 用于定义交易分类的数据结构，包含分类的层级、归属、创建者等信息
 */
export enum TradeClassificationType {
  Asset = 1,
  ProductGroup = IdentityType.FundGroup.value,
  AccountGroup = IdentityType.AccountGroup.value,
}

/**
 * 交易分类信息接口
 * 用于定义交易分类的数据结构，包含分类的层级、归属、创建者等信息
 */
export const TradeClassificationTypes = [
  { label: '资产分类', value: TradeClassificationType.Asset },
  { label: '产品联合组管理', value: TradeClassificationType.ProductGroup },
  { label: '账号组管理', value: TradeClassificationType.AccountGroup },
];

/**
 * 交易分类信息接口
 * 用于定义交易分类的数据结构，包含分类的层级、归属、创建者等信息
 */
export interface TradeClassification {
  /**
   * 分类的唯一标识ID
   */
  id: number;

  /**
   * 分类名称
   */
  name: string;

  /**
   * 分类类型
   * 用于区分不同业务类型的交易分类
   */
  type: TradeClassificationType;

  /**
   * 分类描述信息
   * 可选字段，用于补充说明该分类的用途或特点
   */
  description?: string;

  /**
   * 父级分类ID
   * 用于构建分类的层级结构，顶级分类此值通常为0或-1
   */
  parentId: number;

  /**
   * 父级分类名称
   */
  parentName: string;

  /**
   * 分类层级
   * 表示当前分类在树形结构中的深度，如：1表示一级分类，2表示二级分类等
   */
  level: number;

  /**
   * 创建用户的ID
   */
  createUserId: number;

  /**
   * 创建用户的姓名
   * 用于展示创建者信息
   */
  createUserName: string;

  /**
   * 所属组织ID
   * 标识该分类归属于哪个组织或租户
   */
  orgId: number;
}

/**
 * 交易分类成员信息接口
 * 用于定义归属于某个交易分类的成员（如商品、服务、账户等）的数据结构
 */
export interface TradeClassificationMember {
  /**
   * 成员的唯一标识ID
   */
  id: number;

  /**
   * 所属分类的ID
   * 关联到 TradeClassification 的 id 字段，标识该成员归属于哪个分类
   */
  classificationId: number;

  /**
   * 成员类型
   * 用于区分不同类型的成员，例如：1-商品，2-服务，3-账户等（具体值需根据业务定义）
   */
  memberType: TradeClassificationType;

  /**
   * 成员编码
   * 业务系统中用于唯一标识该成员的代码，可用于外部系统对接或快速查找
   */
  memberCode: string;

  /**
   * 成员名称
   * 成员的展示名称
   */
  memberName: string;

  /**
   * 排序序号
   * 用于在同一分类下对成员进行排序展示，数值越小排序越靠前
   */
  sortOrder: number;
}
