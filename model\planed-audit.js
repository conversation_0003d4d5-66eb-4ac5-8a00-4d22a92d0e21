class HttpResponseStatus {

    constructor({ isSuccess, responseCode, responseMsg, warnMsg }) {

        /** 是否成功，此次操作是否成功完成，如果为false就代表操作失败 */
        this.isSuccess = !!isSuccess;
        /** “0000”:操作成功 */
        this.responseCode = responseCode;
        /** 响应信息，主要包含错误的详细信息，⽐如”⽤户关联失败”等 */
        this.responseMsg = responseMsg;
        /** 警告信息 */
        this.warnMsg = warnMsg;
    }
}

/**
 * 计划下单母单
 */
class PlanTradeMotherOrder {

    constructor({ aopId, fileName, prodCode, prodName, tradeDate, auditStatus, auditMsg, orderStatus, validateStatus }) {

        /** 母单Id，公共返回参数 */
        this.aopId = aopId;
        /** 文件名 */
        this.fileName = fileName;
        /** 产品代码 */
        this.prodCode = prodCode;
        /** 产品名称 */
        this.prodName = prodName;
        /** 交易日期 */
        this.tradeDate = tradeDate;
        /** 审核状态可能的值有：'fail'-失败, 'pass'-通过 */
        this.auditStatus = auditStatus;
        /** 审核信息 */
        this.auditMsg = auditMsg;
        /** 母单状态0-下单失败, 1-下单成功, 2-下单中 */
        this.orderStatus = orderStatus;
        /** 母单有效状态有效Y-是,无效N-否 */
        this.validateStatus = validateStatus;
    }
}

class PlanTradeMotherOrderResponse {

    /**
     * @param {HttpResponseStatus} status
     * @param {Array<PlanTradeMotherOrder>} listParent
     */
    constructor(status, listParent) {

        /** 状态 */
        this.status = status;
        /** 母单列表 */
        this.listParent = listParent || [];
    }
}

/**
 * 计划下单子单
 */
class PlanTradeChildOrder {

    constructor({ aopId, secuCode, investType, side, orderNum, priceModel, marketType, investCode, orderPrice, orderRemark }) {

        /** 母单id */
        this.aopId = aopId;
        /** 标的代码 */
        this.secuCode = secuCode;
        /** 投资种类 */
        this.investType = investType;
        /** 委托方向 */
        this.side = side;
        /** 指令数量 */
        this.orderNum = orderNum;
        /** 价格模式 */
        this.priceModel = priceModel;
        /** 交易市场 */
        this.marketType = marketType;
        /** 投顾编号 */
        this.investCode = investCode;
        /** 指令价格 */
        this.orderPrice = orderPrice;
        /** 指令备注 */
        this.orderRemark = orderRemark;
    }
}

class PlanTradeChildOrderResponse {

    /**
     * @param {HttpResponseStatus} status
     * @param {Array<PlanTradeChildOrder>} listDetail
     */
    constructor(status, listDetail) {
        
        /** 状态 */
        this.status = status;
        /** 子单列表 */
        this.listDetail = listDetail || [];
    }
}

module.exports = {
    
    HttpResponseStatus,
    PlanTradeMotherOrder,
    PlanTradeMotherOrderResponse,
    PlanTradeChildOrder,
    PlanTradeChildOrderResponse,
};
