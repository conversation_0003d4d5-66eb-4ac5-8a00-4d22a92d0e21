<script setup lang="ts">
import type { SystemIdentityInfo } from '../../../../../xtrade-sdk/dist';
import ChooseIdentityView from './ChooseIdentityView.vue';
import { reactive, useTemplateRef, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  title?: string;
  selectedIdentities: string[];
}>();

const dialogState = reactive({
  isVisible: false,
  targetIdentities: [] as string[],
});

watch(
  () => props.modelValue,
  newVal => {
    dialogState.isVisible = newVal;
    if (newVal) {
      dialogState.targetIdentities = [...props.selectedIdentities];
    }
  },
);

const emitter = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [identities: SystemIdentityInfo[]];
}>();

const hideDialog = () => {
  emitter('update:modelValue', false);
  dialogState.targetIdentities = [];
};

const $table = useTemplateRef('$table');

const confirmChoose = async () => {
  const identities = $table.value?.getSelectedRows() || [];
  emitter('confirm', identities);
  hideDialog();
};
</script>

<template>
  <el-dialog
    :model-value="dialogState.isVisible"
    :title="title || '请选择产品'"
    width="900px"
    @close="hideDialog"
    draggable
  >
    <div h-600 of-y-hidden>
      <ChooseIdentityView
        ref="$table"
        :ison="dialogState.isVisible"
        :selected-ids="dialogState.targetIdentities"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hideDialog">取消</el-button>
        <el-button type="primary" @click="confirmChoose">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
