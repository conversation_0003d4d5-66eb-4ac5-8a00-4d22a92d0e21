const { IView } = require('../../../../component/iview');
const BasketView = require('../../fragment/basket');
const TradeView = require('../basket/trade');
const CreditTradeView = require('../basket/basket-trade-credit');
const ChannelView = require('../../fragment/trading-channel');
const AccountGroupView = require('../../fragment/account-group');
const PreviewAndTaskView = require('../basket/preview-and-task');
const { WeightedAccountDetail } = require('../../model/account');
const { TradeChannel, BasketOrderParam } = require('../../model/message');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        
        super(view_name, is_standalone_window, '篮子交易');
        var astype = this.systemEnum.assetsType;
        this.channels = {

            spot: new TradeChannel(1, '现货竞价', astype.stock.code, [astype.stock.code, astype.fund.code, astype.bond.code], { isSpot: true }),
            // credit: new TradeChannel(2, '融资融券', astype.stock.code, [astype.stock.code, astype.fund.code, astype.bond.code], { isCredit: true }),
        };

        this.defaultChannel = this.channels.spot;

        this.states = {

            /** 当前的交易频道 */
            channel: this.defaultChannel,
            /** 当前的交易合约 */
            instrument: null,
            /** 当前的交易合约名称 */
            instrumentName: null,
        };
    }

    createBasketView() {

        var $root = this.$container.querySelector('.block-basket');
        var view = new BasketView('@2021/fragment/basket', false, 275);
        view.loadBuild($root);
        view.registerEvent('basket-local-changed', this.handleBasketLocalChange.bind(this));
        view.registerEvent('basket-changed', this.handleBasketChange.bind(this));
        this.basketView = view;
    }

    createAccountGroup() {

        var $root = this.$container.querySelector('.block-accounts');
        var view = new AccountGroupView('@2021/fragment/account-group', false, { fixAssetType: this.systemEnum.assetsType.stock.code, maxHeight: 275 });
        view.loadBuild($root, { isBasketAlgo: true }, () => {

            var isShareMode = true;
            /** 设置为分摊模式 */
            view.trigger('trade-mode-change', isShareMode);
            /** 隐藏部分数据列 */
            view.trigger('toggle-show-columns', false);
        });
        view.registerEvent('selected-accounts-changed', this.handleAccountChange.bind(this));
        this.accountGroupView = view;
    }

    createDataRecords() {

        var $root = this.$container.querySelector('.data-records');
        var view = new PreviewAndTaskView('@2021/trading/basket/preview-and-task');
        view.loadBuild($root);
        this.recordView = view;
    }

    createTradeView() {

        var $tradeRoot = this.$tradeRoot = this.$container.querySelector('.block-trade');
        this.$thirdColumn = this.$container.querySelector('col.third-column');
        /**
         * 现货竞价、期货、期权类，篮子交易视图
         */
        var tradeView = new TradeView('@2021/trading/basket/trade');
        var $root4Compete = document.createElement('div');
        $root4Compete.classList.add('s-full-height');
        $tradeRoot.appendChild($root4Compete);
        tradeView.loadBuild($root4Compete);
        this.tradeView = tradeView;
        this._focusedTradeView = tradeView;

        /**
         * 两融交易视图
         */
        var creditView = new CreditTradeView('@2021/trading/basket/basket-trade-credit');
        var $root4Credit = document.createElement('div');
        $root4Credit.classList.add('s-full-height');
        $tradeRoot.appendChild($root4Credit);
        creditView.loadBuild($root4Credit, null, () => { $root4Credit.style.display = 'none'; });
        this.trade4Credit = creditView;

        this.tradeViews = [tradeView, creditView];
        /**
         * 监听预览试算、下单事件
         */
        var setPreview = 'preview-basket-orders';
        var orderMade = 'place-basket-orders';
        this.tradeViews.forEach(thisView => {
            thisView.registerEvent(setPreview, this.handlePreviewRequest.bind(this));
            thisView.registerEvent(orderMade, this.handlePlaceRequest.bind(this));
        });
    }

    createChannel() {

        var $root = this.$container.querySelector('.basket-trading-channel');
        var view = new ChannelView('@2021/fragment/trading-channel');
        view.trigger('set-as-channels', this.helper.dict2Array(this.channels));
        view.registerEvent('channel-selected', this.handleChannelChange.bind(this));
        view.loadBuild($root);
        view.trigger('set-default-channel', this.defaultChannel);
        this.channelView = view;
    }

    /**
     * @param {TradeChannel} channel 
     */
    handleChannelChange(channel) {

        this.states.channel = channel;
        this.brocastChannel();
        this.showProperTradeView();
    }

    brocastChannel() {

        var channel = this.states.channel;
        var eventName = 'set-channel';
        this.accountGroupView.trigger(eventName, channel);
        this.basketView.trigger(eventName, channel);
        this.tradeViews.forEach(thisView => { thisView.trigger(eventName, channel); });
        this.recordView.trigger(eventName, channel);
    }

    /**
     * 根据交易渠道，展示适配的交易界面
     */
    showProperTradeView() {
    
        var channel = this.states.channel;
        var channels = this.channels;
        var expected;

        switch (channel) {

            case channels.spot:
            // case channels.future:
            // case channels.option:

                expected = this.tradeView;
                this.$thirdColumn.setAttribute('width', 305);
                this.$tradeRoot.style.width = '305px';
                break;

            // case channels.credit: 

            //     expected = this.trade4Credit; 
            //     this.$thirdColumn.setAttribute('width', 305);
            //     this.$tradeRoot.style.width = '305px';
            //     break;
        }

        this.tradeViews.forEach(thisView => {

            if (thisView === expected) {
                if (thisView.$container) {
                    thisView.$container.parentElement.style.display = 'block';
                }
            }
            else {
                if (thisView.$container) {
                    thisView.$container.parentElement.style.display = 'none';
                }
            }
        });

        if (!expected) {
            this.tradeView.show();
        }

        this._focusedTradeView = expected || this.tradeView;
    }

    /**
     * @param {Boolean} isLocalChanged 
     */
    handleBasketLocalChange(isLocalChanged) {

        /** 篮子列表，是否有未保存的改变 */
        this.isBasketLocalChanged = isLocalChanged;
        // console.log('factor > basket local change: ' + isLocalChanged);
    }

    /**
     * @param {Number} basketId 
     * @param {String} basketName 
     * @param {Boolean} isEtf 
     */
    handleBasketChange(basketId, basketName, isEtf) {

        /** 篮子列表选择的篮子ID */
        this.basketId = basketId;
        /** 篮子列表选择的篮子名称 */
        this.basketName = basketName;
        /** 篮子列表选择的篮子，是否为ETF篮子 */
        this.isEtf = isEtf;
        /** 当前是否选择了有效的篮子 */
        this.isBasketOk = this.helper.isNotNone(basketId);
        // console.log('factor > basket change: ' + basketId + '/' + basketName + '/' + isEtf);
        this.tradeViews.forEach(view => view.trigger('set-as-basket', basketId, basketName, isEtf));
        this.spreadConditionChange();
    }

    /**
     * @param {Array<WeightedAccountDetail>} accounts 
     */
    handleAccountChange(accounts) {

        /** 账号组选定的账号 */
        this.accounts = accounts;
        /** 当前是否选择了至少一个账号 */
        this.areAccountsOk = accounts instanceof Array && accounts.length > 0;
        // console.log('factor > account change: ', accounts);
        this.spreadConditionChange(accounts.map(x => x.accountId).distinct());
    }

    spreadConditionChange(account_ids) {
        this.tradeViews.forEach(tradeView => tradeView.trigger('account-changed', account_ids));
    }

    /**
     * @param {BasketOrderParam} trdParam 
     */
    async handlePreviewRequest(trdParam) {        
        this.check2Send(true, trdParam);
    }

    /**
     * @param {BasketOrderParam} trdParam 
     */
    handlePlaceRequest(trdParam) {
        this.check2Send(false, trdParam);
    }

    /**
     * @param {Boolean} isPreview 
     * @param {BasketOrderParam} trdParam 
     */
    async check2Send(isPreview, trdParam) {

        var isOk = this.isValidatedOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        if (this.isBasketLocalChanged) {

            this.interaction.showConfirm({

                title: '操作确认',
                message: '当前篮子载入后，已发生变动，但尚未保存；<br/>预览或下单，将针对已保存的篮子，是否继续？',
                confirmed: () => {
                    this.sendRequest(isPreview, trdParam);
                },
            });
        }
        else {
            this.sendRequest(isPreview, trdParam);
        }
    }

    /**
     * @param {Boolean} isPreview 
     * @param {BasketOrderParam} trdParam 
     */
    async sendRequest(isPreview, trdParam) {

        if (isPreview) {

            let queryd = this.formOrder(isPreview, trdParam);
            this.recordView.trigger('to-preview', queryd);
        }
        else {

            let regu = trdParam.regulation;
            let regulars = [];
            regu.suspend && regulars.push('停牌');
            regu.cash && regulars.push('现金替代');
            regu.ceiling && regulars.push('涨停');
            regu.floor && regulars.push('跌停');
            
            let mentions = [

                ['方向', trdParam.directionName],
                ['篮子', `${trdParam.basketId} / ${trdParam.basketName}`],
                ['账户数', this.accounts.length],
                ['下单方式', trdParam.methodName],
                [trdParam.methodLabel, `${trdParam.scale} (${trdParam.methodUnit})`],
                ['策略', trdParam.algoName],
                // ['跟盘价', trdParam.stageName],
                // ['偏移量', trdParam.offset],
                ['限制', regulars.length == 0 ? '[无]' : regulars.join('/')],
            ];
    
            let message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    
                    let orderParam = this.formOrder(isPreview, trdParam);
                    this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendBasketOrder, orderParam);
                    setTimeout(() => { this.recordView.trigger('reload-basket-tasks'); }, 1000 * 1);
                    this.interaction.showSuccess('篮子下单请求，已发送');
                },
            });
        }        
    }

    isValidatedOk() {

        if (!this.isBasketOk) {
            return '请选择交易篮子';
        }
        else if (!this.areAccountsOk) {
            return '请选择交易账号';
        }

        return true;
    }

    /**
     * @param {Boolean} isPreview 
     * @param {BasketOrderParam} trdParam 
     */
    formOrder(isPreview, trdParam) {

        var regul = trdParam.regulation;

        return {

            /** 篮子ID */
            basketId: trdParam.basketId,
            /** 价格类型 */
            priceFollowType: trdParam.stage,
            /** 偏移量 */
            priceDeviation: trdParam.offset,
            /** 方向：买入1，卖出-1，调仓0 */
            taskType: trdParam.direction,
            /** 调仓类型 0非调仓 1 普通调仓  2 两融调仓 */
            adjustType: trdParam.adjustType || 0,
            /**业务类型：融资融券... */
            businessFlag: trdParam.businessFlag,
            /** 预览模式，true返回预览结果，服务器不下单，false直接下单 */
            preview: isPreview,
            /** 篮子限制规则 */
            orderRegulation: {

                excludeSuspension: regul.suspend,
                excludeCashSubstitution: regul.cash,
                excludeUpperLimit: regul.ceiling,
                excludeLowerLimit: regul.floor,
            },
            /** 操作的篮子数量，或者金额，或者比例 */
            executeVolume: trdParam.scale,
            /** 下单方式 */
            executeType: trdParam.method,
            /** 账号具体分配规则 */
            taskDetails: this.accounts.map(item => ({

                fundId: item.fundId,
                strategyId: item.strategyId,
                accountId: item.accountId,
                multiple: item.multiple,
            })),

            algorithmBean: {

                algoId: trdParam.algoId,
                // algoName: trdParam.algoName,
                algoParam: trdParam.algoParam,
                effectiveTime: trdParam.effectiveTime,
                expireTime: trdParam.expireTime,
            }
        }
    }

    refresh() {
        this.interaction.showMessage('该页面未提供刷新');
    }
    
    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    build($container) {

        super.build($container);
        this.createChannel();
        this.createBasketView();
        this.createAccountGroup();
        this.createTradeView();
        this.createDataRecords();
        this.brocastChannel();
        this.showProperTradeView();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;
