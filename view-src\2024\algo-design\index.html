<div class="algo-design-index">
    <div class="toolbar s-pd-5 s-flex themed-box">
        <el-button type="primary" size="mini" @click="openCreate()">
            <i class="el-icon-plus"></i> 创建算法
        </el-button>
        <!-- <el-button type="success" size="mini" @click="openDesign()">
            <i class="el-icon-setting"></i> 算法参数设计
        </el-button> -->
        <el-dialog
            width="1000px" 
            class="algo-creation-dialog"
            title="系统算法维护"
            :show-close="true"
            v-bind:close-on-click-modal="false" 
            v-bind:close-on-press-escape="false"
            :visible="dialog.visible" 
            @close="toCancel"
            v-drag>
            <div class="s-pd-15">算法基础信息</div>
            <el-form ref="cform" class="creation-form" :model="formd" :rules="rules" :inline="true" label-width="100px">
                <el-form-item label="算法名称" prop="name">
                    <el-input v-model.trim="formd.name" clearable></el-input>
                </el-form-item>
                <el-form-item label="外部算法ID" prop="externalId">
                    <el-input v-model.trim="formd.externalId" clearable></el-input>
                </el-form-item>
                <el-form-item label="算法开发商" prop="vendorId">
                    <el-select v-model="formd.vendorId" filterable clearable>
                        <el-option v-for="(item, item_idx) in vendors" :key="item_idx" :label="item.vendorName" :value="item.vendorId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="支持券商" prop="borokerId">
                    <el-select v-model="formd.brokerId" filterable clearable>
                        <el-option v-for="(item, item_idx) in brokers" :key="item_idx" :label="item.brokerName" :value="item.brokerId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="算法说明" prop="remark">
                    <el-input v-model.trim="formd.remark" clearable></el-input>
                </el-form-item>
            </el-form>
            <div class="s-pd-15 s-flex" style="gap: 10px;">
                <span style="display: inline-block; width: 76px; text-align: right;">算法参数</span>
                <el-button size="mini" type="primary" class="s-mgl-10" @click="confirmEmpty">
                    <i class="el-icon-plus"></i>
                    <span>添加参数</span>
                </el-button>  
            </div>
            <div class="params-box">
                <div class="param-row" v-for="(item, item_idx) in formd.params" :key="item_idx">
                    <div class="left-no">
                        <label>{{ item_idx + 1 }}.</label>
                    </div>
                    <div class="center-area">
                        <span>名称</span>
                        <el-input v-model.trim="item.label" clearable></el-input>
                        <span>变量</span>
                        <el-input v-model.trim="item.prop" clearable></el-input>
                        <span>描述</span>
                        <el-input v-model.trim="item.remark" clearable></el-input>
                        <span>类型</span>
                        <el-select v-model="item.type" @change="handleDataTypeChange(item)">
                            <el-option v-for="(item2, item2_idx) in dataTypes" :key="item2_idx" :label="item2.label" :value="item2.value"></el-option>
                        </el-select>
                        <span>默认值</span>
                        <template v-if="isInteger(item.type)">
                            <el-input-number v-model.number="item.defaultValue" :precision="0" clearable></el-input-number>
                        </template>
                        <template v-else-if="isDecimal(item.type)">
                            <el-input-number v-model.number="item.defaultValue" :precision="2" clearable></el-input-number>
                        </template>
                        <template v-else-if="isTime(item.type)">
                            <el-time-picker v-model="item.defaultValue" placeholder="时间" clearable></el-time-picker>
                        </template>
                        <template v-else-if="isTimeRange(item.type)">
                            <el-time-picker v-model="item.defaultValue" range-separator="至" start-placeholder="开始" end-placeholder="结束" is-range clearable></el-time-picker>
                        </template>
                        <template v-else-if="isText(item.type)">
                            <el-input v-model.trim="item.defaultValue" clearable></el-input>
                        </template>
                        <template v-else-if="isUserOption(item.type)">
                            <div class="input-wrap">
                                <div class="select-user-options">
                                    <el-select v-model="item.defaultValue" :popper-append-to-body="false" clearable>
                                        <el-option v-for="(item2, item2_idx) in item.uoptions" :key="item2_idx" :label="item2.label" :value="item2.value">
                                            <span class="option-item">
                                                <span class="option-item-label">{{ item2.label }}</span>
                                                <span class="option-item-delete" @click.stop="deleteOption(item, item2.value)">
                                                    <i class="el-icon-close"></i>
                                                </span>
                                            </span>
                                        </el-option>
                                    </el-select>
                                    <el-tooltip placement="top" popper-class="algo-param-option-form" :manual="true" v-model="item.adding">
                                        <div slot="content">
                                            <div class="option-row">
                                                <span>选项文字</span>
                                                <el-input v-model.trim="item.optionLabel" clearable></el-input>
                                            </div>
                                            <div class="option-row">
                                                <span>选项取值</span>
                                                <el-input v-model.trim="item.optionValue" clearable></el-input>
                                            </div>
                                            <div class="option-row">
                                                <span></span>
                                                <el-button size="small" type="primary" @click="confirmNewOption(item)">+ 添加选项</el-button>
                                                <el-button size="small" @click="cancelNewOption(item)">取消</el-button>
                                            </div>
                                        </div>
                                        <i class="add-button el-icon-plus" @click="addNewOption(item)"></i>
                                    </el-tooltip>
                                </div>
                            </div>
                        </template>
                        <span>属性</span>
                        <div class="input-wrap">
                            <div class="switch-controls">
                                <span>必填</span>
                                <el-switch v-model="item.required"></el-switch>
                                <span>可见</span>
                                <el-switch v-model="item.display"></el-switch>
                            </div>
                        </div>
                    </div>
                    <div class="right-btn">
                        <el-button size="mini" type="danger" @click="deleteParam(item)">
                            <i class="el-icon-close"></i>
                        </el-button>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <el-button size="small" type="primary" @click="toCreate">确定</el-button>
                <el-button size="small" @click="toCancel">取消</el-button>
            </div>
        </el-dialog>
    </div>
    <div class="data-table">
        <table>
            <tr>
                <th label="算法名称" prop="name" width="120" overflowt></th>
                <th label="外部算法ID" prop="externalId" width="80" overflowt></th>
                <th label="算法开发商" prop="vendorId" width="100" formatter="formatVendor" overflowt></th>
                <th label="支持券商" prop="brokerId" width="100" formatter="formatBroker" overflowt></th>
                <th label="算法类型" prop="strategyType" width="110" formatter="formatStrategyType" overflowt></th>
                <th label="算法说明" prop="remark" width="200" overflowt></th>
                <!-- <th label="创建时间" prop="createTime" width="80" formatter="formatDateTime" overflowt></th>
                <th label="更新时间" prop="updateTime" width="80" formatter="formatDateTime" overflowt></th> -->
                <th label="操作" fixed-width="80" fixed="right">
                    <a class="icon-button el-icon-edit" event.onclick="editAlgo" title="编辑"></a>
                    <a class="icon-button el-icon-delete s-color-red" event.onclick="deleteAlgo" title="删除"></a>
                </th>
            </tr>
        </table>
    </div>
    <div class="design-box" style="height: 0;"></div>
</div>