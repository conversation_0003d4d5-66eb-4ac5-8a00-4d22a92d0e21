import { Repos } from '../../../xtrade-sdk';

const recordsRepo = new Repos.RecordsRepo();

class RecordService {
  /**
   * 获取当日持仓
   */
  static async getTodayPositions(identityId: number | string) {
    const resp = await recordsRepo.QueryTodayPositionsAll({ identityId });
    if (Array.isArray(resp)) {
      return resp;
    } else {
      console.error(resp);
      return [];
    }
  }
  /**
   * 获取当日订单
   */
  static async getTodayOrders(identityId: number | string) {
    const resp = await recordsRepo.QueryTodayOrdersAll({ identityId });
    if (Array.isArray(resp)) {
      return resp;
    } else {
      console.error(resp);
      return [];
    }
  }

  /**
   * 获取当日成交
   */
  static async getTodayTrades(identityId: number | string) {
    const resp = await recordsRepo.QueryTodayTradeRecordsAll({ identityId });
    if (Array.isArray(resp)) {
      return resp;
    } else {
      console.error(resp);
      return [];
    }
  }
}

export default RecordService;
