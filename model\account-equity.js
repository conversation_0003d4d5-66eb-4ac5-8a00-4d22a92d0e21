
/**
 * 账号权益
 */
class AccountEquity {

    constructor(struc) {

        this.id = struc.id;
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.financeAccount = struc.financeAccount;
        // this.strategyId = struc.strategyId;
        // this.strategyName = struc.strategyName;
        this.assetType = struc.assetType;
        this.maxLimitMoney = struc.maxLimitMoney;
        this.available = struc.available;
        this.closeProfit = struc.closeProfit;
        this.positionProfit = struc.positionProfit;
        this.balance = struc.balance;
        this.preBalance = struc.preBalance;
        this.frozenMargin = struc.frozenMargin;
        this.frozenCommission = struc.frozenCommission;
        this.margin = struc.margin;
        this.commission = struc.commission;
        this.marketValue = struc.marketValue;
        this.inMoney = struc.inMoney;
        this.outMoney = struc.outMoney;
        this.tradingDay = struc.tradingDay;
    }
}

module.exports = { AccountEquity };