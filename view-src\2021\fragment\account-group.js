const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const repoAccount = require('../../../repository/account').repoAccount;
const repoBasket = require('../../../repository/basket');
const { TradeChannel, InstrumentInfo } = require('../model/message');
const { WeightedAccountDetail, AccountGroup, AccountPosition } = require('../model/account');

const AccountGroupOptions = {

    /**
     * 限制分组内的资产类型
     */
    fixAssetType: null,
    /**
     * 表格默认最大高度
     */
    maxHeight: 240,
};

class View extends IView {

    constructor(view_name, is_standalone_window, options = AccountGroupOptions) {

        super(view_name, is_standalone_window, '账号组');

        /**
         * @returns {Array<WeightedAccountDetail>}
         */
        function allocateAccounts() {
            return [];
        }

        this.options = options;
        this.accounts = allocateAccounts();
        var defaultGroup = new AccountGroup({ groupId: 'allgroup-' + new Date().getTime(), groupName: '全部账号' });
        this.allGroup = defaultGroup;
        this.groups = [defaultGroup];
        this.states = {

            isBasketAlgo: false,
            allGroupId: defaultGroup.groupId,
            groupId: defaultGroup.groupId,
            filters: { isChecked: false, showAll: false },
            total: 0,
            checkeds: 0,
            keywords: null,
        };
        /** 表格最大高度设置 */
        this.maxHeight = options.maxHeight;
        /** 监听交易渠道切换 */
        this.registerEvent('set-channel', this.handleChannelChange.bind(this));
        /** 监听限制的资产类型变化 */
        this.registerEvent('set-asset-type', this.handleAssetTypeChange.bind(this));
        /** 监听分摊数量变更 */
        this.registerEvent('allocate-volume-to-account', this.handleVolumeAllocation.bind(this));
        /** 监听设置目标合约 */
        this.registerEvent('set-as-instrument', this.handleInstrumentChange.bind(this));
        /** 监听交易模式切换 */
        this.registerEvent('trade-mode-change', this.handleModeChange.bind(this));
        /** 显示或隐藏：目标合约 + 合约可用 + 委托数量，各列 */
        this.registerEvent('toggle-show-columns', this.handleToggleShowRequest.bind(this));
    }

    /**
     * @param {TradeChannel} channel 
     */
    handleChannelChange(channel) {

        var astype = channel.assetType;

        /**
         * 当前渠道是否支持T0交易周期
         */
        this.isT0 = astype == this.systemEnum.assetsType.future.code ||
                    astype == this.systemEnum.assetsType.option.code;

        /**
         * 交易渠道
         * 1. 该交易渠道，在此处被设置之前，其他步骤可能已提前开始访问；
         * 2. 故，在使用前应对渠道引用变量进行，是否为空的判断
         */
        this.channel = channel;
        
        /**
         * 重置账号列表与账号组
         */
        var isReady = !!this.tableObj;
        if (isReady) {
            this.requestAccounts(() => { this.requestGroups(); });
        }
    }

    handleAssetTypeChange(assetType) {

        this.options.fixAssetType = assetType;
        this.requestAccounts(() => { this.requestGroups(); });
    }

    /**
     * @param {Array<{ account: WeightedAccountDetail, volume: Number }>} settings 
     */
    handleVolumeAllocation(settings) {

        settings.forEach(item => {

            let account = item.account;
            let struct = {

                accountId: account.accountId,
                fundId: account.fundId,
                strategyId: account.strategyId,
                volume: item.volume,
            };

            this.tableObj.updateRow(struct);
        });
    }

    /**
     * 设置为当前合约
     * @param {InstrumentInfo} insInfo
     */
    async handleInstrumentChange(insInfo) {

        /**
         * 当前目标合约信息
         */
        this.insInfo = insInfo;

        /**
         * 重置各个账号的，合约与持仓相关数据字段
         */
        for (let i = 0; i < this.accounts.length; i++) {

            let acnt = this.accounts[i];
            let struct = {

                accountId: acnt.accountId,
                fundId: acnt.fundId,
                strategyId: acnt.strategyId,

                shortInstrument: insInfo ? insInfo.shortInstrument : null,
                instrument: insInfo ? insInfo.instrument : null,
                instrumentName: insInfo ? insInfo.instrumentName : null,
                multiple: insInfo ? insInfo.multiple : 1,

                longPosition: 0,
                shortPosition: 0,
                volume: 0,

                longMarginRatioByMoney: null,
                longMarginRatioByVolume: null,
                shortMarginRatioByMoney: null,
                shortMarginRatioByVolume: null,
            };

            if (insInfo) {

                let marginResp = await repoAccount.getInstrumentMargin(acnt.accountId, insInfo.shortInstrument);
                let { errorCode, data } = marginResp || {};
                if (marginResp && errorCode == 0 && data) {

                    struct.longMarginRatioByMoney = data.longMarginRatioByMoney;
                    struct.longMarginRatioByVolume = data.longMarginRatioByVolume;
                    struct.shortMarginRatioByMoney = data.shortMarginRatioByMoney;
                    struct.shortMarginRatioByVolume = data.shortMarginRatioByVolume;
                }
            }

            let key = this.identifyRecord(struct);
            if (this.tableObj.hasRow(key)) {
                this.tableObj.updateRow(struct);
            }

            console.log('test log > instrument change:', struct);
        }

        if (!insInfo) {
            return;
        }

        var accounts = this.accounts.filter(item => item.assetType == insInfo.assetType);
        if (accounts.length == 0) {
            return;
        }

        if (this.hasListened2AccountPositionReply === undefined) {

            /**
             * 是否已开启对账号特定合约持仓的回应监听
             */
            this.hasListened2AccountPositionReply = true;

            /**
             * 监听持仓查询回执
             */
            this.renderProcess.on(this.serverEvent.accountPositionPush, this.handlePositionReply.bind(this));
        }

        var param = accounts.map(item => ({

            identityId: this.helper.isNotNone(item.strategyId) ? item.strategyId : item.fundId,
            accountId: item.accountId,
            instrument: insInfo.instrument,
        }));

        console.log('test log > to query position:', param);

        /**
         * 发送查询持仓请求
         */
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.requestAccountPosition, param);
    }

    /**
     * @param {Boolean} isShare 是否为分摊模式
     */
    handleModeChange(isShare) {

        if (this.isBasketAlgo) {
            return;
        }

        if (isShare) {
            this.tableObj.showColumns(['分配权重']);
        }
        else {
            this.tableObj.hideColumns(['分配权重']);
        }
    }

    /**
     * 显示或隐藏：目标合约 + 合约可用 + 委托数量，各列
     * @param {Boolean} showUp
     */
    handleToggleShowRequest(showUp) {

        var columns = ['目标合约', '合约可用', '委托数量'];
        if (showUp) {
            this.tableObj.showColumns(columns);
        }
        else {
            this.tableObj.hideColumns(columns);
        }
    }

    /**
     * @param {AccountPosition} pos 
     */
    decideFinalPosition(pos) {
        return this.isT0 ? pos.todayPosition + pos.yesterdayPosition - pos.frozenTodayVolume - pos.frozenVolume
                        : pos.yesterdayPosition - pos.frozenVolume;
    }

    handlePositionReply(event, reply, reqId) {
        
        if (reply.errorCode != 0) {
            return;
        }

        console.log('test log > received position query result:', reply.data);
        var positions = AccountPosition.Convert(reply.data);
        var buy_positions = positions.filter(x => x.direction > 0);
        var sell_positions = positions.filter(x => x.direction < 0);

        buy_positions.forEach(pos => {

            let struct = {

                accountId: pos.accountId,
                fundId: pos.fundId,
                strategyId: pos.strategyId,
                volume: 0,
                longPosition: this.decideFinalPosition(pos),
                shortPosition: 0,
            };

            this.tableObj.updateRow(struct);
        });

        sell_positions.forEach(pos => {

            let struct = {

                accountId: pos.accountId,
                fundId: pos.fundId,
                strategyId: pos.strategyId,
                shortPosition: this.decideFinalPosition(pos),
            };

            this.tableObj.updateRow(struct);
        });
    }

    createApp() {

        new Vue({

            el: this.$toolbar,
            data: {

                groups: this.groups,
                states: this.states,
            },
            computed: {
                isAll: () => { return this.isAllGroup; },
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleGroupChange,
                this.handleFilterChange,
                this.filterRecords,
                this.hope2EditGroup,
                this.hope2DeleteGroup,
                this.hope2Refresh,
                this.hope2SaveGroup,
                this.hope2Config,
            ]),
        });
    }

    get showAll() {
        return this.states.filters.showAll;
    }

    get theGroup() {
        return this.groups.find(item => item.groupId == this.states.groupId);
    }

    get isAllGroup() {
        return this.states.groupId == this.allGroup.groupId;
    }

    get isCustomGroup() {
        return this.states.groupId != this.allGroup.groupId;
    }

    /**
     * 
     * @param {WeightedAccountDetail} record 
     */
    identifyRecord(record) {
        return WeightedAccountDetail.MakeId(record);
    }

    /**
     * @param {WeightedAccountDetail} record
     */
    formatWeight(record, weight, fieldName) {

        return `<input class="xt-table-row-input s-right" 
                       value="${weight}"
                       maxlength="9"
                       onmouseover="this.select();"
                       event.onchange="handleWeightChange" />`;
    }

    /**
     * @param {WeightedAccountDetail} record
     */
    formatConnectionStatus(record) {
        return record.connectionStatus ? '<a class="s-flag s-bg-green">已连接</a>' : '<a class="s-flag s-bg-grey">已断开</a>';
    }

    createTable() {

        this.helper.extend(this, ColumnCommonFunc);
        const tableObj = this.tableObj = new SmartTable(this.$table, this.identifyRecord, this, {

            tableName: 'smt-fag',
            displayName: this.title,
            enableConfigToolkit: true,
            rowSelected: this.handleRowSelect.bind(this),
            rowChecked: this.handleRowCheckChange.bind(this),
            allRowsChecked: this.handleAllRowsChecked.bind(this),
        });

        tableObj.hideColumns(['分配权重']);
        tableObj.setPageSize(*********);
        tableObj.setMaxHeight(this.maxHeight);
        tableObj.fitColumnWidth();
    }

    /**
     * 保持当前显示的账号，具备正确的合约挂载
     */
    syncWithInstrument() {

        if (!this.insInfo) {
            return;
        }

        var insInfo = this.insInfo;
        var checkeds = (/** @returns {Array<WeightedAccountDetail>} */ () => { return this.tableObj.extractAllRecords(); })();
        checkeds.forEach(item => {

            if (this.helper.isNotNone(item.instrument)) {
                return;
            }
        
            let struct = {

                accountId: item.accountId,
                fundId: item.fundId,
                strategyId: item.strategyId,
                
                shortInstrument: insInfo.shortInstrument,
                instrument: insInfo.instrument,
                instrumentName: insInfo.instrumentName,
            };

            this.tableObj.updateRow(struct);
        });
    }

    handleGroupChange() {

        this.states.keywords = null;
        this.states.filters.isChecked = false;
        this.states.filters.showAll = false;

        if (this.isAllGroup) {

            this.states.total = this.accounts.length;
            this.tableObj.refill(this.accounts);
        }
        else {
            
            var group = this.groups.find(item => item.groupId == this.states.groupId);
            var members = group.members;
            var filtereds = this.accounts.filter(acnt => {

                return members.some(memb => {
                    return memb.accountId == acnt.accountId && 
                            (memb.isStrategyOn && memb.strategyId == acnt.strategyId || !memb.isStrategyOn && memb.fundId == acnt.fundId);
                });
            });

            this.states.total = filtereds.length;
            this.tableObj.refill(filtereds);
            
            if (filtereds.length > 0) {
                this.tableObj.checkAll();
            }
            else {
                this.tableObj.uncheckAll();
            }
        }

        this.syncWithInstrument();
        this.filterRecords();
    }

    handleFilterChange() {

        var checkeds = this.tableObj.extractCheckedRecords();

        if (this.showAll) {

            this.tableObj.refill(this.accounts);
            checkeds.forEach(item => {
                this.tableObj.checkRow(this.identifyRecord(item));
            });

            this.spread(checkeds);
            this.states.checkeds = checkeds.length;
            this.states.total = this.accounts.length;
        }
        else {

            this.tableObj.refill(checkeds);
            this.tableObj.checkAll();
            this.states.checkeds = checkeds.length;
            this.states.total = checkeds.length;
        }

        this.syncWithInstrument();
        this.filterRecords();
    }

    filterRecords() {
        this.tableObj.setKeywords(this.states.keywords);
    }

    /**
     * @param {WeightedAccountDetail} rdata
     */
    handleRowSelect(rdata) {

        var isChecked = this.tableObj.isRowChecked(this.identifyRecord(rdata));
        if (isChecked) {
            this.trigger('account-row-selected', rdata);
        }
    }

    /**
     * @param {WeightedAccountDetail} rdata 
     * @param {Boolean} checked 
     * @param {Number} totalChecked 
     */
    handleRowCheckChange(rdata, checked, totalChecked) {
        this.handleCheckChange(totalChecked);
    }

    /**
     * @param {Boolean} isChecked 
     * @param {Number} totalChecked 
     */
    handleAllRowsChecked(isChecked, totalChecked) {
        this.handleCheckChange(totalChecked);
    }

    /**
     * @param {Array<WeightedAccountDetail>} rows 
     */
    typeRows(rows) {
        return rows;
    }

    /**
     * @param {Number} totalChecked 
     */
    handleCheckChange(totalChecked) {

        this.states.filters.isChecked = false;
        this.states.checkeds = totalChecked;

        /**
         * 对篮子算法交易，做单账号特殊处理
         */
        
        if (this.isBasketAlgo) {
            
            let checks = this.typeRows(this.tableObj.extractCheckedRecords());
            if (checks.length > 1) {

                while (checks.length > 1) {
                
                    let pre = checks.shift();
                    this.tableObj.checkRow(this.identifyRecord(pre), false);
                }
            }

            this.spread(checks);
        }
        else {
            this.spread(totalChecked == 0 ? [] : this.tableObj.extractCheckedRecords());
        }
    }

    /**
     * @param {Array<WeightedAccountDetail>} accounts
     */
    spread(accounts) {
        this.trigger('selected-accounts-changed', accounts);
    }

    /**
     * @param {WeightedAccountDetail} record 
     * @param {HTMLInputElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousWeight 
     * @param {String} fieldName 
     */
    handleWeightChange(record, $ctr, $cell, previousWeight, fieldName) {
        
        let newWeight = +$ctr.value;

        if (isNaN(newWeight) || newWeight < 0 || newWeight > 999999) {
            $ctr.value = previousWeight;
        }
        else {

            let struct = {

                accountId: record.accountId,
                fundId: record.fundId,
                strategyId: record.strategyId,
                multiple: newWeight,
            };

            this.tableObj.updateRow(struct);

            if (this.tableObj.isRowChecked(this.identifyRecord(record))) {
                this.spread(this.tableObj.extractCheckedRecords());
            }
        }
    }

    /**
     * @param {WeightedAccountDetail} first 
     * @param {WeightedAccountDetail} second 
     * @param {*} fieldName 
     * @param {*} direction 
     */
    sortByWeight(first, second, fieldName, direction) {

        if (direction == 'ASC') {
            return first.multiple < second.multiple ? 1 : first.multiple > second.multiple ? -1 : 0;
        }
        else {
            return first.multiple < second.multiple ? -1 : first.multiple > second.multiple ? 1 : 0;
        }
    }

    /**
     * @param {AccountGroup} group 
     */
    hope2EditGroup(group) {
        this.openEditDialog(group);
    }

    hope2Refresh() {
        this.requestAccounts(() => { this.requestGroups(); });
    }

    hope2SaveGroup() {
        this.openEditDialog(this.isAllGroup ? undefined : this.theGroup);
    }

    hope2Config() {
        this.tableObj.showColumnConfigPanel();
    }

    /**
     * @param {AccountGroup} group 
     */
    openEditDialog(group) {

        if (this.dialogApp) {

            this.dialog.visible = true;

            if (group instanceof AccountGroup) {

                this.dialog.groupId = group.groupId;
                this.dialog.groupName = group.groupName;
            }
            else {

                this.dialog.groupId = null;
                this.dialog.groupName = null;
            }

            return;
        }

        this.dialog = {

            title: '保存账号组',
            visible: true,
            groupId: null,
            groupName: null,
        };

        if (group instanceof AccountGroup) {

            this.dialog.groupId = group.groupId;
            this.dialog.groupName = group.groupName;
        }

        this.dialogApp = new Vue({

            el: this.$container.querySelector('.xtcontainer > .module-edit-group'),
            data: {
                dialog: this.dialog,
            },
            computed: {
                showSaveAs: () => { return !this.isAllGroup; }
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.saveGroup,
                this.saveGroupAs,
                this.unsaveGroup,
            ]),
        });
    }

    /**
     * @returns {{ isOk: Boolean, message: String, result: Array<WeightedAccountDetail> }}
     */
    extractCheckeds() {
        
        /**
         * @returns {Array<WeightedAccountDetail>}
         */
        function extract() {
            return thisObj.tableObj.extractCheckedRecords();
        }

        var thisObj = this;
        var checkeds = extract();

        if (checkeds.length == 0) {
            return { isOk: false, message: '选择至少一个账号' };
        }

        var diffAssets = checkeds.map(item => item.assetType).distinct();
        if (diffAssets.length > 1) {
            return { isOk: false, message: '包含了>=2种资产类型的账号' };
        }
        
        return { isOk: true, message: null, result: checkeds };
    }

    saveGroup() {
        this.checkAndSave();
    }

    /**
     * 保存分组
     * @param {Boolean} isSavingAsNew 
     */
    async checkAndSave(isSavingAsNew) {

        if (this.helper.isNone(this.dialog.groupName)) {

            this.interaction.showError('请输入适当的分组名称');
            return;
        }

        var checking = this.extractCheckeds();
        if (!checking.isOk) {

            this.interaction.showError(checking.message);
            return;
        }

        var checkeds = checking.result;
        var is4Creation = isSavingAsNew || this.isAllGroup;
        var groupId = is4Creation ? null : this.dialog.groupId;
        var now = Date.now();
        var flatteds = checkeds.map(acnt => {

            let struc = {

                groupId: groupId,
                groupName: this.dialog.groupName,
                createUserId: this.userInfo.userId,
                createTime: now,

                orgId: this.userInfo.orgId,
                accountId: acnt.accountId,
                accountName: acnt.accountName,

                fundId: acnt.fundId,
                fundName: acnt.fundName,
                strategyId: null,
                strategyName: null,

                multiple: acnt.multiple,
            };

            if (this.helper.isNone(acnt.strategyId)) {

                delete struc.strategyId;
                delete struc.strategyName;
            }
            else {

                struc.strategyId = acnt.strategyId;
                struc.strategyName = acnt.strategyName;
            }

            return struc;
        });

        this.exitDialog();
        var resp = is4Creation ? await repoBasket.saveAccountGroup(flatteds)
                               : await repoBasket.updateAccountGroup(flatteds);

        if (resp.errorCode != 0) {

            this.interaction.showError(`账号分组保存失败：${resp.errorMsg}`);
            return;
        }

        if (is4Creation) {

            let reply = resp.data;
            let groups = AccountGroup.Convert(reply);
            let newGroup = groups[0];
            this.groups.push(newGroup);
            this.states.groupId = newGroup.groupId;
            this.handleGroupChange();
        }
        else {

            this.theGroup.members.clear();
            this.theGroup.members.merge(checkeds.map(account => AccountGroup.ConvertMember(account)));
        }
    }

    saveGroupAs() {
        this.checkAndSave(true);
    }

    unsaveGroup() {
        this.exitDialog();
    }

    exitDialog() {

        this.dialog.visible = false;
        this.dialog.groupId = null;
        this.dialog.groupName = null;
    }

    /**
     * @param {AccountGroup} group 
     */
    hope2DeleteGroup(group) {
        
        this.interaction.showConfirm({

            title: '操作确认',
            message: `是否确认删除分组 / ${group.groupName}？`,
            confirmed: () => { this.deleteGroup(group); },
        });
    }

    /**
     * @param {AccountGroup} group 
     */
    async deleteGroup(group) {
        
        var groupId = group.groupId;
        var resp = await repoBasket.deleteAccountGroup(groupId);

        if (resp.errorCode != 0) {

            this.interaction.showError(`分组删除失败：${resp.errorMsg}`);
            return;
        }

        /** 从列表中将该分组删除 */
        this.groups.remove(item => item instanceof AccountGroup && item.groupId == groupId);
        var isInSelected = groupId == this.states.groupId;
        if (isInSelected) {

            /**
             * 删除处于选中状态的分组后，模拟回到全部选项
             */
            
            this.states.groupId = this.allGroup.groupId;
            this.handleGroupChange();
        }
    }

    /**
     * 将分组内账号信息，写入账号表格
     */
    updateAccountByGroups() {

        /**
         * @returns {WeightedAccountDetail}
         */
        var seek = (key) => { return this.tableObj.getRowData(key); };

        this.groups.forEach(grp => {

            grp.members.forEach(memb => {

                let struct = {

                    accountId: memb.accountId,
                    fundId: memb.fundId,
                    strategyId: memb.strategyId,
                    multiple: memb.multiple,
                };

                let key = this.identifyRecord(struct);
                let matched = seek(key);

                /**
                 * 避免出现非预期的异常情况，导致被重复更新
                 */
                if (matched && matched.multiple == 0) {
                    this.tableObj.updateRow(struct);
                }
            });
        });
    }

    async requestGroups() {

        var resp = await repoBasket.getAccountGroup();
        if (resp.errorCode == 0) {

            let records = resp.data;
            let groups = AccountGroup.Convert(records);
            this.groups.clear();
            this.groups.push(this.allGroup);
            this.groups.merge(groups);
            this.updateAccountByGroups(); 
        }
        else {

            this.interaction.showError('获取账号资金详情发生异常：' + resp.errorMsg);
            this.groups.clear();
        }
    }

    async requestAccounts(callback) {
        
        /**
         * 重新载入时，重置到全部账号选项
         */
        if (this.isCustomGroup) {
            this.states.groupId = this.allGroup.groupId;
        }

        this.accounts.clear();
        var allType = 0;
        var assetType = this.channel ? this.channel.assetType : this.helper.isNotNone(this.options.fixAssetType) ? this.options.fixAssetType : allType;
        var resp = await repoAccount.batchGetAccountCash(assetType);
        
        if (resp.errorCode == 0) {

            let records = resp.data;
            let accounts = records instanceof Array ? records.map(x => new WeightedAccountDetail(x)) : [];

            if (this.channel && this.channel.options.isCredit) {
                accounts.remove(item => item instanceof WeightedAccountDetail && item.isCredit !== true);
            }

            if (this.insInfo) {

                let insInfo = this.insInfo;
                accounts.forEach(item => {

                    item.shortInstrument = insInfo.shortInstrument;
                    item.instrument = insInfo.instrument;
                    item.instrumentName = insInfo.instrumentName;
                });
            }

            this.accounts.merge(accounts);
            this.tableObj.refill(accounts);
        }
        else {

            this.interaction.showError('获取账号资金详情发生异常：' + resp.errorMsg);
            this.tableObj.clear();
        }

        this.states.total = this.accounts.length;
        typeof callback == 'function' && callback();
    }

    specializeUi() {

        if (this.isBasketAlgo) {

            this.$container.querySelector('.account-group').classList.add('single-account-mode');
            this.$container.classList.add('single-account-mode');
        }
    }

    build($container, options = { isBasketAlgo: false }) {

        super.build($container);
        this.$toolbar = $container.querySelector('.xtcontainer > .ag-toolbar');
        this.$table = $container.querySelector('.table-component');
        this.isBasketAlgo = this.states.isBasketAlgo = !!(options || {}).isBasketAlgo;
        this.specializeUi();
        this.createApp();
        this.createTable();
        this.requestAccounts(() => { this.requestGroups(); });
    }
}

module.exports = View;
