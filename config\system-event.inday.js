
const helper = require('../libs/helper').helper;
const systemIndayEvent = {
	/**
	 * 请求/答复toolbox窗口ID
	 */
	toolboxWinIdRequest: 0,
	toolboxWinIdAnswer: 0,
	/**
	 * 请求/答复最新可交易列表
	 */
	tradableListChange: 0,
	tradableListChangeNotify: 0,
	/**
	 * 可交易股票可买可卖数量变化
	 */
	stockVolumeChange: 0,
	stockVolumeChangeNotify: 0,
	/**
	 * 窗口上下文股票发生变化
	 */
	contextStockChange: 0,
	contextStockChangeNotify: 0,
	/**
	 * 由交易面板的下单股票发生变化引起
	 */
	tradingPanelStockChange: 0,
	tradingPanelStockChangeNotify: 0,
	/**
	 * 窗口上下文股票列表（券池）发生变化
	 */
	contextPoolChange: 0,
	contextPoolChangeNotify: 0,

	/**
	 * 信号提示 & 可交易列表，匹配到的合约交集
	 */
	signalChange: 0,
	signalHitStocks: 0,
	signalHitStocksNotify: 0,
	askTradableList: 0,
	askLastInstrument: 0,
	/**
	 * 快捷键设置
	 */
	shortcutUpdate: 0,
	//由快捷键设置的窗口通知主窗口去更新全局的快捷键
	shortcutUpdateNotify: 0,
	/**
	 * 行业 概念 自定义组合等通知给组合交易
	 */
	poolTradingChange: 0,
	poolTradingChangeNotify: 0,

	/**
	 * 当自定义组合被更改时候 通知自定义组合列表 完成更改
	 */
	savePoolModify: 0,
	savePoolModifyNotify: 0,
	savePoolModifyReceipt: 0,
	savePoolModifyReceiptNotify: 0,
	tradableListChangeReceived: 0,
	/**
	 * 主动去刷新可交易列表
	 */
	refreshTradableList: 0
};

for (let event_name in systemIndayEvent) {
	systemIndayEvent[event_name] = 'se-t0-' + helper.convertCamel2Hyphen(event_name);
}

module.exports = { systemIndayEvent };
