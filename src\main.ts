import './assets/style/main.css';
import 'virtual:uno.css';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

import App from './App.vue';
import router from './router';
import { getUser, setUser, try2ConfigServer } from './script';
import { LoginService } from './api';
import { getMacAddress, getOsInfo } from '../shared/servers';

import { GlobalState, LogLevel } from '../../xtrade-sdk';
// import PageOperations, { makeEnums } from './enum/page-operation';

// 设置日志级别
const loggers = GlobalState.GetLoggers();
loggers.forEach(item => item.setLogLevel(LogLevel.DEBUG));
const defaultLogger = GlobalState.GetLogger();

// 检查是否已经登录（本地有缓存的用户信息，即认为是登录状态）
async function checkLogin() {
  const localUser = getUser();
  if (!localUser) {
    return;
  }

  const Mac = getMacAddress();
  const Os = getOsInfo();
  return await LoginService.login(localUser.username, localUser.password, Mac, Os, false);
}

function assemblePage() {
  const app = createApp(App);
  app.use(ElementPlus, { locale: zhCn });
  app.use(createPinia());
  app.use(router);
  app.mount('#app');
}

async function setup() {
  await try2ConfigServer();
  const resp = await checkLogin();
  const { trade, quote } = resp || {};
  const asok = trade && trade.errorCode == 0;
  defaultLogger.info('login check', { trade, quote, asok });

  if (asok) {
    // 静默登录成功，更新设置本地的用户信息
    setUser(GlobalState.GetUserInfo()!);
  } else {
    // 静默登录失败，清空用户信息，后续页面跳转到登录页
    setUser();
  }

  assemblePage();
}

setup();
