<div class="yuliang-root s-full-size" :class="states.opened ? '' : 'wider'">
    <div class="left-fixed s-full-height">
        <div class="account-title s-center">可用股票账号</div>
        <div class="accounts-box s-full-size">
            <div v-for="(item, item_dix) in accounts" :key="item_dix" class="account-item">
                <span class="text s-full-size s-ellipsis">{{ item.accountName }}</span>
                <el-button type="primary" size="mini" class="btn" @click="open(item)">打开</el-button>
            </div>
        </div>
    </div>
    <div class="right s-full-size">
        <el-button v-if="states.opened" type="primary" size="mini" class="toggle-btn" @click="toggle">
            <i class="el-icon-arrow-left"></i>
            <span>隐藏账号列表</span>
        </el-button>
        <el-button v-else type="primary" size="mini" class="toggle-btn" @click="toggle">
            <span>展开账号列表</span>
            <i class="el-icon-arrow-right"></i>
        </el-button>
        <div class="tabs-box">
            <!-- tabs -->
        </div>
        <div class="pages-box">
            <!-- pages -->
        </div>
    </div>
</div>