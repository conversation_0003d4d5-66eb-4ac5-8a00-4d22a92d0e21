<div class="template-root">
    <!-- 产品和各交易账户总览 -->
    <div class="top-part">
        <template>
            <el-popover title="数据导出" placement="bottom" width="240" trigger="click" :visible-arrow="false" v-model="states.exporting.visible">
                <div slot="reference" class="export-pop-holder" style="position: absolute; right: 30px; color: transparent;">.</div>
                <div class="export-options s-pd-10">
                    <label>导出数据</label>
                    <el-select style="width: 100px;" class="s-mgl-10" v-model="states.exporting.which">
                        <el-option v-for="(item, item_idx) in whiches" :key="item_idx" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-button type="primary" size="small" class="s-mgl-10" :disable="!states.exporting.which" @click="exportTable">导出</el-button>
                </div>
            </el-popover>
            <div v-for="(sta_item, sta_idx) in statistics" :key="sta_idx" class="each-block">
                <div class="block-title bigger-font">{{ sta_item.title }}</div>
                <div class="detail-list">
                    <div v-for="(dt_item, dt_idx) in filterVisible(sta_item.details)" :key="dt_idx" class="data-item">
                        <div v-if="dt_item.isProductExposure" class="item-label">
                            <span>{{ dt_item.label }}</span>
                            <el-select class="s-mgl-10" style="width: 60px;" v-model="states.future" @change="handleFutureChange">
                                <el-option v-for="item in futures" :key="item" :label="item" :value="item"></el-option>
                            </el-select>
                            <span class="s-pdl-5">规模</span>
                        </div>
                        <div v-else class="item-label">{{ dt_item.label }}</div>
                        <div v-if="dt_item.formatter" v-html="dt_item.formatter(dt_item.value, dt_item.unit, dt_item, sta_item.details)" class="item-value"></div>
                        <div v-else class="item-value">{{ dt_item.value }}{{ dt_item.unit }}</div>
                    </div>
                </div>
            </div>
        </template>
    </div>
    <!-- 主要表格区域 -->
    <div class="main-part">
        <!-- 股票 -->
        <div class="left-area">
            <div class="area-title area-title-stock">
                <template>
                    <div class="title-text bigger-font">股票交易</div>
                    <div class="control-label">资产占比：</div>
                    <div class="control-read">{{ formatPct(states.percent) }}</div>
                </template>
            </div>
            <div class="data-tables">
                <div class="table-caption">账号概览</div>
                <div class="table-account">
                    <table>
                        <tr>
                            <th label="资金账号" prop="资金账号" min-width="80" sortable overflowt></th>
                            <th label="总资产" prop="总资产" min-width="100" align="right" thousands sortable overflowt></th>
                            <th label="可用资金" prop="可用资金" min-width="100" align="right" thousands sortable overflowt></th>
                            <th label="持仓市值" prop="持仓市值" min-width="100" align="right" thousands sortable overflowt></th>
                            <th label="持仓盈亏" prop="持仓盈亏" min-width="100" class-maker="makeBenefitClass" align="right" thousands sortable overflowt></th>
                            <th label="当日盈亏" prop="当日盈亏" min-width="100" class-maker="makeBenefitClass" align="right" thousands sortable overflowt></th>
                        </tr>
                    </table>
                </div>
                <div class="table-caption table-strategy-caption">策略概览</div>
                <div class="table-strategy">
                    <table>
                        <tr>
                            <th label="策略" prop="策略" width="100" sortable overflowt></th>
                            <th label="股票资产" prop="股票资产" width="100" align="right" thousands sortable overflowt></th>
                            <th label="可用资金" prop="可用资金" width="100" align="right" thousands sortable overflowt></th>
                            <th label="持仓市值" prop="持仓市值" width="100" align="right" thousands sortable overflowt></th>
                            <th label="持仓盈亏" prop="持仓盈亏" width="100" class-maker="makeBenefitClass" align="right" thousands sortable overflowt></th>
                            <th label="当日盈亏" prop="当日盈亏" width="100" class-maker="makeBenefitClass" align="right" thousands sortable overflowt></th>
                            <th label="资金账号" prop="资金账号" width="100" sortable overflowt></th>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <!-- 期货 -->
        <div class="right-area">
            <div class="area-title area-title-future">
                <template>
                    <div class="title-text bigger-font">期货交易</div>
                    <div class="control-label">资产占比：</div>
                    <div class="control-read">{{ formatPct(states.percent) }}</div>
                </template>
            </div>
            <div class="data-tables">
                <div class="table-caption">对冲提示</div>
                <div class="table-hedge">
                    <table>
                        <tr>
                            <th label="品种" prop="品种" width="100" sortable overflowt></th>
                            <th label="持仓净卖空数" prop="持仓净卖空数" width="100" thousands-int sortable overflowt></th>
                            <th label="实时应完成净卖空数" prop="实时应完成净卖空数" width="100" thousands-int sortable overflowt></th>
                        </tr>
                    </table>
                </div>
                <div class="table-caption">交易执行</div>
                <div class="table-exec">
                    <table>
                        <tr>
                            <th label="品种" prop="品种" width="80" sortable overflowt></th>
                            <th label="净卖开" prop="净卖开" width="70" align="right" thousands-int sortable overflowt></th>
                            <th label="卖新开" prop="卖新开" width="70" align="right" thousands-int sortable overflowt></th>
                            <th label="买平仓" prop="买平仓" width="70" align="right" thousands-int sortable overflowt></th>
                            <th label="买新开" prop="买新开" width="70" align="right" thousands-int sortable overflowt></th>
                            <th label="卖平仓" prop="卖平仓" width="70" align="right" thousands-int sortable overflowt></th>
                            <th label="股票净买入市值" prop="股票净买入市值" width="130" align="right" header-class="toowide-cell" thousands sortable overflowt></th>
                            <th label="股指净开空市值" prop="股指净开空市值" width="130" align="right" header-class="toowide-cell" thousands sortable overflowt></th>
                            <th label="市值敞口" prop="市值敞口" width="130" class-maker="makeBenefitClass" align="right" thousands sortable overflowt></th>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
