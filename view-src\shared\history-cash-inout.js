

const { BaseList } = require('./baselist');
const repoTrading = require('../../repository/trading').repoTrading;
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const NumberMixin = require('../../mixin/number').NumberMixin;

class View extends BaseList {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '出入金');

        this.dateFormat = 'yyyyMMdd';
        this.condition = {

            date: null,
            isShowCreating: false,
            happenDate: null,
            cashIn: null,
            cashOut: null,
        };

        this.paging = {

            pageSizes: this.systemSetting.tablePagination.pageSizes,
            pageSize: this.systemSetting.tablePagination.pageSize,
            layout: this.systemSetting.tablePagination.layout,
            total: 0,
            page: 0,
        };
    }

    handleContextChanged() {
        this.turn2Request();
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    search() {
        this.turn2Request();
    }

    handleRefresh() {

        this.paging.page = 1;
        this.turn2Request();
    }

    async turn2Request() {

        if (!this.context) {

            this.paging.total = 0;
            this.paging.page = 0;
            this.tableObj.refill([]);
            return;
        }

        var $loading = this.interaction.showLoading({ text: `正在请求${this.title}...` });
        try {

            $loading.close();
            var inouts = await this.requestHistoryInouts();
            this.paging.total = inouts.length;
            this.tableObj.refill(inouts);
        } 
        catch (ex) {

            $loading.close();
            console.error(ex);
            this.interaction.showError(ex.message);
        }
    }

    /**
     * @returns {Array}
     */
    async requestHistoryInouts() {

        let date_obj = this.condition.date;
        let date_str = date_obj instanceof Date ? date_obj.format(this.dateFormat) : '';
        let resp = await repoTrading.getIoMoney(this.context.identityId, date_str);
        if (resp.errorCode !== 0) {
            throw new Error(`历史出入金查询错误，返回代码 = ${ resp.errorCode }/${ resp.errorMsg }`);
        }

        return resp.data;
    }

    toggleAddIORecord() {
        this.condition.isShowCreating = !this.condition.isShowCreating;
    }

    async addIORecord() {

        const condition = this.condition;
        const todayStr = new Date().format(this.dateFormat);
        let { happenDate, cashIn, cashOut } = this.condition;

        if (typeof cashIn != 'number') {
            cashIn = 0;
        }

        if (typeof cashOut != 'number') {
            cashOut = 0;
        }

        if (!(happenDate instanceof Date)) {
            this.interaction.showError('请指定有效的交易日');
            return;
        }
        else if (happenDate.format(this.dateFormat) > todayStr) {
            this.interaction.showError('请勿指定未来的交易日');
            return;
        }
        else if (cashIn < 0) {
            this.interaction.showError('入金无效');
            return;
        }
        else if (cashOut < 0) {
            this.interaction.showError('出金无效');
            return;
        }
        else if (cashIn < 0.01 && cashOut < 0.01) {
            this.interaction.showError('出金或入金，至少需要为0.01');
            return;
        }

        var resp = await repoTrading.insertIoMoney(this.context.identityId, {

            in_money: cashIn, 
            out_money: cashOut,
            trading_day: happenDate.format(this.dateFormat),
            type: 0,
        });


        var { errorCode, errorMsg } = resp;
        if (errorCode == 0) {

            this.interaction.showSuccess('出入金记录已提交');
            setTimeout(() => {
                this.turn2Request();
                this.resetStatus();
                this.renderProcess.emit('deposite-withdraw-on-account', this.helper.deepClone(this.context));
            }, 200);
        }
        else {
            this.interaction.showError(`出入金添加错误：${errorCode}/${errorMsg}`);
        }
    }

    formatMoneyType(row_data) {
        return row_data.type == 1 ? '分红入金' : '---';
    }

    resetStatus() {

        const condition = this.condition;
        condition.isShowCreating = false;
        condition.cashIn = null;
        condition.cashOut = null;
    }

    createToolbarApp() {

        this.toolbarApp = new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                condition: this.condition,
                paging: this.paging,
            },

            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [this.search, this.toggleAddIORecord, this.addIORecord, this.handlePageSizeChange, this.handlePageChange]),
        });
    }

    createTableComponent() {
        
        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.table-control');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-hcio',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        return tableObj;
    }

    handleTableFiltered(filtered_count) {
        this.paging.total = filtered_count;
    }

    build($container) {
        super.build($container);
    }
}

module.exports = View;