/**
 * 散股
 */
class SanguItemInfo {
    
    constructor(values) {

        /** 账号ID */
        this.accountId = values[0];
        /** 合约代码 */
        this.instrument = values[1];
        /** 合约名称 */
        this.instrumentName = null;
        /** 最新价格 */
        this.price = 0;
        /** 持仓量 */
        this.totalPosition = values[2];
        /** 散股数量 */
        this.sanguPosition = values[3];
        /** 处理方式 */
        this.operateWay = values[4];
        /** 账号名称 */
        this.accountName = values[5];
    }
}

/**
 * 散股处理方式
 */
const SanguOperationWays = { adjustPos: '调仓', closePos: '平仓' };

module.exports = {

    SanguItemInfo,
    SanguOperationWays,
};