const { IView } = require('../../../component/iview');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { NumberMixin } = require('../../../mixin/number');
const { BizHelper } = require('../../../libs/helper-biz');
const { T0TaskInfo, T0PositionInfo } = require('../../../model/t0');
const { AlgorithmInfo, AlgorithmGroup } = require('../../../model/algorithm-info');
const { Position } = require('../../../model/position');
const { AccountDetail } = require('../../../model/account');
const { repoAccount } = require('../../../repository/account');
const { repoPosition } = require('../../../repository/position');
const { repoAlgo, AlgorithmClasses } = require('../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '创建日内回转');
        this.algoGrps = [new AlgorithmGroup()].splice(1);
        this.algoes = [new AlgorithmInfo({})].splice(1);
        this.pools = [new T0PositionInfo({})].splice(1);
    }

    get productId() {
        
        let matched = this.getContextAccount();
        return matched ? matched.fundId : null;
    }

    get strategyId() {

        let matched = this.getContextAccount();
        return matched ? matched.strategyId : null;
    }

    get accountId() {

        let matched = this.getContextAccount();
        return matched ? matched.accountId : null;
    }

    /**
     * 选择的仓位，合约代码，列表
     */
    get choices() {
        return this.crdata.choices;
    }

    getContextAccount() {
        return this.accounts.find(x => this.getProperAccountId(x) == this.crdata.recordId);
    }

    /**
     * @param {Array<T0PositionInfo>} records 
     */
    typeds(records) {
        return records;
    }

    /**
     * @param {T0TaskInfo} task 
     */
    async transfer(task) {

        const ref = this.crdata;
        const xtr = task.xtrade;
        const alg = task.algop;

        // 账号、算法、任务名称、创建或编辑

        // 需要根据account id反推出匹配的账号条目
        ref.recordId = null;
        ref.algoId = xtr.algo_id;
        ref.task_name = task.task_name;
        ref.isEditing = this.helper.isNotNone(ref.algoId);
        ref.title = ref.task_name ? `创建日内回转：${ref.task_name}` : '创建日内回转';

        // 算法参数

        ref.money = alg.money;
        ref.lose = alg.lose;
        ref.time = alg.time;
        ref.end_date = alg.end_date;

        /**
         * 同步仓位、可用资金数据
         */
        await this.syncPosition();

        // 要交易的合约

        ref.choices = task.entrusts.map(x => x.stock_code);
        this.tchoice.refill(task.entrusts.map(x => {

            let matched = this.matchPosition(x.stock_code);
            return new T0PositionInfo({

                stock_code: x.stock_code,
                stock_name: x.stock_name,
                // 根据账号持仓信息设置
                totalPosition: matched ? matched.totalPosition : 0,
                // 根据账号持仓信息设置
                closablePosition: matched ? matched.closablePosition : 0,
                targetPosition: x.target_volume,
            });
        }));

        ref.visible = true;
        setTimeout(() => { this.tchoice.fitColumnWidth(); }, 500);
    }

    matchPosition(stock_code) {
        return this.pools.find(x => x.stock_code == stock_code);
    }

    /**
     * @param {T0PositionInfo} position 
     */
    formStockDisplay(position) {
        return `${position.stock_code} / ${position.stock_name} / ${position.closablePosition}`;
    }

    reset() {

        this.pools.clear();
        this.tchoice.clear();
    }

    async syncPosition() {

        if (!this.isAccountChoosed()) {
            return this.reset();
        }

        try { await this.requestPositions(); } catch(ex) { console.error(ex); }
        try { await this.requestAccountDetail(); } catch(ex) { console.error(ex); }
    }

    handleAccountChange() {
        
        this.requestAlgoes();
        this.syncPosition();
    }

    handleAlgoChange() {
        //
    }

    handleChoiceChange() {
        this.syncChoice2Table();
    }

    syncChoice2Table() {
        this.tchoice.refill(this.pools.filter(x => this.choices.indexOf(x.stock_code) >= 0));
    }

    handleSelect(selected) {

        const icode = selected.instrument;
        const iname = selected.instrumentName;
        const ref = this.crdata;
        ref.keywords = `${iname}-${icode}`;

        let matched = this.matchPosition(icode);
        let position = new T0PositionInfo({

            stock_code: icode,
            stock_name: iname,
            totalPosition: matched ? matched.totalPosition : 0,
            closablePosition: matched ? matched.closablePosition : 0,
            targetPosition: matched ? matched.closablePosition : 0,
        });

        if (!this.pools.some(x => x.stock_code == icode)) {
            return this.interaction.showError(`${icode} / ${iname}，无可用持仓`);
        }
        else if (this.choices.indexOf(icode) >= 0) {
            return this.interaction.showMessage(`${icode} / ${iname}，已在列表中`);
        }
        else {
            
            this.choices.push(icode);
            this.tchoice.putRow(position);
        }
    }

    /**
     * @param {String} keywords 
     * @param {Function} callback 
     */
    suggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {

            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleUserInput() {

        const ref = this.crdata;
        if (event.keyCode == 8) {

            event.returnValue = false;
            ref.keywords = null;
            this.handleClearIns();
        }
        else if (typeof ref.keywords == 'string' && ref.keywords.trim().length == 0) {
            this.handleClearIns();
        }
    }

    handleClearIns() {

        var ref = this.crdata;
        ref.keywords = null;
    }

    importStocks() {

        let is_ok = true;

        if (!this.isAccountChoosed()) {

            is_ok = false;
            this.interaction.showError('请选择一个账号，导入的股票，需要做持仓匹配');
        }
        else if (!this.hasAnyPositions()) {

            is_ok = false;
            this.interaction.showError(`账号无持仓数据`);
        }

        this.crdata.importing.visible = is_ok;
    }

    extractStocks() {

        let content = this.crdata.importing.content;
        if (typeof content != 'string' || content.trim().length == 0) {
            return;
        }

        let keywords = content.replace(/\r|\n|\t|[ ]/g, ',').split(',').map(x => x.trim()).filter(x => x.length > 0);
        if (keywords.length == 0) {
            return;
        }

        let stocks = [{ instrument: null }].splice(1);
        keywords = keywords.distinct();
        keywords.forEach(each => {

            let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, each);
            let matches2 = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, each, true);
            let results = matches.length == 1 ? matches : matches2.length == 1 ? matches2 : [];
            if (results.length == 1) {
                stocks.push(results[0]);
            }
        });

        if (stocks.length == 0) {
            return this.interaction.showError('未提取到有效合约');
        }

        let positions = this.pools.filter(x => stocks.some(y => y.instrument == x.stock_code));
        if (positions.length == 0) {
            return this.interaction.showError(`提取数量 = ${stocks.length}，无相应持仓`);
        }

        let news = positions.filter(x => this.choices.indexOf(x.stock_code) < 0);
        if (news.length == 0) {
            return this.interaction.showError(`匹配的持仓合约，均已加入`);
        }

        this.hideImport();
        this.choices.push(...news.map(x => x.stock_code));
        this.syncChoice2Table();
        this.interaction.showSuccess(`提取数量 = ${stocks.length}，加入数量 = ${news.length}`);
    }

    hideImport() {

        const ref = this.crdata.importing;
        ref.visible = false;
        ref.content = null;
    }

    isValidated() {
        
        var ref = this.crdata;

        if (this.helper.isNone(ref.recordId)) {
            return '账号未选择';
        }
        else if (this.algoGrps.length == 0) {
            return '算法列表未加载';
        }
        else if (this.helper.isNone(ref.algoId)) {
            return '算法未选择';
        }
        else if (this.helper.isNone(ref.task_name)) {
            return '算法名称未输入';
        }
        else if (typeof ref.money != 'number' || ref.money < 0) {
            return '算法分配资金，无效';
        }
        else if (ref.money > ref.totalCanUse) {
            return '算法分配资金，大于总可用' + ref.totalCanUse;
        }
        // else if (typeof ref.lose != 'number' || ref.lose < 0) {
        //     return '单日止损金额，无效';
        // }
        // else if (!ref.time) {
        //     return '请选择平仓时间';
        // }
        else if (!ref.end_date) {
            return '请选择策略有效期';
        }
        
        let positions = this.typeds(this.tchoice.extractAllRecords());
        if (positions.length == 0) {
            return '未选择交易合约';
        }

        return true;
    }

    collect() {
        
        let dirs = this.systemTrdEnum.tradingDirection;
        let ref = this.crdata;
        let algo_param = JSON.stringify({ money: ref.money, lose: ref.lose, time: ref.time ? ref.time.getTime() : null });

        return this.typeds(this.tchoice.extractAllRecords()).map(item => {

            return {

                direction: dirs.sell.code,
                identityId: this.strategyId || this.productId,
                accountId: this.accountId,
                algorithmMappingId: ref.algoId,
                instrument: item.stock_code,
                volume: item.targetPosition,
                effectiveTime: 0,
                expireTime: new Date(ref.end_date.format('yyyy-MM-dd') + ' 23:59:59').getTime(),
                limitAction: 0,
                afterAction: 0,
                userId: this.userInfo.userId,
                algoParam: algo_param,
                taskName: ref.task_name,
            };
        });
    }

    confirm() {

        var result = this.isValidated();
        if (typeof result == 'string') {
            return this.interaction.showError(result);
        }

        let tasks = this.collect();
        this.send(tasks);
    }

    async send(tasks) {

        let resp = await repoAlgo.order(tasks, 2);
        let { errorCode, errorMsg, data } = resp;
        
        // resp sample = {
        //     "data": {
        //         "accountId_strategyId_yuliang": {
        //             "data": "YL-03",
        //             "errorCode": 2006,
        //             "errorMsg": "冻结策略资金失败"
        //         }
        //     },
        //     "errorCode": 0,
        //     "errorMsg": "success"
        // };

        if (errorCode == 0) {

            let map = data || {};
            let errors = [{ code: -1, msg: null }].splice(1);

            for (let key in map) {

                let item = map[key];
                let code = item.errorCode;
                let msg = item.errorMsg;
                if (code != 0) {
                    errors.push({ code, msg });
                }
            }

            if (errors.length == 0) {

                this.interaction.showSuccess('母单任务已保存');
                this.crdata.visible = false;
                this.trigger('save-success');
            }
            else {
                this.interaction.showAlert(`任务创建失败：${errors[0].code}/${errors[0].msg}`);
            }
        }
        else {
            this.interaction.showError(`母单保存失败：${errorCode}/${errorMsg}`);
        }
    }

    giveup() {

        this.reset();
        this.crdata.visible = false;
    }

    setupCreation() {

        this.accounts = [new AccountDetail({})].splice(1);
        this.crdata = {

            visible: true,
            isEditing: false,
            title: '编辑日内回转',
            
            accounts: this.accounts,            
            pools: this.pools,
            algoGrps: this.algoGrps,

            recordId: null,
            algoId: null,
            choices: [],
            task_name: null,
            importing: { visible: false, content: '' },
            
            totalCanUse: 0,
            money: 0,
            lose: 0,
            time: new Date(),
            end_date: new Date(),
            keywords: null,
        };

        this.vcreation = new Vue({

            el: this.$container.firstElementChild,
            data: this.crdata,
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleAccountChange,
                this.handleAlgoChange,
                this.handleChoiceChange,

                this.formStockDisplay,
                this.isAccountChoosed,
                this.takeAll,
                this.displayPercentInfo,
                
                this.suggest,
                this.handleUserInput, 
                this.handleSelect,
                this.handleClearIns,

                this.importStocks,
                this.extractStocks,
                this.hideImport,
                this.confirm,
                this.giveup,
                this.formatSelectAccountName,
                this.getProperAccountId,
            ]),
        });

        this.vcreation.$nextTick(() => {
            this.setupTable();
        });
    }

    /**
     * @param {AccountDetail} account 
     */
    getProperAccountId(account) {
        return this.helperUi.getProperAccountId(account);
    }

    setupTable() {

        let $table = this.vcreation.$el.querySelector('.stocks-list');
        this.helper.extend(this, ColumnCommonFunc);
        let tchoice = new SmartTable($table, this.identify, this, { tableName: 'smt-hst2', displayName: '票池', headerHeight: 32, rowHeight: 32 });
        tchoice.setMaxHeight(999);
        this.tchoice = tchoice;
    }

    /**
     * @param {T0PositionInfo} record 
     */
    identify(record) {
        return record.stock_code;
    }

    /**
     * @param {T0PositionInfo} record 
     */
    formatCodeName(record) {
        return `${record.stock_code} / ${record.stock_name}`;
    }

    /**
     * @param {T0PositionInfo} record 
     */
    formatTarget(record) {
        return `<div class="el-input">
                    <input event.onchange="handleTargetChange" value="${record.targetPosition}" type="text" autocomplete="off" class="el-input__inner" />
                </div>`;
    }

    formatActions(record) {
        return '<button class="danger" event.onclick="remove">删除</button>';
    }

    /**
     * @param {T0PositionInfo} record 
     * @param {HTMLInputElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousVal 
     * @param {String} fieldName 
     */
    handleTargetChange(record, $ctr, $cell, previousVal, fieldName) {
        
        let target = parseInt($ctr.value);
        let is_ok = typeof target == 'number' && target >= 0 && target <= record.closablePosition;
        $ctr.value = record.targetPosition = is_ok ? target : record.closablePosition;
    }

    /**
     * @param {T0PositionInfo} record 
     */
    remove(record) {

        this.tchoice.deleteRow(record.stock_code);
        let index = this.choices.indexOf(record.stock_code);
        index >= 0 && this.choices.splice(index, 1);
    }

    async requestAccounts() {

        var resp = await repoAccount.batchGetAccountCash();
        var { errorCode, errorMsg, data } = resp;
        
        if (errorCode == 0) {

            let accounts = data instanceof Array ? data.map(x => new AccountDetail(x)) : [];
            this.accounts.refill(accounts);
        }
        else {
            this.interaction.showError('账号获取异常：' + errorMsg);
        }
    }

    async requestAlgoes() {

        var account = this.accounts.find(x => this.getProperAccountId(x) == this.crdata.recordId);
        if (!account) {

            this.algoGrps.clear();
            this.algoes.clear();
            return;
        }

        var resp = await repoAlgo.queryAlgoes(AlgorithmClasses.t0, account.accountId);
        var { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {
            return this.interaction.showError(`算法加载失败：${errorCode}/${errorMsg}`);
        }

        var algoes = Array.isArray(data) ? data : [];
        var map = algoes.groupBy(x => x.supplierName);
        var flattends = [];
        this.algoGrps.clear();
        
        for (let supplier_name in map) {

            let subset = map[supplier_name];
            let members = (Array.isArray(subset) ? subset : []).map(x => new AlgorithmInfo(x));
            this.algoGrps.push(new AlgorithmGroup(supplier_name, members));
            flattends.merge(members);
        }

        this.algoes.refill(flattends);
    }

    isAccountChoosed() {
        return this.helper.isNotNone(this.crdata.recordId);
    }

    hasAnyPositions() {
        return this.pools.length > 0;
    }

    takeAll() {

        const ref = this.crdata;
        ref.money = ref.totalCanUse || 0;
    }

    displayPercentInfo() {

        let { money, totalCanUse } = this.crdata;
        return totalCanUse > 0 && money > 0 ? (Math.min(money / totalCanUse, 1) * 100).toFixed(2) + '%' : '';
    }

    async requestAccountDetail() {
        
        let resp = await repoAccount.qdetail({

            userId: this.userInfo.userId,
            identity_id: this.strategyId || this.productId,
            account_id: this.accountId,
        });

        let { data, errorCode, errorMsg } = resp;
        if (errorCode == 0) {

            let detail_info = (data.list || [])[0] || {};
            let detail = new AccountDetail(detail_info);
            this.crdata.totalCanUse = detail.available || 0;
        }
        else {
            this.interaction.showError(`账号详情获取错误：${errorCode}/${errorMsg}`);
        }
    }

    async requestPositions() {

        if (!this.isAccountChoosed()) {
            return;
        }

        let params = {

            user_id: this.userInfo.userId,
            fund_id: this.productId,
            strategy_id: this.strategyId,
            account_id: this.accountId,
        };

        let resp = await repoPosition.query(params);
        let { data, errorCode, errorMsg } = resp;

        if (errorCode != 0) {
            return this.interaction.showError(`仓位查询错误：${errorCode}/${errorMsg}`);
        }

        let positions = data.map(x => new Position(x)).map(x => new T0PositionInfo({

            stock_code: x.instrument,
            stock_name: x.instrumentName,
            totalPosition: x.totalPosition,
            closablePosition: x.closableVolume,
            // todo23
            targetPosition: x.closableVolume,
        }));

        this.pools.refill(positions);
    }

    build($container) {

        super.build($container);
        this.setupCreation();
        this.requestAccounts();
        this.requestAlgoes();
    }
}

module.exports = View;
