<div class="typical-data-view">

	<div class="user-toolbar themed-box">
		<!---->
	</div>

	<div class="data-list">
		<table>
			<tr>
				<!-- <th label="ID" min-width="80" prop="id" overflowt sortable searchable></th> -->
				<th label="账号ID" min-width="80" prop="accountId" overflowt sortable searchable></th>
				<th label="账号名称" min-width="202.0" prop="accountName" formatter="formatAccountName" overflowt sortable searchable></th>
				<th label="产品ID" min-width="80" prop="fundId" overflowt sortable searchable></th>
				<th label="产品名称" min-width="80" prop="fundName" overflowt sortable searchable></th>
				<th label="订单ID" min-width="80" prop="orderId" overflowt sortable searchable></th>
				<th label="代码" min-width="100" prop="instrument" overflowt sortable searchable></th>
				<th label="名称" min-width="80" prop="instrumentName" overflowt sortable searchable></th>
				<th label="交易日" min-width="100" prop="occurDate" sortable></th>
				<th type="program" label="类型" min-width="90" prop="compactType" formatter="formatCompactType" sortable overflowt></th>
				<th label="证券合约数量" min-width="90" prop="openVolume" align="right" summarizable thousands-int></th>
				<th label="证券合约金额" min-width="90" prop="openAmount" align="right" summarizable thousands></th>
				<th label="未还合约数量" min-width="90" prop="volume" align="right" summarizable thousands-int></th>
				<th label="未还合约金额" min-width="90" prop="tradeAmount" align="right" summarizable thousands></th>
				<th label="到期时间" min-width="100" prop="expireDate" formatter="formatDate" sortable></th>

				<th fixed="right"
					label="状态" 
					min-width="70"
					prop="compactStatus"
					align="right" 
					formatter="formatCompactStatus"></th>
			</tr>
		</table>
	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>