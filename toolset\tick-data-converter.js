
const TickDataConverter = {

    ConvertStandardTick: function(matrix) {

        var arr_basic = matrix.slice(0, 10);
		var arr_sell_hands = matrix[10];
		var arr_sell_price = matrix[11];
		var arr_buy_hands = matrix[12];
		var arr_buy_price = matrix[13];

		return {

			time: arr_basic[0] * 1000,
			open: arr_basic[1],
			high: arr_basic[2],
			low: arr_basic[3],
			latest: arr_basic[4],
			preclose: arr_basic[5],
			volume: arr_basic[6],
			amount: arr_basic[7],
			ceiling: arr_basic[8],
			floor: arr_basic[9],
			sells: arr_sell_price.map((price, idx) => { return [price, arr_sell_hands[idx]]; }),
			buys: arr_buy_price.map((price, idx) => { return [price, arr_buy_hands[idx]]; }),
		};
    },

    ConvertSimpleTick: function(matrix) {
        //
    },

    ConvertTransaction: function(matrix) {

        return {
			time: matrix[0] * 1000,
			direction: matrix[1],
			price: matrix[2],
			volume: matrix[3] * 0.01
		};
    },

    ConvertKline: function(matrix) {

        var date_time = null;
        if(typeof matrix[0] == 'number') {
            date_time = matrix[0] * 1000;
        }
        else {
            var dt_str = matrix[0];
            if(dt_str.length == 8) {
                date_time = new Date(`${dt_str.substr(0,4)}-${dt_str.substr(4,2)}-${dt_str.substr(6,2)} 00:00:00`).getTime();
            }
            else {
                date_time = new Date(dt_str).getTime();
            }
        }
        
        return {

			time: date_time,
			open: matrix[1],
			high: matrix[2],
			low: matrix[3],
			close: matrix[4],
			// 成交量
			volume: matrix[5],
			// 成交金额
            amount: matrix[6],
            // 上一时间片收盘价
            preclose: matrix[7],
		};
    },
};
module.exports = { TickDataConverter };