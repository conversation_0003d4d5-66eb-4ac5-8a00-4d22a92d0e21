const { IView } = require('../../../../component/iview');
const { SmartTable } = require('../../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { NumberMixin } = require('../../../../mixin/number');
const { repoFund } = require('../../../../repository/fund');
const { repoStatistics } = require('../../../../repository/statistics');

class StatisticDataItem {

    constructor(visible, { label = '', prop = '', unit = '', value = 0, formatter = undefined }) {

        this.visible = !!visible;
        this.label = label;
        this.prop = prop;
        this.unit = unit;
        this.value = value;

        /** 敞口项，需要在后面添加一个种类选择 */
        this.isProductExposure = label == '敞口/';
        this.formatter = typeof formatter == 'function' ? formatter : undefined;
    }
}

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '概览');
    }

    prepare() {
        
        // 顶部APP数据字段

        this.futures = ['IC', 'IF', 'IH', 'IM'];
        this.tstates = { 
            future: this.futures[0],
            /** 用于（下方表格）数据导出的pop选项，非本部分组件的功能 */
            exporting: { visible: false, which: null },
        };

        this.statistics = [
            {
                title: '产品账户总览', 
                details: [

                    new StatisticDataItem(true, { label: '总收益', prop: '总收益', unit: '元', value: null, formatter: this.formatProductProfit.bind(this) }),
                    new StatisticDataItem(false, { label: 'BP', prop: 'BP', unit: 'BP', value: null, formatter: undefined }),
                    new StatisticDataItem(true, { label: '总资产', prop: '总资产', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '多空敞口', prop: '多空敞口', unit: '元', value: null, formatter: this.formatProductExposure.bind(this) }),
                    new StatisticDataItem(false, { label: '敞口比例', prop: '敞口比例', unit: '元', value: null, formatter: undefined }),
                    new StatisticDataItem(true, { label: '敞口/', prop: '敞口规模', unit: '%', value: null, formatter: this.formatPct.bind(this) }),
                ]
            },
            {
                title: '股票账户', 
                details: [

                    new StatisticDataItem(true, { label: '股票收益', prop: '股票收益', unit: '元', value: null, formatter: this.formatStockProfit.bind(this) }),
                    new StatisticDataItem(false, { label: 'BP', prop: '股票BP', unit: 'BP', value: null, formatter: undefined }),
                    new StatisticDataItem(true, { label: '股票资产', prop: '股票资产', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '可用资金', prop: '股票可用资金', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '持仓市值', prop: '股票市值', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '融资余额', prop: '融资余额', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '融券余额', prop: '融券余额', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                ]
            },
            {
                title: '期货账户', 
                details: [
                    
                    new StatisticDataItem(true, { label: '期货收益', prop: '期货收益', unit: '', value: null, formatter: this.formatFutureProfit.bind(this) }),
                    new StatisticDataItem(false, { label: 'BP', prop: '期货BP', unit: 'BP', value: null, formatter: undefined }),
                    new StatisticDataItem(true, { label: '期货资产', prop: '期货资产', unit: '', value: null, formatter: this.formatThousands }),
                    new StatisticDataItem(true, { label: '可用资金', prop: '期货可用资金', unit: '', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '持仓市值', prop: '期货市值', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '多/空市值', prop: '多头市值', unit: '元', value: null, formatter: this.formatLongShort.bind(this) }),
                    new StatisticDataItem(false, { label: '空头市值', prop: '空头市值', unit: '元', value: null, formatter: this.formatThousands.bind(this) }),
                    new StatisticDataItem(true, { label: '风险度', prop: '风险度', unit: '%', value: null, formatter: this.formatPct.bind(this) }),
                ]
            },
        ];

        this.whiches = {

            account: { label: '账号概览', value: 1 },
            strategy: { label: '策略概览', value: 2 },
            hedge: { label: '对冲提示', value: 3 },
            exec: { label: '交易执行', value: 4 },
        };

        /**
         * 
         */
        if (this.is4Strategy) {
            delete this.whiches.strategy;
        }

        // 下方左&右title区域APP数据字段

        this.mstates = {

            stock: { percent: null },
            future: { percent: null },
        };
    }

    createTopApp() {

        this.vtop = new Vue({

            el: this.$top,
            data: {
                futures: this.futures,
                states: this.tstates,
                statistics: this.statistics,
                whiches: this.whiches,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.filterVisible, 
                this.handleFutureChange, 
                this.exportTable,
            ]),
        });
    }

    createMLeftApp() {

        new Vue({

            el: this.$mleft,
            data: { 
                states: this.mstates.stock,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.formatPct, this.formatSelectAccountName]),
        });
    }

    createMRightApp() {

        new Vue({

            el: this.$mright,
            data: { 
                states: this.mstates.future,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.formatPct]),
        });
    }

    identity(record) {
        return record.no;
    }

    createTables() {
        
        this.helper.extend(this, ColumnCommonFunc);
        const ref = this.$container;
        let tables = {
            $account: ref.querySelector('.table-account'),
            $strategy_caption: ref.querySelector('.table-strategy-caption'),
            $strategy: ref.querySelector('.table-strategy'),
            $hedge: ref.querySelector('.table-hedge'),
            $exec: ref.querySelector('.table-exec'),
        };

        this.taccount = this.createTable(tables.$account, 'smt-jh23', '账号概览');
        this.thedge = this.createTable(tables.$hedge, 'smt-j92a', '对冲提示');
        this.texec = this.createTable(tables.$exec, 'smt-k274', '交易执行');

        if (this.is4Product) {
            this.tstrategy = this.createTable(tables.$strategy, 'smt-kv2u', '策略概览');
        }
        else {
            tables.$strategy_caption.remove();
            tables.$strategy.remove();
        }
    }

    /**
     * @param {HTMLTableElement} $table 
     */
    createTable($table, tableName, displayName, options = {}) {

        const tableObj = new SmartTable($table, this.identity, this, Object.assign({ tableName, displayName }, options));
        tableObj.setMaxHeight(200);
        return tableObj;
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatPct(value, unit, item, details) {
        return NumberMixin.methods.percentage(value);
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatThousands(value, unit, item, details) {
        return `${NumberMixin.methods.thousandsDecimal(value)} ${item.unit}`;
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatProductProfit(value, unit, item, details) {

        let first = details.find(x => x.label == '总收益');
        let second = details.find(x => x.label == 'BP');
        return this.formatx(first, second, 'BP');
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatProductExposure(value, unit, item, details) {

        let first = details.find(x => x.label == '多空敞口');
        let second = details.find(x => x.label == '敞口比例');
        return this.formatx(first, second, '%');
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatStockProfit(value, unit, item, details) {

        let first = details.find(x => x.label == '股票收益');
        let second = details.find(x => x.label == 'BP');
        return this.formatx(first, second, 'BP');
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatFutureProfit(value, unit, item, details) {

        let first = details.find(x => x.label == '期货收益');
        let second = details.find(x => x.label == 'BP');
        return this.formatx(first, second, 'BP');
    }

    /**
     * @param {StatisticDataItem} item 
     * @param {Array<StatisticDataItem>} details 
     */
    formatLongShort(value, unit, item, details) {

        let first = details.find(x => x.label == '多/空市值');
        let firstValue = first.value;
        let second = details.find(x => x.label == '空头市值');
        let secondValue = second.value;

        let class1 = firstValue > 0 ? 's-color-red' : '';
        let class2 = secondValue > 0 ? 's-color-green' : '';
        let display1 = NumberMixin.methods.thousandsDecimal(firstValue, 2);
        let display2 = NumberMixin.methods.thousandsDecimal(secondValue, 2);
        return `<span class="${class1}">${display1}</span> 元 / <span class="${class2}">${display2}</span> ${unit}`;
    }

    /**
     * @param {StatisticDataItem} first 
     * @param {StatisticDataItem} second 
     */
    formatx(first, second, unit) {

        if (first && second) {

            let class1 = NumberMixin.methods.classBenefit(first.value);
            let class2 = NumberMixin.methods.classBenefit(second.value);
            let display1 = NumberMixin.methods.thousandsInt(first.value);
            let display2 = NumberMixin.methods.fixed2(second.value);
            return `<span class="${class1}">${display1}</span> 元 / <span class="${class2}">${display2}</span> ${unit}`;
        }
        else {
            return '--';
        }
    }

    /**
     * @param {Array<StatisticDataItem>} details 
     */
    filterVisible(details) {
        return details.filter(x => x.visible);
    }

    handleFutureChange() {
        this.qsummary();
    }

    resizeTables() {

        this.taccount.fitColumnWidth();
        this.thedge.fitColumnWidth();
        this.texec.fitColumnWidth();
        this.tstrategy && this.tstrategy.fitColumnWidth();
    }

    handleProductChange(product_id) {

        this.resizeTables();

        if (this.productId == product_id) {
            return;
        }

        this.productId = product_id;
        this.requestAll();
    }

    isProductPresent() {
        return this.helper.isNotNone(this.productId);
    }

    keepUpdated() {

        this.updateJob = setInterval(async () => {
            
            if (this._isUpdateJobRunning) {
                return;
            }

            this._isUpdateJobRunning = true;
            await this.qsummary();
            await this.qaccounts();
            this.is4Product && await this.qstrategies();
            await this.qhedges();
            await this.qexecs();
            this._isUpdateJobRunning = false;

        }, 30 * 1000);
    }

    requestAll() {
        
        this.qsummary();
        this.qaccounts();
        this.qhedges();
        this.qexecs();

        if (this.is4Product) {
            this.qstrategies();
        }
    }

    async qsummary() {

        if (!this.isProductPresent()) {
            return;
        }

        let resp = await repoStatistics.qsummary(this.productId, this.tstates.future);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0 && this.helper.isJson(data)) {
            this.statistics.forEach(st => {
                st.details.forEach(dt => { dt.value = data[dt.prop]; });
            });
        }
        else {
            this.interaction.showError(`统计数据，获取失败：${errorCode}/${errorMsg}`);
        }
    }

    async qaccounts() {

        if (!this.isProductPresent()) {
            return;
        }

        let resp = await repoStatistics.qaccounts(this.productId);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            this.mstates.stock.percent = data['资产占比'];
            data.data.forEach((each, no) => { each.no = no; });
            this.taccount.refill(data.data);
        }
        else {
            this.interaction.showError(`账号概览，数据获取失败：${errorCode}/${errorMsg}`);
        }
    }

    async qstrategies() {

        if (!this.isProductPresent()) {
            return;
        }

        let resp = await repoStatistics.qstrategies(this.productId);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            data.forEach((each, no) => { each.no = no; });
            this.tstrategy.refill(data);
        }
        else {
            this.interaction.showError(`策略概览，数据获取失败：${errorCode}/${errorMsg}`);
        }
    }

    async qhedges() {

        if (!this.isProductPresent()) {
            return;
        }

        let resp = await repoStatistics.qhedges(this.productId);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            this.mstates.future.percent = data['资产占比'];
            data.data.forEach((each, no) => { each.no = no; });
            this.thedge.refill(data.data);
        }
        else {
            this.interaction.showError(`期货对冲，数据获取失败：${errorCode}/${errorMsg}`);
        }
    }

    async qexecs() {

        if (!this.isProductPresent()) {
            return;
        }

        let resp = await repoStatistics.qexecs(this.productId);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            data.forEach((each, no) => { each.no = no; });
            this.texec.refill(data);
        }
        else {
            this.interaction.showError(`期货交易执行，数据获取失败：${errorCode}/${errorMsg}`);
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新动作发执行');
        this.requestAll();
    }

    exportSome() {
        setTimeout(() => { this.vtop.$el.querySelector('.export-pop-holder').click(); }, 20);
    }

    exportTable() {

        const ref = this.whiches;
        const ts = new Date().format('yyyy-MM-dd-hhmmss');

        switch (this.tstates.exporting.which) {
            
            case ref.account.value: this.taccount.exportAllRecords(`${ref.account.label}-${ts}`); break;
            case ref.strategy.value: this.tstrategy.exportAllRecords(`${ref.strategy.label}-${ts}`); break;
            case ref.hedge.value: this.thedge.exportAllRecords(`${ref.hedge.label}-${ts}`); break;
            case ref.exec.value: this.texec.exportAllRecords(`${ref.exec.label}-${ts}`); break;
        }
    }

    handleResize(width, height) {
        this.resizeTables();
    }

    build($container, { is4Product, is4Strategy }) {

        super.build($container);
        this.is4Product = !!is4Product
        this.is4Strategy = !!is4Strategy;
        const ref = this.$container;
        this.$top = ref.querySelector('.top-part');
        this.$mleft = ref.querySelector('.area-title-stock');
        this.$mright = ref.querySelector('.area-title-future');

        this.registerEvent('set-context-identity', this.handleProductChange.bind(this));
        this.prepare();
        this.createTopApp();
        this.createMLeftApp();
        this.createMRightApp();
        this.createTables();
        this.keepUpdated();
        this.listen2WinResized(this.handleResize.bind(this));
    }
}

module.exports = View;