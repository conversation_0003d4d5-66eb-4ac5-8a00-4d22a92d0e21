const IView = require('../../../component/iview').IView;
const repoFund = require('../../../repository/fund').repoFund;
const echarts = require('echarts');

class View extends IView {

    constructor(view_name, stand_alone, { title = '净值走势' }) {

        super(view_name, stand_alone, title);
        this.registerEvent('setContextData', this.setContextData.bind(this));
    }

    setContextData(identityType, identityId, identityName) {
        this.requestNavs(identityType, identityId, identityName);
    }

    async requestNavs(identityType, identityId, identityName) {

        var resp = await repoFund.queryHistoryNav(identityType, identityId);

        if (resp.errorCode == 0) {

            var series = (resp.data || {}).series;
            this.drawChart(series instanceof Array ? series : []);
        }
        else {
            this.interaction.showError(`${identityName} > 净值数据访问错误 > ${resp.errorMsg}`);
        }
    }

    /**
     * 
     * @param {Array<{ tradingDay, nav, sumNav }>} series 
     */
    drawChart(series) {

        if (this.$component) {

            let options = this.options;
            options.xAxis.data.clear();
            options.xAxis.data.merge(series.map(item => item.tradingDay));
            options.series[0].data.clear();
            options.series[0].data.merge(series.map(item => item.nav));
            options.series[1].data.clear();
            options.series[1].data.merge(series.map(item => item.sumNav));
            this.$component.setOption(options);
            return;
        }

        var $chart = this.$container.querySelector('.chart-nav');
        var captions = ['净值', '累计净值'];
        var options = {

            title: {

                show: false,
                text: this.title,
            },

            tooltip: {
                trigger: 'axis',
            },

            legend: {

                data: captions,
                textStyle: { color: '#fff' },
            },

            xAxis: {
        
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    lineStyle: { color: '#fff' },
                },
                data: series.map(item => item.tradingDay),
            },

            yAxis: {
                
                type: 'value',
                scale: true,
                axisLine: {
                    lineStyle: { color: '#fff' },
                },
                splitLine: {
                    lineStyle: { type: 'dashed' },
                },
            },

            series: [

                {
                    name: captions[0],
                    type: 'line',
                    smooth: true,
                    showSymbol: false,
                    lineStyle: { color: 'orange', width: 1 },
                    data: series.map(item => item.nav),
                },

                {
                    name: captions[1],
                    type: 'line',
                    smooth: true,
                    showSymbol: false,
                    lineStyle: { color: 'red', width: 1 },
                    data: series.map(item => item.sumNav),
                }
            ],
        };

        // 设置图形选项
        this.$component = echarts.init($chart);
        this.$component.setOption(options);
        this.options = options;
    }

    dispose() {
        this.trigger('close');
    }

    build($container) {
        this.$container = $container;
    }
}

module.exports = View;
