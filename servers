module.exports = [
    {
        serverName: 'MOM测试环境',
        servers: {
            quoteRestfulServer: '**************5:8100',
            restServer: '**************5:8083',
            tradeServer: '**************5:9190'
        }
    },
    {
        serverName: 'host-08测试',
        servers: {
            quoteRestfulServer: '**************5:8100',
            restServer: '***************:8980',
            tradeServer: '***************:8991'
        }
    },
	{
		serverName: 'DEV-TEST',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '***************:8994',
			tradeServer: '***************:8993'
		}
	},
	{
		serverName: '实盘环境',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '************:28080',
			tradeServer: '************:9199'
		}
	},
	{
		serverName: 'YWR本地',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '***************:8080',
			tradeServer: '***************:9190'
		}
	},
	{
		serverName: 'BROKER升级',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '*************:8180',
			tradeServer: '*************:9190'
		}
	},
	{
		serverName: '新动力MOM平台深圳',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '47.106.247.36:8091',
			tradeServer: '47.106.247.36:9190'
		}
	},
	{
		serverName: '丰润深圳',
		servers: {
			quoteRestfulServer: '**************:8080',
			restServer: '**************:8091',
			tradeServer: '**************:9190'
		}
	},
	{
		serverName: 'newui升级克隆',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '**************:8091',
			tradeServer: '**************:9190'
		}
	},
	{
		serverName: '中金正式',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '**************:8091',
			tradeServer: '**************:9190'
		}
	},
	{
		serverName: 'YL_Localhost',
		servers: {
			quoteRestfulServer: '**************5:8100',
			restServer: '***************:8080',
			tradeServer: '***************:9190'
		}
	},
	{
        serverName: "XJH_本地测试",
        servers: {
            quoteRestfulServer: '**************5:8100',
            restServer: '***************:8994',
            tradeServer: '***************:9190'
        }
    }
];
