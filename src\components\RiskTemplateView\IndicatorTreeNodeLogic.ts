import { RiskStepControl, RiskStepControls } from '@/enum/riskc';
import type { RiskIndicator, RiskRule } from '../../../../xtrade-sdk/dist';
import { deepClone, formatDateTime } from '@/script';

export interface LeafNode {
  id: number;
  code: string;
  name: string;
  midCode: string;
  topCode: string;
  indicator: RiskIndicator;
}

export interface Level2Node {
  // 第二层节点，依然为分类，id仅为统计控件节点标识
  id: string;
  name: string;
  code: string;
  children: LeafNode[];
}

export interface Level1Node {
  // 第一层节点，为分类，id仅为统计控件节点标识
  id: string;
  name: string;
  code: string;
  children: Level2Node[];
}

/**
 * 构建风控指标树
 */
export function buildTree(indicators: RiskIndicator[]): Level1Node[] {
  const level_1st_map: { [levelCode: string]: Level1Node } = {};
  indicators.forEach(idc => {
    // check level 1st
    let level1 = level_1st_map[idc.firstLevelCode];
    if (level1 === undefined) {
      level1 = {
        id: idc.firstLevelCode,
        name: idc.firstLevelName,
        code: idc.firstLevelCode,
        children: [],
      };
      level_1st_map[idc.firstLevelCode] = level1;
    }

    // check level 2nd
    let level2 = level1.children.find(n => n.code === idc.secondLevelCode);
    if (level2 === undefined) {
      level2 = {
        id: idc.secondLevelCode,
        name: idc.secondLevelName,
        code: idc.secondLevelCode,
        children: [],
      };
      level1.children.push(level2);
    }

    // add indicator node
    level2.children.push({
      id: idc.id,
      name: idc.indicatorName,
      code: idc.secondLevelCode,
      midCode: idc.secondLevelCode,
      topCode: idc.firstLevelCode,
      indicator: idc,
    });
  });

  return Object.values(level_1st_map);
}

/**
 * 指标树节点搜索功能
 */
export function filterNode(value: string, data: any) {
  if (!value) return true;
  const { name } = data as Level1Node | Level2Node;
  const { indicatorName, firstLevelName, secondLevelName } = (data as LeafNode).indicator;
  const scopes = [name, indicatorName, firstLevelName, secondLevelName];
  const values = scopes.filter(x => typeof x === 'string');
  return values.some(val => val.includes(value));
}

/**
 * 当前节点是否为叶子节点
 */
export function isLeafNode(item: LeafNode | Level2Node | Level1Node) {
  const { children } = item as any;
  const isLeaf = !children || children.length == 0;
  return isLeaf;
}

/**
 * 当前节点是否为叶子节点，并且没有匹配的设计组件
 */
export function isLeafNodeAndNoComponent(item: LeafNode | Level2Node | Level1Node) {
  const isLeaf = isLeafNode(item);
  return isLeaf && !(item as LeafNode).indicator.clientName;
}

/**
 * 根据控制环节的值，反推其对应的多个控制环节
 */
export function translateCheckObject(checkObject: number) {
  const objects = RiskStepControls.filter(x => (checkObject & x.value) > 0);
  return objects;
}

export function formatCheckObject(rule: RiskRule) {
  const { checkObject, checkInterval } = rule;
  const objects = deepClone(translateCheckObject(checkObject));
  const matched = objects.find(x => x.value == RiskStepControl.progressing.value);
  if (matched) {
    matched.label = `${matched.label} (${checkInterval}秒执行一次)`;
  }

  return objects.length == 0 ? 'N/A' : objects.map(x => x.label).join('/');
}

export function formatRuleDayRange(rule: RiskRule) {
  const { beginDay, endDay } = rule;
  const fmt = 'yyyy-MM-dd';
  const none_text = '不限';
  const begin_text = beginDay ? formatDateTime(beginDay, fmt) : none_text;
  const end_text = endDay ? formatDateTime(endDay, fmt) : none_text;
  const descrip = !beginDay && !endDay ? none_text : `${begin_text} ~ ${end_text}`;
  return descrip;
}

export function formatRuleTimeRange(rule: RiskRule) {
  function format(hms: number | string) {
    if (!hms) {
      return '';
    }

    hms = hms.toString();
    while (hms.length < 6) {
      hms = '0' + hms;
    }

    const hms_str = hms.substring(0, 2) + ':' + hms.substring(2, 4) + ':' + hms.substring(4, 6);
    return hms_str;
  }

  const { beginTime, endTime } = rule;
  const none_text = '不限';
  const begin_text = beginTime ? format(beginTime) : none_text;
  const end_text = endTime ? format(endTime) : none_text;
  const descrip = !beginTime && !endTime ? none_text : `${begin_text} ~ ${end_text}`;
  return descrip;
}
