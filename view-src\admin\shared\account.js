const drag = require('../../../directives/drag');
const { <PERSON><PERSON>erWindow, app } = require('@electron/remote');
const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const systemEnum = require('../../../config/system-enum').systemEnum;
const repoAccount = require('../../../repository/account').repoAccount;
const repoAccountAround = require('../../../repository/account-around').repoAccountAround;
const repoProcedure = require('../../../repository/procedure').repoProcedure;
const repoTerminal = require('../../../repository/terminal').repoTerminal;
const repoBroker = require('../../../repository/broker').repoBroker;
const repoOrg = require('../../../repository/org').repoOrg;
const TabList = require('../../../component/tab-list').TabList;
const Tab = require('../../../component/tab').Tab;
const Splitter = require('../../../component/splitter').Splitter;
const { ChildAccountBasic, V3StandardAccount, AccountDetail } = require('../../../model/account');
const encryptPasscodeAllowed = app.encryptionOptions.encryptPasscode;
const Accessibles = { enabled: 2, disabled: 1 };

class Terminal {
    constructor(struc, interface_type) {
        var terminal_name;
        this.id = struc.id;
        this.interfaceType = interface_type;
        this.status = struc.status;

        if (interface_type !== undefined) {
            /**
             * 用于终端下拉列表，终端名称显示
             */

            var at = systemEnum.terminalInterface;
            var type_name;

            switch (interface_type) {
                case at.stock.code:
                    type_name = at.stock.mean;
                    break;
                case at.future.code:
                    type_name = at.future.mean;
                    break;
                case at.stockOption.code:
                    type_name = at.stockOption.mean;
                    break;
                default:
                    type_name = '未知终端';
                    break;
            }

            terminal_name = `[${type_name}] ${struc.terminalName}`;
        } else {
            /**
             * 用于账号数据行，终端名称显示
             */
            terminal_name = struc.terminalName;
        }

        this.terminalName = terminal_name;
    }
}

class Workflow {
    constructor(struc) {
        this.id = struc.id;
        this.orgId = struc.orgId;
        this.workflowName = struc.workFlowName;

        var content = struc.content;
        this.content =
            content instanceof Array
                ? content.map((x) => {
                      return { roleType: x.roleType, roleName: x.roleName, defaultOffLineSetting: x.defaultOffLineSetting };
                  })
                : [];
    }
}

class Broker {
    constructor(struc) {
        this.id = struc.id;
        this.brokerId = struc.brokerId;
        this.brokerName = struc.brokerName;
        this.brokerType = struc.brokerType;
        var servers = struc.servers;
        this.servers = typeof servers == 'string' ? servers.split(',') : [];
    }
}

class FeeInfo {
    constructor(struc) {
        this.id = struc.id;
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;

        this.commission = struc.commission;
        this.commissionLeast = struc.commissionLeast;
        this.shOther = struc.shOther;
        this.shTransferFee = struc.shTransferFee;
        this.stampDuty = struc.stampDuty;
        this.szOther = struc.szOther;
        this.szTransferFee = struc.szTransferFee;

        this.createTime = struc.createTime;
        this.updateTime = struc.updateTime;
    }
}

class Organization {
    constructor(struc) {
        this.id = struc.id;
        this.orgName = struc.orgName;
    }
}

class Controller extends IView {
    get workingAccount() {
        return this._workingAccount;
    }

    get isStock() {
        return !this.formdata || this.formdata.assetType == this.systemEnum.assetsType.stock.code;
    }

    get isFuture() {
        return this.formdata && this.formdata.assetType == this.systemEnum.assetsType.future.code;
    }

    get isOption() {
        return this.formdata && this.formdata.assetType == this.systemEnum.assetsType.option.code;
    }

    /**
     * @param {V3StandardAccount} account
     */
    setAsWorkingAccount(account) {
        this._workingAccount = account;
    }

    clearWorkingAccount() {
        delete this._workingAccount;
    }

    get $form() {
        return this.dialogEditApp.$refs.editform;
    }

    get $feeform() {
        return this.dialogFeeConfigApp.$refs.feeform;
    }

    get $passform() {
        return this.dialogPassModeApp.$refs.passform;
    }

    get $preform() {
        return this.dialogPreBalanceApp.$refs.preform;
    }

    get $cform() {
        return this.dialogChildApp.$refs.childform;
    }

    constructor(view_name) {
        
        super(view_name, false, '账号管理');
        this.accountStatus = {
            enabled: { code: Accessibles.enabled, mean: '启用' },
            disabled: { code: Accessibles.disabled, mean: '禁用' },
        };

        this.settings = { unset: '[未设置]' };
        this.accountTypes = [
            { code: false, mean: '普通' },
            { code: true, mean: '融资融券' },
        ];

        this.accounts = [new V3StandardAccount({})].splice(1);
        this.physicals = [new V3StandardAccount({})].splice(1);

        /**
         * 交易终端列表
         */
        this.terminals = [new Terminal({})];
        this.terminals.pop();
        this.typedTerminals = [new Terminal({})];
        this.typedTerminals.pop();

        /**
         * 绑定到订单审核的流程列表
         */
        this.workflows = [new Workflow({})];
        this.workflows.pop();

        /**
         * 发行商列表
         */
        this.brokers = [new Broker({})].splice(1);
        this.typedBrokers = [new Broker({})].splice(1);

        /**
         * 机构列表
         */
        this.orgs = [new Organization({})];
        this.orgs.pop();
    }

    identifyRecord(record) {
        return record.id;
    }

    isAccountEnabled(account_status) {
        return account_status === this.accountStatus.enabled.code;
    }

    createToolbarApp() {
        
        this.searching = {
            keywords: '',
        };

        var pagination = this.systemSetting.tablePagination;
        this.paging = {
            pageSizes: pagination.pageSizes,
            pageSize: pagination.pageSize,
            layout: pagination.layout,
            total: 0,
            page: 1,
        };

        this.toolbar = new Vue({

            el: this.$container.querySelector('.part-upper > .toolbar'),
            data: {
                searching: this.searching,
                paging: this.paging,
                allow2CreateAccount: this.isTopAdmin,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.create,
                this.createChildAccount,
                this.filterRecords, 
                this.exportSome, 
                this.refresh, 
                this.config, 
                this.handlePageSizeChange, 
                this.handlePageChange,
            ]),
        });
    }

    /**
     * @param {V3StandardAccount} first
     * @param {V3StandardAccount} second
     * @param {String} prop_name
     * @param {String} direction
     */
    sortTerminal(first, second, prop_name, direction) {

        let a = (first.terminals[0] || {}).terminalName || '';
        let b = (second.terminals[0] || {}).terminalName || '';

        if (a == b) {
            return 0;
        }
        else if (direction == 'ASC') {
            return a > b ? 1 : -1;
        }
        else {
            return a < b ? 1 : -1;
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatTerminal(record) {

        var terminals = record.terminals;
        var count = terminals.length;
        var display_name = count == 0 ? this.settings.unset : count == 1 ? terminals[0].terminalName : `${terminals[0].terminalName}(${count})`;

        if (this.isTopAdmin) {
            return `<a class="s-underline s-cp" event.onclick="showBindTerminalDialog">${display_name}</a>`;
        } else {
            return `<a>${display_name}</a>`;
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatTerminalText(record) {
        return record.terminals.map((x) => x.terminalName).join(',');
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatConnectionStatus(record) {
        
        if (record.isChildAccount || !this.isTopAdmin) {

            return record.connectionStatus
                ? '<a title="已连接" class="s-flag s-cp s-bg-green">已连接</a>'
                : '<a title="已断开" class="s-flag s-cp s-bg-red">已断开</a>';
        }
        else {

            return record.connectionStatus
                ? '<a title="已连接，点击可断开连接" class="s-flag s-cp s-bg-green" event.onclick="toggleConnection">已连接</a>'
                : '<a title="已断开，点击可尝试连接" class="s-flag s-cp s-bg-red" event.onclick="toggleConnection">已断开</a>';
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatConnectionStatusText(record) {
        return record.connectionStatus ? '已连接' : '已断开';
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatWorkflow(record) {
        return this.isTopAdmin
            ? `<a class="s-underline s-cp" event.onclick="showBindWorkflowDialog">${record.workFlowName || this.settings.unset}</a>`
            : `<a>${record.workFlowName || this.settings.unset}</a>`;
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatWorkflowText(record) {
        return `${record.workFlowName || this.settings.unset}`;
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatAccountStatus(record) {

        var classes = [];
        this.isAccountEnabled(record.status) && classes.push('is-checked');
        (record.isChildAccount || !this.isTopAdmin) && classes.push('is-disabled');
        return this.helperUi.formatSwitchButton(this.isTopAdmin ? this.switchStatus : null, classes.join(' '));
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatAccountStatusText(record) {
        return this.isAccountEnabled(record.status) ? '正常' : '已禁用';
    }

    /**
     * @param {V3StandardAccount} record
     */
    hasNoWorkflow(record) {
        return this.helper.isNone(record.workFlowId);
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatPreRiskControlStatus(record) {

        if (this.hasNoWorkflow(record)) {
            return '';
        }

        var classes = [];
        // 机构管理员可变更盘前风控
        var can_change = this.isOrgAdmin && !record.isChildAccount;

        if (record.preRiskControl) {
            classes.push('is-checked');
        }

        if (!can_change) {
            classes.push('is-disabled');
        }

        return this.helperUi.formatSwitchButton(can_change ? this.switchPreRiskControl : null, classes.join(' '));
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatPreRiskControlStatusText(record) {
        return record.preRiskControl ? '已启用' : '已禁用';
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatAutoApproveStatus(record) {

        if (this.hasNoWorkflow(record)) {
            return '';
        }

        var classes = [];
        // 券商运维人员可变更快速审核
        var can_change = this.isTopAdmin && !record.isChildAccount;

        if (record.autoApprove) {
            classes.push('is-checked');
        }

        if (!can_change) {
            classes.push('is-disabled');
        }

        return this.helperUi.formatSwitchButton(can_change ? this.switchAutoApprove : null, classes.join(' '));
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatAutoApproveStatusText(record) {
        return record.autoApprove ? '已启用' : '已禁用';
    }

    /**
     * @param {V3StandardAccount} record
     */
    formatActions(record) {

        if (record.isChildAccount) {

            if (!this.isTopAdmin) {
                return '';
            }

            return `<span class="smart-table-action">
                        <a class="lock-button icon-button">...</a>
                        <ul>
                            <li><a class="icon-button el-icon-edit" event.onclick="editChild"> 编辑</a></li>
                            <li><a class="icon-button el-icon-delete s-color-red" event.onclick="delete"> 删除账号</a></li>
                        </ul>
                    </span>`;
        }

        if (this.isTopAdmin) {

            return `<span class="smart-table-action">
                    <a class="lock-button icon-button">...</a>
                    <ul>
                        <li><a class="icon-button el-icon-edit" event.onclick="edit"> 编辑</a></li>
                        <li><a class="icon-button iconfont icon-bidui" event.onclick="compare"> 比对</a></li>
                        <li><a class="icon-button iconfont icon-fugai" event.onclick="cover"> 覆盖</a></li>
                        <li><a class="icon-button iconfont icon-shezhi11" event.onclick="initialize"> 初始化</a></li>
                        ${ false ? '<li><a class="icon-button iconfont icon-qingsuan" event.onclick="liquidate"> 清算</a></li>' : '' }
                        <li><a class="icon-button iconfont icon-shezhi11" event.onclick="showConfigFeeDialog"> 费用设置</a></li>
                        <li><a class="icon-button el-icon-delete s-color-red" event.onclick="delete"> 删除账号</a></li>
                    </ul>
                </span>`;
        }
        else {
            return `<span class="smart-table-action">
                    <a class="lock-button icon-button">...</a>
                    <ul>
                        <li><a class="icon-button el-icon-edit" event.onclick="editPreBalance"> 权益维护</a></li>
                        <li><a class="icon-button iconfont icon-bidui" event.onclick="compare"> 比对</a></li>
                        <li><a class="icon-button iconfont icon-fugai" event.onclick="cover"> 覆盖</a></li>
                        <li><a class="icon-button iconfont icon-fugai" event.onclick="coverFinance"> 资金覆盖</a></li>
                        <li><a class="icon-button iconfont icon-fugai" event.onclick="transferCash"> 资金划转</a></li>
                        <li><a class="icon-button iconfont icon-zijinjiancha" event.onclick="checkAvailable"> 可用资金检查</a></li>
                        <li><a class="icon-button iconfont icon-tianjiafengkong" event.onclick="controlRisk"> 风控设置</a></li>
                        <li><a class="icon-button iconfont icon-tab_yunwei" event.onclick="controlBehavior"> 交易行为设置</a></li>
                        <li><a class="icon-button iconfont icon-shezhi11" event.onclick="showConfigFeeDialog"> 费用设置</a></li>
                        <li><a class="icon-button el-icon-shezhi11" event.onclick="showModPasscodeDialog"> 密码维护</a></li>
                    </ul>
                </span>`;
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    switchStatus(record) {
        
        var new_status = this.isAccountEnabled(record.status) ? this.accountStatus.disabled.code : this.accountStatus.enabled.code;
        var copy = new V3StandardAccount({});
        this.helper.extend(copy, record);
        copy.status = new_status;

        this.submit(copy, () => {
            this.refresh();
        });
    }

    /**
     * @param {V3StandardAccount} record
     */
    async switchPreRiskControl(record) {

        if (this.helper.isNone(record.workFlowName) && !record.preRiskControl) {
            
            this.interaction.showError('工作流未绑定，无法启用盘前风控');
            return;
        }

        const new_value = record.preRiskControl ? false : true;
        const resp = await repoAccount.setAccountPreRiskControl(record.accountId, new_value);
        const { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess('盘前风控，设置已保存');
            this.refresh();
        } 
        else {
            this.interaction.showError('盘前风控保存失败：' + errorMsg);
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    async switchAutoApprove(record) {

        if (this.helper.isNone(record.workFlowName) && !record.autoApprove) {
            
            this.interaction.showError('工作流未绑定，无法启用快速审核');
            return;
        }

        const new_value = record.autoApprove ? false : true;
        const resp = await repoAccount.setAccountAutoApprove(record.accountId, new_value);
        const { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess('快速审核，设置已保存');
            this.refresh();
        } 
        else {
            this.interaction.showError('快速审核保存失败：' + errorMsg);
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    showBindTerminalDialog(record) {

        if (!this.isTopAdmin) {
            return;
        }

        this.setAsWorkingAccount(record);

        if (this.dialogTerminalApp) {

            this.tmndata.visible = true;
            this.tmndata.selected = record.terminals.map(x => x.id);
            this.tmndata.accountName = record.accountName;
            this.requestBindTerminals();
            return;
        }

        this.tmndata = {

            visible: false,
            selected: [],
            accountName: null,
        };

        this.tmndata.selected = record.terminals.map(x => x.id);
        this.tmndata.accountName = record.accountName;

        this.dialogTerminalApp = new Vue({

            el: this.$container.querySelector('.dialog-terminal'),
            directives: { drag },
            data: {

                dialog: this.tmndata,
                typedTerminals: this.typedTerminals,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.saveTerminal, this.unsaveTerminal]),
        });

        this.requestBindTerminals();
        this.dialogTerminalApp.$nextTick(() => {
            this.tmndata.visible = true;
        });
    }

    async saveTerminal() {

        var account = this.workingAccount;
        var account_id = account.id;
        var terminal_ids = this.tmndata.selected;
        var terminals = this.helper.deepClone(this.terminals.filter(x => terminal_ids.includes(x.id)));

        let resp = await repoAccount.bindTerminal(account_id, terminal_ids);
        if (resp.errorCode == 0) {

            this.interaction.showSuccess('已绑定交易终端');
            this.refresh();
        } 
        else {
            this.interaction.showError('绑定交易终端发生异常：' + resp.errorMsg);
        }

        this.tmndata.visible = false;
        this.clearWorkingAccount();
    }

    unsaveTerminal() {
        this.tmndata.visible = false;
        this.clearWorkingAccount();
    }

    /**
     * @param {V3StandardAccount} record
     */
    showBindWorkflowDialog(record) {

        /**
         * 非完整功能用户，不能绑定流程
         */

        if (!this.isTopAdmin) {
            return;
        }

        this.setAsWorkingAccount(record);

        if (this.dialogWorkflowApp) {
            this.wfdata.visible = true;
            this.wfdata.workflowId = record.workFlowId;
            this.wfdata.accountName = record.accountName;
            this.requestBindWorkflows();
            return;
        }

        this.wfdata = {
            visible: false,
            workflowId: null,
            accountName: null,
        };

        this.wfdata.workflowId = record.workFlowId;
        this.wfdata.accountName = record.accountName;
        this.dialogWorkflowApp = new Vue({
            el: this.$container.querySelector('.dialog-workflow'),
            directives: { drag },
            data: {
                dialog: this.wfdata,
                workflows: this.workflows,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.saveWorkflow, this.unsaveWorkflow]),
        });

        this.requestBindWorkflows();
        this.dialogWorkflowApp.$nextTick(() => {
            this.wfdata.visible = true;
        });
    }

    async saveWorkflow() {
        var account = this.workingAccount;
        var account_id = account.id;
        var workflow_id = this.wfdata.workflowId;

        if (this.helper.isNone(workflow_id)) {
            let resp = await repoProcedure.bindProcedure(null, account_id);
            if (resp.errorCode == 0) {
                this.tableObj.updateRow({ id: account_id, workFlowId: null, workFlowName: null });
                this.interaction.showSuccess('已取消绑定（订单审核）流程');
            } else {
                this.interaction.showError('取消绑定（订单审核）流程发生异常：' + resp.errorMsg);
            }
        } else {
            let matched = this.workflows.find((x) => x.id === workflow_id);
            let workflow_name = matched.workflowName;
            let resp = await repoProcedure.bindProcedure(workflow_id, account_id);

            if (resp.errorCode == 0) {
                this.interaction.showSuccess('已绑定（订单审核）流程');
                this.refresh();
            } else {
                this.interaction.showError('绑定（订单审核）流程发生异常：' + resp.errorMsg);
            }
        }

        this.wfdata.visible = false;
        this.clearWorkingAccount();
    }

    unsaveWorkflow() {
        this.wfdata.visible = false;
        this.clearWorkingAccount();
    }

    create() {
        this.clearWorkingAccount();
        this.showEditDialog();
    }

    /**
     * @param {V3StandardAccount} row 
     */
    formatNameCol(row) {
        return (row.isChildAccount ? '(子) ' : '') + this.formatAccountName(row);
    }

    createChildAccount() {
        this.showChildDialog();
    }

    /**
     * @param {V3StandardAccount} account 
     */
    showChildDialog(account) {

        if (this.dialogChildApp == undefined) {

            const rules = {

                accountId: { required: true, message: '请选择从属于账号' },
                accountName: { required: true, message: '请输入策略账号别名' },
            };
    
            this.cdialog = { visible: false, title: null };
            this.formChild = new ChildAccountBasic({});
            this.dialogChildApp = new Vue({
    
                el: this.$container.querySelector('.dialog-child-account'),
                directives: { drag },
                data: {
                    dialog: this.cdialog,
                    rules: rules,
                    formd: this.formChild,
                    accounts: this.physicals,
                },
                methods: this.helper.fakeVueInsMethod(this, [

                    this.formatSelectAccountName,
                    this.isChildModify,
                    this.saveChild, 
                    this.unsaveChild,
                ])
            });
        }

        const ref = this.formChild;
        this.helper.resetMap(ref, null);
        if (account) {

            ref.id = account.id;
            ref.accountId = account.accountId;
            ref.accountName = account.accountName;
        }

        this.cdialog.title = account ? '编辑策略账号' : '新建策略账号';
        this.cdialog.visible = true;
        this.dialogChildApp.$nextTick(() => { this.$cform.clearValidate(); });
    }

    async submitChild() {

        let body = Object.assign({}, this.formChild);
        let resp = this.helper.isNone(this.formChild.id) ? await repoAccount.createChildAccount(body) : await repoAccount.updateChildAccount(body);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            this.refresh();
            this.interaction.showSuccess('策略账号已保存');
        } 
        else {
            this.interaction.showError(`策略账号保存失败：${errorCode}/${errorMsg}`);
        }
    }

    isChildModify() {
        return !!this.formChild.id;
    }

    saveChild() {

        this.$cform.validate(async (isok) => {

            if (isok) {

                await this.submitChild();
                this.cdialog.visible = false;
                this.refresh();
            }
        });
    }

    unsaveChild() {
        this.cdialog.visible = false;
    }

    /**
     * @param {V3StandardAccount} record
     */
    edit(record) {

        this.setAsWorkingAccount(record);
        this.showEditDialog(record);
    }    

    /**
     * @param {V3StandardAccount} record
     */
    editChild(record) {
        this.showChildDialog(record);
    }

    /**
     * @param {V3StandardAccount} record
     */
    editPreBalance(record) {
        this.setAsWorkingAccount(record);
        this.showEditPreBalanceDialog(record);
    }

    /**
     * @param {V3StandardAccount} record
     */
    toggleConnection(record) {

        var account_id = record.id;
        var account_name = record.accountName;
        var connect_status = record.connectionStatus;
        var target_behavior = connect_status ? repoAccount.disconnect : repoAccount.connect;
        var message = connect_status ? '是否，断开连接（将影响到后续交易）？' : '是否，建立连接？';

        this.interaction.showConfirm({

            title: '操作确认',
            message: message,
            confirmed: async () => {

                let target_status = connect_status ? false : true;
                let target_status_name = connect_status ? '断开' : '连接';
                let resp = await target_behavior(account_id);

                if (resp.errorCode === 0) {

                    this.interaction.showSuccess(`账号[${account_name}]已[${target_status_name}]`);
                    this.updateAccountConnectStatus(account_id, target_status);
                    this.refresh();
                } 
                else {
                    this.interaction.showError(`账号[${account_name}]未[${target_status_name}]，错误信息：${resp.errorCode}/${resp.errorMsg}`);
                }
            },
        });
    }

    updateAccountConnectStatus(account_id, target_status) {
        this.tableObj.updateRow({ id: account_id, connectionStatus: target_status });
    }

    /**
     * @param {V3StandardAccount} record
     */
    compare(record) {
        this.executeGeneralTask(record, '比对', repoAccountAround.overlap, [record.id, false]);
    }

    /**
     * @param {V3StandardAccount} record
     */
    cover(record) {
        this.executeGeneralTask(record, '覆盖', repoAccountAround.overlap, [record.id, true]);
    }

    /**
     * @param {V3StandardAccount} record
     */
    initialize(record) {
        this.executeGeneralTask(record, '初始化', repoAccount.initAccount, [record.id]);
    }

    /**
     * @param {V3StandardAccount} record
     */
    liquidate(record) {
        this.executeGeneralTask(record, '清算', repoAccount.liquidationAccount, [record.id]);
    }

    /**
     * @param {V3StandardAccount} record
     */
    coverFinance(record) {
        this.executeGeneralTask(record, '资金覆盖', repoAccount.overlapFinance, [{ accountId: record.id, overlap: true }]);
    }

    /**
     * @param {V3StandardAccount} record
     */
    transferCash(record) {

        const thisObj = this;

        function setcontext() {

            const { accountId, accountName } = record;
            thisObj.transferApp.setContext(accountId, accountName);
            thisObj.transferApp.show();
        }

        if (this.transferApp) {

            setcontext();
            return;
        }
        
        const TransferView = require('./account-component/transfer');
        this.transferApp = new TransferView('@admin/shared/account-component/transfer');
        this.transferApp.loadBuild(this.$container);
        setcontext();
    }

    /**
     * @param {V3StandardAccount} record
     */
    checkAvailable(record) {
        this.interaction.showError('功能开发中...');
    }

    /**
     * @param {V3StandardAccount} record
     */
    controlRisk(record) {
        let account_id = record.id;
        let account_name = record.accountName;

        this.openWinRskSetting({
            type: this.systemEnum.identityType.account.code,
            identity: account_id,
            name: account_name,
        });
    }

    /**
     * @param {V3StandardAccount} record
     */
    controlBehavior(record) {

        if (!this.tbmgr) {

            const { TradeBehaviorManager } = require('../trade-behavior/trade-behavior-manager');
            this.tbmgr = new TradeBehaviorManager(this);
        }

        this.tbmgr.openSetting({
            identityType: this.systemEnum.identityType.account.code,
            identity: record.id,
            name: record.accountName,
        });
    }

    saveFeeConfig() {
        this.$feeform.validate((result) => {
            if (result) {
                this.submitFeeSetting();
                this.feeDialog.visible = false;
                this.refresh();
                this.clearWorkingAccount();
            }
        });
    }

    unsaveFeeConfig() {
        this.feeDialog.visible = false;
        this.clearWorkingAccount();
    }

    async submitFeeSetting() {
        let is_create = this.helper.isNone(this.feedata.model.id);
        let resp = is_create ? await repoAccount.saveAccountFee(this.feedata.model) : await repoAccount.updateAccountFee(this.feedata.model);

        if (resp.errorCode == 0) {
            this.interaction.showSuccess('费率设置已保存');
        } else {
            this.interaction.showError('费率设置发生异常：' + resp.errorMsg);
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    showModPasscodeDialog(record) {
        const self = this;
        this.setAsWorkingAccount(record);

        if (this.dialogPassModeApp) {
            this.passdata.accountName = record.accountName;
            this.passdata.pwd = null;
            this.passdata.txPassword = record.extInfo.txPassword;
            this.dialogPassModeApp.record = record;
            this.passcodeDialog.visible = true;

            setTimeout(() => {
                this.$passform.clearValidate();
            }, 200);
            return;
        }

        var rules = {
            pwd: [{ required: true, message: '请输入资金账号密码' }],
        };

        this.passcodeDialog = { visible: false, title: '密码维护' };
        this.passdata = {
            accountName: null,
            pwd: null,
            txPassword: null,
        };

        this.passdata.accountName = record.accountName;
        this.passdata.txPassword = record.extInfo.txPassword;

        this.dialogPassModeApp = new Vue({
            el: this.$container.querySelector('.dialog-password'),
            directives: { drag },
            data: {
                dialog: this.passcodeDialog,
                rules: rules,
                formd: this.passdata,
                record,
            },
            computed: {
                isStock() {
                    return this.record.assetType == self.systemEnum.assetsType.stock.code;
                },

                isFuture() {
                    return this.record.assetType == self.systemEnum.assetsType.future.code;
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [this.savePass, this.unsavePass]),
        });

        this.dialogPassModeApp.$nextTick(() => {
            this.passcodeDialog.visible = true;
        });
    }

    savePass() {
        var copy = new V3StandardAccount({});
        var account = this.helper.deepClone(this.workingAccount);
        this.helper.extend(copy, account);
        copy.pwd = encryptPasscodeAllowed ? this.helper.aesEncrypt(this.passdata.pwd) : this.passdata.pwd;
        copy.extInfo.txPassword = this.passdata.txPassword;

        this.submit(copy, () => {
            this.passcodeDialog.visible = false;
            this.refresh();
        });
    }

    unsavePass() {
        this.passcodeDialog.visible = false;
    }

    /**
     * @param {V3StandardAccount} record
     */
    showConfigFeeDialog(record) {

        this.setAsWorkingAccount(record);

        if (this.dialogFeeConfigApp) {

            this.feeDialog.visible = true;
            this.feedata.accountName = record.accountName;
            this.requestFeeSetting(record.id);
            setTimeout(() => {
                this.$feeform.clearValidate();
            }, 200);
            return;
        }

        var rules = {

            stampDuty: [{ required: true, message: '请输入印花税' }],
            shTransferFee: [{ required: true, message: '请输入过户费(上海)' }],
            szTransferFee: [{ required: true, message: '请输入过户费(深圳)' }],
            commission: [{ required: true, message: '请输入交易佣金' }],
            commissionLeast: [{ required: true, message: '请输入最低佣金' }],
            shOther: [{ required: true, message: '请输入其他费用(上海)' }],
            szOther: [{ required: true, message: '请输入其他费用(深圳)' }],
        };

        this.feeDialog = { visible: false, title: '费率设置' };
        this.feedata = { model: new FeeInfo({}), accountName: null };

        this.feedata.accountName = record.accountName;
        this.dialogFeeConfigApp = new Vue({
            
            el: this.$container.querySelector('.dialog-fee'),
            directives: { drag },
            data: {
                dialog: this.feeDialog,
                rules: rules,
                feedata: this.feedata,
                isReadonly: !this.isTopAdmin,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.saveFeeConfig, this.unsaveFeeConfig]),
        });

        this.requestFeeSetting(record.id);
        this.dialogFeeConfigApp.$nextTick(() => {
            this.feeDialog.visible = true;
        });
    }

    async requestFeeSetting(account_id) {
        if (this._isRequestingFeeSetting) {
            return;
        }

        this._isRequestingFeeSetting = true;
        var loading = this.interaction.showLoading({ text: `获取费率详情...` });

        try {
            let resp = await repoAccount.getAccountFee(account_id);
            if (resp.errorCode === 0) {
                let struc = resp.data || {};
                let fee_info = new FeeInfo(struc);
                this.helper.extend(this.feedata.model, fee_info);
            } else {
                this.interaction.showError(`获取费率详情失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (ex) {
            this.interaction.showError(`获取费率详情异常`);
            console.error(ex);
        } finally {
            this._isRequestingFeeSetting = false;
            loading.close();
        }
    }

    /**
     * @param {V3StandardAccount} record
     */
    delete(record) {
        this.interaction.showConfirm({
            title: '删除确认',
            message: `删除账号：${record.accountName} ?`,
            confirmed: async () => {
                await this.deleteRecord(record);
                this.refresh();
            },
        });
    }

    /**
     * @param {V3StandardAccount} record
     */
    async deleteRecord(record) {
        let result = await repoAccount.removeAccount(record.id);
        if (result.errorCode == 0) {
            this.interaction.showSuccess('账号已删除');
            this.tableObj.deleteRow(record.id);
            this.clearWorkingAccount();
        } else {
            this.interaction.showError('删除操作错误：' + result.errorMsg);
        }
    }

    /**
     * execute a general task
     * @param {V3StandardAccount} record
     * @param {String} task_name
     * @param {Function} task
     * @param {Array} args
     * @param {Function} callback
     */
    async executeGeneralTask(record, task_name, task, args, callback) {
        this.interaction.showConfirm({
            title: '操作确认',
            message: `<span>是否确认，对账号 [${record.accountName}] 执行 [${task_name}] 操作</span>`,
            confirmed: async () => {
                let resp = await task.call(repoAccount, ...args);
                if (typeof callback == 'function') {
                    callback(resp);
                } else {
                    if (resp.errorCode === 0) {
                        this.interaction.showSuccess(`[${task_name}] 操作已执行`);
                    } else {
                        this.interaction.showError(`操作 [${task_name}] 发生异常：${resp.errorCode}/${resp.errorMsg}`);
                    }
                }
            },
        });
    }

    openWinRskSetting({ type, identity, name }) {
        let args = { type, identity, name, action: 'identity' };
        let title = '风控设置';

        if (this.winRsk && !this.winRsk.isDestroyed()) {
            this.winRsk.show();
            this.winRsk.webContents.send('set-context-data', args);
            return;
        }

        var window_options = { width: 940, height: 620, minimizable: false, maximizable: false, highlight: true };
        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, win_id) => {
            this.winRsk = BrowserWindow.fromId(win_id);
            this.winRsk.on('closed', () => { this.winRsk = null; });
            setTimeout(() => { this.winRsk.webContents.send('set-context-data', args); }, 1000);
        });

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@admin/risk-dialog', title, window_options);
    }

    setupTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.part-upper > table');

        if (this.isTopAdmin) {

            let $org_cols = $table.querySelectorAll('th[condition="org-only"]');
            $org_cols.forEach($col => { $col.remove(); });
        }
        else {

            let $manage_cols = $table.querySelectorAll('th[condition="manage-only"]');
            $manage_cols.forEach($col => { $col.remove(); });
        }

        this.tableObj = new SmartTable($table, this.identifyRecord, this, {
            
            tableName: 'smt-aa',
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: (filtered_count) => { this.paging.total = filtered_count; },
            rowSelected: (account) => { this.handleAccountChange(account); },
        });

        this.tableObj.setPageSize(this.paging.pageSize);
        this.tableObj.setMaxHeight(200);
    }

    setupDynamicPart() {

        let $splitter = this.$container.querySelector('.splitter-bar');
        let $lower = this.$container.querySelector('.part-lower');
        let $summary = this.$container.querySelector('.account-summary');

        if (this.hasSummary) {
            
            this.buildSplitter($splitter);
            this.setupSummary($summary);
        } 
        else {

            $splitter.remove();
            $lower.remove();
            this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
        }
    }

    adjustTableHeight(win_width, win_height, is_maximized) {
        this.tableObj.setMaxHeight(win_height - (is_maximized ? 180 : 165));
    }

    _handleActivation() {
        this.tableObj.fitColumnWidth();
    }

    _handlePotentialFactorChange() {
        this.refresh();
    }

    /**
     * @param {HTMLElement} $splitter
     */
    buildSplitter($splitter) {
        this.splitter = new Splitter('admin-account', $splitter, this.handleSpliting.bind(this), { previousMinHeight: 155, nextMinHeight: 200 });
        setTimeout(() => {
            this.splitter.recover();
        }, 1000);
    }

    /**
     * @param {Number} previous_height
     * @param {Number} next_height
     */
    handleSpliting(previous_height, next_height) {
        this.tableObj.setMaxHeight(previous_height - 75);
        this.summary.tabs.forEach((tab) => {
            tab.viewEngine.setHeight(next_height);
        });
    }

    /**
     * @param {HTMLElement} $summary
     */
    setupSummary($summary) {

        var tablist = new TabList({

            allowCloseTab: false,
            embeded: true,
            lazyLoad: false,
            $navi: $summary.querySelector('.summary-tabs'),
            $content: $summary.querySelector('.summary-content'),
            tabCreated: this.handleTabCreated.bind(this),
        });

        tablist.openTab(true, '@shared/order-list', '今日订单');
        tablist.openTab(true, '@shared/position-list', '今日持仓');
        tablist.openTab(true, '@shared/exchange-list', '今日成交');
        tablist.openTab(true, '@shared/history-order-list', '历史订单');
        tablist.openTab(true, '@shared/history-position-list', '历史持仓');
        tablist.openTab(true, '@shared/history-exchange-list', '历史成交');
        tablist.openTab(true, '@shared/history-equity-list', '历史权益');
        tablist.openTab(true, '@shared/history-cash-inout', '出入金');

        this.summary = tablist;
    }

    /**
     * @param {Tab} tab
     */
    handleTabCreated(tab) {
        if (this.contextInfo) {
            this.summary.fireEventOnTab(tab, this.systemEvent.viewContextChange, this.constructContextParms(this.contextInfo));
        }
    }

    /**
     * @param {V3StandardAccount} account
     */
    handleAccountChange(account) {
        
        if (this.isTopAdmin) {
            return;
        }
        else if (this.contextInfo && this.contextInfo.id === account.id) {
            return;
        }

        this.contextInfo = account;
        if (this.summary) {
            this.summary.fireEventOnAllTabs(this.systemEvent.viewContextChange, this.constructContextParms(account));
        }
    }

    /**
     * @param {V3StandardAccount} account
     */
    constructContextParms(account) {
        return {
            accountId: account.id,
            accounts: [
                {
                    accountId: account.id,
                    accountName: account.accountName,
                    assetType: account.assetType,
                    isCredit: !!account.credit,
                },
            ],
        };
    }

    /**
     * @returns {Array<AccountDetail>}
     */
    async requestAccountDetail() {

        var details = [];

        try {
            let resp = await repoAccount.batchGetAccountCash();
            let { errorCode, errorMsg, data } = resp;
            details = errorCode == 0 && Array.isArray(data) ? data : [];
        }
        catch (ex) {
            console.error(ex);
        }

        return details;
    }

    async requestAccounts() {

        if (this._isRequesting) {
            return;
        }

        this._isRequesting = true;
        var loading = this.interaction.showLoading({ text: `获取账号列表...` });

        try {

            let details = await this.requestAccountDetail();
            let resp = await repoAccount.getAll();
            let { errorCode, errorMsg, data } = resp;
            if (errorCode === 0) {

                /**
                 * 如下几个字段，在账号列表上（数值）缺失，从详情接口获取进行扩充
                 */

                let accounts = data.map(row => {

                    let account = new V3StandardAccount(row);
                    let matched = details.find(item => item.id == row.id);
                    if (matched) {

                        account.preBalance = matched.preBalance;
                        account.available = matched.available;
                        account.frozenMargin = matched.frozenMargin;
                    }

                    return account;
                });

                this.physicals.refill(accounts.filter(x => !x.isChildAccount));
                this.accounts.refill(accounts);

                // 之前选中行
                let last_selected = this.tableObj.selectedRow;
                this.tableObj.refill(accounts);
                
                // 有保留的关键字，则进行过滤
                if (this.searching.keywords) {
                    this.filterRecords();
                }
                
                if (accounts.length > 0) {

                    let rowd = last_selected ? accounts.find(x => x.id == last_selected.id) : accounts[0];
                    this.tableObj.selectRow(rowd.id);
                }
            } 
            else {
                this.interaction.showError(`获取账号列表失败，${errorCode}/${errorMsg}`);
            }
        } 
        catch (ex) {

            this.interaction.showError(`获取账号列表异常`);
            console.error(ex);
        } 
        finally {

            this._isRequesting = false;
            loading.close();
        }
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    filterRecords() {
        let thisObj = this;
        let keywords = this.searching.keywords;

        /**
         * @param {V3StandardAccount} record
         */
        function filterByPinyin(record) {
            return thisObj.helper.pinyin(record.accountName).indexOf(keywords) >= 0;
        }

        this.paging.page = 1;
        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.mixedFilter(filterByPinyin, 'or');
    }

    exportSome() {
        this.tableObj.exportAllRecords(`账号列表-${new Date().format('yyyyMMdd')}`);
    }

    refresh() {
        
        this.paging.page = 1;
        setTimeout(() => { this.requestAccounts(); }, 0);
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    async requestBindTerminals() {

        if (this._isRequestingTerminal) {
            return;
        }

        this._isRequestingTerminal = true;
        var loading = this.interaction.showLoading({ text: `获取交易终端列表...` });

        try {

            let resp = await repoTerminal.getAll();
            if (resp.errorCode === 0) {

                let list = resp.data || [];
                if (list instanceof Array) {

                    let terminals = list.map((x) => new Terminal(x, x.interfaceType));
                    this.terminals.clear();
                    this.terminals.merge(terminals);
                    this.filterTypedTerminals();
                }
            } else {
                this.interaction.showError(`获取交易终端列表失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        } 
        catch (ex) {
            this.interaction.showError(`获取交易终端列表异常`);
            console.error(ex);
        } 
        finally {
            this._isRequestingTerminal = false;
            loading.close();
        }
    }

    filterTypedTerminals() {

        const asset_type = this.workingAccount.assetType;
        const matched_setting = this.helper.dict2Array(systemEnum.terminalInterface).find((x) => x.matchedAssetType == asset_type);
        this.typedTerminals.length = 0;

        if (matched_setting) {

            const interface_type = matched_setting.code;
            this.typedTerminals.merge(this.terminals.filter((x) => x.interfaceType == interface_type));
        } 
        else {
            this.typedTerminals.merge(this.terminals);
        }
    }

    async requestBindWorkflows() {
        if (this._isRequestingWorkflow) {
            return;
        }

        this._isRequestingWorkflow = true;
        var loading = this.interaction.showLoading({ text: `获取审批流程模板列表...` });

        try {
            let resp = await repoProcedure.getProcedure();
            if (resp.errorCode === 0) {
                let list = resp.data || [];
                if (list instanceof Array) {
                    let templates = list.map((x) => new Workflow(x));
                    this.workflows.clear();
                    this.workflows.merge(templates);
                }
            } else {
                this.interaction.showError(`获取审批流程模板列表失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (ex) {
            this.interaction.showError(`获取审批流程模板列表异常`);
            console.error(ex);
        } finally {
            this._isRequestingWorkflow = false;
            loading.close();
        }
    }

    async requestBindBrokers() {
        if (this._isRequestingBroker) {
            return;
        }

        this._isRequestingBroker = true;
        var loading = this.interaction.showLoading({ text: `获取经纪商列表...` });

        try {
            let resp = await repoBroker.getAll();
            if (resp.errorCode === 0) {
                let list = resp.data || [];
                if (list instanceof Array) {
                    let brokers = list.map((x) => new Broker(x));
                    this.brokers.clear();
                    this.brokers.merge(brokers);
                    this.resetTypedBrokers(this.formdata ? this.formdata.assetType : this.systemEnum.assetsType.stock.code);
                }
            } else {
                this.interaction.showError(`获取经纪商失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (ex) {
            this.interaction.showError(`获取经纪商异常`);
            console.error(ex);
        } finally {
            this._isRequestingBroker = false;
            loading.close();
        }
    }

    async requestBindOrgs() {
        if (this._isRequestingOrgs) {
            return;
        }

        this._isRequestingOrgs = true;
        var loading = this.interaction.showLoading({ text: `获取机构列表...` });

        try {
            let resp = await repoOrg.getAll();
            if (resp.errorCode === 0) {
                let list = resp.data || [];
                if (list instanceof Array) {
                    let orgs = list.map((x) => new Organization(x));
                    this.orgs.clear();
                    this.orgs.merge(orgs);
                }
            } else {
                this.interaction.showError(`获取机构失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (ex) {
            this.interaction.showError(`获取机构异常`);
            console.error(ex);
        } finally {
            this._isRequestingOrgs = false;
            loading.close();
        }
    }

    /**
     * @param {V3StandardAccount} account
     * @param {Function} callback
     */
    async submit(account, callback) {

        let is_create = this.helper.isNone(account.id);
        let post_account = this.helper.deepClone(account);
        post_account.extInfo = JSON.stringify(post_account.extInfo);
        let result = is_create ? await repoAccount.createAccount(post_account) : await repoAccount.updateAccount(post_account);

        if (result.errorCode != 0) {
            this.interaction.showError('账号信息保存失败：' + result.errorMsg);
        } 
        else {

            if (is_create) {
                callback(new V3StandardAccount(result.data));
            } 
            else {
                callback(account);
            }

            this.interaction.showSuccess('账号信息已保存');
        }
    }

    /**
     * @param {Boolean} is_create
     * @param {V3StandardAccount} revised 结构，和account一致（除了extInfo字段缺失外）
     */
    updateBack2Table(is_create, revised) {
        try {
            if (is_create) {
                revised.extInfo = this.helper.deepClone(this.formdata.extInfo);
                this.tableObj.insertRow(revised);
            } else {
                this.tableObj.updateRow(revised);
            }
        } catch (ex) {
            console.error(ex);
        }

        this.dialog.visible = false;
    }

    save() {
        this.$form.validate((result) => {
            if (result) {
                if (encryptPasscodeAllowed) {
                    this.formdata.pwd = this.helper.aesEncrypt(this.formdata.pwd);
                }

                this.submit(this.formdata, (revised) => {

                    let is_create = this.helper.isNone(this.formdata.id);
                    let matched_org = this.orgs.find(x => x.id == revised.orgId);
                    revised.orgName = matched_org ? matched_org.orgName : null;
                    this.updateBack2Table(is_create, revised);
                });
            }
        });
    }

    unsave() {
        this.dialog.visible = false;
    }

    resetTypedBrokers(asset_type) {
        var brokers = asset_type == this.systemEnum.assetsType.option.code ? this.brokers : this.brokers.filter((x) => x.brokerType == asset_type);
        this.typedBrokers.clear();
        this.typedBrokers.merge(brokers);
    }

    takeMyOrg() {
        this.formdata.orgId = this.userInfo.orgId;
        this.formdata.orgName = this.userInfo.orgName;
    }

    handleAssetTypeChange() {
        this.formdata.bkId = null;
        this.formdata.brokerId = null;
        this.formdata.brokerName = null;
        this.resetTypedBrokers(this.formdata.assetType);
    }

    handleOrgChange() {
        var matched = this.orgs.find((x) => x.id == this.formdata.orgId);
        this.formdata.orgName = matched instanceof Organization ? matched.orgName : null;
    }

    handleBrokerChange() {
        var matched = this.typedBrokers.find((x) => x.id == this.formdata.bkId);
        this.formdata.brokerId = matched instanceof Broker ? matched.brokerId : null;
        this.formdata.brokerName = matched instanceof Broker ? matched.brokerName : null;
    }

    /**
     * @param {V3StandardAccount} account
     */
    showEditDialog(account) {

        if (this.dialogEditApp) {

            this.helper.extend(this.formdata, account ? this.helper.deepClone(account) : new V3StandardAccount({}));
            this.dialog.title = account ? '编辑账号信息' : '新建账号';
            this.requestBindOrgs();
            this.requestBindBrokers();

            if (this.isOrgFixed) {
                this.takeMyOrg();
            }

            this.dialog.visible = true;
            setTimeout(() => {
                this.$form.clearValidate();
            }, 200);

            return;
        }

        const rules = {
            orgId: { required: true, message: '请选择所属机构' },
            bkId: { required: true, message: '请选择所属经纪商' },
            credit: { required: true, message: '请选择股票账号类型' },
            accountName: { required: true, message: '请输入账号名称' },
            financeAccount: { required: true, message: '请输入资金账号' },
            assetType: { required: true, message: '请选择账号类型' },
            pwd: { required: true, message: '请输入资金账号密码' },

            'extInfo.tradeAccount': { required: true, message: '请输入交易账号' },
            'extInfo.shSecId': { required: true, message: '请输入沪市股东代码' },
            'extInfo.szSecId': { required: true, message: '请输入深市股东代码' },
            'extInfo.yybId': [{ required: true, message: '请输入营业部代码' }],
        };

        this.dialog = { visible: false, title: null };
        this.formdata = new V3StandardAccount({});
        this.dialogEditApp = new Vue({
            el: this.$container.querySelector('.dialog-editing'),
            directives: { drag },
            data: {
                dialog: this.dialog,
                rules: rules,
                formd: this.formdata,
                orgs: this.orgs,
                brokers: this.typedBrokers,
                assetTypes: this.helper.deepClone({
                    stock: this.systemEnum.assetsType.stock,
                    future: this.systemEnum.assetsType.future,
                    option: this.systemEnum.assetsType.option,
                }),

                accountTypes: this.helper.deepClone(this.accountTypes),
                accountStatus: this.helper.deepClone(this.accountStatus),
                isOrgFixed: this.isOrgFixed,
                allow2ChangePasscode: this.isOrgAdmin,
            },

            computed: {
                isStock: () => {
                    return this.isStock;
                },

                isFuture: () => {
                    return this.isFuture;
                },
            },

            methods: this.helper.fakeVueInsMethod(this, [this.handleAssetTypeChange, this.handleOrgChange, this.handleBrokerChange, this.save, this.unsave]),
        });

        this.helper.extend(this.formdata, account ? this.helper.deepClone(account) : new V3StandardAccount({}));
        this.dialog.title = account ? '编辑账号信息' : '新建账号';

        if (this.isOrgFixed) {
            this.takeMyOrg();
        }

        this.dialog.visible = true;
        this.requestBindOrgs();
        this.requestBindBrokers();
        this.dialogEditApp.$nextTick(() => {
            this.$form.clearValidate();
        });
    }

    /**
     * @param {V3StandardAccount} record
     */
    async showEditPreBalanceDialog(record) {

        if (this.dialogPreBalanceApp) {

            this.preForm.account_id = record.id;
            this.renderPreForm();
            setTimeout(() => { this.preDialog.visible = true; }, 200);
            return;
        }

        var rules = {
            pre_balance: [
                {
                    validator: (rule, value, callback) => {
                        if (isNaN(value)) {
                            callback('请输入数字');
                        } else {
                            callback();
                        }
                    },
                },
            ],
            available: [
                {
                    validator: (rule, value, callback) => {
                        if (isNaN(value)) {
                            callback('请输入数字');
                        } else {
                            callback();
                        }
                    },
                },
            ],
            frozen: [
                {
                    validator: (rule, value, callback) => {
                        if (isNaN(value)) {
                            callback('请输入数字');
                        } else {
                            callback();
                        }
                    },
                },
            ],
            buy_balance: [
                {
                    validator: (rule, value, callback) => {
                        if (isNaN(value)) {
                            callback('请输入数字');
                        } else {
                            callback();
                        }
                    },
                },
            ],
            sell_balance: [
                {
                    validator: (rule, value, callback) => {
                        if (isNaN(value)) {
                            callback('请输入数字');
                        } else {
                            callback();
                        }
                    },
                },
            ],
            sell_quota: [
                {
                    validator: (rule, value, callback) => {
                        if (isNaN(value)) {
                            callback('请输入数字');
                        } else {
                            callback();
                        }
                    },
                },
            ],
        };

        this.preDialog = { visible: false, title: '权益维护' };
        this.preForm = { account_id: record.id, pre_balance: '', available: '', frozen: '', buy_balance: '', sell_balance: '', sell_quota: ''};

        this.dialogPreBalanceApp = new Vue({
            el: this.$container.querySelector('.dialog-pre-balance'),
            directives: { drag },
            data: {
                dialog: this.preDialog,
                rules: rules,
                form: this.preForm,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.savePreBalance, this.closePreBalance]),
        });

        this.renderPreForm();
        await this.helper.sleep(200);
        this.dialogPreBalanceApp.$nextTick(() => {
            this.preDialog.visible = true;
        });
    }

    savePreBalance() {
        this.$preform.validate((result) => {
            if (result) {
                if (
                    Object.keys(this.preForm)
                        .filter((key) => key != 'account_id')
                        .map((key) => this.preForm[key])
                        .every((val) => val === '')
                ) {
                    this.interaction.showWarning('请至少填写一个字段');
                    return;
                } else {
                    this.submitPreBalance();
                }
            }
        });
    }

    renderPreForm() {

        let matched = this.accounts.find(x => x.id == this.workingAccount.id);
        if (matched) {

            this.preForm.available = matched.available;
            this.preForm.frozen = matched.frozenMargin;
            this.preForm.pre_balance = matched.preBalance;
            this.preForm.buy_balance = matched.loanBuyBalance;
            this.preForm.sell_balance = matched.loanSellBalance;
            this.preForm.sell_quota = matched.loanSellQuota;
        }
    }

    async submitPreBalance() {

        let resp = await repoAccount.updatePreBalance(this.preForm);
        if (resp.errorCode == 0) {
            this.interaction.showSuccess('昨日权益已保存');
            this.closePreBalance();
            this.requestAccounts();
        } 
        else {
            this.interaction.showError('保存昨日权益发生异常：' + resp.errorMsg);
        }
    }

    async closePreBalance() {

        this.preDialog.visible = false;
        this.clearWorkingAccount();
        await this.helper.sleep(200);
        Object.keys(this.preForm).forEach((key) => {
            this.preForm[key] = '';
        });
    }

    listen2Events() {
        
        this.registerEvent(this.systemEvent.tabActivated, this._handleActivation.bind(this));
        this.renderProcess.on('deposite-withdraw-on-account', this._handlePotentialFactorChange.bind(this));
        this.renderProcess.on('adjust-position-on-account', this._handlePotentialFactorChange.bind(this));
    }

    async build($container, view_option) {

        super.build($container);
        this.isSuperAdmin = this.userInfo.isSuperAdmin;
        this.isBrokerAdmin = this.userInfo.isBrokerAdmin;
        this.isOrgAdmin = this.userInfo.isOrgAdmin;
        this.isTopAdmin = this.isSuperAdmin || this.isBrokerAdmin;
        this.isOrgFixed = !this.isTopAdmin;
        this.hasSummary = !this.isTopAdmin;

        if (!this.hasSummary) {

            const $innerRoot = this.$container.firstElementChild;
            $innerRoot.classList.remove('xsplitter');
            $innerRoot.classList.add('s-full-height');

            const $upper = this.$container.querySelector('.part-upper');
            $upper.classList.add('no-summary');
            $upper.style.height = '100%';
        }

        this.createToolbarApp();
        this.setupTable();
        this.setupDynamicPart();
        this.listen2Events();
        this.requestAccounts();
    }
}

module.exports = Controller;
