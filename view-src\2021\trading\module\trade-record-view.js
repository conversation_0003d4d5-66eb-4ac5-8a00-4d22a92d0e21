const IView = require('../../../../component/iview').IView;
const SmartTable = require('../../../../libs/table/smart-table').SmartTable;
const { TradeChannel } = require('../../model/message');
const { ModelConverter } = require('../../../../model/model-converter');
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');

class TradeRecordView extends IView {

    /**
     * 交易数据格式转换器
     */
    static get ModelConverter() {
        return ModelConverter;
    }

    get isSpot() {
        return this.channel && this.channel.options.isSpot;
    }

    get isFuture() {
        return this.channel && this.channel.options.isFuture;
    }

    get isOption() {
        return this.channel && this.channel.options.isOption;
    }

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        /** 拼音样本字典 */
        this.pinyinMap = {};

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };

        /** 当前交易渠道，所支持的所有资产类型，字典表（用于快速检索目的） */
        this.assetTypeMap = {};
    }

    createToolbarApp() {
        throw new Error('not implemented');
    }

    /**
     * 查询首屏数据
     */
    async queryFirstScreen() {
        throw new Error('not implemented');
    }

    /**
     * 查询全量数据
     */
    async queryAll() {
        throw new Error('not implemented');
    }
    
    /**
     * 订阅实时交易数据
     */
    subChange() {
        throw new Error('not implemented');
    }

    /**
     * 退订实时交易数据
     */
    unsubChange() {
        throw new Error('not implemented');
    }

    /**
     * 监听实时交易数据推送
     */
    listen2DataChange() {
        throw new Error('not implemented');
    }

    filterByChannel() {
        throw new Error('not implemented');
    }

    identifyRecord(record) {
        return record.id;
    }

    createFooterRowApp() {
        
        new Vue({

            el: this.$container.querySelector('.user-footer'),
            data: {
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    createTable(tableName) {

        var $table = this.$container.querySelector('.data-list');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: tableName,
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
            rowDbClicked: this.handleRowDbClick.bind(this),
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        this.tableObj = tableObj;
    }

    listen2Events() {

        /** 监听交易渠道切换 */
        this.registerEvent('set-channel', this.setAsChannel.bind(this));
        /** 监听设置表格高度指令 */
        this.registerEvent('table-max-height', this.setTableHeight.bind(this));
        /** 监听表格滚动至左端 */
        this.registerEvent('table-scroll-2-left', this.scroll2Left.bind(this));
        /** 监听TAB激活 */
        this.registerEvent(this.systemEvent.tabActivated, this.handleActivated.bind(this));
        /** 监听交易服务器获得重连 */
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, this.handleConnectionRecovered.bind(this));
    }

    /**
     * 处理交易服务器连接重新恢复事件
     */
    handleConnectionRecovered() {
        // todo21: to request full records again
    }

    /**
     * 消耗完整的服务器推送数据
     * @param {Array<String>} titles 标题文字序列
     * @param {Array<Array>} contents 内容数组
     * @param {Number} totalSize 首屏数据时，通过该参数返回总数据量
     */
    consumeBatchPush(titles, contents, totalSize) {

        this.paging.page = 1;
        this.paging.total = totalSize >= 0 ? totalSize : contents.length;

        /**
         * 子类需扩充该方法，实现各自的本地化功能
         */
    }

    /**
     * 判定某个资产类型，是否可出现在当前交易渠道
     */
    isRecordAssetQualified(assetType) {
        return this.assetTypeMap[assetType] === true;
    }

    handleChannelChange() {

        this.hasChannelChanged = false;

        /**
         * 渠道产生切换后，仅当表格包含数据时，作数据过滤（后续列表装载数据时，会参考当前渠道进行自然过滤）
         */

        if (this.tableObj.rowCount > 0) {

            this.filterByChannel();
            this.tableObj.scroll2Top(0);
        }
    }

    /**
     * @param {TradeChannel} channel 
     */
    setAsChannel(channel) {

        if (this.channel && this.channel.code === channel.code) {
            
            console.error('channel no change');
            return;
        }

        /** 当前处于的交易渠道 */
        this.channel = channel;
        /** 是否交易渠道产生了切换 */
        this.hasChannelChanged = true;
        this.helper.clearHash(this.assetTypeMap);
        channel.supporteds.forEach(assetCode => { this.assetTypeMap[assetCode] = true; });
        this.handleChannelChange();
    }

    /**
     * 设置表格最大高度
     * @param {*} height 
     */
    setTableHeight(height) {
        this.tableObj.setMaxHeight(height);
    }

    /**
     * 表格滚动至左端
     */
    scroll2Left() {
        this.tableObj.scroll2Left(0);
    }

    /**
     * 处理tab获得焦点事件
     */
    handleActivated(tab) {

        this.tableObj.fitColumnWidth();
        if (this.hasChannelChanged) {
            this.handleChannelChange();
        }
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize, true);
        this.tableObj.setPageIndex(1);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    handleRowDbClick(record) {
        //
    }

    /**
     * 测试样本内容对应的拼音是否能被关键字命中
     * @param {String} sample 目标字串样本
     * @param {String} keywords 关键字
     */
    testPy(sample, keywords) {

        let matched_py = this.pinyinMap[sample];
        if (matched_py === undefined) {
            matched_py = this.pinyinMap[sample] = this.helper.pinyin(sample);
        }

        return typeof keywords == 'string' && keywords.length >= 1 && matched_py.indexOf(keywords) >= 0;
    }

    rebindOrderStatus(records, propName) {
        return this.makeFilters(records, propName, this.systemEnum.orderStatus);
    }
    
    rebindDirection(records, propName) {
        return this.makeFilters(records, propName, this.systemTrdEnum.tradingDirection);
    }

    rebindBusinessFlag(records, propName) {
        return this.makeFilters(records, propName, this.systemTrdEnum.businessFlag);
    }
    
    rebindAssetType(records, propName) {
        return this.makeFilters(records, propName, this.systemEnum.assetsType);
    }
    
    rebindYesNo(records, propName) {
        return this.makeFilters(records, propName, this.systemEnum.yesNo);
    }

    makeFilters(records, propName, translators) {
        return SmartTable.MakeColFilters(records, propName, { translators: translators });
    }

    refresh() {
        
        this.interaction.showSuccess('刷新请求，已发出');
        this.reloadRecords();
    }

    reloadRecords() {
        
        this.resetControls();
        this.turn2Request();
    }

    /** 重置控件状态 */
    resetControls() {
        this.tableObj.removeAllColFilters();
    }

    async turn2Request() {

        await this.quickRequest();
        this.batchRequest();
    }

    /**
     * 快速获取首页数据
     */
    async quickRequest() {

        var resp = await this.queryFirstScreen();
        if (resp.errorCode != 0) {
            this.interaction.showError(`交易数据获取错误 > ${resp.errorMsg}`);
        }
        else {

            let bean = resp.data;
            let contents = bean.contents;
            /** 首行数据，为标题栏 */
            let titles = (contents || []).shift();
            this.consumeBatchPush(titles, contents || [], bean.totalSize);
        }
    }

    /**
     * 异步获取全量数据
     */
    async batchRequest() {

        let resp = await this.queryAll();
        if (resp.errorCode != 0) {
            this.interaction.showError(`交易数据获取错误 > ${resp.errorMsg}`);
        }
        else {

            /**
             * 对于标题字串数组
             * 1. 仅当存在至少1条数据时，后端才给出具体的标题信息
             * 2. 如果没有数据，则标题信息也不存在
             */

            let records = resp.data;
            if (records.length == 0) {
                return;
            }

            let titles = records.shift();
            this.consumeBatchPush(titles, records);
        }
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${date}-${time}`);
    }

    showLoading(message) {

        this.hideLoading();
        this._$loading = this.interaction.showLoading({ text: message || '正在加载交易数据' });
        this._closeTimer = setTimeout(() => { this.hideLoading(); }, 1000 * 20);
    }

    hideLoading() {

        if (this._$loading !== undefined) {

            this._$loading.close();
            delete this._$loading;
        }

        if (this._closeTimer !== undefined) {

            clearTimeout(this._closeTimer);
            delete this._closeTimer;
        }
    }

    dispose() {
        this.unsubChange();
    }

    build($container, tableName) {

        super.build($container);
        this.createToolbarApp();
        this.createFooterRowApp();
        this.helper.extend(this, ColumnCommonFunc);
        this.createTable(tableName);
        this.listen2Events();
        this.listen2DataChange();
    }
}

module.exports = { TradeRecordView };