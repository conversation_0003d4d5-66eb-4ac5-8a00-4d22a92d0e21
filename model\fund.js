class SimpleAccountInfo {

    constructor({ accountId, accountName, assetType, financeAccountName }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.assetType = assetType;
        this.financeAccountName = financeAccountName;
    }
}

class SimpleUserInfo {

    constructor({ userId, userName, shareType }) {

        this.userId = userId;
        this.userName = userName;
        this.shareType = shareType;
    }
}

class FundInfo {

    constructor({
        amacCode,
        basisReference,
        closedFlag,
        establishedDay,
        fundManager,
        fundName,
        fundOrganization,
        fundType,
        id,
        nav,
        orgId,
        orgName,
        reportTemplates,
        riskEnable,
        strategyType,
        valuation,
        users = [new SimpleUserInfo({})].splice(1),
        accounts = [new SimpleAccountInfo({})].splice(1),
    }) {
        this.amacCode = amacCode;
        this.basisReference = basisReference;
        this.closedFlag = closedFlag;
        this.establishedDay = establishedDay;
        this.fundManager = fundManager;
        this.fundName = fundName;
        this.fundOrganization = fundOrganization;
        this.fundType = fundType;
        this.id = id;
        this.nav = nav;
        this.orgId = orgId;
        this.orgName = orgName;
        this.reportTemplates = reportTemplates;
        this.riskEnable = riskEnable;
        this.strategyType = strategyType;
        this.valuation = valuation;
        this.users = users;
        this.accounts = accounts;
    }
}

module.exports = {
    
    SimpleAccountInfo,
    SimpleUserInfo,
    FundInfo,
};