import * as XLSX from 'xlsx';

export interface UploadSheetData {
  sheet: string;
  matrix: string[][];
}

export interface UploadExcelFileData {
  fileName: string;
  sheets: UploadSheetData[];
}

/**
 * 读取excel表格数据
 */
export async function readExcel(file: File): Promise<{ sheet: string; matrix: string[][] }[]> {
  const arrayBuffer = await file.arrayBuffer();
  const workbook = XLSX.read(arrayBuffer);
  const sheets = workbook.SheetNames;
  const data = sheets.map(sheet_name => {
    const worksheet = workbook.Sheets[sheet_name];
    const matrix = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];
    return { sheet: sheet_name, matrix };
  });

  return data;
}
