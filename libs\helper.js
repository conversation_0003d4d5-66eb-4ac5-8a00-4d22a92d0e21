﻿const randtoken = require('rand-token');
const Regex = {

    all_cn: /^[\u4E00-\u9FA5]+$/,
    has_cn: /[\u4E00-\u9FA5]+/,
    no_cn: /^[a-zA-Z0-9~!@#$%^&*()-_=+\[\]\\<>,;"'./\|]+$/,
    all_en: /^[a-zA-Z]+$/,
    all_digit: /^[0-9]+$/,
};

/**
 * common helpers concentrated on data computing
 */

function isJson(obj) {

    var class2type = {};
    var toString = class2type.toString;
    var hasOwn = class2type.hasOwnProperty;

    if (!obj || toString.call(obj) !== '[object Object]') {
        return false;
    }

    var proto = Object.getPrototypeOf(obj);

    // Objects with no prototype (e.g., `Object.create( null )`) are plain
    if (!proto) {
        return true;
    }

    // Objects with prototype are plain if they were constructed by a global Object function
    var ctor = hasOwn.call(proto, 'constructor') && proto.constructor;
    /*
        make a tolerance on deciding if a function instance can be a [json]
    */
    return typeof ctor === 'function';
}

function deepClone(obj) {

    if (!isJson(obj) && !(obj instanceof Array)) {
        return obj;
    }

    function do_clone(attachable, obj) {

        if (obj instanceof Array) {

            for (var idx = 0; idx < obj.length; idx++) {

                if (obj[idx] instanceof Array) {
                    do_clone((attachable[idx] = []), obj[idx]);
                } 
                else if (isJson(obj[idx])) {
                    do_clone((attachable[idx] = {}), obj[idx]);
                } 
                else {
                    attachable[idx] = obj[idx];
                }
            }
        } 
        else {

            for (var key in obj) {

                if (obj[key] instanceof Array) {

                    do_clone((attachable[key] = []), obj[key]);
                } 
                else if (isJson(obj[key])) {
                    do_clone((attachable[key] = {}), obj[key]);
                } 
                else {
                    attachable[key] = obj[key];
                }
            }
        }
    }

    var attachable = obj instanceof Array ? [] : {};
    do_clone(attachable, obj);
    return attachable;
}

function extend(args) {

    var arg_len = args.length;
    if (arg_len == 0) {
        return;
    } 
    else if (arg_len == 1) {
        return args[0];
    } 
    else if (args[0] == null || args[0] == undefined) {
        return args[0];
    }

    var last_ele = args[arg_len - 1];
    var has_exclude_keys = arg_len >= 3 && last_ele instanceof Array && last_ele.length > 0;
    var exclude_keys = has_exclude_keys ? last_ele : [];
    var member_count = has_exclude_keys ? arg_len - 1 : arg_len;

    for (let idx = member_count - 1; idx >= 1; idx--) {

        let obj_after = args[idx];
        let obj_before = args[idx - 1];
        if (obj_after == null || obj_after == undefined || typeof obj_after == 'number' || typeof obj_after == 'boolean' || typeof obj_after == 'string') {
            continue;
        }

        if (obj_after instanceof Array) {

            if (obj_before instanceof Array && obj_after.length == obj_before.length) {

                for (let pos = 0; pos < obj_after.length; pos++) {
                    obj_before[pos] = obj_after[pos];
                }
            }
        }
        else {

            for (let key in obj_after) {

                if (has_exclude_keys && exclude_keys.includes(key)) {
                    continue;
                }
                
                obj_before[key] = obj_after[key];
            }
        }
    }

    return args[0];
}

function createArray(length, element) {

    if ('number' != typeof length && parseInt(length) <= 0) {
        throw new Error('指定数组长度必须为正整数');
    }

    for (var t = [], n = 0; n < length; n++) {

        let fillObj = Object.prototype.toString.call(element) === '[object Object]' || element instanceof Array ? deepClone(element) : element;
        t.push(fillObj);
    }
    return t;
}

function readValue(target_obj, key_line, default_value) {

    if (target_obj === null || target_obj === undefined) {
        return default_value;
    }
    var chained_keys = key_line instanceof Array ? key_line : typeof key_line == 'string' ? key_line.split('.') : [key_line];
    if (chained_keys.length == 0) {
        return default_value;
    }
    var val = target_obj[chained_keys.shift()];
    while (chained_keys.length > 0 && val !== null && val !== undefined) {
        val = val[chained_keys.shift()];
    }
    return val;
}

function setValue(target_obj, key_line, value) {

    if (target_obj === null ||
        target_obj === undefined ||
        (!(key_line instanceof Array) && typeof key_line != 'string' && typeof key_line != 'number')) {
        return target_obj;
    }

    var chained_keys = key_line instanceof Array ? 
                       key_line : typeof key_line == 'string' ? 
                       key_line.split('.') : [key_line.toString()];

    if (chained_keys.length == 0) {
        return target_obj;
    }

    var node = target_obj;
    while (chained_keys.length > 0) {

        let node_key = chained_keys.shift();
        let left_len = chained_keys.length;
        if (left_len == 0) {
            node[node_key] = value;
        } 
        else {
            if (node[node_key] === undefined || node[node_key] === null) {
                node[node_key] = {};
            }
            node = node[node_key];
        }
    }

    return target_obj;
}

/**
 * @param {Number} arg1 
 * @param {Number} arg2 
 */
function safeMul(arg1, arg2) {

	var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
    try { m += s1.split('.')[1].length; } catch(ex) {}
    try { m += s2.split('.')[1].length; } catch(ex) {}
    var tmp = Number(s1.replace('.', '')) * Number(s2.replace('.', ''));
    return tmp / Math.pow(10, m);
}

/**
 * @param {Number} arg1 
 * @param {Number} arg2 
 */
function safeDevide(arg1, arg2) {

    if (arg2 == 0) {
        return Infinity;
    }

	var m1 = 0, m2 = 0, s1 = arg1.toString(), s2 = arg2.toString();
    try { m1 += s1.split('.')[1].length; } catch(ex) {}
    try { m2 += s2.split('.')[1].length; } catch(ex) {}
    var m = m1 - m2;
    var absm = Math.abs(m);
    var tmp = Number(s1.replace('.', '')) / Number(s2.replace('.', ''));
    return m == 0 ? tmp : m < 0 ? tmp * Math.pow(10, absm) : tmp / Math.pow(10, absm);
}

const helper = {

    time2String: function (time_input, format_str) {

        if (!time_input) {
            return time_input;
        }

        var time = new Date(time_input);
        if (typeof format_str == 'string' && format_str.trim().length > 0) {
            return time.format(format_str);
        }

        return time.format('yyyy-MM-dd hh:mm:ss');
    },

    /**
     * judge if a given something is a json-like object
     */
    isJson: function (obj) {
        return isJson(obj);
    },

    /**
     * 判断某个值是否可以被认作None
     * @param {*} something
     */
    isNone: function (something) {
        return (something === undefined || something === null || (typeof something == 'string' && something.trim().length == 0));
    },

    /**
     * 判断某个值是否不能被认作None
     * @param {*} something
     */
    isNotNone: function (something) {
        return !this.isNone(something);
    },

    /**
     * create an array with and set each element to be the same with given value
     */
    createArray: function (length, element) {
        return createArray(length, element);
    },

    /**
     * extend object a with object b ( a will change possibly, b has no changes )
     * @parameters: extend(a[,b][,c][,d]...)
     * @direction: from d -> c -> b -> a
     */
    extend: function () {
        return extend(arguments);
    },

    /**
     * get a slice from [arguments] object
     */
    getArgumentsSlice(args, start_idx) {

        if (start_idx >= args.length) {
            return [];
        }
        var slice = [];
        for (let idx = start_idx; idx <= args.length - 1; idx++) {
            slice.push(args[idx]);
        }
        return slice;
    },

    /**
     * softly extend an object <if data members does not exist then create & set a default value>
     */
    extendMembers: function (obj, properties, default_val) {

        if (properties instanceof Array) {
            properties.forEach(prop_name => {
                if (typeof prop_name == 'string' && obj[prop_name] === undefined) {
                    obj[prop_name] = default_val === undefined ? null : default_val;
                }
            });
        }
        return obj;
    },

    /**
     * convert a json<map> values to be an array
     * @param {*} a 
     * @returns {Array}
     */
    dict2Array: function (a) {

        var b, c;
        if (!isJson(a)) return [];
        b = [];
        for (c in a) b.push(a[c]);
        return b;
    },

    /**
     * convert a json<map> keys to be an array
     */
    dictKey2Array: function (a) {
        return !isJson(a) ? [] : Object.keys(a);
    },

    /**
     * convert an array into a json <key/value>
     * you should have the awareness: the given generator should return an unique token for every element of the array
     * @param {Array} arr target data list (required)
     * @param {Function} key_gen (required) a function to generate the key
     * @param {Function} val_gen (optional) a function to generate the value
     */
    array2Dict: function (arr, key_gen, val_gen) {

        if (!(arr instanceof Array) || arr.length == 0) {
            return {};
        }
        var dict = {};
        if (typeof val_gen == 'function') {
            arr.forEach(ele => { dict[key_gen(ele)] = val_gen(ele); });
        }
        else {
            arr.forEach(ele => { dict[key_gen(ele)] = ele; });
        }
        return dict;
    },

    /**
     * to remove all keys of of a json
     */
    clearHash: function (obj) {

        if (isJson(obj)) {
            try {
                for (let key in obj) { delete obj[key]; }
            }
            catch (ex) {
                console.log(ex);
            }
        }
        return obj;
    },

    attachProp: function (obj, properties, default_val) {

        if (this.isJson(obj) && properties instanceof Array) {

            properties.forEach(prop_name => {

                if (typeof prop_name == 'string' && obj[prop_name] === undefined) {
                    obj[prop_name] = default_val === undefined ? null : default_val;
                }
            });
        }
        return obj;
    },

    /**
     * deeply clone an object (both arrary and json are supported)
     */
    deepClone: function (obj) {
        return deepClone(obj);
    },

    /**
     * make a random number between a given range and return
     * @parameters: [lower_limit] and [uppper_limit] must be given and validated, [is_integer] is optional
     */
    makeRandomNum: function (lower_limit, uppper_limit, is_integer) {
        var rd = Math.random() * (uppper_limit - lower_limit + 1) + lower_limit;
        return !!is_integer ? parseInt(rd) : rd;
    },

    /**
     * make a token
     */
    makeToken: function (with_prefix = false) {
        return (with_prefix === true ? 'tk' : '') + randtoken.generate(16);
    },

    /**
     * convert a camel case naming to hyphen case
     * @param {String} naming
     */
    convertCamel2Hyphen: function (naming) {
        return typeof naming != 'string' ? naming : naming.replace(/([A-Z])/g, '-$1').toLowerCase();
    },

    /**
     * read a value by chained keys from an object
     */
    readValue: function (target_obj, key_line, default_value) {
        return readValue(target_obj, key_line, default_value);
    },

    /**
     * set a value to an object with given key & value
     */
    setValue: function (target_obj, key_line, value) {
        return setValue(target_obj, key_line, value);
    },

    deleteKey: function (target_obj, key_line) {
        return setValue(target_obj, key_line, undefined);
    },

    /**
     * to fake a group of methods by an instance of a class
     * @param {Object} target_object
     * @param {Array<String>|Array<Function>} methods
     */
    fakeVueInsMethod: function (target_object, methods) {

        if (!target_object || typeof target_object != 'object' || !(methods instanceof Array) || methods.length == 0) {
            return {};
        }

        var method_map = {};
        var add2Map = function (the_method) {

            if (typeof the_method == 'function') {

                method_map[the_method.name] = function () {
                    return the_method.call(target_object, ...arguments);
                };
            } 
            else if (typeof the_method == 'string') {

                let trimed_name = the_method.trim();
                if (typeof target_object[trimed_name] == 'function') {

                    method_map[trimed_name] = function () {
                        return target_object[trimed_name].call(target_object, ...arguments);
                    };
                }
            }
        };

        for (let idx = 0; idx < methods.length; idx++) {
            add2Map(methods[idx]);
        }
        return method_map;
    },

    sleep: function (delay_ms) {
        return new Promise(resolve => { setTimeout(() => { resolve(); }, delay_ms); });
    },

    string2Date: function (str) {

        let _year = str.substr(0, 4);
        let _month = str.substr(4, 2);
        let _date = str.substr(6, 2);
        return new Date([_year, _month, _date].join('-'));
    },

    readKey: function (target, key) {

        if (!target) {
            return null;
        }
        if (!key) {
            throw `[this function should provide explicit key]`;
        }
        let keys = key.split('.');
        if (keys.length === 1 && target[key] === undefined) {
            return null;
        }
        let result = target;
        try {
            keys.forEach(this_key => {
                result = result[this_key];
            });
        } 
        catch (e) {
            // console.warn('[you want to get a not exist property]');
            result = null;
        }
        return result;
    },

    /**
     * 
     * @param {String} original_content 
     */
    aesEncrypt: function (original_content) {

        if (typeof original_content != 'string' || original_content.trim().length == 0) {
            return '';
        }

        var sn = 'DaBingGe';
        var result = '';
        var snNum = [];

        for (let i = 0, j = 0; i < original_content.length; i++ , j++) {
            if (j == sn.length) {
                j = 0;
            }
            snNum.push(original_content.charCodeAt(i) ^ sn.charCodeAt(j));
        }

        for (let k = 0; k < original_content.length; k++) {

            if (snNum[k] < 10) {
                result += "00" + snNum[k];
            }
            else {

                if (snNum[k] < 100) {
                    result += "0" + snNum[k];
                }
                else {
                    result += snNum[k];
                }
            }

        }

        return result;
    },

    /**
     * 获取某个字符串内容的拼音
     * @param {String} content 字符串内容
     * @param {Boolean} only_first 是否仅首字母（默认true） 
     */
    pinyin: function (content, only_first = true) {

        if (typeof content != 'string') {
            return '';
        }

        if (this.libPyConverter === undefined) {
            this.libPyConverter = require('./3rd/pinyin-converter').ConvertPinyin;
        }

        return this.libPyConverter(content, !!only_first).toLowerCase();
    },

    /**
     * 值比较
     * @param {*} asc 默认升序
     * @returns a大于b返回1；a小于b返回-1；相等返回0
     */
    compare(a, b, asc = true) {

        const bigger = 1, same = 0, smaller = -1;
        const flag = asc ? 1 : -1;
        let result = same;

        if (a === b) {
            return same;
        }

        let _1st = {

            isempty: a === undefined || a === null || typeof a == 'string' && a.trim().length == 0,
            isnum: typeof a == 'number',
            isstr: typeof a == 'string',
        };

        let _2nd = {

            isempty: b === undefined || b === null || typeof b == 'string' && b.trim().length == 0,
            isnum: typeof b == 'number',
            isstr: typeof b == 'string',
        };

        /**
         * @returns {String}
         */
        function tostr(value) {
            return value.toString();
        }

        /**
         * @param {String} value 
         */
        function is_cn(value) {
            return Regex.all_cn.test(value);
        }

        if (_1st.isempty) {
            result = _2nd.isempty ? same : smaller;
        }
        else if (_1st.isnum) {

            if (_2nd.isempty) {
                result = bigger;
            }
            else if (_2nd.isnum) {
                result = a > b ? bigger : a < b ? smaller : same;
            }
            else if (_2nd.isstr) {
                result = tostr(a).localeCompare(b);
            }
        }
        else if (_1st.isstr) {

            if (_2nd.isempty) {
                result = bigger;
            }
            else if (_2nd.isnum) {
                result = a.localeCompare(tostr(b));
            }
            else if (_2nd.isstr) {

                let len_a = a.length;
                let len_b = b.length;
                let shorter = Math.min(len_a, len_b);
                for (let cur = 0; cur < shorter; cur++) {

                    let c1 = a[cur];
                    let c2 = b[cur];
                    if (c1 != c2) {
                        
                        if (is_cn(c1)) {
                            
                            if (is_cn(c2)) {
                                result = c1.localeCompare(c2, 'zh-CN');
                            }
                            else {
                                result = bigger;
                            }
                        }
                        else {
                            
                            if (is_cn(c2)) {
                                result = smaller;
                            }
                            else {
                                result = c1.localeCompare(c2);
                            }
                        }

                        break;
                    }
                }

                if (result == same) {
                    result = len_a > len_b ? bigger : len_a < len_b ? smaller : same;
                }
            }
        }

        return result * flag;
    },

    unicode: function (content) {
        return typeof content != 'string' ? content : escape(content).replace(/\%u/g, '\\u');
    },

    ununicode: function (content) {
        return typeof content != 'string' ? content : unescape(content.replace(/\\u/g, '%u'));
    },

    /**
     * 
     * @param {String} encrypted_content 
     */
    aesDecrypt: function (encrypted_content) {

        if (typeof encrypted_content != 'string' || encrypted_content.trim().length == 0) {
            return '';
        }

        var sn = "DaBingGe";
        var snNum = [];
        var result = "";

        for (let i = 0; i < encrypted_content.length; i += 3) {
            snNum.push(parseInt(encrypted_content.substr(i, 3)));
        }

        for (let i = 0, j = 0; i < snNum.length; i++ , j++) {
            if (j == sn.length) {
                j = 0;
            }
            result += String.fromCharCode(snNum[i] ^ sn.charCodeAt(j));
        }
        return result;
    },

    /**
     * 安全乘法
     * @param {Number} arg1 
     * @param {Number} arg2 
     */
    safeMul: function(arg1, arg2) {
        return typeof arg1 == 'number' && typeof arg2 == 'number' ? safeMul(arg1, arg2) : 0;
    },

    /**
     * 安全除法
     * @param {Number} arg1 
     * @param {Number} arg2 
     */
    safeDevide: function(arg1, arg2) {
        return typeof arg1 == 'number' && typeof arg2 == 'number' ? safeDevide(arg1, arg2) : 0;
    },

    simplifyAmount: function(amount) {
        return amount >= 100000000 ? `${+(amount / 100000000).toFixed(2)}亿` : `${+(amount / 10000).toFixed(1)}万`;
    },

    /**
     * convert a matrix of dataset to an array of json object
     * @param {Array<string>} props 
     * @param {Array<Array<any>>} matrix 
     */
    convertMatrix2Json(props, matrix) {
        
        let rows = [];
        if (matrix instanceof Array && matrix.length > 0) {
            rows = matrix.map(eles => {
                let rowd = {};
                props.forEach((prop_name, col_idx) => { rowd[prop_name] = eles[col_idx]; });
                return rowd;
            });
        }
        return rows;
    },

    /**
     * 充值一个json的所有键值，将跳过对象值
     * @param {*} map 
     * @param {*} value 应为值类型
     */
    resetMap(map, value) {

        if (this.isJson(map)) {
            for (let key in map) {
                if (typeof map[key] != 'object' || map[key] === null) {
                    map[key] = value;
                }
            }
        }
    },

    /**
     * 创建一个安全的定时器，防止回调函数被过载执行
     * @param {number} interval 定时间隔
     * @param {Function} callback 定时回调函数
     */
    safeSetInterval(interval, callback) {

        let isRunning = false; // 标记是否正在执行回调

        const intervalId = setInterval(() => {

            if (!isRunning) {

                isRunning = true;
                Promise.resolve(callback()).finally(() => { isRunning = false; });
            }
        }, interval);

        return intervalId;
    },
};

module.exports = { helper };
