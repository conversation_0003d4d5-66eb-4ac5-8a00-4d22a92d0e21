class HttpTickData {

    constructor({ 
        askPrice, 
        askVolume, 
        avgAskPrice, 
        bidPrice, 
        bidVolume, 
        highPrice, 
        instrument, 
        lastPrice, 
        lowPrice, 
        lowerLimitPrice, 
        openPrice, 
        preClosePrice, 
        totalAskVolume, 
        turnover, 
        updateTime, 
        upperLimitPrice, 
        volume,
    }) {
        
        this.askPrice = askPrice ;
        this.askVolume = askVolume ;
        this.avgAskPrice = avgAskPrice ;
        this.bidPrice = bidPrice ;
        this.bidVolume = bidVolume ;
        this.highPrice = highPrice ;
        this.instrument = instrument ;
        this.lastPrice = lastPrice ;
        this.lowPrice = lowPrice ;
        this.lowerLimitPrice = lowerLimitPrice ;
        this.openPrice = openPrice ;
        this.preClosePrice = preClosePrice ;
        this.totalAskVolume = totalAskVolume ;
        this.turnover = turnover ;
        this.updateTime = updateTime ;
        this.upperLimitPrice = upperLimitPrice ;
        this.volume = volume ;
    }
}

module.exports = {
    HttpTickData,
};