{
  "compilerOptions": {
    "target": "ES6",
    "module": "ESNext",
    "lib": ["es6", "dom"],
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false
  },

  // 包含的文件
  "include": ["src/**/*"],
  // 排除的文件
  "exclude": ["node_modules", "dist"]
}
