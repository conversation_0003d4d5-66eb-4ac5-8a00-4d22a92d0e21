﻿/*
    logging module
*/

const electron = require('electron');
const { isRenderProcess } = require('../config/environment');
const app = isRenderProcess ? require('@electron/remote').app : electron.app;
const encryptLogAllowed = app.encryptionOptions.encryptLog;
const logBaseName = process.platform == 'darwin' ? '/tmp/logs/log' : 'logs/log';
const log4js = require('log4js');
const { logLevel } = require('../config/environment');
const encrypter = require('./encrypter').encrypter;

var now = new Date();
var year = now.getFullYear();
var month = now.getMonth() + 1;
var date = now.getDate();
var salt = `logkey1973${year}${month < 10 ? '0' + month : month}${date < 10 ? '0' + date : date}`;

function extendLogger(logger_ins) {

    function extend(method_name) {

        var revised_name = `__${method_name}__`;
        logger_ins[revised_name] = logger_ins[method_name];
        logger_ins[method_name] = function (message, ...args) {
            logger_ins[revised_name](encrypter.encrypt(message, salt), ...args);
        };
    }

    extend('trace');
    extend('info');
    extend('debug');
    extend('warn');
    extend('error');
    extend('fatal');

    return logger_ins;
}

log4js.configure({

    appenders: {

        default: { type: 'console' },
        appender_sys: { type: 'dateFile', filename: logBaseName, pattern: '-yyyy-MM-dd.log', alwaysIncludePattern: true },
        appender_trd: { type: 'dateFile', filename: logBaseName, pattern: '-yyyy-MM-dd.log', alwaysIncludePattern: true },
        appender_http: { type: 'dateFile', filename: logBaseName, pattern: '-yyyy-MM-dd-http.log', alwaysIncludePattern: true },
    },

    categories: {

        default: { appenders: ['default'], level: logLevel },
        sys: { appenders: ['appender_sys'], level: logLevel },
        trd: { appenders: ['appender_trd'], level: logLevel },
        http: { appenders: ['appender_http'], level: logLevel },
    }
});

function getConsoleLogger() {
    return encryptLogAllowed ? extendLogger(log4js.getLogger('default')) : log4js.getLogger('default');
}

function getSystemLogger() {
    return encryptLogAllowed ? extendLogger(log4js.getLogger('sys')) : log4js.getLogger('sys');
}

function getTradingLogger() {
    return encryptLogAllowed ? extendLogger(log4js.getLogger('trd')) : log4js.getLogger('trd');
}

function getHttpLogger() {
    return log4js.getLogger('http');
}

function formPerformanceLogContent(topic, task, given_time) {
    return `\txh3nsdyUWNYF6e269JVFHUJW52ksnslko023u-${topic}\t${task}\t${given_time || new Date().getTime()}`;
}

module.exports = {

    getConsoleLogger,
    getSystemLogger, 
    getTradingLogger, 
    getHttpLogger, 
    formPerformanceLogContent,
};
