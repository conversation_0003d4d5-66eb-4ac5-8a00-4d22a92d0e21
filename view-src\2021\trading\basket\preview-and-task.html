<div class="basket-preview-and-task">

    <!-- 预览与任务切换 -->

    <div class="tab-row">
        <el-radio-group v-model="states.tab" @change="tabChanged">
            <el-radio-button :label="tabs.preview.value">{{ tabs.preview.label }}</el-radio-button>
            <el-radio-button :label="tabs.task.value">{{ tabs.task.label }}</el-radio-button>
        </el-radio-group>
		<el-button v-if="isTask()" type="primary" size="small" @click="refreshTask">
			<i class="el-icon-refresh"></i>
			<span>刷新任务</span>
		</el-button>
    </div>
	
	<!-- 篮子母单平铺 -->

    <div class="mother-list">
        <table>
			<tr>
				<th label="母任务ID" width="100" prop="taskId" sortable overflowt></th>
				<th label="账号" width="200" prop="accountName" formatter="formatAccountName" sortable overflowt></th>
				<th label="算法" width="160" prop="algorithmName" sortable overflowt></th>
				<th label="运行状态" width="100" prop="status" formatter="formatMotherStatus" sortable overflowt></th>
				<th label="母单进度" width="100" watch="totalTraded,totalTarget" formatter="formatMotherProgress" align="right" sortable overflowt></th>
				<th label="分配资金" width="100" prop="algoParam" formatter="formatMoney" align="right" sortable overflowt></th>
				<th label="创建时间" width="100" prop="createTime" formatter="formatTime" sortable overflowt></th>
				<th label="开始时间" width="100" prop="effectiveTime" formatter="formatTime" sortable overflowt></th>
				<th label="结束时间" width="100" prop="expireTime" formatter="formatTime" sortable overflowt></th>
				<th label="操作" width="150" watch="status,totalTraded,totalTarget" formatter="formatMotherActions"></th>
			</tr>
		</table>
    </div>

    <!-- 篮子子单平铺 -->

    <div class="child-list">
        <table>
			<tr>
				<th label="子任务ID" width="100" prop="id" sortable overflowt></th>
                <th label="账号" width="200" prop="accountName" formatter="formatAccountName" sortable overflowt></th>
				<th label="算法" width="160" prop="algorithmName" sortable overflowt></th>
				<th label="代码/名称" width="140" prop="instrument" formatter="formatCodeName" sortable overflowt></th>
                <th label="方向" width="70" prop="direction" formatter="formatDirection" sortable></th>
				<th label="委托数量" width="80" prop="volume" sortable></th>
				<th label="进度" width="80" watch="tradedVolume,volume" formatter="formatChildProgress" sortable></th>
				<th label="成交均价" width="90" prop="tradePrice" align="right" precision="2" sortable></th>
				<!-- <th label="市场均价" width="90" prop="marketPrice" align="right" precision="2" sortable></th> -->
				<th label="已成交额" width="90" prop="tradeAmount" align="right" precision="2" sortable thousands></th>
				<!-- <th label="撤单率" width="90" prop="cancelRate" align="right" percentage by100 sortable></th> -->
				<th label="未成交量" width="90" prop="pendingVolume" align="right" sortable thousands-int></th>
				<th label="错误信息" width="200" prop="remark" sortable overflowt></th>
				<th label="操作" width="80" prop="algorithmStatus" fixed="right" formatter="formatChildActions"></th>
			</tr>
		</table>
    </div>
</div>