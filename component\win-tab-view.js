
const BaseWindow = require('./base-window').BaseWindow;
const TabList = require('./tab-list').TabList;
const Routing = require('../model/routing').Routing;

class WinTabView extends BaseWindow {
    
    constructor() {

        super('@win-tab-view', true);
        this.$navi = document.querySelector('.win-top-tabs');
        this.$content = document.querySelector('.win-top-content');
        this.$content.classList.add('s-scroll-bar');
    }

    createWinHeaderApp() {

        this.headerApp = new Vue({

            el: document.querySelector('.layout-header'),
            data: this.winStates,
            methods: this.helper.fakeVueInsMethod(this, [this.minimize, this.maximize, this.unmaximize, this.close]),
        });
    }

    setupTopTabView() {

        this.viewHost = new TabList({

            $navi: this.$navi, 
            $content: this.$content,
        });
    }

    handleViewRouting() {

        if (this.thisWindow.isVisible()) {
            this.try2RecoverViews();
        }
    }

    try2RecoverViews() {
        
        if (this.helper.isNone(window.name)) {
            return;
        }

        var openStates = JSON.parse(window.name);
        var { openedViews, windowOption } = openStates;
        
        if (openedViews instanceof Array && openedViews.length > 0) {
            openedViews.forEach(the_view => { this.addNewView(windowOption, the_view.routing, the_view.title, true); });
        }
    }

    addNewView(win_options, routing_struc, title, is_from_recover) {

        var routing = Routing.duplicate(routing_struc);
        if (win_options.maximizable === false || win_options.resizable === false) {

            this.winStates.showMaximize = false;
            this.winStates.showRestore = false;
        }

        this.viewHost.openTab(true, routing.name, title);
        if (!is_from_recover) {
            this.recordOpenedViews(win_options, routing_struc, title);
        }
    }

    recordOpenedViews(win_options, routing_struc, title) {

        var history = window.name ? JSON.parse(window.name) : { windowOption: null, openedViews: [] };
        history.windowOption = win_options;
        history.openedViews.push({ routing: routing_struc, title: title });
        window.name = JSON.stringify(history);
    }

    listen2Events() {

        // 添加新的tab
        this.renderProcess.on('add-first-tab', (event, routing_struc, title, win_options) => {
            this.addNewView(win_options, routing_struc, title, false); 
        });
    }

    build() {
        
        this.brocastReady();
        this.createWinHeaderApp();
        this.setupTopTabView();
        this.listen2Events();
    }
}

module.exports = WinTabView;