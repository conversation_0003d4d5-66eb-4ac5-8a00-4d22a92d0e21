<div class="config-area">
    <el-form class="config-form" :model="form" label-width="100px" @submit.native.prevent>
        <template v-if="!currentItem">
            <el-form-item label="模板名称">
                <el-input v-model.trim="form.templateName" placeholder="请输入模板名称"></el-input>
            </el-form-item>
            <el-form-item label="定时刷新">
                <el-input-number class="interval-input" v-model="form.interval" :min="0" :step="1"></el-input-number> 秒
            </el-form-item>
            <div class="desp">
                注: 为0时不启用定时刷新
            </div>
        </template>
        <template v-if="currentItem">
            <el-form-item label="小数位">
                <el-select v-model="currentItem.float">
                    <el-option v-for="float in floatList" :key="float.value" :label="float.label" :value="float.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="百分比">
                    <el-checkbox v-model="currentItem.percent"></el-checkbox>
            </el-form-item>
            <template v-if="multi">
                <el-form-item label="控件类型">
                    <el-radio-group v-model="currentItem.widget">
                        <el-radio label="chart">图表</el-radio>
                        <el-radio label="table">表格</el-radio>
                    </el-radio-group>
                </el-form-item>
                <template v-if="currentItem.widget == 'chart'">
                    <!-- <el-form-item label="图表高度">
                        <el-input-number :min="200" :max="600" :step="5" v-model="currentItem.height"></el-input-number>
                    </el-form-item> -->
                    <el-form-item label="默认图表类型">
                        <el-select v-model="currentItem.type">
                            <el-option v-for="type in types" :key="type.value" :value="type.value" :label="type.label"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="y轴名称">
                        <el-input v-model.trim="currentItem.yAxisName" placeholder="非必填"></el-input>
                    </el-form-item>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item v-if="currentItem.type == 'line' || currentItem.type == 'area'" label="平滑曲线">
                                <el-checkbox v-model="currentItem.smooth"></el-checkbox>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="堆叠">
                                <el-checkbox v-model="currentItem.stack"></el-checkbox>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </template>
                <template v-if="currentItem.widget == 'table'">
                    <el-form-item label="转置表格">
                        <el-checkbox v-model="currentItem.inverse"></el-checkbox>
                    </el-form-item>
                </template>
            </template>
        </template>
    </el-form>
    <div v-if="deleting" class="delete-area iconfont"></div>
</div>