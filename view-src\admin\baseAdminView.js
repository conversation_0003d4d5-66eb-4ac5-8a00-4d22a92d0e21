const remote = require('@electron/remote');
const IView = require('../../component/iview').IView;

class BaseAdminView extends IView {
    constructor(view_name, title) {
        super(view_name, true, title);
        this.listen2Event();
    }

    throttle(method) {
        clearTimeout(method.tId);
        method.tId = setTimeout(() => {
            typeof method === 'function' ? method() : null;
        }, 100);
    }

    resizeWindow() {
        console.warn('recommend to implement [resizeWindow] to improve user experience');
    }

    listen2Event() {
        window.onresize = () => {
            this.throttle(this.resizeWindow.bind(this));
        };
    }

    openWinRskSetting({ type, identity, name, action }) {
        let args = { type, identity, name, action: action || 'identity' };
        let title = '风控设置';

        if (this.winRsk && !this.winRsk.isDestroyed()) {
            this.winRsk.show();
            this.winRsk.webContents.send('set-context-data', args);
            return;
        }

        var window_options = { width: 940, height: 620, minimizable: false, maximizable: false, highlight: true };
        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, win_id) => {
            this.winRsk = remote.BrowserWindow.fromId(win_id);
            this.winRsk.on('closed', () => { this.winRsk = null; });
            setTimeout(() => { this.winRsk.webContents.send('set-context-data', args); }, 1000);
        });

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@admin/risk-dialog', title, window_options);
    }
}

module.exports = { BaseAdminView };
