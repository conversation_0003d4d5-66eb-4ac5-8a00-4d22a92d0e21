const axios = require('axios').default;
const path = require('path');
module.exports = class BaseComponent {
    constructor(dir) {
        this.dir = dir;
    }

    async getTemplate() {
        return await axios.get(path.resolve(this.dir, './index.html'));
    }

    async build() {
        let resp = await this.getTemplate();
        return {
            template: resp.data,
            ...this.options,
        };
    }
};
