<script setup lang="ts">
import VirtualizedTable from '../../common/VirtualizedTable.vue';
import { computed, ref, watch } from 'vue';
import type { ColumnDefinition, ProductInfo, RowAction } from '@/types';
import { remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';
import { ElMessage } from 'element-plus';

import {
  Repos,
  TradeClassificationType,
  type TradeClassificationMember,
} from '../../../../../xtrade-sdk/dist';

const { classType, classId, className, least } = defineProps<{
  classType: TradeClassificationType;
  classId: number | null;
  className: string | null;
  // 至少选择多少个
  least?: number;
}>();

const repoInstance = new Repos.ClassificationRepo();
const repoGovenInstance = new Repos.GovernanceRepo();

watch([() => classType, () => classId], () => {
  handleContextChange();
});

const columns: ColumnDefinition<ProductInfo> = [
  { key: 'fundId', title: '产品ID', width: 150, minWidth: 150, sortable: true },
  { key: 'fundName', title: '产品名称', width: 300, minWidth: 300, sortable: true },
  // { key: 'fundManager', title: '产品经理', width: 100, minWidth: 100, sortable: true },
  // { key: 'establishedDay', title: '成立日期', width: 120, sortable: true },
  { key: 'fundOrganization', title: '产品机构', width: 300, minWidth: 300, sortable: true },
];

const rowActions: RowAction<ProductInfo>[] = [
  {
    label: '移除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const containedFundsAsMembers = ref<TradeClassificationMember[]>([]);
const allFunds = ref<ProductInfo[]>([]);
const checkedFunds = ref<ProductInfo[]>([]);
const selectedFundId = ref<string>('');

const availables = computed(() => {
  return allFunds.value.filter(x => !checkedFunds.value.some(y => y.id == x.id));
});

// 删除分类成员
async function deleteRow(row: ProductInfo) {
  const result = await deleteConfirm('从产品联合组移除确认', `是否移除产品： ${row.fundName}？`);
  if (result !== true) {
    return;
  }

  const matched = containedFundsAsMembers.value.find(x => x.memberCode == row.id)!;
  const resp = await repoInstance.deleteTradeClassificationMember(matched.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已移除');
    remove(containedFundsAsMembers.value, x => x.memberCode == row.id);
    remove(checkedFunds.value, x => x === row);
  } else {
    ElMessage.error(`移除失败：${errorCode}/${errorMsg}`);
  }
}

function add2Table() {
  const matched = checkedFunds.value.find(x => x.id == selectedFundId.value);
  if (!matched) {
    const matched2 = allFunds.value.find(x => x.id == selectedFundId.value);
    if (matched2) {
      add2Group(matched2);
    }
  }

  // reset to empty
  selectedFundId.value = '';
}

async function add2Group(row: ProductInfo) {
  const member: TradeClassificationMember = {
    id: null as any,
    classificationId: classId!,
    memberCode: row.id,
    memberName: row.fundName,
    memberType: TradeClassificationType.ProductGroup,
    sortOrder: checkedFunds.value.length + 2,
  };

  const resp = await repoInstance.createTradeClassificationMember(classId!, member);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success(`已保存`);
    await request();
    await requestFunds();
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
}

async function handleContextChange() {
  console.log('product/input props changed', { classType, classId, className, least });
  if (!classId || classType != TradeClassificationType.ProductGroup) {
    containedFundsAsMembers.value = [];
    return;
  }
  await request();
  await requestFunds();
}

async function request() {
  const list = (await repoInstance.getTradeClassificationMembers(classId!)).data || [];
  containedFundsAsMembers.value = list;
}

async function requestFunds() {
  const list = (await repoGovenInstance.QueryProducts()).data || [];
  allFunds.value = list;
  const targetIds = containedFundsAsMembers.value.map(x => x.memberCode);
  checkedFunds.value = list.filter(x => targetIds.some(id => id == x.id));
}

function getSelectedRows() {
  return checkedFunds.value;
}

defineExpose({
  getSelectedRows,
});
</script>

<template>
  <div class="choose-panel" h-full of-y-hidden>
    <VirtualizedTable
      identity="id"
      style="height: 100%"
      :columns="columns"
      :data="checkedFunds"
      :row-actions="rowActions"
      :row-action-width="80"
      :enable-search="false"
      select
      fixed
    >
      <template #actions>
        <div class="waiting-list" flex aic gap-10>
          <el-select
            v-model="selectedFundId"
            @change="add2Table"
            placeholder="请选择要添加的产品"
            style="width: 200px"
            filterable
          >
            <el-option
              v-for="item in availables"
              :key="item.id"
              :label="item.fundName"
              :value="item.id"
            ></el-option>
          </el-select>
          <span class="c-[var(--g-text-color-1)]">剩余可选数量 = {{ availables.length }}</span>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
