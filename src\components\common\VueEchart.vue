<script lang="ts" setup>
import type { AnyObject } from '@/types';
import type { ThemeOption } from 'echarts/types/src/util/types.js';
import { ref } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { SVGRenderer } from 'echarts/renderers';
import { Bar<PERSON><PERSON>, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components';

use([
  SVGRenderer,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);

const { theme, option } = defineProps<{
  theme?: ThemeOption;
  option: AnyObject;
}>();
const emit = defineEmits<{
  highlight: [e: any];
}>();

const chart = ref<InstanceType<typeof VChart> | null>(null);
const root = ref<HTMLElement | null>(null);
const showChart = ref(true);

function handleHighlight(e: any) {
  emit('highlight', e);
}

async function setOption(option: AnyObject, config?: AnyObject) {
  chart.value?.setOption(option, config);
}

function getOption() {
  return chart.value?.getOption();
}

function resize() {
  chart.value?.resize();
}

defineExpose({ setOption, getOption, resize });
</script>
<template>
  <div ref="root" class="echart-wrapper">
    <v-chart
      v-if="showChart"
      @highlight="handleHighlight"
      class="chart"
      :theme="theme"
      ref="chart"
      :option="option"
    />
  </div>
</template>
<style lang="scss" scoped>
.echart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
