﻿/**
 * tick数据服务
 */

const electron = require('electron');
const BrowserWindow = electron.BrowserWindow;
const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;

const protoBuf = require('protobufjs');
const protocol = protoBuf.loadSync(`${__dirname}/component/quotedatastructs.proto`);
const tickBean = protocol.lookupType('proto.TickBean');
const transactionBean = protocol.lookupType('proto.Transaction');
const klineBean = protocol.lookupType('proto.KLineBean');
const TickDataConverter = require('../toolset/tick-data-converter').TickDataConverter;


/**
 * 订阅tick行情的订阅者（一个窗口可对应多个订阅者）
 */
class Suscriber {

	constructor(windowId, viewId, key_instrument, key_tick_type) {

		this.windowId = windowId;
		this.viewId = viewId;
		// 数据冗余（通过冗余数据，对应到数据推送，反查出参与订阅的视图）
		this.keyIns = key_instrument;
		this.keyType = key_tick_type;
	}
}

class TickServiceModule extends ServerEnvMainModule {

	constructor(module_name) {

		super(module_name);
		/**
		 * 订阅关系map
		 * key：instrument + tick type
		 * value：Suscriber 列表（来自多个窗口，多个数据视图，对同一个合约的指定类型行情订阅）
		 */
		this.bigmap = {};
	}

	/**
	 * 使用合约 + 行情类型，生成map key
	 */
	makeMapKey(instrument, tick_type) {
		return `${instrument}.${tick_type}`;
	}

	ensureGet(key) {
		return this.bigmap[key] || (this.bigmap[key] = []);
	}

	/**
	 * 记录行情订阅者
	 * winId: <required>
	 * viewId: <required>
	 * instruments: <required>
	 * tick_type: <required>
	 */
	register(winId, viewId, instruments, tick_type) {

		for (let each_ins of instruments) {
			let subscriber_set = this.ensureGet(this.makeMapKey(each_ins, tick_type));
			if (subscriber_set.find((x) => x.windowId === winId && x.viewId === viewId) === undefined) {
				subscriber_set.push(new Suscriber(winId, viewId, each_ins, tick_type));
			}
		}
	}

	isTickTypeOk(tick_type) {
		return tick_type == this.systemTrdEnum.tickType.tick
				|| tick_type == this.systemTrdEnum.tickType.simpleTick
				|| tick_type == this.systemTrdEnum.tickType.transaction
				|| tick_type == this.systemTrdEnum.tickType.kline;
	}

	/**
	 * 在最小粒度级别（视图级别），订阅行情
	 */
	subscribe(winId, viewId, instruments, tick_type) {
		
		if (!(instruments instanceof Array) || instruments.length == 0) {
			this.loggerConsole.error('tick service > instruments must contain at least one instrument');
			return;
		}
		else if (!this.isTickTypeOk(tick_type)) {
			this.loggerConsole.error(`tick service > tick type [${tick_type}] cannot be recognized for sub operation`);
			return;
		}

		this.register(winId, viewId, instruments, tick_type);
		// 不对可能存在的重复订阅作检查，当前发出订阅的视图，有正当理由给予当前视图一次来自服务器的完整订阅回执
		this.subFromServer(instruments, tick_type);
	}

	/**
	 * 执行实际的订阅操作
	 */
	subFromServer(instruments, tick_type) {
		this.doSubUnsub(this.serverFunction.subscribeTick, instruments, tick_type);
	}

	/**
	 * 执行实际的退订操作
	 */
	unsubFromServer(instruments, tick_type) {
		this.doSubUnsub(this.serverFunction.unsubscribeTick, instruments, tick_type);
	}

	doSubUnsub(behavior, instruments, tick_type) {

		if (instruments.length == 0) {
			return;
		}

		let request_data = { ticks: [], simpleTicks: [], transactions: [], bars: [] };
		if(tick_type == this.systemTrdEnum.tickType.tick) {
			request_data.ticks = instruments;
		}
		else if(tick_type == this.systemTrdEnum.tickType.simpleTick) {
			request_data.simpleTicks = instruments;
		}
		else if(tick_type == this.systemTrdEnum.tickType.transaction) {
			request_data.transactions = instruments;
		}
		else {
			request_data.bars = instruments;
		}
		
		// tick/simple tick/transaction/kline 4种tick类型，无法通过response进行区别，唯有通过req id进行辨别
		let message = { fc: behavior, reqId: tick_type, dataType: 1, body: JSON.stringify(request_data) };
		this.quoteServer.send(message);
	}

	/**
	 * 删除已记录的行情订阅
	 * winId: <required>
	 * viewId: <optional>，不指定则代表取消所有来自window id所框定范围的订阅
	 * instruments: <optional>，不指定则默认为取消window id + view id所框定范围内的全部
	 * tick_type: <optional>， 如果指定了instruments，则必须指定tick type；如果没有指定instruments，则即使指定了tick type也将会被忽略
	 */
	getPotentialTypedSubscribers(winId, viewId, instruments, tick_type) {

		// 按tick类型分组匹配到的订阅者集合
		var typed_list_map = {};
		var tryGetMapSet = (tick_type) => {
			return typed_list_map[tick_type] || (typed_list_map[tick_type] = []);
		};

		/**
		 * 未指定具体合约，则针对所有合约
		 */

		if (instruments instanceof Array && instruments.length > 0) {

			for (let single_ins of instruments) {

				let subscriber_set = this.ensureGet(this.makeMapKey(single_ins, tick_type));
				// 要退订的合约&行情类型，没有订阅过或已完全退订，故无需退订
				if (subscriber_set.length == 0) {
					continue;
				}

				// 删除匹配到的合约&行情类型，所框定的视图
				subscriber_set.remove((x) => x.windowId === winId && (viewId === null || x.viewId === viewId));
				// 如果已经没有任何视图监听该合约&特定行情类型，才执行退订
				if (subscriber_set.length == 0) {
					// 获得行情类型对应的map
					let typed_map = tryGetMapSet(tick_type);
					// 将当前合约记录到对应行情类型的map当中
					typed_map.push(single_ins);
				}
			}
		} 
		else {
			/**
			 * 未指定具体合约，则默认取消由window id + view id 所框定的范围内，所有合约的行情
			 * 如view id未提供，则框定范围为window id所确定的所有视图
			 */
			for (let key in this.bigmap) {

				let subscriber_set = this.bigmap[key];
				if (subscriber_set.length == 0) {
					continue;
				}

				let key_type = subscriber_set[0].keyType;
				let key_ins = subscriber_set[0].keyIns;
				// 将订阅该合约&行情类型的视图全部删除
				subscriber_set.remove((x) => x.windowId === winId && (viewId === null || x.viewId === viewId));

				// 如果已经没有任何视图监听该合约&特定行情类型，才执行退订
				if (subscriber_set.length == 0) {
					// 获得行情类型对应的map
					let typed_map = tryGetMapSet(key_type);
					// 将当前合约记录到对应行情类型的map当中
					typed_map.push(key_ins);
				}
			}
		}

		return typed_list_map;
	}

	/**
	 * 在最小粒度级别，取消已订阅行情
	 */
	unsubscribe(winId, viewId, instruments, tick_type) {

		this.loggerConsole.info(`to unsub kline of ${instruments}/${tick_type}`);
		if(!(instruments instanceof Array) || instruments.length == 0) {
			return;
		}
		else if(!this.isTickTypeOk(tick_type)) {
			this.loggerConsole.error(`tick service > tick type cannot be recognized for unsub operation, param: ${winId}/${viewId}/${instruments}/${tick_type}`);
			return;
		}

		var typed_map_set = this.getPotentialTypedSubscribers(winId, viewId, instruments, tick_type);
		for (let this_type in typed_map_set) {
			this.unsubFromServer(typed_map_set[this_type], this_type);
		}
	}

	feedConsumers(behavior, instrument, tick_type, tick_data) {

		let subscriber_set = this.ensureGet(this.makeMapKey(instrument, tick_type));
		if (subscriber_set.length == 0) {
			let error_msg = `tick service > cannot find registered view for instrument = ${instrument} & tick type = ${tick_type}`;
			this.loggerSys.warn(error_msg);
			this.loggerConsole.warn(error_msg);
			return;
		}

		var illegal_wins = [];
		subscriber_set.forEach((the_subscriber) => {

			let winid = the_subscriber.windowId;
			let target_win = BrowserWindow.fromId(winid);
			if (target_win !== null) {
				target_win.webContents.send(behavior, the_subscriber.viewId, instrument, tick_type, tick_data);
			} 
			else if (illegal_wins.indexOf(winid) < 0) {
				illegal_wins.push(winid);
			}
		});

		if (illegal_wins.length > 0) {
			subscriber_set.remove((x) => illegal_wins.indexOf(x.windowId) >= 0);
		}
	}

	standarizeTickData(matrix) {
		return TickDataConverter.ConvertStandardTick(matrix);
	}

	standarizeTransactionData(trans_data_arr) {
		return TickDataConverter.ConvertTransaction(trans_data_arr);
	}

	standarizeKlineData(kline_data_arr) {
		return TickDataConverter.ConvertKline(kline_data_arr);
	}

	handleTickChange(message_package) {

		/*
		tick change data structure:

		tick
		-------------
		{
			"exchange": "SHSE", 
			"instrumentID": "SHSE.600620", 
			"updateTime":1558493716, 
			"strTime": "2019-05-22 10:55:16.000", 
			"lastPrice":7.12, 
			"openPrice":8.12, 
			"highPrice":8.25, 
			"lowPrice":7.12, 
			"volume": "3676300", 
			"turnover": "29455787", 
			"upperLimitPrice":8.12, 
			"lowerLimitPrice":7.12, 
			"settlePrice":0, 
			"presettlePrice":0, 
			"preClosePrice":8.12, 
			"askPrice": [7.12, 7.12, 7.12, 7.12, 7.12, 7.12, 7.12, 7.12, 7.12, 7.12], 
			"askVolume": [14800, 16800, 38600, 1500, 5500, 2200, 1200, 11200, 6400, 2800], 
			"bidPrice": [7.12, 7.12, 7.12, 7.12, 7.12, 7.12, 7.75, 7.12, 7.12, 7.12], 
			"bidVolume": [41100, 6800, 31300, 80100, 40700, 300, 4600, 200, 10000, 1200], 
			"numTrades": 2540, 
			"direction":0
		}
		
		simple tick
		--------------
		{"instrument" :"SZSE.002230", "lastPrice" :31.5}

		transaction
		--------------
		{
			"exchange": "SZSE", 
			"instrumentID": "SZSE.001979", 
			"updateTime": 1558494678.58, 
			"strTime": "2019-05-22 11:11:18", 
			"index":7939333, 
			"price": 21.12, 
			"volume": "335", 
			"turnover": 7125.45, 
			"direction": -1, 
			"orderKindFunctionCode": "0C", 
			"askOrder": 7939331, 
			"bidOrder": 7882782
		}

		kline
		---------------
		{
			"exchange": "SHSE", 
			"instrumentID": "SHSE.600282", 
			"barType": 60, 
			"strTime": "2019-05-13 09:30:00", 
			"updateTime": 1.557711E9, 
			"openPrice": 3.43, 
			"highPrice": 3.43, 
			"lowPrice": 3.42, 
			"closePrice": 3.42, 
			"volume": 733200, 
			"turnover": 2510774, 
			"preClosePrice": 3.47, 
			"position": 0, 
			"adjFactor": 5.801752, 
			"flag": 0
		}

		*/

		var tick_type = message_package.dataType;
		var body_data = message_package.body;
		var tick_instrument = null;
		var tick_data = null;

		if (tick_type == this.systemTrdEnum.tickType.tick) {

			let med_data = tickBean.decode(body_data);
			tick_instrument = med_data.instrumentID;
			tick_data = {
				instrument: med_data.instrumentID,
				time: med_data.updateTime * 1000,
				open: med_data.openPrice,
				high: med_data.highPrice,
				low: med_data.lowPrice,
				latest: med_data.lastPrice,
				preclose: med_data.preClosePrice,
				volume: this.helper.isJson(med_data.volume) ? med_data.volume.low : med_data.volume,
				amount: this.helper.isJson(med_data.turnover) ? med_data.turnover.low : med_data.turnover,
				ceiling: med_data.upperLimitPrice,
				floor: med_data.lowerLimitPrice,
				sells: med_data.askPrice.map((price, idx) => { return [price, med_data.askVolume[idx]]; }),
				buys: med_data.bidPrice.map((price, idx) => { return [price, med_data.bidVolume[idx]]; }),
			};
		} 
		else if (tick_type == this.systemTrdEnum.tickType.simpleTick) {
			tick_instrument = null;
			tick_data = JSON.parse(body_data);
		}
		else if (tick_type == this.systemTrdEnum.tickType.transaction) {

			let med_data = transactionBean.decode(body_data);
			tick_instrument = med_data.instrumentID;
			tick_data = {
				time: med_data.updateTime * 1000,
				direction: med_data.direction,
				price: med_data.price,
				volume: (typeof med_data.volume !== 'number' ? med_data.volume.low : med_data.volume) * 0.01,
			};
		}
		else if(tick_type == this.systemTrdEnum.tickType.kline) {

			let med_data = klineBean.decode(body_data);
			tick_instrument = med_data.instrumentID;
			tick_data = {
				instrument: med_data.instrumentID,
				time: med_data.updateTime * 1000,
				open: med_data.openPrice,
				high: med_data.highPrice,
				low: med_data.lowPrice,
				close: med_data.closePrice,
				volume: this.helper.isJson(med_data.volume) ? med_data.volume.low : med_data.volume,
				amount: this.helper.isJson(med_data.turnover) ? med_data.turnover.low : med_data.turnover,
			};
		}
		else {
			var msg = `unexpected tick data with tick type = ${tick_type}`;
			this.loggerConsole.fatal(msg);
			this.loggerSys.fatal(msg);
			return;
		}

		this.feedConsumers(this.serverEvent.tickPriceChanged.toString(), tick_instrument, tick_type, tick_data);
	}

	handleSubTickResponse(message_package) {

		// tick/simple tick/transaction/kline 4种tick类型，无法通过response进行区别，唯有通过req id进行辨别
		var sub_tick_type = message_package.reqId;
		var message = JSON.parse(message_package.body);

		if (message.errorCode !== 0) {
			
			let error_msg = `tick service > sub tick failed with fatal error[${message.errorCode}]: ${JSON.stringify(message)}`;
			this.loggerConsole.fatal(error_msg);
			this.loggerSys.fatal(error_msg);
			return;
		}

		/*
		sub response data structure:

		tick
		-------------
		{
			"data": {
				"bars":{}, 
				"ticks":{
					"SHSE.600620": [
						// 时间，open，high，low，last price，preclose price，volume，amount, ceiling price, floor price
						// 服务器端已经将该数组打散为单个并列个体，整个数组长度讲改变
						[1558493713, 8.14, 8.25, 7.82, 7.85, 8.12, 3675700, 29451077, 10.01, 8.32], 
						// 卖10档位--手数
						[15400, 16800, 38600, 1500, 5500, 2200, 1200, 11200, 6400, 2800], 
						// 卖10档位--价格
						[7.85, 7.86, 7.87, 7.88, 7.89, 7.91, 7.92, 7.94, 7.95, 7.96], 
						// 买10档位--手数
						[41100, 5500, 31300, 80100, 40700, 300, 4600, 200, 10000, 1200], 
						// 买10档位--价格
						[7.84, 7.82, 7.81, 7.8, 7.77, 7.76, 7.75, 7.74, 7.73, 7.72]
					]
				}
			}, 
			"errorCode":0, 
			"errorMsg": "success"
		}
		
		simple tick
		--------------
		{"instrument" :"SZSE.002230", "lastPrice" :31.5}

		transaction
		--------------
		{
			"data":{
				"bars":{}, 
				"ticks":{}, 
				"transactions":{
					"SZSE.000815":[
						[1558495784.53,  1,  13.85,  400]
					]
				}
			}, 
			"errorCode":0, 
			"errorMsg": "success"
		}

		kline
		---------------
		{
			"data":{
				"bars":{
					"SZSE.001979":[
						// time_stamp, open, high, low, close, volume, amount
						[1558493100, 21.07, 21.09, 21.07, 21.07, 60000, 1264684]
					]
				}, 
				"ticks":{}
			}, 
			"errorCode": 0, 
			"errorMsg": "success"
		}

		*/

		if(sub_tick_type == this.systemTrdEnum.tickType.tick) {

			let ticks_dict = message.data.ticks;
			for(let the_instrument in ticks_dict) {
				let matrix = ticks_dict[the_instrument];
				if(!(matrix instanceof Array) || matrix.length != 14) {
					this.loggerConsole.fatal(`tick sub response contains invalidated data for instrument/${the_instrument}`);
					continue;
				}
				let tick_data = this.standarizeTickData(matrix);
				this.feedConsumers(this.serverEvent.subscribeTickReceived.toString(), the_instrument, sub_tick_type, tick_data);
			}
		}
		else if(sub_tick_type == this.systemTrdEnum.tickType.simpleTick) {
			this.loggerConsole.error('sub simple tick response is not handled');
		}
		else if(sub_tick_type == this.systemTrdEnum.tickType.transaction) {

			let trans_dict = message.data.transactions;
			for(let the_instrument in trans_dict) {
				let matrix = trans_dict[the_instrument];
				if(!(matrix instanceof Array) || matrix.length == 0) {
					continue;
				}
				let trans_data = matrix.map(x => this.standarizeTransactionData(x));
				this.feedConsumers(this.serverEvent.subscribeTickReceived.toString(), the_instrument, sub_tick_type, trans_data);
			}
		}
		else if(sub_tick_type == this.systemTrdEnum.tickType.kline) {

			let bars_dict = message.data.bars;
			for(let the_instrument in bars_dict) {
				let matrix = bars_dict[the_instrument];
				if(!(matrix instanceof Array) || matrix.length == 0) {
					continue;
				}
				let kline_data = matrix.map(x => this.standarizeKlineData(x));
				this.feedConsumers(this.serverEvent.subscribeTickReceived.toString(), the_instrument, sub_tick_type, kline_data);
			}
		}
	}

	handleUnsubTickResponse(message_package) {

		var message = JSON.parse(message_package.body);
		if (message.errorCode != 0) {
			let error_msg = `tick service > unsub tick failed with fatal error[${message.errorCode}]: ${JSON.stringify(message)}`;
			this.loggerConsole.fatal(error_msg);
			this.loggerSys.fatal(error_msg);
		} 
		else {
			this.loggerConsole.warn('tick service > un-sub tick response is not handled');
			this.loggerConsole.log(JSON.stringify(message));
		}
	}

	listen2Commands() {

		this.mainProcess.on(this.systemEvent.sysLoadingCompleted, (event) => {
			
			if(!this.userInfo.quoteServerRequired) {
				return;
			}

			// 监听行情数据推送
			this.quoteServer.listen2Event(this.serverEvent.tickPriceChanged, this.handleTickChange.bind(this));
			this.quoteServer.listen2Event(this.serverEvent.KlineChanged, this.handleTickChange.bind(this));
			// 监听行情订阅、取消订阅的响应
			this.quoteServer.listen2Event(this.serverEvent.subscribeTickReceived, this.handleSubTickResponse.bind(this));
			this.quoteServer.listen2Event(this.serverEvent.unsubscribeTickReceived, this.handleUnsubTickResponse.bind(this));

			// 监听订阅、取消订阅：简版行情、标准行情、交易队列请求
			this.mainProcess.on(this.systemEvent.subscribeTick, (event, winId, viewId, instruments, tick_type) => { this.subscribe(winId, viewId, instruments, tick_type); });
			this.mainProcess.on(this.systemEvent.unsubscribeTick, (event, winId, viewId, instruments, tick_type) => { this.unsubscribe(winId, viewId, instruments, tick_type); });
		});
	}

	run() {
		this.loggerSys.info('load module tick-service > begin');
		this.listen2Commands();
		this.loggerSys.info('load module tick-service > end');
	}
}

module.exports = { TickServiceModule };
