import { ServerEvent } from '../config/server-event';
import { SocketDataPackage } from '../types/data-package';
import { UserLoginResponse } from '../types/table/admin';

/**
 * Socket 错误信息
 */
export interface SocketError {

    name: string;
    message: string;
    stack?: string;
}

/**
 * 回调函数注册类型：bind = 绑定， unbind = 取消绑定， once = 绑定后一定但执行则自动解除绑定
 */
export type CallbackRegisterType = 'bind' | 'unbind' | 'once';

/**
 * 抽象通信接口
 */
export interface ICommunication {

    /**
     * 服务器主机IP
     */
    get host(): string;

    /**
     * 服务器主机端口
     */
    get port(): number;

    /**
     * 下一个RequestId
     */
    get nextReqId(): number;

    /**
     * 注入消息（当前应用场景为：通过HTTP请求行情，并通过该接口注入行情数据，传递给订阅者）
     */
    injectMessage: (serverEvent: ServerEvent, data: SocketDataPackage) => void;

    /**
     * 注册连接建立回调函数（支持多次注册不同函数）
     * @param rgtype 注册类型
     * @param callback 回调函数，rgtype = unbind 时不提供代表解除所有绑定
     */
    onConnected(rgtype: CallbackRegisterType, callback?: () => void): void;

    /**
     * 注册连接超时回调函数（支持多次注册不同函数）
     * @param rgtype 注册类型
     * @param callback 回调函数，rgtype = unbind 时不提供代表解除所有绑定
     */
    onTimeouted(rgtype: CallbackRegisterType, callback?: () => void): void;
    
    /**
     * 注册连接错误回调函数（支持多次注册不同函数）
     * @param rgtype 注册类型
     * @param callback 回调函数，rgtype = unbind 时不提供代表解除所有绑定
     */
    onErrored(rgtype: CallbackRegisterType, callback?: (err: SocketError) => void): void;
    
    /**
     * 注册连接关闭回调函数（支持多次注册不同函数）
     * @param rgtype 注册类型
     * @param callback 回调函数，rgtype = unbind 时不提供代表解除所有绑定
     */
    onClosed(rgtype: CallbackRegisterType, callback?: () => void): void;

    /**
     * 订阅服务器事件
     */
    subscribe: (server_event: number | string, callback: (data: SocketDataPackage) => void) => void;

    /**
     * 退订服务器事件
     */
    unsubscribe: (server_event: number | string, callback?: (data: SocketDataPackage) => void) => void;
    
    /**
     * 建立连接
     */
    connect(): void;

    /**
     * 登录Socket服务器
     * @param userName - 用户名
     * @param password - 密码
     * @param mac - MAC地址
     * @param os - 操作系统信息
     */
    signin(userName: string, password: string, mac: string, os: string): Promise<UserLoginResponse>;

    /**
     * 从Socket服务器注销
     */
    signout(): Promise<void>;

    /**
     * 断开连接
     */
    disconnect(): void;

    /**
     * 发送数据（返回值：true/已发送，false/未发送）
     */
    send(data: SocketDataPackage): void;

    /**
     * 释放资源
     */
    dispose(): void;
}