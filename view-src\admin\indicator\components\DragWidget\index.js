const BaseComponent = require('../BaseComponent');
const draggable = require('vuedraggable');
const helper = require('../../../../../libs/helper').helper;
module.exports = class DragWidget extends BaseComponent {
    constructor() {
        super(__dirname);
        this.options = {
            components: {
                draggable,
            },
            props: {
                indicatorList: {
                    required: true,
                },
            },
            data() {
                return {
                    types: [],
                    tabs: [
                        { name: '1', label: '单值序列' },
                        { name: '2', label: '多值序列' },
                    ],
                    activeName: '1',
                    widgetHeight: 0,
                    deleting: false,
                    attached: {
                        value: '3.1415926',
                        float: -1,
                        rawData: [],
                        yAxisName: '',
                        height: 400,
                        widget: 'chart',
                        type: 'line',
                        smooth: false,
                        data: [],
                        columns: [],
                        inverse: false,
                        percent: false,
                        stack: null,
                        style: {
                            width: '100%',
                            minWidth: '100%',
                            maxWidth: '100%',
                        },
                        wStyle: {},
                        position: {},
                        relatedIndex: 0,
                        current: false,
                    },
                    query: '',
                };
            },
            computed: {
                widgetStyle() {
                    return {
                        height: `${this.widgetHeight}px`,
                    };
                },
            },
            watch: {
                indicatorList() {
                    this.renderTypes();
                },
                activeName() {
                    this.renderTypes();
                },
                query() {
                    this.renderTypes();
                },
            },
            mounted() {
                this.onResize();
            },
            methods: {
                onResize() {
                    this.widgetHeight = window.innerHeight - 133;
                    window.addEventListener('resize', e => {
                        this.widgetHeight = window.innerHeight - 133;
                    });
                },
                renderTypes() {
                    this.types = this.indicatorList
                        .filter(indicator => indicator.valueType == this.activeName && indicator.name.includes(this.query))
                        .map((indicator, index) => {
                            return {
                                ...indicator,
                                key: index,
                                ...this.attached,
                            };
                        });
                },
                handleDown(e) {
                    let ele = e.target;
                    let matched = this.types.find(type => type.id == ele.dataset.id);
                    let matchedIndex = this.types.indexOf(matched);
                    let index = this.types.indexOf(matched);
                    matched.wStyle = {
                        position: 'fixed',
                        zIndex: 1,
                        top: `${ele.getBoundingClientRect().top}px`,
                        left: 0,
                        width: '140px',
                    };
                    let newType = helper.deepClone({ ...matched, key: Date.now(), wStyle: {} });
                    this.types.splice(matchedIndex, 0, newType);
                    const disX = e.clientX - ele.offsetLeft;
                    const disY = e.clientY - ele.getBoundingClientRect().top;

                    const dragDomWidth = ele.offsetWidth;

                    const screenWidth = document.body.clientWidth;

                    const minDragDomLeft = ele.offsetLeft;
                    const maxDragDomLeft = screenWidth - ele.offsetLeft - dragDomWidth;

                    const minDragDomTop = ele.offsetTop;

                    let styL = minDragDomLeft;

                    document.onmousemove = e => {
                        // console.log(e);

                        let left = e.clientX - disX;
                        let top = e.clientY - disY;

                        if (-left > minDragDomLeft) {
                            left = -minDragDomLeft;
                        } else if (left > maxDragDomLeft) {
                            left = maxDragDomLeft;
                        }

                        if (-top > minDragDomTop) {
                            top = -minDragDomTop;
                        }

                        matched.wStyle = { ...matched.wStyle, left: `${left + styL}px`, top: `${top}px` };
                        this.renderHoverBox(e.clientY, e.clientX);
                    };

                    document.onmouseup = e => {
                        document.onmousemove = null;
                        document.onmouseup = null;
                        this.dropItem({ ...matched, key: Date.now() }, matchedIndex, e.clientY, e.clientX);
                    };
                },
                renderHoverBox(mouseTop, mouseLeft) {
                    let previewArea = this.$parent.$refs.previewArea;
                    let previewList = previewArea.previewList;
                    let matched = previewList.find(box => {
                        let { left, top, height, width } = box.position;
                        return parseInt(box.style.width) == 100 && mouseTop >= top && mouseTop <= top + height && mouseLeft >= left && mouseLeft <= left + width;
                    });
                    previewArea.hoveringIndex = previewList.indexOf(matched);
                    if (matched) {
                        let { top, height, width } = matched.position;
                        previewArea.splitStyle = {
                            width: `${width / 2}px`,
                            height: `${height}px`,
                            top: `${top}px`,
                            left: mouseLeft - 160 <= width / 2 ? '160px' : `${160 + width / 2}px`,
                        };
                    } else {
                        previewArea.splitStyle = {};
                    }
                },
                dropItem(item, itemIndex, mouseTop, mouseLeft) {
                    let previewArea = this.$parent.$refs.previewArea;
                    let $previewArea = document.querySelector('.preview-area');
                    let previewList = previewArea.previewList;
                    /* judge position */
                    const atPreviewArea = () => {
                        return mouseLeft >= 160 && mouseLeft <= 160 + $previewArea.clientWidth && mouseTop >= 40;
                    };

                    if (atPreviewArea()) {
                        if (previewList.length == 0) {
                            // no data
                            previewList.push(item);
                        } else {
                            if (previewArea.hoveringIndex >= 0) {
                                let half = {
                                    width: '50%',
                                    minWidth: '50%',
                                    maxWidth: '50%',
                                };
                                let index = previewArea.hoveringIndex;
                                let hoveringBox = previewList[index];
                                hoveringBox.style = half;
                                item.style = half;
                                hoveringBox.relatedIndex = parseInt(previewArea.splitStyle.left) > 160 ? 1 : -1;
                                item.relatedIndex = hoveringBox.relatedIndex > 0 ? -1 : 1;
                                previewList.splice(hoveringBox.relatedIndex > 0 ? index + 1 : index, 0, item);
                            } else {
                                previewList.push(item);
                            }
                        }
                    }

                    this.types.splice(itemIndex + 1, 1);
                    previewArea.splitStyle = {};
                    previewArea.hoveringIndex = -1;
                },
            },
        };
    }
};
