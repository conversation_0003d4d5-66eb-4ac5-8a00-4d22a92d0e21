<!DOCTYPE html>  
<html lang="en">  
<head>  
    <meta charset="UTF-8">  
    <meta name="viewport" content="width=device-width, initial-scale=1.0">  
    <title>Virtual Scrolling Table</title>  
    <style>  
        #container {  
            height: 400px;  
            overflow-y: auto;  
            border: 1px solid #ccc;  
        }  
        table {
			table-layout: fixed;
            width: 100%;  
            border-collapse: collapse;
        }
        th, td {  
			box-sizing: border-box;
			height: 32px;
            border: 1px solid #ddd;
			line-height: 30px;
			padding: 0 10px;
            text-align: left;
        }
    </style>  
</head>  
<body>  
    <div id="container">  
        <table id="table">  
            <thead>  
                <tr>  
                    <th>ID</th>  
                    <th>Name</th>  
                </tr>  
            </thead>  
            <tbody>  
                <!-- Rows will be dynamically inserted here -->  
            </tbody>  
        </table>  
    </div>  
  
    <script>  
        const container = document.getElementById('container');
        const table = document.querySelector('#table');
		const tbody = document.querySelector('#table tbody');
        const rowHeight = 32;
        const totalRows = 99999;
        const visibleRowCount = Math.ceil(container.clientHeight / rowHeight);  
  
        const data = Array.from({ length: totalRows }, (_, i) => ({  
            id: i + 1,  
            name: `Name ${i + 1}`  
        }));
  
        function renderRows(startIndex, endIndex) {
		
			let before_rows = startIndex;
			let visible_rows = endIndex - startIndex;
			let after_rows = totalRows - endIndex;
			
			console.log({
				start_end: `${startIndex}/${endIndex}`,
				visible_rows,
				before_rows,
				after_rows,
				total: before_rows + after_rows + visible_rows,
			});
			
			table.style.marginTop = before_rows * rowHeight + 'px';
			table.style.marginBottom = after_rows * rowHeight + 'px';
            tbody.innerHTML = '';
			
            for (let i = startIndex; i < endIndex; i++) {
                const row = document.createElement('tr');  
                row.innerHTML = `<td>${data[i].id}</td><td>${data[i].name}</td>`;  
                tbody.appendChild(row);
            }
        }  
  
        function updateVisibleRows() {
		
            const scrollTop = container.scrollTop;  
            const startIndex = Math.floor(scrollTop / rowHeight);  
            const endIndex = Math.min(startIndex + visibleRowCount, totalRows);
            renderRows(startIndex, endIndex);
        }
  
        // Initial render  
        updateVisibleRows();
  
        // Listen for scroll events  
        container.addEventListener('scroll', updateVisibleRows);  
    </script>  
</body>  
</html>