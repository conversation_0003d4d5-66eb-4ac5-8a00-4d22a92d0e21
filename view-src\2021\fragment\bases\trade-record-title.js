/**
 * 通用委托数据标题顺序
 */
const OrderTitles = [

    'id',
    'parentOrderId',
    'userId',
    'userName',
    'strategyId',
    'strategyName',
    'fundId',
    'fundName',
    'accountId',
    'financeAccount',
    'accountName',
    'instrument',
    'instrumentName',
    'frozenMargin',
    'frozenCommission',
    'frozenVolume',
    'tradedAmount',
    'tradedVolume',
    'tradedPrice',
    'cancelledVolume',
    'volumeOriginal',
    'commission',
    'errorCode',
    'errorMsg',
    'isForeign',
    'assetType',
    'orderStatus',
    'localOrderId',
    'exchangeOrderId',
    'orderPrice',
    'orderPriceType',
    'direction',
    'businessFlag',
    'positionEffect',
    'hedgeFlag',
    'optionType',
    'coverFlag',
    'isForceClose',
    'tradeTime',
    'orderTime',
    'tradingDay',
    'customId',
    'createTime',
    'updateTime',
    'adjustFlag',
    'remark',
    'ipMac',
    'receiveOrderTime',
    'sendTerminalTime',
    'receiveExchangeOrderIdTime',
    'identityId',
];
    
/**
 * 通用持仓数据标题顺序
 */
const PositionTitles = [

    'id',
    'accountId',
    'financeAccount',
    'accountName',
    'fundId',
    'fundName',
    'strategyId',
    'strategyName',
    'tradingDay',
    'direction',
    'instrument',
    'instrumentName',
    'yesterdayPosition',
    'todayPosition',
    'closeProfit',
    'floatProfit',
    'avgPrice',
    'lastSettlePrice',
    'usedCommission',
    'usedMargin',
    'marketValue',
    'assetType',
    'frozenVolume',
    'frozenTodayVolume',
    'positionCost',
    'settlementPrice',
    'marginRateByMoney',
    'marginRateByVolume',
    'updateTime',
    'identityId',
];

/**
 * 通用成交数据标题顺序
 */
const ExchangeTitles = [

    'id',
    'orderId',
    'userId',
    'userName',
    'accountId',
    'accountName',
    'fundId',
    'fundName',
    'assetType',
    'strategyId',
    'strategyName',
    'tradingDay',
    'direction',
    'positionEffect',
    'instrument',
    'instrumentName',
    'tradeTime',
    'isToday',
    'volume',
    'tradedPrice',
    'tradeId',
    'exchangeOrderId',
    'commission',
    'adjustFlag',
    'updateTime',
    'identityId',
];

module.exports = { OrderTitles, PositionTitles, ExchangeTitles };