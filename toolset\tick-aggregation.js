

// 股票交易时间
let stock_trade_time = [9 * 60 + 30, 11 * 60 + 30, 13 * 60, 15 * 60]

let sample_times = {}

let sample_ordered_times = {}


let index = 0;
for(let i = 0; i < stock_trade_time.length/2; i++) {
    let current_sample_time = stock_trade_time[(2 * i)]*60;
    let end_sample_time = stock_trade_time[(2 * i + 1)]*60;
    while (current_sample_time < end_sample_time){
        sample_times[current_sample_time] = index;
        sample_ordered_times[index] = current_sample_time;
        current_sample_time += 1;
        index += 1;
    }
}



function get_sample_time_point(quote_str_time) {
	
    let temp_times = quote_str_time.split(':');
    let real_sample_time_point = parseInt(temp_times[0])*3600 + parseInt(temp_times[1])*60 + parseInt(temp_times[2]);
    let sample_time_point = real_sample_time_point;
    let time_stamp = sample_times[real_sample_time_point];
    if (time_stamp == undefined) {
        sample_time_point = real_sample_time_point - 1;
        time_stamp = sample_times[sample_time_point];
        if(time_stamp == undefined) {
			return [real_sample_time_point, undefined];
		}
    }

    return [real_sample_time_point, sample_time_point];
}


function get_bar_begin_end_time_point(time_point, bar_type) {
    let index = sample_times[time_point]
    let bar_start_index = parseInt(index/bar_type)*bar_type;
    return [sample_ordered_times[bar_start_index], sample_ordered_times[bar_start_index+bar_type-1]];
}


function copy_bar(bar, time_diff) {
    let target_bar = copy_list(bar);
    target_bar.time -= time_diff;
    return target_bar;
}


function copy_list(bar) {
    let target_bar = {}
    for(let key in bar) {
        target_bar[key] = bar[key];
    }
    return target_bar;
}


/**
 *
 * @param base_bars [update_time, open, high, low, close, volume, amount]
 * @param bar_type
 * @returns {Array}
 */
function AggregateTick(base_bars, bar_type) {

    let ret_bars = []
    let target_bar, pre_base_bar, pre_sample_point;
    let bar_len = base_bars.length;
    for(let i = 0; i < bar_len; i++){
        let bar = base_bars[i];
        // todo 时间戳转字符串
        let sample_point_ret = get_sample_time_point(new Date(base_bars[i].time).format('hh:mm:ss'));

        let current_sample_point = sample_point_ret[1];
        if(current_sample_point == undefined) continue;
        sample_point_ret = get_bar_begin_end_time_point(current_sample_point, bar_type);
        let start_sample_point = sample_point_ret[0];

        if(pre_base_bar == undefined){
            target_bar = copy_bar(bar, current_sample_point - start_sample_point);
        }else {
            if(pre_sample_point >= start_sample_point){
                target_bar.volume += bar.volume;
                target_bar.amount += bar.amount;
                target_bar.close = bar.close;
                if(target_bar.high < bar.high){
                    target_bar.high = bar.high;
                }
                if(target_bar.low > bar.low){
                    target_bar.low = bar.low
                }
                if (i == bar_len-1){
                    ret_bars.push(copy_list(target_bar));
                }
            }else {
                ret_bars.push(copy_list(target_bar));
                target_bar = copy_bar(bar, current_sample_point - start_sample_point);
            }
        }
        pre_sample_point = current_sample_point;
        pre_base_bar = bar;
    }
    return ret_bars
}

module.exports = { AggregateTick };