<div class="account-group s-full-height">
	<div class="account-group-internal themed-box s-scroll-bar s-full-height">
		<div class="xtcontainer s-border-box s-full-height">

			<div class="ag-toolbar xtheader themed-header">
				<template>
					<span class="header-title">{{ states.isBasketAlgo ? '选择账号' : '选择账号组' }}</span>
					<el-select placeholder="请选择账号组"
								v-model="states.groupId"
								@change="handleGroupChange">

								<el-option v-for="(item, item_idx) in groups"
											:key="item_idx"
											:label="item.groupName"
											:value="item.groupId">

											<div class="account-group-item">
												<span class="group-name">{{ item.groupName }}</span>
												<span v-if="item.groupId != states.allGroupId" class="group-opers s-pull-right">
													<a @click.stop="hope2EditGroup(item)" title="编辑分组名称" class="btn iconfont icon-bianjihexiugai"></a>
													<a @click.stop="hope2DeleteGroup(item)" title="删除该分组" class="btn el-icon-close"></a>
												</span>
											</div>
								</el-option>
					</el-select>

					<el-input placeholder="关键字过滤"
							  prefix-icon="el-icon-search"
							  class="s-w-150 s-mgl-10"
							  v-model="states.keywords"
							  @change="filterRecords" clearable></el-input>

					<el-checkbox v-model="states.filters.isChecked" v-show="false" @change="handleFilterChange">仅显示已勾选</el-checkbox>
					<el-checkbox v-if="!isAll" v-model="states.filters.showAll" v-show="false" @change="handleFilterChange">显示所有账号</el-checkbox>

					<div class="s-pull-right">

						<span class="s-mgr-10 checked-info">勾选账号 = {{ states.checkeds }} / {{ states.total }}</span>

						<a @click="hope2Refresh" class="s-mgr-10 themed-hover button-refresh" title="刷新">
							<i class="iconfont icon-shuaxin"></i>
						</a>

						<a @click="hope2SaveGroup" class="s-mgr-10 themed-hover button-saveas" title="保存为账号分组">
							<i class="iconfont icon-baocun"></i>
						</a>

						<a @click="hope2Config" class="s-mgr-10 themed-hover button-table-cfg" title="表格配置">
							<i class="iconfont icon-shezhi11"></i>
						</a>

					</div>

				</template>

			</div>

			<div class="module-edit-group">
				<template>
					<el-dialog width="290px"
							   class="lighted-box"
							   :title="dialog.title"
							   :visible="dialog.visible"
							   :close-on-click-modal="false"
							   :show-close="false">

						<div class="dialog-body-inner">
							<label>账号组名称</label>
							<el-input v-model.trim="dialog.groupName" class="s-mgl-10" :maxlength="30" style="width: 190px;"></el-input>
						</div>

						<div slot="footer">
							<el-button type="primary" @click="saveGroup">保存</el-button>
							<el-button type="info" @click="saveGroupAs" v-if="showSaveAs">另存为</el-button>
							<el-button type="default" @click="unsaveGroup">取消</el-button>
						</div>
					</el-dialog>
				</template>
			</div>

			<div class="table-external">
				<div class="table-component s-full-width">
					<table>
						<tr>
							<th type="check" fixed-width="40" fixed></th>
							<th label="账号名称" 
								prop="accountName" 
								min-width="202.0" 
								formatter="formatAccountName" searchable sortable overflowt></th>

							<th label="资产类型" 
								fixed-width="80" 
								prop="assetType" 
								watch="assetType" 
								formatter="formatAssetType" sortable></th>

							<th label="可用资金" 
								prop="available" 
								min-width="120" 
								align="right" sortable overflowt summarizable thousands></th>

							<th label="净资产" 
								prop="balance" 
								min-width="120" 
								align="right" sortable overflowt summarizable thousands></th>

							<th label="目标合约"
								watch="shortInstrument, instrumentName" 
								min-width="130" overflowt>$shortInstrument$/$instrumentName$</th>

							<th label="多头可用"
								prop="longPosition"
								min-width="80" thousands-int summarizable sortable overflowt></th>

							<th label="空头可用"
								prop="shortPosition"
								min-width="80" thousands-int summarizable sortable overflowt></th>

							<th label="分配权重" 
								prop="multiple" 
								min-width="80" 
								align="right" 
								sorting-method="sortByWeight" 
								formatter="formatWeight" sortable summarizable></th>

							<th label="委托数量"
								prop="volume" 
								min-width="80" 
								align="right" sortable overflowt summarizable></th>

							<th label="总权益" 
								prop="balance" 
								min-width="120" 
								align="right" sortable overflowt summarizable thousands></th>

							<th label="冻结资金" 
								prop="frozenMargin" 
								min-width="100" 
								align="right" sortable overflowt summarizable thousands></th>
							
							<th label="连接状态" 
								min-width="80"
								prop="connectionStatus" 
								formatter="formatConnectionStatus" sortable overflowt></th>
							
							<th label="产品" prop="fundName" min-width="140" searchable sortable overflowt></th>
						</tr>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>