syntax = "proto2";
package proto;

option java_package = "com.quant.quote.proto";

option java_outer_classname = "QuoteDataProto";

message QueueDataBean{
    required string dataType = 1;
    required bytes data = 2;
    optional int32 param = 3;
}

message HistoryDataBean{
    repeated bytes historyData = 1;
}

message TickBean{
    required string exchange = 1;           //交易所代码
    required string instrumentID = 2;       //证券ID
    required double  updateTime = 3;         //行情时间戳
    required string strTime = 4;            //可视化时间
    required float  lastPrice = 5;          //最新价
    required float  openPrice = 6;          //开盘价
    required float  highPrice = 7;          //最高价
    required float  lowPrice  = 8;          //最低价

    required int64  volume = 9;             //成交总量
    required int64  turnover = 10;          //成交总金额
    optional int64  position = 11;          //合约持仓量(期)
    required float  upperLimitPrice = 12;    //涨停价
    required float  lowerLimitPrice = 13;    //跌停价
    required float  settlePrice = 14;        //结算价
    required float  preClosePrice = 15;      //昨收盘价

    repeated float askPrice = 16;               //买价
    repeated int32 askVolume = 17;              //买量
    repeated float bidPrice = 18;               //卖价
    repeated int32 bidVolume = 19;              //卖量
    optional int32 numTrades = 20;             //成交笔数
    optional int32 direction = 21;             //成交方向

}

message KLineBean{
    required string exchange = 1;           //交易所代码
    required string instrumentID = 2;       //证券ID
    required int32  barType = 3;            //bar类型，以秒为单位，比如1分钟bar, bar_type=60
    required string strTime = 4;            //可视化时间
    required double  updateTime = 5;        //行情时间戳
    required float  openPrice = 6;
    required float  highPrice = 7;
    required float  lowPrice = 8;
    required float  closePrice = 9;
    required int64  volume = 10;
    required int64  turnover = 11;
    required float  preClosePrice = 12;
    optional int64  position = 13;          //持仓量
    optional float  adjFactor = 14;         //复权因子
    optional int32  flag = 15;              //除权出息标记
}

message SecondTickBean{
    required string exchange = 1;           //交易所代码
    required string instrumentID = 2;       //证券ID
    required string strTime = 3;            //可视化时间
    required double updateTime = 4;         //行情时间戳
    required int64  volume = 5;             //成交总量
    required int64  turnover = 6;           //成交总金额
    optional int64  position = 7;           //合约持仓量(期)
    required float  preClosePrice = 8;
    required int32  changeFlag = 9;         //volume和turnover是否有变化 0表示没有变化 1表示变化在0ms 5表示在500ms
    repeated float  price = 10;             //价格
}

message PriceBean{
    required int32 changeFlag = 1;        //volume和turnover是否有变化
    repeated float price = 2;
}


message DailyKLineBean{
    required string exchange = 1;           //交易所代码
    required string instrumentID = 2;       //证券ID
    required int32  barType = 3;            //bar类型，以秒为单位，比如1分钟bar, bar_type=60
    required string strTime = 4;            //可视化时间
    required double  updateTime = 5;         //行情时间戳
    required float  openPrice = 6;
    required float  highPrice = 7;
    required float  lowPrice = 8;
    required float  closePrice = 9;
    required int64  volume = 10;
    required int64  turnover = 11;

    optional int64  position = 12;           //仓位
    required float  settlePrice = 13;        //结算价
    required float  upperLimitPrice = 14;    //涨停价
    required float  lowerLimitPrice = 15;    //跌停价

    required float  preClosePrice = 16;
    optional float  adjFactor = 17;         //复权因子
    optional int32  flag = 18;              //除权出息标记
}

message Transaction{
    required string exchange = 1;           //交易所代码
    required string instrumentID = 2;       //证券ID
    required double  updateTime = 3;        //时间戳
    required string strTime = 4;            //可视化时间
    required int32  index = 5;              // 成交编号
    required float  price = 6;              // 成交价格
    required int64  volume = 7;             // 成交数量
    required double turnover = 8;           // 成交额
    required int32  direction= 9;            // 买卖方向
    optional string  orderKindFunctionCode = 10;              // 委托类别和委托代码
    optional int32 askOrder = 11;           // 卖方委托号
    optional int32 bidOrder = 12;           // 买方委托号
}

message Order{
    required string exchange = 1;           // 交易所代码
    required string instrumentID = 2;       // 证券ID
    required double updateTime = 3;         // 时间戳
    required string strTime = 4;            // 可视化时间
    required int32  orderId = 5;            // 委托号
    required float  price = 6;              // 委托价格
    required int32  volume = 7;             // 订单数量
    optional string  orderKindFunctionCode = 8;              // 委托类别和委托代码
}

message OrderQueue{
    required string exchange = 1;           // 交易所代码
    required string instrumentID = 2;       // 证券ID
    required double updateTime = 3;         // 时间戳
    required string strTime = 4;            // 可视化时间
    required int32  direction = 5;          // 买卖方向
    required float  price = 6;              // 委托价格
    required int32  orderCount = 7;         // 订单数量
    required int32  numOfDetail = 8;        // 明细个数
    repeated int32  volume = 9;             // 订单明细
}

message OrderBook{
    required string instrumentID = 1;       //证券ID
    required double  updateTime = 2;        //行情时间戳
    required float  lastPrice = 3;          //最新价
    required float  openPrice = 4;          //开盘价
    required float  highPrice = 5;          //最高价
    required float  lowPrice  = 6;          //最低价
    required int64  volume = 7;             //成交总量
    required int64  turnover = 8;           //成交总金额
    required int64  lastVolume = 9;         //成交量
    required int64  lastTurnover = 10;      //成交额
    repeated float askPrice = 11;           //买价
    repeated int32 askVolume = 12;          //买量
    repeated float bidPrice = 13;           //卖价
    repeated int32 bidVolume = 14;          //卖量
}

message QuoteResult{
    required int32 errorCode = 1;       // 错误代码
    required string errorMsg = 2;       // 错误信息
    required int32 dataType = 3;        // 数据类型
    repeated bytes data = 4;            // 数据
}
