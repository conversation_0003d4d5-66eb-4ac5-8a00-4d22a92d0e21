import { ipc<PERSON><PERSON><PERSON>, contextBridge } from 'electron';
import { Ipc<PERSON><PERSON><PERSON> } from '../shared/electron-api-types';

const api: IpcRenderer = {
  on: ipcRenderer.on.bind(ipcRenderer),
  emit: ipcRenderer.emit.bind(ipc<PERSON><PERSON>er),
  off: ipcRenderer.off.bind(ipc<PERSON><PERSON><PERSON>),
  send: ipcRenderer.send,
  invoke: ipcRenderer.invoke,
};

contextBridge.exposeInMainWorld('ipcRenderer', api);
