import { Repos } from '../../../xtrade-sdk';
import type { TickType2CallbackParamMap, TickCallbackMethod } from '../../../xtrade-sdk';

const tickRepo = new Repos.TickRepo();
class TickService {
  /**
   * 订阅合约行情
   * @param instrument - 合约代码
   * @param tickType - 行情类型
   * @param callback - 回调函数
   */
  static subscribeTick<T extends keyof TickType2CallbackParamMap>(
    instrument: string,
    tickType: T,
    callback: TickCallbackMethod<T>,
  ): void {
    tickRepo.SubscribeTick(instrument, tickType, callback);
  }

  /**
   * 退订合约行情
   * @param instrument - 合约代码
   * @param tickType - 行情类型
   * @param callback - 回调函数
   */
  static unsubscribeTick<T extends keyof TickType2CallbackParamMap>(
    instrument: string,
    tickType: T,
    callback: TickCallbackMethod<T>,
  ): void {
    tickRepo.UnsubscribeTick(instrument, tickType, callback);
  }
}

export default TickService;
