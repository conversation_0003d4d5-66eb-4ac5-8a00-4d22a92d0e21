<div class="new-report body-main">
    <template>
        <div v-if="showPanel" class="template-header no-print">
            <transition name="custom-fade">
                <el-select :style="selectStyle" v-model="templateId">
                    <el-option v-for="template in templates" :key="template.id"
                        :label="template.report_info.title_format" :value="template.id"></el-option>
                </el-select>
            </transition>
            <i class="toggle-btn iconfont icon-caidan" @click="toggle"></i>
            <i class="normal-btn iconfont icon-export" @click="handleExport"></i>
        </div>
        <div v-if="regression_detail !== null" class="regression-container" v-cloak>
            <!--<span class="regression-title" align="left">回测参数：</span>-->
            <div class="regression-msg-item regression-msg-money">
                <span class="regression-msg-title">本金</span>
                <br />
                <span>{{ typeof(regression_detail.baseMoney * 1.0) === 'number' ? thousands(regression_detail.baseMoney) + ' 元' : '' }}</span>
            </div>
            <div class="regression-msg-item regression-msg-time">
                <span class="regression-msg-title">开始回测时间</span>
                <br />
                <span>{{ regression_detail.startTime }}</span>
            </div>
            <div class="regression-msg-item regression-msg-time">
                <span class="regression-msg-title">结束回测时间</span>
                <br />
                <span>{{ regression_detail.endTime }}</span>
            </div>
            <div class="regression-msg-item">
                <span class="regression-msg-title">基准代码</span>
                <br />
                <span>{{ regression_detail.benchSymbol }}</span>
            </div>
            <div class="regression-msg-item regression-msg-percentage">
                <span class="regression-msg-title">买入费率</span>
                <br />
                <span>{{ percentage(regression_detail.buyCommissionRatio * 1.0) }}</span>
            </div>
            <div class="regression-msg-item regression-msg-percentage">
                <span class="regression-msg-title">卖出费率</span>
                <br />
                <span>{{ percentage(regression_detail.sellCommissionRatio * 1.0) }}</span>
            </div>
            <div class="regression-msg-item regression-msg-percentage">
                <span class="regression-msg-title">成交比率</span>
                <br />
                <span>{{ percentage(regression_detail.transactionRatio * 1.0) }}</span>
            </div>
            <div class="regression-msg-item regression-msg-percentage">
                <span class="regression-msg-title">保证金率</span>
                <br />
                <span>{{ percentage(regression_detail.marginRatio * 1.0) }}</span>
            </div>
            <div class="regression-msg-item regression-msg-percentage">
                <span class="regression-msg-title">滑点率</span>
                <br />
                <span>{{ percentage(regression_detail.slippageRatio * 1.0) }}</span>
            </div>
            <div class="regression-msg-item regression-msg-percentage">
                <span class="regression-msg-title">状态</span>
                <br />
                <span v-html="formatRegStatus(regression_detail.isEnd)"></span>
            </div>
        </div>
        <div class="clearfix"></div>
        <preview-area :destroy="destroy" :controller="controller" :identity-id="identity" :raw-data="rawData"
            :draggable="false" :template-detail="templateDetail" ref="previewArea" :diff="35"></preview-area>
    </template>
</div>