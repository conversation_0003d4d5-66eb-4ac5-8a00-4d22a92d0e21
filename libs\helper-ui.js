﻿/**
 * helpers specially prepared for UI
 */

const systemEnum = require('../config/system-enum').systemEnum;
const systemTrdEnum = require('../config/system-enum.trading').systemTrdEnum;
const systemSetting = require('../config/system-setting').systemSetting;
const { helper } = require('./helper');
const { AccountDetail } = require('../model/account');
const AllAssetTypes = [];
const AllBusinessFlags = [];

const helperUi = {

    makeDomId: function () {
        return 'node-' + Math.random().toString().replace('.', '');
    },

    formatAssetType: function (asset_type) {

        if (AllAssetTypes.length == 0) {
            AllAssetTypes.merge(helper.dict2Array(systemEnum.assetsType));
        }

        let matched = AllAssetTypes.find((x) => x.code === asset_type) || {};
        let asset_name = matched.mean || '未知资产';
        return asset_name;
    },

    /**
     *
     * @param {Function|String} handler function or function name
     * @param {String} class_name element switch class names (in string)
     */
    formatSwitchButton(handler, class_name) {
        
        var handler_name = typeof handler == 'function' ? handler.name : typeof handler == 'string' ? handler : null;
        var inline_click_str = handler_name ? `event.onclick="${handler_name}"` : '';
        return `<div class="el-switch ${class_name || ''}" ${inline_click_str}>
                    <input type="checkbox" class="el-switch__input">
                    <span class="el-switch__core" style="width: 40px;"></span>
                </div>`;
    },

    formatSelectAccountName(account) {

        if (typeof account == 'string') {
            return account;
        }
        else if (typeof account == 'object' && account !== null) {

            let { financeAccountName, financeAccount, accountName, label, identityName, fundName, strategyName } = account;
            let iname = accountName || identityName || label;
            let aname = financeAccountName || financeAccount || iname;

            if (strategyName) {
                return aname ? `${aname} (${strategyName})` : strategyName;
            }
            else {
                if (iname == aname) {
                    return aname;
                }
                else {
                    return aname ? `${aname} (${iname})` : iname;
                }
            }
        }
        else {
            return account;
        }
    },

    makeOrderStatusHtml: function (status_code) {

        var ords = systemEnum.orderStatus;
        var shared = 's-flag s-order';

        switch (status_code) {

            case ords.created.code:
                return `<span class='${shared}-preparing'>${ords.created.mean}</span>`;
            case ords.notConfirmed.code:
                return `<span class='${shared}-preparing'>${ords.notConfirmed.mean}</span>`;
            case ords.confirmed.code:
                return `<span class='${shared}-preparing'>${ords.confirmed.mean}</span>`;
            case ords.notTraded.code:
                return `<span class='${shared}-waiting'>${ords.notTraded.mean}</span>`;
            case ords.partialTraded.code:
                return `<span class='${shared}-progressing'>${ords.partialTraded.mean}</span>`;
            case ords.partialCanceled.code:
                return `<span class='${shared}-ok'>${ords.partialCanceled.mean}</span>`;
            case ords.traded.code:
                return `<span class='${shared}-ok'>${ords.traded.mean}</span>`;
            case ords.invalid.code:
                return `<span class='${shared}-invalid'>${ords.invalid.mean}</span>`;
            case ords.canceled.code:
                return `<span class='${shared}-canceled'>${ords.canceled.mean}</span>`;
            case ords.toCancel.code:
                return `<span class='${shared}-waiting'>${ords.toCancel.mean}</span>`;
            case ords.tobeAudit.code:
                return `<span class='${shared}-waiting'>${ords.tobeAudit.mean}</span>`;
            case ords.reject.code:
                return `<span class='${shared}-waiting'>${ords.reject.mean}</span>`;
            default:
                return status_code || systemSetting.nullValue;
        }
    },

    makeOrderStatusText: function (status_code) {

        var ords = systemEnum.orderStatus;

        switch (status_code) {

            case ords.created.code:
                return ords.created.mean;
            case ords.notConfirmed.code:
                return ords.notConfirmed.mean;
            case ords.confirmed.code:
                return ords.confirmed.mean;
            case ords.notTraded.code:
                return ords.notTraded.mean;
            case ords.partialTraded.code:
                return ords.partialTraded.mean;
            case ords.partialCanceled.code:
                return ords.partialCanceled.mean;
            case ords.traded.code:
                return ords.traded.mean;
            case ords.invalid.code:
                return ords.invalid.mean;
            case ords.canceled.code:
                return ords.canceled.mean;
            case ords.toCancel.code:
                return ords.toCancel.mean;
            default:
                return status_code || systemSetting.nullValue;
        }
    },

    makeDirectionHtml: function (direction, asset_type, effect_flag) {

        if (asset_type === systemEnum.assetsType.stock.code) {
            return direction === systemTrdEnum.tradingDirection.buy.code ? '<span class="s-flag s-bg-red">买</span>' : '<span class="s-flag s-bg-green">卖</span>';
        } 
        else {

            var direction_name = direction === systemTrdEnum.tradingDirection.buy.code ? '买' : '卖';
            var effect_name = effect_flag === undefined || effect_flag === null ? '' : effect_flag === 0 ? '开' : '平';
            var operation_name = direction_name + effect_name;
            return direction === systemTrdEnum.tradingDirection.buy.code ? `<span class='s-flag s-bg-red'>${operation_name}</span>` : `<span class='s-flag s-bg-green'>${operation_name}</span>`;
        }
    },

    makeDirectionText: function (field_value, asset_type, effect_flag) {

        if (asset_type === systemEnum.assetsType.stock.code) {
            return field_value === systemTrdEnum.tradingDirection.buy.code ? '买' : '卖';
        } 
        else {

            let direction_name = field_value === systemTrdEnum.tradingDirection.buy.code ? '买' : '卖';
            let effect_name = effect_flag === undefined || effect_flag === null ? '' : effect_flag === 0 ? '开' : '平';
            let operation_name = direction_name + effect_name;
            return field_value === systemTrdEnum.tradingDirection.buy.code ? operation_name : operation_name;
        }
    },

    makeBusinessFlagText: function (business_flag) {
        
        if (AllBusinessFlags.length == 0) {
            AllBusinessFlags.merge(helper.dict2Array(systemTrdEnum.businessFlag));
        }

        let matched = AllBusinessFlags.find(item => item.code === business_flag) || {};
        let flag_name = matched.mean || '未知标识';
        return flag_name;
    },

    makeYesNoLabelHtml: function (flag, options) {

        options = options || { revertColor: false, yesLabel: '是', noLabel: '否' };
        var class_name = !!flag || (!flag && options.revertColor === true) ? 's-bg-green' : 's-bg-red';
        var label = !!flag ? options.yesLabel : options.noLabel;
        return `<span class="s-flag ${class_name}">${label}</span>`;
    },

    makeYesNoLabelText: function (flag, options) {

        options = options || { yesLabel: '是', noLabel: '否' };
        var label = !!flag ? options.yesLabel : options.noLabel;
        return label;
    },

    extractPaginationCondition: function (payload) {

        if (!payload) {
            return { pageSize: systemSetting.tablePagination.pageSize, pageNo: 1 };
        }

        var sorting = payload.sortInfo || payload.sort;

        return {

            pageSize: payload.pageSize,
            pageNo: payload.page,
            sort: !sorting || sorting.prop === undefined || sorting.order === undefined ? null : `${sorting.prop} ${sorting.order === 'ascending' ? 'asc' : 'desc'}`,
        };
    },

    /**
     * 从一个账号信息上提取其account id
     * 1. 实体账号，取accountId
     * 2. 虚拟账号（具有strategyId的账号，就视为虚拟账号），取id
     * @param {AccountDetail} account 
     */
    getProperAccountId(account) {
        
        let { strategyId, id, accountId } = account || {};
        return strategyId ? id : accountId || null;
    }
};

module.exports = { helperUi };
