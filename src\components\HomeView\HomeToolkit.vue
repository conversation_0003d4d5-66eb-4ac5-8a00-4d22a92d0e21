<script setup lang="ts">
import { LoginService } from '@/api';
import { getUser, setUser } from '@/script';
import { ref } from 'vue';
import router from '@/router';

const user = ref(getUser())!;

const handleLogout = async () => {
  await LoginService.logout();
  setUser();
  router.push({ name: 'login' });
};
</script>

<template>
  <div class="home-toolkit" w-310 flex aic gap-20>
    <span flex aic gap-24>
      <i class="iconfont icon-more" thb1></i>
      <i class="iconfont icon-setting" thb1></i>
      <i class="iconfont icon-calendar" thb1></i>
      <i class="iconfont icon-bell" thb1></i>
    </span>
    <el-dropdown trigger="hover">
      <span flex aic gap-10 cursor-pointer>
        <img src="../../assets/image/avatar.png" w-40 h-40 rounded-full />
        <div v-if="user" w-60 overflow-hidden cursor-pointer>
          <label
            :title="user.fullName"
            block
            w-full
            fs-14
            fw-600
            lh-20
            toe
            color="[--g-text-color-2]"
            cursor-pointer
          >
            {{ user.fullName }}
          </label>
          <label cursor-pointer block w-full fw-400 lh-16 toe color="[--g-text-color-3]">
            {{ user.roleName }}
          </label>
        </div>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<style scoped>
.home-toolkit {
  i {
    font-size: 24px;
  }
}
</style>
