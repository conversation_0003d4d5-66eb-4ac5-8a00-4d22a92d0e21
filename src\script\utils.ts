export const isJson = (obj: any) => {
  const class2type = {};
  const toString = class2type.toString;
  const hasOwn = class2type.hasOwnProperty;
  const fnToString = hasOwn.toString;
  const ObjectFunctionString = fnToString.call(Object);

  if (!obj || toString.call(obj) !== '[object Object]') {
    return false;
  }

  const proto = Object.getPrototypeOf(obj);

  // Objects with no prototype (e.g., `Object.create( null )`) are plain
  if (!proto) {
    return true;
  }

  // Objects with prototype are plain if they were constructed by a global Object function
  const ctor = hasOwn.call(proto, 'constructor') && proto.constructor;
  return typeof ctor === 'function' && fnToString.call(ctor) === ObjectFunctionString;
};

export const isNotJson = (obj: any) => {
  return !isJson(obj);
};

export const deepAssign = (target: any, ...sources: any[]) => {
  if (!isJson(target)) {
    return target;
  } else if (!sources.length) {
    return target;
  }

  while (sources.length > 0) {
    const source = sources.shift();
    if (!isJson(source)) {
      continue;
    }

    for (const key in source) {
      const sourceValue = source[key];
      const targetValue = target[key];

      if (isJson(sourceValue) && isJson(targetValue)) {
        // 如果都是对象，递归合并
        deepAssign(targetValue, sourceValue);
      } else if (isJson(sourceValue)) {
        // 如果源值是对象但目标不是，则目标设为空对象再赋值
        target[key] = deepAssign({}, sourceValue);
      } else {
        // 否则直接赋值
        target[key] = sourceValue;
      }
    }
  }

  return target;
};

export const sleep = async (ms: number) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 设置本地存储项
 *
 * 该方法用于将数据存储到浏览器的本地存储中它支持存储字符串或对象类型的数据
 * 如果提供的值为undefined或null，则会从本地存储中移除相应的项
 *
 * @param name 存储项的名称
 * @param value 存储项的值，可以是字符串、对象或undefined/null
 */
export const setLocal = (name: string, value: string | object | undefined) => {
  if (value === undefined || value === null) {
    localStorage.removeItem(name);
  } else if (typeof value === 'object') {
    localStorage.setItem(name, JSON.stringify(value));
  } else {
    localStorage.setItem(name, value);
  }
};

/**
 * 创建一个防抖函数
 *
 * 防抖函数会在一定时间内（wait毫秒）只执行一次回调。
 * 如果在等待时间内再次调用，会重置计时器。
 *
 * @param func 需要进行防抖处理的函数
 * @param wait 等待时间，单位为毫秒
 * @returns 返回一个新的防抖函数
 *
 * @example
 * const debouncedFn = debounce((value: string) => {
 *   console.log(value);
 * }, 300);
 *
 * // 多次快速调用，只会在最后一次调用后300ms执行一次
 * debouncedFn("test1");
 * debouncedFn("test2");
 * debouncedFn("test3"); // 只有这次会在300ms后执行
 */
export const debounce = <T extends (...args: Parameters<T>) => ReturnType<T>>(
  func: T,
  wait: number,
) => {
  let timeout: NodeJS.Timeout | undefined;
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
};

/**
 * 创建一个节流函数
 *
 * 节流函数会确保在指定的时间间隔内最多只执行一次回调函数。
 * 如果在等待时间内多次调用，只有第一次调用会被执行。
 *
 * @param func 需要进行节流处理的函数
 * @param wait 等待时间，单位为毫秒
 * @returns 返回一个新的节流函数
 *
 * @example
 * const throttledFn = throttle((value: string) => {
 *   console.log(value);
 * }, 300);
 *
 * // 多次快速调用，每300ms最多执行一次
 * throttledFn("test1"); // 会执行
 * throttledFn("test2"); // 被忽略
 * throttledFn("test3"); // 被忽略
 */
export const throttle = <T extends (...args: Parameters<T>) => ReturnType<T>>(
  func: T,
  wait: number,
) => {
  let timeout: NodeJS.Timeout | undefined;
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (!timeout) {
      timeout = setTimeout(() => {
        func.apply(this, args);
        timeout = undefined;
      }, wait);
    }
  };
};

/**
 * 判断传入的参数是否是有效数字
 *
 * @param value 要判断的值
 * @returns 如果是有效数字返回true，否则返回false
 */
export const isValidNumber = (value: unknown): value is number => {
  if (typeof value === 'number') {
    return !isNaN(value);
  }
  if (typeof value === 'string' && value.trim() !== '') {
    return !isNaN(Number(value)) && isFinite(Number(value));
  }
  return false;
};

/**
 * 从localStorage中获取指定名称的数据
 * 如果数据不存在，返回默认值或null
 *
 * @param name 存储名称
 * @param defaultValue 可选的默认值
 * @returns 存储的数据，如果不存在则返回默认值或null
 */
export function getLocal<T>(name: string): T | null;
export function getLocal<T>(name: string, defaultValue: T): T;
export function getLocal<T>(name: string, defaultValue?: T): T | null {
  const val = localStorage.getItem(name);
  if (val) {
    try {
      return JSON.parse(val);
    } catch (ex) {
      console.warn('Failed to parse local storage value:', val, ex);
      return val as any as T;
    }
  } else if (defaultValue !== undefined) {
    return defaultValue;
  } else {
    return null;
  }
}

/**
 * 生成一个随机字符串
 *
 * @param length 字符串长度，默认为16
 * @returns 生成的随机字符串
 */
export const randomString = (length = 16) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const maxPos = chars.length;
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return result;
};

/**
 * 深度克隆对象或数组
 *
 * @param obj 要克隆的对象或数组
 * @returns 深度克隆后的对象或数组
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as T;
  }

  const clonedObj = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }

  return clonedObj;
};

export const isNone = (obj: unknown) => {
  return obj === undefined || obj === null || (typeof obj == 'string' && obj.trim().length == 0);
};

export const isNotNone = (obj: unknown) => {
  return !isNone(obj);
};

/**
 * 判断数字是否是正数
 *
 * @param num 要判断的数字
 * @returns 如果是正数返回true，否则返回false
 */
export const isPositive = (num: number): boolean => {
  return num > 0;
};

/**
 * 将TypeScript枚举转换为数组
 *
 * @param enumObj 要转换的枚举对象
 * @returns 转换后的数组，每个元素包含label和value属性
 *
 * @example
 * enum Direction { Up = 1, Down = -1 }
 * enumToArray(Direction) // 返回 [{label: 'Up', value: 1}, {label: 'Down', value: -1}]
 *
 * enum Color { Red = 'red', Blue = 'blue' }
 * enumToArray(Color) // 返回 [{label: 'Red', value: 'red'}, {label: 'Blue', value: 'blue'}]
 */
export const enumToArray = <T extends Record<string, unknown>>(enumObj: T) => {
  const arr: Array<{ label: string; value: T[keyof T] }> = [];
  const type = Object.values(enumObj).some(val => typeof val === 'number') ? 'number' : 'string';
  for (const label in enumObj) {
    if (typeof enumObj[label] === type) {
      const value = enumObj[label];
      arr.push({ label, value });
    }
  }
  return arr;
};

/**
 * to compare two values
 * @param asc sorting, by default = ascending
 * @returns a > b = 1; a < b = -1; a = b = 0;
 */
export const compare = (a: any, b: any, asc = true) => {
  const Regex = {
    all_cn: /^[\u4E00-\u9FA5]+$/,
    has_cn: /[\u4E00-\u9FA5]+/,
    no_cn: /^[a-zA-Z0-9~!@#$%^&*()-_=+[\]\\<>,;"'./|]+$/,
    all_en: /^[a-zA-Z]+$/,
    all_digit: /^[0-9]+$/,
  };

  const bigger = 1,
    same = 0,
    smaller = -1;
  const flag = asc ? 1 : -1;
  let result = same;

  if (a === b) {
    return same;
  }

  const _1st = {
    isempty: a === undefined || a === null || (typeof a == 'string' && a.trim().length == 0),
    isnum: typeof a == 'number',
    isstr: typeof a == 'string',
  };

  const _2nd = {
    isempty: b === undefined || b === null || (typeof b == 'string' && b.trim().length == 0),
    isnum: typeof b == 'number',
    isstr: typeof b == 'string',
  };

  const tostr = (value: object) => {
    return value.toString();
  };

  const is_cn = (value: string) => {
    return Regex.all_cn.test(value);
  };

  if (_1st.isempty) {
    result = _2nd.isempty ? same : smaller;
  } else if (_1st.isnum) {
    if (_2nd.isempty) {
      result = bigger;
    } else if (_2nd.isnum) {
      result = a > b ? bigger : a < b ? smaller : same;
    } else if (_2nd.isstr) {
      result = tostr(a as object).localeCompare(b as string);
    }
  } else if (_1st.isstr) {
    if (_2nd.isempty) {
      result = bigger;
    } else if (_2nd.isnum) {
      result = (a as string).localeCompare(tostr(b as object));
    } else if (_2nd.isstr) {
      const len_a = (a as string).length;
      const len_b = (b as string).length;
      const shorter = Math.min(len_a, len_b);

      for (let cur = 0; cur < shorter; cur++) {
        const c1 = (a as string)[cur];
        const c2 = (b as string)[cur];

        if (c1 != c2) {
          if (is_cn(c1)) {
            if (is_cn(c2)) {
              result = c1.localeCompare(c2, 'zh-CN');
            } else {
              result = bigger;
            }
          } else {
            if (is_cn(c2)) {
              result = smaller;
            } else {
              result = c1.localeCompare(c2);
            }
          }

          break;
        }
      }

      if (result == same) {
        result = len_a > len_b ? bigger : len_a < len_b ? smaller : same;
      }
    }
  }

  return result * flag;
};

export const grandSearch = <T = unknown>(
  data: Record<string, T>[],
  options: { keyword: string; fields?: string[] },
): Record<string, T>[] => {
  const { keyword, fields } = options || {};

  if (!Array.isArray(data) || data.length == 0 || !keyword) {
    return data;
  }

  const lowered = keyword.toLowerCase();
  const has_fields = Array.isArray(fields) && fields.length > 0;

  if (has_fields) {
    return data.filter(item => {
      const values = fields.map(field => item[field]);
      return values.some(value => {
        return value?.toString().toLowerCase().includes(lowered);
      });
    });
  } else {
    return data.filter(item => {
      return Object.values(item).some(value => {
        return value?.toString().toLowerCase().includes(lowered);
      });
    });
  }
};

/**
 * to delete(remove) some elements from an array
 * @param arr the target array
 */
export const remove = <T = unknown>(arr: Array<T>, predict: (ele: T) => boolean) => {
  const indexes = [] as number[];
  arr.forEach((ele, ele_idx) => predict(ele) === true && indexes.push(ele_idx));
  while (indexes.length > 0) {
    arr.splice(indexes.pop()!, 1);
  }
};

/**
 * 提取一个序列当中不同的值
 * @param arr 目标序列
 * @param predicate 推断函数
 */
export const distinct = <T, K>(arr: T[], predicate: (obj: T) => K): K[] => {
  const is_func = typeof predicate == 'function';
  const distincts: K[] = [];

  for (let idx = 0; idx < arr.length; idx++) {
    const ele = arr[idx] as any;
    const expected = is_func ? predicate(ele) : ele;
    if (!distincts.includes(expected)) {
      distincts.push(expected);
    }
  }

  return distincts;
};

/**
 * 使用异或运算加密字符串
 *
 * @param originalContent 需要加密的原始字符串
 * @returns 加密后的字符串，如果输入为空则返回空字符串
 *
 * @example
 * aesEncrypt('hello') // 返回加密后的字符串
 */
export const aesEncrypt = (originalContent: string): string => {
  if (!originalContent?.trim()) {
    return '';
  }

  const key = 'DaBingGe';
  const snNum: number[] = [];
  let result = '';

  // 使用异或运算加密每个字符
  for (let i = 0, j = 0; i < originalContent.length; i++, j = (j + 1) % key.length) {
    snNum.push(originalContent.charCodeAt(i) ^ key.charCodeAt(j));
  }

  // 将加密后的数字转换为固定长度的字符串
  for (const num of snNum) {
    result += num.toString().padStart(3, '0');
  }

  return result;
};

/**
 * 解密由aesEncrypt加密的字符串
 *
 * @param encryptedContent 加密后的字符串
 * @returns 解密后的原始字符串，如果输入为空则返回空字符串
 *
 * @example
 * const encrypted = aesEncrypt('hello');
 * aesDecrypt(encrypted) // 返回 'hello'
 */
export const aesDecrypt = (encryptedContent: string): string => {
  if (!encryptedContent?.trim()) {
    return '';
  }

  const key = 'DaDiaoGe666';
  const snNum = [];
  let result = '';

  // 每3个字符解析为一个数字
  for (let i = 0; i < encryptedContent.length; i += 3) {
    snNum.push(parseInt(encryptedContent.slice(i, i + 3), 10));
  }

  // 使用异或运算解密每个字符
  for (let i = 0, j = 0; i < snNum.length; i++, j = (j + 1) % key.length) {
    result += String.fromCharCode(snNum[i] ^ key.charCodeAt(j));
  }

  return result;
};

/**
 * 在一个数字数组中找到与传入值最接近的值
 */
export function findNearestValue<T>(arr: Array<T>, expected: number, prediction: (p: T) => number) {
  let nearest = arr[0];
  let diff = Math.abs(prediction(nearest) - expected);

  arr.forEach(each_ele => {
    const each_value = prediction(each_ele);
    const cur_diff = Math.abs(each_value - expected);
    if (cur_diff < diff) {
      nearest = each_ele;
      diff = cur_diff;
    }
  });

  return nearest;
}

/**
 * 对一个数值序列进行求和
 * @param digits 目标序列
 * @returns
 */
export function sum(digits: Array<number>) {
  let total = 0;
  digits.forEach(x => (total += x));
  return total;
}

/**
 * 从一个数值序列找到最大的数
 * @param digits 目标序列
 */
export function max(digits: Array<number>) {
  let value = digits[0];
  digits.forEach(x => x > value && (value = x));
  return value;
}

/**
 * 从一个数值序列找到最大的数序号
 * @param digits 目标序列
 */
export function maxIndex(digits: Array<number>) {
  let value = digits[0];
  let index = 0;
  digits.forEach((x, _) => {
    if (x > value) {
      value = x;
      index = _;
    }
  });
  return index;
}

/**
 * 从一个数值序列找到最小的数
 * @param digits 目标序列
 */
export function min(digits: Array<number>) {
  let value = digits[0];
  digits.forEach(x => x < value && (value = x));
  return value;
}

/**
 * 从一个数值序列找到最小的数序号
 * @param digits 目标序列
 */
export function minIndex(digits: Array<number>) {
  let value = digits[0];
  let index = 0;
  digits.forEach((x, _) => {
    if (x < value) {
      value = x;
      index = _;
    }
  });
  return index;
}
