const httpRequest = require('../libs/http').http;
const { RiskGroup, RiskGroupItem } = require('../model/risk');
const { RiskMessage } = require('../model/risk-message');

class riskRepository {

    constructor() {

        this.urls = {
            config: '/risk/configuration',
            behavior: '/risk/behavior',
            flowcontrol: '/risk/flowcontrol',
            griskc: '/global/risk',
            group: '../v4/fund/group',
            group_item: '../v4/fund/group/detail',
        };        
    }

    /**
     * @param {string | number} identity
     * @returns {{ errorCode, errorMsg, data: Array<RiskMessage> }}
     */
    getRiskMessage(identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/message', { params: { identity, sort: 'createTime desc' } }).then(
                resp => { resolve(resp.data); }, 
                err => { reject(err); });
        });
    }

    /**
     * @param {{ fundId, strategyId, accountId, start, end, pageNo, pageSize }} scope
     * @returns {{ errorCode, errorMsg, data: any }}
     */
    searchRiskMessage(scope) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/message/search', { params: scope }).then(
                resp => { resolve(resp.data); }, 
                err => { reject(err); });
        });
    }

    saveTemplate (templateModels) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/risk/template', templateModels).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    getTemplateList() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/template').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    deleteTemplate(id) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/risk/template', {
                params: {
                    template_id: id
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getRiskRuleList (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get(this.urls.config, { params: { identity } })
                        .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    saveRiskRule (configuration) {
        return new Promise((resolve, reject) => {
            httpRequest.post(this.urls.config, configuration).then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    updateRiskRule(configuration) {
        return new Promise((resolve, reject) => {
            httpRequest.put(this.urls.config, configuration).then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    deleteRiskRule(configuration_id) {
        return new Promise((resolve, reject) => {
            httpRequest.delete(this.urls.config, { params: { configuration_id }})
                        .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getTradeBehavior (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get(this.urls.flowcontrol, { params: { identity } })
                        .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    saveTradeBehavior (setting) {
        return new Promise((resolve, reject) => {
            httpRequest.post(this.urls.flowcontrol, setting).then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getGlobalRiskControlSetting() {
        return new Promise((resolve, reject) => {
            httpRequest.get(this.urls.griskc)
                        .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    saveGlobalRiskControlSetting(setting) {
        return new Promise((resolve, reject) => {
            httpRequest.post(this.urls.griskc, setting).then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getRiskRuleTemplate(templateId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/template/detail', {
                params: {
                    template_id: templateId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getRiskKind () {
        return new Promise((resolve,reject) => {
            httpRequest.get('/risk/riskkind').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    saveLimitRule (limitations) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/risk/restriction', limitations).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    updateLimitRule(limitations) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/risk/restriction', limitations).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getLimitRules (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/restriction', {
                params: {
                    identity: identity
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    deleteLimitRules (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/risk/restriction', {
                params: {
                    restriction_id: identity
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getRiskClassification () {
        return new Promise((resolve,reject) => {
            httpRequest.get('/risk/classification').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: Array<RiskGroup> }}
     */
    getRiskGroups () {
        return new Promise((resolve,reject) => {
            httpRequest.get(this.urls.group).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: RiskGroup }}
     */
    createRiskGroup (group) {
        return new Promise((resolve,reject) => {
            httpRequest.post(this.urls.group, group).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: RiskGroup }}
     */
    updateRiskGroup (group) {
        return new Promise((resolve,reject) => {
            httpRequest.put(this.urls.group, group).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    deleteRiskGroup (id) {
        return new Promise((resolve,reject) => {
            httpRequest.delete(this.urls.group, { params: { id } }).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: Array<RiskGroupItem> }}
     */
    getRiskGroupItems (group_id) {
        return new Promise((resolve,reject) => {
            httpRequest.get(this.urls.group_item, { params: { group_id } }).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: RiskGroupItem }}
     */
    createRiskGroupItem (fundGroupDetail) {
        return new Promise((resolve,reject) => {
            httpRequest.post(this.urls.group_item, fundGroupDetail).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    /**
     * @returns {{ errorCode, errorMsg, data: RiskGroupItem }}
     */
    updateRiskGroupItem (fundGroupDetail) {
        return new Promise((resolve,reject) => {
            httpRequest.put(this.urls.group_item, fundGroupDetail).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }

    deleteRiskGroupItem (id) {
        return new Promise((resolve,reject) => {
            httpRequest.delete(this.urls.group_item, { params: { id } }).then(resp => { resolve(resp.data); },  err => { reject(err); });
        });
    }
}

module.exports = { repoRisk:  new riskRepository() };