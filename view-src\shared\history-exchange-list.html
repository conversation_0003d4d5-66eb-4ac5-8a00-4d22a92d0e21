<div class="summary-history-exchange">

	<div class="user-toolbar themed-box">

		<el-date-picker size="mini" type="daterange" v-model="condition.dateRange" start-placeholder="开始日期" range-separator="至" end-placeholder="结束日期"></el-date-picker>
        <el-select size="mini" placeholder="账号" v-model="condition.accountId" class="s-mgl-10 s-w-240" filterable clearable>
            <el-option v-for="(item, item_idx) in condition.accounts" :key="item_idx" :value="item.accountId" :label="formatSelectAccountName(item)"></el-option>
        </el-select>
        <el-input size="mini" placeholder="完整合约代码" v-model.trim="condition.keywords" class="s-mgl-10 s-w-120" clearable></el-input>
        <el-button size="mini" type="primary" size="small" @click="search" class="s-mgl-10">查询</el-button>
		
		<el-pagination :page-sizes="paging.pageSizes" 
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total" 
					   :current-page.sync="paging.page"
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange" 
					   @current-change="handlePageChange"></el-pagination>

	</div>

	<div class="table-control">
		<table>
            <tr>
                <th label="报单编号" min-width="80" prop="exchangeOrderId" overflowt></th>
                <th label="账号名称" min-width="202.0" prop="accountName" formatter="formatAccountName" overflowt sortable searchable></th>
                <th label="发起人" min-width="90" prop="userName" overflowt sortable searchable></th>
                <th label="代码" fixed-width="100" prop="instrument" overflowt sortable searchable></th>
                <th label="名称" fixed-width="80" prop="instrumentName" overflowt sortable searchable></th>
                <th label="交易日" fixed-width="80" prop="tradingDay" sortable></th>

                <th type="program" 
                    label="方向" 
                    fixed-width="70" 
                    prop="direction" 
                    watch="direction" 
                    formatter="formatDirection" 
                    export-formatter="formatDirectionText"
                    export-formatter="formatDirectionText" sortable></th>

                <th label="成交量" fixed-width="80" prop="volume" align="right" summarizable thousands-int></th>
                <th label="成交价" fixed-width="60" prop="tradedPrice" align="right" formatter="formatPrice"></th>
                <th label="手续费" min-width="80" prop="commission" align="right" thousands summarizable></th>
                <th label="成交编号" min-width="150" prop="tradeId" overflowt></th>

                <th type="program" 
                    label="成交时间" 
                    fixed-width="100" 
                    prop="tradeTime" 
                    watch="tradeTime" 
                    formatter="formatTime" sortable></th>

                <th type="program" 
                    label="资产类型" 
                    fixed-width="100" 
                    prop="assetType" 
                    watch="assetType" 
                    formatter="formatAssetType" sortable></th>
                
                <th label="产品" min-width="150" prop="fundName" sortable overflowt searchable></th>
            </tr>
        </table>
	</div>

</div>