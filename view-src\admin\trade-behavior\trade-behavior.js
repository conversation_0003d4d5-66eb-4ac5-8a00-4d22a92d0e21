const { IView } = require('../../../component/iview');
const drag = require('../../../directives/drag');
const { repoRisk } = require('../../../repository/risk');

class TradeBehaviorDialog extends IView {

    constructor(view_name, is_standalone_window, title = '交易行为设置') {

        super(view_name, is_standalone_window, title);

        this.context = {

            identityType: null,
            identity: null,
            name: null,
            id: null,
        };

        this.behavior = {

            /** 是否启用 */
            riskEnable: false,
            /** 订单自成交检查 */
            orderSelfTransaction: true,
            /** 母单自成交检查 */
            algoOrderSelfTransaction: true,
            /** 沪深各市场流速（笔/秒） */
            flowSpeed: 0,
            /** 账户总委托数（笔） */
            totalEntrust: 0,
            /** 净买入金额（万） */
            netBuyAmount: 0,
            /** 单笔委托金额（万） */
            singleOrderAmount: 0,
            /** 单笔委托数量（手） */
            singleOrderVolume: 0,
            /** 比例规则启用阈值 */
            ratioEnableThreshold: 0,
            /** 撤单比 */
            cancelRatio: 0,
            /** 废单比 */
            invalidRatio: 0,
            /** 委托价格偏离度（价格笼子，仅股票有效） */
            cagePriceLimit: 2,
        };

        this.account = {

            /** 撤单率 (%) */
            cancelRate: null,
            /** 下单速度 */
            orderSpeed: null,
            /** 错单数 */
            errorCount: null,
            /** 资金检查（可用检查） */
            availableCheck: false,
            /** 持仓检查 */
            positionCheck: false,
            /** 比对覆盖 */
            overlapBalance: false,
        };
    }

    is4Account() {
        return this.context.identityType == this.systemEnum.identityType.account.code;
    }

    setContext({ identityType, identity, name }) {

        this.context.id = null;
        Object.assign(this.context, { identityType, identity, name });
        this.requestSetting();
    }

    resetMap(map) {

        for (let key in map) {
            let val = map[key];
            map[key] = typeof val == 'boolean' ? false : null;
        }
    }

    extractProps(data) {

        let { cancelRate, orderSpeed, errorCount, availableCheck, positionCheck, overlapBalance } = data;
        return { cancelRate, orderSpeed, errorCount, availableCheck, positionCheck, overlapBalance };
    }

    async requestSetting() {
        
        let loading = this.interaction.showLoading({ text: '正在获取设置...' });
        let resp = await repoRisk.getTradeBehavior(this.context.identity);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {

            if (this.helper.isJson(data)) {
                
                let { 

                    riskEnable,
                    orderSelfTransaction, 
                    algoOrderSelfTransaction,
                    flowSpeed,
                    totalEntrust,
                    netBuyAmount,
                    singleOrderAmount,
                    singleOrderVolume,
                    ratioEnableThreshold,
                    cancelRatio,
                    invalidRatio,
                    cagePriceLimit,

                } = data;

                let behavior_data = { 

                    riskEnable: !!riskEnable,
                    orderSelfTransaction, 
                    algoOrderSelfTransaction,
                    flowSpeed,
                    totalEntrust,
                    netBuyAmount,
                    singleOrderAmount,
                    singleOrderVolume,
                    ratioEnableThreshold,
                    cancelRatio,
                    invalidRatio,
                    cagePriceLimit,
                };

                let props = this.extractProps(data);
                Object.assign(this.behavior, behavior_data);
                if (this.is4Account()) {
                    Object.assign(this.account, props);
                }

                this.context.id = data.id;
            }
            else {

                this.context.id = null;
                this.resetMap(this.behavior);
                this.resetMap(this.account);
            }
        }
        else {
            this.interaction.showError(`查询错误：${errorCode}/${errorMsg}`);
        }

        loading.close();
    }

    isValidated() {
        
        let message = null;
        
        if (this.is4Account()) {

            let props = this.extractProps(this.account);

            // if (typeof props.cancelRate != 'number' || props.cancelRate < 0 || props.cancelRate > 100) {
            //     message = '撤单率取值 0 ~ 100';
            // }
            // else if (typeof props.orderSpeed != 'number' || props.orderSpeed < 0 || props.orderSpeed > 10000) {
            //     message = '下单速度取值 0 ~ 10000';
            // }
            // else if (typeof props.errorCount != 'number' || props.errorCount < 0 || props.errorCount > 10000) {
            //     message = '错单数取值 0 ~ 10000';
            // }
        }

        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    async save() {

        if (!this.isValidated()) {
            return;
        }

        let { identity, identityType, id } = this.context;
        let setting = Object.assign({ identity, identityType, id }, this.behavior);
        if (this.is4Account()) {
            Object.assign(setting, this.account);
        }

        let resp = await repoRisk.saveTradeBehavior(setting);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {
            this.interaction.showSuccess(`已保存`);
        }
        else {
            this.interaction.showError(`保存错误：${errorCode}/${errorMsg}`);
        }

        this.requestSetting();
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.querySelector('.trade-behavior-root'),
            directives: { drag },
            data: {
                behavior: this.behavior,
                account: this.account,
                context: this.context,
            },
            computed: {
                is4Account: () => { return this.is4Account(); },
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.save,
            ]),
        });
    }

    listen2Events() {

        this.renderProcess.on('set-context-data', (event, args) => {
            this.setContext(args);
        });
    }

    build($container) {

        this.$container = $container;
        this.listen2Events();
        this.createApp();
    }
}

module.exports = TradeBehaviorDialog;
