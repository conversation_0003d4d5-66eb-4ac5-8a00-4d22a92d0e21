.template-root {

    box-sizing: border-box;
    height: 100%;
    padding: 10px 15px 35px 15px;
    overflow-y: auto;

    * {
        box-sizing: border-box;
    }

    .top-part,
    .main-part {
        border: 1px solid rgb(86, 86, 86);
        // background-color: #23354D;
    }

    .el-input__inner {
        background-color: #23354D;
    }

    .top-part {

        min-width: 1100px;
        height: 280px;
        display: flex;
        border-radius: 8px;
        overflow-x: auto;
        overflow-y: hidden;
    
        .each-block {
    
            padding: 0 20px;
            height: 100%;
            flex-grow: 1;

            &:not(:last-child) {
                border-right: 1px solid rgb(86, 86, 86);
            }
        
            .block-title {

                margin-bottom: 20px;
                border-bottom: 1px solid rgb(86, 86, 86);
                line-height: 40px;
            }
        
            .detail-list {
                margin-top: 10px;
            }
        
            .data-item {
                
                height: 30px;
                line-height: 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
        }
    }

    .main-part {

        margin-top: 10px;
        padding: 10px 20px 20px 20px;
        border-top: 1px solid rgb(86, 86, 86);
        display: flex;
        justify-content: space-between;
        border-radius: 8px;

        .left-area {

            width: 45%;
            padding-right: 20px;
            flex-grow: 0;
            border-right: 1px solid rgb(86, 86, 86);
        }

        .right-area {

            width: 55%;
            flex-grow: 0;
            padding-left: 20px;
        }

        .area-title {

            display: flex;
            align-items: center;
            height: 30px;
            line-height: 30px;
            border-bottom: 1px solid rgb(86, 86, 86);

            > * {
                margin-right: 10px;
            }
        }

        .table-caption {

            height: 40px;
            line-height: 40px;
        }

        // .toowide-cell {

        //     .header-text {

        //         word-wrap: break-word;
        //         word-break: break-all;
        //     }
        // }
    }

    .bigger-font {
        font-size: 16px;
    }
}