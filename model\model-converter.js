
const Order = require('./order').Order;
const Position = require('./position').Position;
const TradeRecord = require('./trade-record').TradeRecord;

const ModelConverter = {

    /**
     * 将原始二维数据订单数据，转换为订单数据对象列表
     * @param {Array<String>} titles 
     * @param {Array<Array>} contents 
     * @returns {Array<Order>} 订单列表
     */
    formalizeOrders(titles, contents) {
        return this._formalizeModels(titles, contents, Order);
    },

    /**
     * 将原始二维数据持仓数据，转换为持仓数据对象列表
     * @param {Array<String>} titles 
     * @param {Array<Array>} contents 
     * @returns {Array<Position>} 持仓列表
     */
    formalizePositions(titles, contents) {
        return this._formalizeModels(titles, contents, Position);
    },

    /**
     * 将原始二维数据成交数据，转换为成交数据对象列表
     * @param {Array<String>} titles 
     * @param {Array<Array>} contents 
     * @returns {Array<TradeRecord>} 成交列表
     */
    formalizeTradeRecords(titles, contents) {
        return this._formalizeModels(titles, contents, TradeRecord);
    },

    /**
     * @param {Array<String>} titles 
     * @param {Array<Array>} contents 
     * @param {Object} DataType 目标数据类型（订单、持仓、成效）
     */
    _formalizeModels(titles, contents, DataType) {

        if (contents.length == 0) {
            return [];
        }

        var records = [];
        for (let idx = 0; idx < contents.length; idx++) {

            let plain_struc = {};
            let vals = contents[idx];
            for (let idx2 = 0; idx2 < vals.length; idx2++) {

                let field_name = titles[idx2];
                if (field_name === undefined) {
                    break;
                }
                plain_struc[field_name] = vals[idx2];
            }

            records.push(new DataType(plain_struc));
        }

        return records;
    },
};

module.exports = { ModelConverter };