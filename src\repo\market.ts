import Utils from '../modules/utils';
import { BaseRepo } from '../modules/base-repo';
import { InstrumentInfo } from '../types/common';
import { GetInstruments, SetInstruments } from '../global-state';

export class MarketRepo extends BaseRepo {
    
    /**
     * 下载合约数据
     * @param assetType - 资产类型
     * @param qualifify - 需要对返回数据再次进行过滤（比如查询股票合约时，默认会顺带返回基金合约数据）
     */
    async DownloadInstruments(assetType: number, qualifify = true) {

        const list = GetInstruments(assetType);
        if (list.length > 0) {
            return list;
        }

        const resp = await this.assist.Get<InstrumentInfo[]>('/instrument', { type: assetType, status: true });
        const { errorCode, errorMsg, data } = resp;
        
        if (errorCode === 0 && Array.isArray(data) && data.length >= 1) {

            const matrix = (data as any) as any[][];
            const titles = matrix.shift()!;
            const instruments = Utils.convertMatrix2Json<InstrumentInfo>(titles, matrix);
            // 查询股票合约时，返回数据夹杂了各类基金合约，需要过滤掉
            const qualified = qualifify !== true ? instruments : instruments.filter(x => x.assetType === assetType);

            /**
             * 返回原始数据不带市场前缀，进行补齐
             */
            qualified.forEach(x => {
                x.instrument = `${x.exchangeId}.${x.instrument}`;
            });

            SetInstruments(assetType, qualified);
            return qualified;
        }
        else {
            return [];
        }
    }
}