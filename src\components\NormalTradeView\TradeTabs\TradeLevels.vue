<script setup lang="ts">
import { computed } from 'vue';
import { formatNumber, getColorClass } from '@/script/formatter';
import type { AccountInfo, StandardTick } from '@/types';
import { GlobalState } from '../../../../../xtrade-sdk/dist';

const { lastTick, selectedAccount } = defineProps<{
  lastTick?: StandardTick;
  selectedAccount?: AccountInfo;
}>();

// 定义emit事件
const emit = defineEmits<{
  clickLevel: [price: number];
}>();

// 档位
const priceLevels = computed(() => {
  return [
    [
      { level: 5, price: lastTick?.askPrice[4], volume: lastTick?.askVolume[4] },
      { level: 4, price: lastTick?.askPrice[3], volume: lastTick?.askVolume[3] },
      { level: 3, price: lastTick?.askPrice[2], volume: lastTick?.askVolume[2] },
      { level: 2, price: lastTick?.askPrice[1], volume: lastTick?.askVolume[1] },
      { level: 1, price: lastTick?.askPrice[0], volume: lastTick?.askVolume[0] },
    ],
    [
      { level: -1, price: lastTick?.bidPrice[0], volume: lastTick?.bidVolume[0] },
      { level: -2, price: lastTick?.bidPrice[1], volume: lastTick?.bidVolume[1] },
      { level: -3, price: lastTick?.bidPrice[2], volume: lastTick?.bidVolume[2] },
      { level: -4, price: lastTick?.bidPrice[3], volume: lastTick?.bidVolume[3] },
      { level: -5, price: lastTick?.bidPrice[4], volume: lastTick?.bidVolume[4] },
    ],
  ];
});

// 底部信息字段定义
const tradeInfoFields = computed(() => {
  return [
    [
      {
        label: '今开',
        key: 'todayPrice',
        value: formatNumber(lastTick?.openPrice, {
          default: '--',
        }),
        needColor: true,
        colorValue: !lastTick ? '' : lastTick?.openPrice - lastTick?.preClosePrice,
      },
      {
        label: '昨收',
        key: 'yesterdayPrice',
        value: formatNumber(lastTick?.preClosePrice, {
          default: '--',
        }),
        needColor: false,
      },
    ],
    [
      {
        label: '最高',
        key: 'highPrice',
        value: formatNumber(lastTick?.highPrice, {
          default: '--',
        }),
        needColor: true,
        colorValue: !lastTick ? '' : lastTick?.highPrice - lastTick?.preClosePrice,
      },
      {
        label: '最低',
        key: 'lowPrice',
        value: formatNumber(lastTick?.lowPrice, {
          default: '--',
        }),
        needColor: true,
        colorValue: !lastTick ? '' : lastTick?.lowPrice - lastTick?.preClosePrice,
      },
    ],
  ];
});

// 计算涨跌幅
const percent = computed(() => {
  if (!lastTick) return '';
  const percent = (lastTick.lastPrice - lastTick.preClosePrice) / lastTick.preClosePrice;
  return formatNumber(percent, { percent: true, default: '--', prefix: true });
});

// 涨跌值
const change = computed(() => {
  if (!lastTick) return '';
  return formatNumber(lastTick.lastPrice - lastTick.preClosePrice, {
    default: '--',
    prefix: true,
  });
});

const instrumentName = computed(() => {
  if (!lastTick) return '';
  const instruments = GlobalState.GetInstruments();
  const instrument = instruments.find(x => x.instrument === lastTick?.instrumentID);
  if (instrument) {
    return instrument.instrumentName;
  }
  return '--';
});

const updateTime = computed(() => {
  if (!lastTick) return '--';
  return lastTick.strTime.split(' ')[1];
});

// const trend = computed(() => {
//   if (!lastTick) return '';
//   return lastTick.lastPrice > lastTick.preClosePrice ? '⬆' : '⬇';
// });

// 点击价格档位修改价格输入框中的值
const handleClickLevel = (level: (typeof priceLevels)['value'][0][0]) => {
  if (level.price) {
    emit('clickLevel', level.price);
  }
};
</script>

<template>
  <div p-10 h-full fs-14>
    <!-- 合约信息 -->
    <div bg="[--g-block-bg-2]" py-10 px-15>
      <div>
        <div flex jcsb mb-20>
          <div fs-16 fw-600>{{ instrumentName }}</div>
          <div flex aic gap-6>
            <div
              fs-16
              fw-600
              :class="lastTick ? getColorClass(lastTick.lastPrice - lastTick.preClosePrice) : ''"
            >
              {{
                formatNumber(lastTick?.lastPrice, {
                  default: '--',
                })
              }}
            </div>
            <!-- <div>{{ trend }}</div> -->
          </div>
        </div>
        <div flex jcsb>
          <div>{{ lastTick?.instrumentID }}</div>
          <div flex aic gap-6>
            <div
              :class="lastTick ? getColorClass(lastTick.lastPrice - lastTick.preClosePrice) : ''"
              fw-600
            >
              {{ change }}
            </div>
            <div
              :class="lastTick ? getColorClass(lastTick.lastPrice - lastTick.preClosePrice) : ''"
              fw-600
            >
              {{ percent }}
            </div>
          </div>
        </div>
      </div>
      <div mt-10>
        <div mb-10 v-for="(row, rowIndex) in tradeInfoFields" :key="rowIndex" flex aic jcsb gap-50>
          <div v-for="(field, fieldIndex) in row" :key="fieldIndex" flex-1 flex aic jcsb>
            <div w-40 color="[--g-text-color-1]">{{ field.label }}</div>
            <div
              :class="
                field.needColor && field.colorValue !== undefined
                  ? getColorClass(field.colorValue)
                  : ''
              "
            >
              {{ field.value }}
            </div>
          </div>
        </div>
      </div>
      <div color="[--g-text-color-1]" mt-10 flex justify-end>更新时间({{ updateTime }})</div>
    </div>
    <!-- 价格档位列表 -->
    <div pt-10 flex aic>
      <div flex-1 v-for="(col, index) in priceLevels" :key="index">
        <div
          v-for="level in col"
          :key="level.level"
          flex
          aic
          jcsb
          h-30
          pl-10
          pr-10
          cursor-pointer
          transition="all duration-100"
          hover="bg-[--g-bg-hover-2]"
          @click="handleClickLevel(level)"
        >
          <!-- 档位标签 -->
          <div w-25 flex aic>
            {{ level.level > 0 ? '卖' + level.level : '买' + Math.abs(level.level) }}
          </div>

          <!-- 价格 -->
          <div flex-1 flex aic jcc :class="getColorClass(level.price! - lastTick?.preClosePrice!)">
            {{ formatNumber(level.price, { default: '--' }) }}
          </div>

          <!-- 数量 -->
          <div w-30 flex jce>
            {{
              formatNumber(level.volume ? level.volume / 100 : 0, {
                default: '--',
                fix: 0,
                abbreviate: true,
              })
            }}
          </div>
        </div>
      </div>
    </div>
    <!-- 账号信息 -->
    <div mt-10 py-10 px-15>
      <div flex aic jcsb mb-10>
        <div color="[--g-text-color-1]">账号</div>
        <div>{{ selectedAccount?.accountName }}</div>
      </div>
      <div flex aic jcsb mb-10>
        <div color="[--g-text-color-1]">总资产</div>
        <div>{{ formatNumber(selectedAccount?.balance, { default: '--', separator: true }) }}</div>
      </div>
      <div flex aic jcsb mb-10>
        <div color="[--g-text-color-1]">总市值</div>
        <div>
          {{ formatNumber(selectedAccount?.marketValue, { default: '--', separator: true }) }}
        </div>
      </div>
      <div flex aic jcsb>
        <div color="[--g-text-color-1]">可用资金</div>
        <div>
          {{ formatNumber(selectedAccount?.available, { default: '--', separator: true }) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
