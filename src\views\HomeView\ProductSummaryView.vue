<script setup lang="ts">
import { shallowRef, computed } from 'vue';
import ProductList from '@/components/ProductSummaryView/SummaryProductList.vue';
import ProductProfit from '@/components/ProductSummaryView/ProductProfit.vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab, ProductInfo } from '@/types';

// 当前选中行
const activeRow = shallowRef<ProductInfo>();

// 选择事件
const handleRowSelect = (rowd: ProductInfo) => {
  activeRow.value = rowd;
};

// 标签页配置
const tabs = computed<ComponentTab[]>(() => [
  {
    label: '持仓',
    component: 'TodayPositions',
    props: {
      activeItem: activeRow.value,
      type: 'product',
    },
  },
  {
    label: '委托',
    component: 'TodayOrders',
    props: {
      activeItem: activeRow.value,
      type: 'product',
    },
  },
  {
    label: '成交',
    component: 'TodayRecords',
    props: {
      activeItem: activeRow.value,
      type: 'product',
    },
  },
]);
</script>

<template>
  <div flex>
    <ProductList @select="handleRowSelect" />
    <div flex="~ col" flex-1 min-w-1>
      <ProductProfit :product="activeRow" />
      <ComponentTabs flex-1 min-h-1 :tabs="tabs" />
    </div>
  </div>
</template>

<style scoped></style>
