const IView = require('../../../component/iview').IView;
const TabList = require('../../../component/tab-list').TabList;

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '两融查询');
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        if (this.contextInfo === undefined) {
            return;
        }

        this.tabList.fireEventOnTab(tab, this.systemEvent.viewContextChange, this.contextInfo);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {
        //
    }

    buildProductSummaries() {

        var $tab = this.$container.querySelector('.credit-tabs');
        this.tabList = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: $tab,
            $content: this.$container.querySelector('.credit-content'),
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        this.tabList.openTab(true, '@2021/fragment/credit/contract', '合约');
        this.tabList.openTab(true, '@2021/fragment/credit/debt', '标的');
    }

    handleActivated() {
        
        this.tabList.tabs.forEach(tab => {
            tab.viewEngine.table ? tab.viewEngine.table.fitColumnWidth() : null;
        });
    }

    listen2Events() {

        /** 监听TAB激活 */
        this.registerEvent(this.systemEvent.tabActivated, this.handleActivated.bind(this));
    }

    refresh() {
        this.interaction.showMessage('该页面未提供刷新');
    }
    
    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    build($container) {

        super.build($container);
        this.buildProductSummaries();

        let tabs = $container.querySelector('.s-unselectable');
        tabs.setAttribute('style', 'display:none');

        this.listen2Events();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);

    }
}

module.exports = View;