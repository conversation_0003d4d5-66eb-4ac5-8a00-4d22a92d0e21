import { Socket } from 'net';
import { SocketCommunication } from './socket';
import { SocketError } from './protocol';
import { SocketDataPackage } from '../types/data-package';
import { NsMessageResolver } from './native-message-resolver';
import { GetLogger } from '../global-state';
import { ServerFunction } from '../config/server-function';

const defaultLogger = GetLogger();

/**
 * Native Socket 通信类
 */
export class NativeSocketCommunication extends SocketCommunication {

    private socket: Socket | null = null;
    private isConnected = false;
    private resolver: NsMessageResolver;

    constructor(server_type: string, host: string, port: number) {

        super(server_type, host, port);
        this.resolver = new NsMessageResolver();
    }

    private handleConnectedNs() {
        
        defaultLogger.info('Native Socket server connected', this.serverType, this.server);
        this.isConnected = true;
        super.handleConnected();
    }

    private handleClosedNs(hadError: boolean) {
        
        defaultLogger.info('Native Socket server closed, with error = ', hadError, this.serverType, this.server);
        this.isConnected = false;
        super.handleClosed();
    }

    private handleTimeoutedNs() {

        defaultLogger.error('Native Socket timeout', this.serverType, this.server);
        super.handleTimeouted();
    }

    private handleErroredNs(err: SocketError) {

        defaultLogger.error('Native Socket timeout', err, this.serverType, this.server);
        this.isConnected = false;
        super.handleErrored(err);
    }

    private handleMessageReceivedNs(data: Buffer) {

        const messages = this.resolver.decode(data);
        messages.forEach(msg => { 
            defaultLogger.trace('Native Socket message received', msg);
            super.handleMessageReceived(msg); 
        });
    }
    
    connect(): void {

        if (this.isConnected) {
            return;
        }

        this.isConnected = false;
        const socket = new Socket();
        socket.on('connect', this.handleConnectedNs.bind(this));
        socket.on('close', this.handleClosedNs.bind(this));
        socket.on('timeout', this.handleTimeoutedNs.bind(this));
        socket.on('error', this.handleErroredNs.bind(this));
        socket.on('data', this.handleMessageReceivedNs.bind(this));

        socket.connect(this.port, this.host, () => {

            socket.setKeepAlive(true);
            socket.setNoDelay(true);
        });
        
        this.socket = socket;
    }

    disconnect(): void {

        if (this.isConnected) {

            this.isConnected = false;
            this.socket?.destroy();
            this.dispose();
        }
    }

    send(data: SocketDataPackage) {
        
        if (this.isConnected) {

            this.precheck(data);
            const encoded = this.resolver.encode(data);
            this.socket?.write(encoded);
            
            if (data.fc === ServerFunction.HeartBeat) {
                defaultLogger.trace('Sending heart beating signal');
            }
            else {
                defaultLogger.debug('Sending data out', { data, encoded });
            }
        }
        else {
            defaultLogger.error('Unable to send message due to disconnection', this.serverType, this.server, data);
        }
    }

    dispose(): void {

        super.dispose();
        this.socket = null;
    }
}