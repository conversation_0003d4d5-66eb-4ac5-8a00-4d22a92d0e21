const { BaseAdminView } = require('./baseAdminView');
const { UserInfo } = require('../../model/user-info');
const { repoApp } = require('../../repository/app');
const { repoUser } = require('../../repository/user');
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');
const { TemplateAppConfig, AppTypes } = require('../../config/webapp');

class WebApp {

    static isTemplate(url) {
        return typeof url == 'string' && url.startsWith(TemplateAppConfig.TemplateUrlPrefix);
    }

    constructor(struc = { id, appName, describe, url, createUserId, users: [{ userId, userName, fullName, shareType }] }) {

        this.id = struc.id;
        this.appName = struc.appName;
        this.describe = struc.describe || '';

        /**
         * 1. 一般WEB应用，存储为实际为url字串；
         * 2. 基金报告模板，则存储为一个模板数据的json结构字符串；
         */
        this.url = struc.url;

        /**
         * 以下几个字段，来自于模板类型的应用
         */
        this.appType = AppTypes.normal.code;
        this.title = '';
        this.templateId = '';
        this.componentName = '';

        this.resolve();
        this.createUserId = struc.createUserId;
        this.users = struc.users instanceof Array ? struc.users : [];
    }

    resolve() {

        if (!WebApp.isTemplate(this.url)) {
            return;
        }

        try {

            let url = this.url;
            let tmpl = JSON.parse(url.replace(TemplateAppConfig.TemplateUrlPrefix, ''));
            let out_keys = Object.keys(tmpl);
            let inner_keys = Object.keys(this);
            out_keys.forEach(prop => { inner_keys.some(x => x == prop) && (this[prop] = tmpl[prop]); });

            /**
             * 从url解析出模板结构后，清楚url本身的存值
             */
            this.url = '';
        }
        catch(ex) {
            console.error({ url, ex });
        }
    }
}

class View extends BaseAdminView {

    constructor(view_name) {

        super(view_name, '应用管理');
        this.appTypes = AppTypes;
        this.apps = [new WebApp({})].splice(1);
        this.users = [new UserInfo({})].splice(0);
        this.form = { visible: false };
        this.formd = new WebApp({});
        this.controls = ['NewReportViewRoute'];
        this.shared = { visible: false, users: [] };
        this.setAsCurrent(null);
    }

    get $refs() {
        return this.vueIns.$refs;
    }

    get $cform() {
        return this.$refs.cform;
    }

    createVueApp() {

        this.rules = {
            
            appType: { required: true, message: '请选择应用类型' },
            appName: { required: true, message: '请输入应用名称' },

            url: [
                { type: 'string', required: true, message: '请输入URL地址' },
                {
                    validator: (rule, value, callback) => {
                        callback(value.startsWith('http') ? undefined : '请以 http:// 或 https:// 开头'); 
                    },
                },
            ],

            title: { required: true, message: '请输入报告名称' },

            templateId: [
                { type: 'string', required: true, message: '请输入模板ID' },
                {
                    validator: (rule, value, callback) => {
                        callback(typeof value == 'string' && /^[0-9]+$/.test(value) ? undefined : '报告模板ID为全数字'); 
                    }
                }
            ],

            componentName: [
                { type: 'string', required: true, message: '请输入控件名称' },
                {
                    validator: (rule, value, callback) => {
                        callback(typeof value == 'string' && /^[a-zA-Z]+$/.test(value) ? undefined : '控件名称为全英文字符串'); 
                    }
                },
            ]

        };

        this.vueIns = new Vue({

            el: this.$container.firstElementChild,
            data: {
                
                apps: this.apps,
                form: this.form,
                formd: this.formd,
                rules: this.rules,
                shared: this.shared,
                users: this.users,
                appTypes: this.appTypes,
                controls: this.controls,
                tableProps: { ...this.systemSetting.tableProps },
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },
            },
            directives: { drag },
            components: {
                DataTables: DataTables.DataTables,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.refresh,
                this.handleShareShown,
                this.showEdit,
                this.showCreate,
                this.hope2Remove,
                this.beforeSave,
                this.setAsCurrent,
                this.share,
                this.closeShare,
                this.closeCreate,
                this.hope2Share,
                this.handleAppTypeChange,
                this.isTemplateType,
                this.formatAppType,
            ]),
        });
    }

    async requestUsers() {

        let resp = await repoUser.getAll();
        if (resp.errorCode === 0) {
            this.users.refill(resp.data || []);
        } 
        else {
            this.interaction.showError(`获取用户列表失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
        }
    }

    async requestApps() {

        let resp;
        let loading = this.interaction.showLoading({ text: '请求应用...' });
        try {
            await this.requestUsers();
            resp = await repoApp.getAll();
        }
        catch(ex) {
            console.error(ex);
        }
        finally {
            loading.close();
        }

        let { errorCode, errorMsg, data } = resp;
        if (errorCode === 0) {

            let records = data instanceof Array ? data : [];
            let apps = records.map(x => new WebApp(x));

            apps.forEach(item => {
                item.users.forEach(usr => {

                    let matched = this.users.find(x => x.id == usr.userId);
                    /** 扩充字段 ~ 仅存在于界面逻辑中 */
                    usr.fullName = matched.fullName;
                });
            });

            this.apps.refill(apps);
        }
        else {
            this.interaction.showError(`获取列表信息失败，详细信息:${errorCode}/${errorMsg}`);
        }
    }
  
    handleShareShown() {
        this.requestUsers();
    }

    /**
     * @param {WebApp} row 
     */
    setAsCurrent(row) {
        this.current = row;
    }

    /**
     * @param {WebApp} row 
     */
    showEdit(row) {

        this.helper.extend(this.formd, this.helper.deepClone(row));
        this.form.visible = true;
    }

    showCreate() {

        this.helper.extend(this.formd, new WebApp({ createUserId: this.userInfo.userId }));
        this.form.visible = true;
    }

    closeCreate() {

        this.form.visible = false;
        this.$cform.clearValidate();
    }

    refresh() {
        this.requestApps();
    }

    /**
     * @param {WebApp} row 
     */
    hope2Share(row) {

        this.shared.users.refill(row.users.map(x => x.userId));
        this.shared.visible = true;
    }

    async share() {
                    
        let resp = await repoApp.share2Users(this.current.id, 1, this.shared.users);
        if (resp.errorCode === 0) {

            this.interaction.showSuccess('应用已分享成功!');
            this.closeShare();
            this.refresh();
        }
        else {
            this.interaction.showError(`分享报告失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
        }
    }

    closeShare() {

        this.shared.users.clear();
        this.shared.visible = false;
    }

    isTemplateType() {
        return this.formd.appType == this.appTypes.report.code;
    }

    /**
     * @param {WebApp} row 
     */
    formatAppType(row, column_def, app_type) {

        let matched = Object.values(this.appTypes).find(x => x.code == app_type);
        return matched ? matched.mean : app_type;
    }

    handleAppTypeChange() {
        //
    }

    /**
     * @param {WebApp} row 
     */
    hope2Remove(row) {

        this.interaction.showConfirm({

            title: '警告',
            message: '确定要删除当前应用吗',
            confirmed: () => {
                this.removeApp(row);
            },
        });
    }

    /**
     * @param {WebApp} row 
     */
    async removeApp(row) {

        let id = row.id;
        let resp = await repoApp.deleteApp(id);

        if (resp.errorCode === 0) {

            this.apps.remove(x => x.id === id);
            this.interaction.showSuccess('删除报告成功!');
        } 
        else {
            this.interaction.showError(`删除报告失败,详细信息:${resp.errorCode}/${resp.errorMsg}!`);
        }
    }

    beforeSave() {
        this.$cform.validate(valid => { valid && this.save(); });
    }

    /**
     * @param {WebApp} data 
     */
    typedAsApp(data) {
        return data;
    }

    async save() {
        
        let cloned = this.typedAsApp(this.helper.deepClone(this.formd));

        if (this.isTemplateType()) {

            let { appType, title, templateId, componentName } = cloned;
            let template_str = JSON.stringify({ appType, title, templateId, componentName });
            cloned.url = `${TemplateAppConfig.TemplateUrlPrefix}${template_str}`;
        }

        delete cloned.appType;
        delete cloned.title;
        delete cloned.templateId;
        delete cloned.componentName;
        console.log(cloned);

        let resp = cloned.id ? await repoApp.updateApp(cloned) : await repoApp.createApp(cloned);
        if (resp.errorCode === 0) {

            this.closeCreate();
            this.refresh();
        }
        else {
            this.interaction.showError(`保存应用失败，错误信息：${resp.errorCode}/${resp.errorMsg}!`);
        }
    }

    resizeWindow() {

        let height = this.thisWindow.getSize()[1];
        let extra = 35 + 30 + 30;
        let net_height = height - extra;
        let box = this.$container.querySelector('.app-view-root .s-scroll-bar');

        if (box) {
            box.style.height = net_height + 'px';
        }
    }

    build($container) {

        super.build($container);
        this.createVueApp();
        this.requestApps();
        this.resizeWindow();
    }
}

module.exports = View;
