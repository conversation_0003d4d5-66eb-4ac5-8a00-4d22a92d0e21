const { TypicalDataView } = require('../classcial/typical-data-view');
const { TradeRecord } = require('../../../model/trade-record');
const { repoTrading } = require('../../../repository/trading');

class View extends TypicalDataView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '成交记录');
    }

    /**
     * @param {TradeRecord} record 
     */
    testRecords(record) {
        
        var kw = this.states.keywords;
        return this.tableObj.matchKeywords(record)
            || this.testPy(record.instrumentName, kw)
            || this.testPy(record.accountName, kw)
            || this.testPy(record.strategyName, kw)
            || this.testPy(record.fundName, kw)
            || this.testPy(record.userName, kw);
    }

    handleContextChange(identityId) {

        if (identityId === this.identityId) {
            return;
        }

        this.identityId = identityId;
        this.tableObj.clear();

        if (this.helper.isNotNone(identityId)) {

            this.resetControls();
            this.requestRecords();
        }
    }

    async requestRecords() {

        var identityId = this.identityId;
        var resp = await repoTrading.getTodayExchanges({

            userId: this.userInfo.userId,
            account: this.is4Account ? identityId : null,
            fund: this.is4Product ? identityId : null,
            strategy: this.is4Strategy ? identityId : null,
            pageNo: 1,
            pageSize: 999999,
            token: this.userInfo.token,
            searchValue: null,
            checked: true,
        });

        var records = resp.data.list;
        var typeds = records.map(x => new TradeRecord(x));
        this.tableObj.refill(typeds);
    }

    refresh() {
        
        if (this.helper.isNotNone(this.identityId)) {
            super.refresh();
        }
    }

    build($container, options) {

        super.build($container, options, {

            heightOffset: 119,
            tableName: 'smt-fre',
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
        });

        this.is4Product = !!this.voptions.is4Product;
        this.is4Strategy = !!this.voptions.is4Strategy;
        this.is4Account = !!this.voptions.is4Account;

        /** 监听上下文切换 */
        this.registerEvent('set-context-identity', this.handleContextChange.bind(this));
    }
}

module.exports = View;