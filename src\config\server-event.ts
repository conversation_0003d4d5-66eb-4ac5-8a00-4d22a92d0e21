/**
 * 从服务器接收到的功能码
 */
export enum ServerEvent {

    /** 心跳事件 */
    HeartBeat = 1,
    /** Pong响应事件 */
    Pong = 8,
    /** 服务器错误事件 */
    ServerError = 11014,
    /** 登出服务器应答事件 */
    LogoutAnswered = 11100,
    /** 登录交易服务器应答事件 */
    LogOnAnswered = 20000,
    /** 下单回执事件 */
    OrderReceived = 20001,
    /** 撤单回执事件 */
    CancelOrderReceived = 20002,
    /** 订阅tick回执事件 */
    SubscribeTickReceived = 20006,
    /** 退订tick回执事件 */
    UnsubscribeTickReceived = 20007,
    /** 令牌生成事件 */
    TokenGenerated = 20008,
    /** 服务器判断结果事件 */
    ServerJudgeResult = 20016,
    /** 批量订单数据推送事件 */
    MotherOrderPush = 20022,
    /** 今日订单推送事件 */
    TodayOrderPush = 20030,
    /** 今日成交推送事件 */
    TodayTradeRecordPush = 20031,
    /** 今日持仓推送事件 */
    TodayPositionPush = 20032,
    /** 账号权益数据推送事件 */
    AccountEquityPush = 20033,
    /** 账号持仓数据推送事件 */
    AccountPositionPush = 20034,
    /** 批量订单处理结果推送事件 */
    MotherOrderResult = 20035,
    /** 平仓应答事件 */
    ClosePositionReply = 20101,
    /** 算法下单应答事件 */
    AlgoOrderReceived = 23000,
    /** 算法单撤销应答事件 */
    CancelAlgoOrderReceived = 23001,
    /** 算法单查询应答事件 */
    AlgoOrderPush = 23002,
    /** 订单变化推送事件（持续性） */
    OrderChanged = 30001,
    /** 成交变化推送事件（持续性） */
    TradeChanged = 30002,
    /** 持仓变化推送事件（持续性） */
    PositionChanged = 30003,
    /** tick价格变化推送事件（持续性） */
    TickPriceChanged = 30004,
    /** 账户状态变化事件 */
    AccountStatusChanged = 30005,
    /** 风险警报接收事件 */
    RiskAlertReceived = 30006,
    /** 强制踢出事件 */
    ForcedKickOut = 30010,
    /** K线变化事件 */
    KLineChanged = 30011,
    /** 指令通知事件 */
    NotifyInstruction = 30012,
    /** 券池可交易股票变化事件 */
    TradableStockChanged = 30015,
}