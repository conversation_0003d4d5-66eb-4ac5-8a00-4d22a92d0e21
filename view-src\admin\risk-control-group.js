const { <PERSON><PERSON><PERSON>Window } = require('@electron/remote');
const { IView } = require('../../component/iview');
const { repoRisk } = require('../../repository/risk');
const { repoFund } = require('../../repository/fund');
const { repoStrategy } = require('../../repository/strategy');
const { repoAccount } = require('../../repository/account');
const { repoUser } = require('../../repository/user');
const { RiskGroup, RiskGroupItem } = require('../../model/risk');
const { FundInfo } = require('../../model/fund');
const { StrategyInfo } = require('../../model/strategy');
const { V3StandardAccount } = require('../../model/account');

class UIRiskGroup extends RiskGroup {

    constructor(struct, collapsed) {

        super(struct);
        this.collapsed = collapsed != false;
    }
}

class UIRiskGroupItem extends RiskGroupItem {

    constructor(struct, editing) {

        super(struct);
        this.editing = !!editing;
    }
}

class View {

    /**
     * @param {IView} view 
     */
    constructor(view) {

        this.hostView = view;
        this.groups = [new UIRiskGroup({})].splice(1);
        this.users = [{ id: null, fullName: null }].splice(1);
        this.funds = [new FundInfo({})].splice(1);
        this.strategies = [new StrategyInfo({})].splice(1);
        this.accounts = [new V3StandardAccount({})].splice(1);
        this.identities = [{ label: null, value: null, type: 1 }].splice(1);
        this.working = new UIRiskGroupItem({}, true);

        this.dialog = {

            visible: false,
            form: { id: null, name: null, remark: null },
            rules: {
                name: [{ type: 'string', required: true, message: '请输入分组名称' }],
                remark: [{ type: 'string', required: false, message: '请选择备注信息' }],
            },
        };

        const { fund, strategy, account } = this.hostView.systemEnum.identityType;
        this.types = [fund, strategy, account];
        this.data = {

            dialog: this.dialog,
            types: { fund, strategy, account },
            groups: this.groups,

            funds: this.funds,
            strategies: this.strategies,
            accounts: this.accounts,
        };

        this.receiverDialog = {

            visible: false,
            form: { id: null, groupName: null, receivers: [] },
            rules: {
                receivers: [{ required: false, message: '请选择风控消息接收人' }],
            },
        };

        this.msgReceiver = {

            dialog: this.receiverDialog,
            receivers: this.users,
        };
    }

    get systemEnum() {
        return this.hostView.systemEnum;
    }
    
    get systemEvent() {
        return this.hostView.systemEvent;
    }

    get renderProcess() {
        return this.hostView.renderProcess;
    }

    get userInfo() {
        return this.hostView.userInfo;
    }

    get helper() {
        return this.hostView.helper;
    }

    get interaction() {
        return this.hostView.interaction;
    }

    get $form() {
        return this.hostView.vueApp.$refs.groupForm;
    }

    createGroup() {
        this.editGroup();
    }

    /**
     * @param {UIRiskGroup} group 
     */
    editGroup(group) {
        
        let { id, groupName, remark } = group || {};

        const ref = this.dialog;
        ref.visible = true;
        ref.form.id = id || null;
        ref.form.name = groupName || null;
        ref.form.remark = remark || null;
    }

    /**
     * @param {UIRiskGroupItem} row 
     */
    editGroupItem(row) {
        
        row.editing = true;
        this.working = this.helper.deepClone(row);
    }

    closeDialog() {

        const ref = this.dialog;
        ref.visible = false;
        ref.form.id = null;
        ref.form.name = null;
        ref.form.remark = null;
    }

    saveGroup() {

        this.$form.validate(isok => {

            if (isok) {
                this.toSaveGroup();
            }
        });
    }

    async toSaveGroup() {

        const { id, name, remark } = this.dialog.form;
        let group;
        let isnew = this.helper.isNone(id);

        if (isnew) {

            group = new RiskGroup({

                id: null,
                orgId: this.userInfo.orgId,
                groupName: name,
                remark: remark,
                setting: { enableAlgoTrade: 1, enableCommonTrade: 0 },
            });

            let ts = new Date().getTime();
            group.createTime = ts;
            group.updateTime = ts;
        }
        else {

            let matched = this.groups.find(x => x.id == id);
            group = Object.assign({}, matched);
            group.groupName = name;
            group.remark = remark;
            group.updateTime = new Date().getTime();
        }

        delete group.items;
        this.closeDialog();
        let resp = await repoRisk.createRiskGroup(group);
        let { errorCode, errorMsg, data } = resp;

        if (errorCode != 0) {
            return this.interaction.showError(`保存失败：${errorCode}/${errorMsg}`);
        }

        if (isnew) {
            this.groups.unshift(new UIRiskGroup(data));
        }
        else {

            let matched = this.groups.find(x => x.id == id);
            Object.assign(matched, data);
        }

        this.interaction.showSuccess('分组已保存');
    }

    /**
     * @param {UIRiskGroup | UIRiskGroupItem} group 
     */
    isGroup(group) {
        return group instanceof UIRiskGroup;
    }

    /**
     * @param {UIRiskGroup} group
     */
    toggleCollapse(group) {

        group.collapsed = !group.collapsed;
        if (!group.collapsed && group.items.length == 0) {
            this.reloadItems(group.id);
        }
    }

    /**
     * @param {UIRiskGroup} group 
     */
    addGroupItem(group) {

        group.collapsed = false;
        let ts = new Date().getTime();
        let gitem = new RiskGroupItem({

            id: null,
            orgId: this.userInfo.orgId,
            groupId: group.id,
            groupName: group.groupName,
            identity: null,
            identityName: null,
            identityType: null,
            createTime: ts,
            updateTime: ts,
        });

        let ugitem = new UIRiskGroupItem(gitem, true);
        group.items.unshift(ugitem);
    }

    /**
     * @param {UIRiskGroup} row 
     */
    openRiskSetting(row) {
        
        let { id, groupName} = row;
        let args = {

            type: this.systemEnum.identityType.group.code,
            identity: id,
            name: groupName,
            action: 'identity',
        };

        if (this.winRsk && !this.winRsk.isDestroyed()) {

            this.winRsk.show();
            this.winRsk.webContents.send('set-context-data', args);
            return;
        }

        let options = { width: 940, height: 620, minimizable: false, maximizable: false, highlight: true };
        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, win_id) => {

            this.winRsk = BrowserWindow.fromId(win_id);
            this.winRsk.on('closed', () => { this.winRsk = null; });
            setTimeout(() => { this.winRsk.webContents.send('set-context-data', args); }, 1000);
        });

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@admin/risk-dialog', `风控组风控`, options);
    }

    /**
     * @param {UIRiskGroup} row 
     */
    openBehaviorSetting(row) {
        
        let { id, groupName} = row;
        if (!this.tbmgr) {

            const { TradeBehaviorManager } = require('./trade-behavior/trade-behavior-manager');
            this.tbmgr = new TradeBehaviorManager(this.hostView);
        }

        this.tbmgr.openSetting({

            identityType: this.systemEnum.identityType.group.code,
            identity: id,
            name: groupName,
        });
    }

    /**
     * @param {UIRiskGroup} row 
     */
    openReceiverDialog(row) {
        
        const ref = this.receiverDialog;
        ref.form.id = row.id;
        ref.form.groupName = row.groupName;
        ref.form.receivers = row.receivers.map(x => x.receiverId);
        ref.visible = true;

        if (this.users.length == 0) {
            this.requestOrgUsers();
        }
    }

    closeReceiverDialog() {

        const ref = this.receiverDialog;
        ref.form.id = null;
        ref.form.groupName = null;
        ref.form.receivers = [];
        ref.visible = false;
    }

    saveReceiver() {

        this.$form.validate(async isok => {

            if (!isok) {
                return;
            }

            let receivers = this.receiverDialog.form.receivers.map(userId => {
                let matched = this.users.find(y => y.id == userId);
                return {
                    receiverId: userId, 
                    receiverName: matched ? matched.fullName : null,
                };
            });

            let resp = await repoRisk.updateRiskGroup({ 
                id: this.receiverDialog.form.id, 
                groupName: this.receiverDialog.form.groupName,
                receivers,
            });
            let { errorCode, errorMsg, data } = resp;
            if (errorCode == 0) {
                this.requestGroups();
            }
            else {
                this.interaction.showError(`接收人保存失败：${errorCode}/${errorMsg}`);
            }

            this.closeReceiverDialog();
        });
    }

    async requestOrgUsers() {

        let resp = await repoUser.getAll();
        if (resp.errorCode === 0) {
            this.users.refill(resp.data || []);
        }
    }

    /**
     * @param {UIRiskGroupItem} row 
     */
    cancelEditGroupItem(row) {
        
        let isnew = this.helper.isNone(row.id);
        if (isnew) {

            let matched = this.groups.find(x => x.id == row.groupId);
            matched.items.remove(x => x === row);
        }
        else {
            this.reloadItems(row.groupId);
        }
    }

    /**
     * @param {UIRiskGroup | UIRiskGroupItem} row 
     */
    formatTime(column, row, datetime) {
        return datetime ? new Date(datetime).format('yyyy-MM-dd hh:mm:ss') : (datetime || '');
    }

    /**
     * @param {UIRiskGroupItem} row 
     */
    identityTypeChange(row) {

        row.identity = null;
        row.identityName = null;
    }

    /**
     * @param {UIRiskGroupItem} row 
     */
    identityChange(row) {

        let mached = this.identities.find(x => x.value == row.identity);
        row.identityName = mached.label;
    }

    /**
     * @param {UIRiskGroupItem} gitem 
     */
    async saveGroupItem(gitem) {

        const { groupName, identityType, identity, identityName } = gitem;
        console.log({ groupName, identityType, identity, identityName });

        if (this.helper.isNone(identityType)) {
            return this.interaction.showError('请选择对象类型');
        }
        else if (this.helper.isNone(identity)) {
            return this.interaction.showError('请选择对象');
        }

        this.toSaveGroupItem(gitem);
    }

    /**
     * @param {RiskGroupItem} row
     */
    async toSaveGroupItem(row) {

        let isnew = this.helper.isNone(row.id);
        let resp = isnew ? await repoRisk.createRiskGroupItem(row) : await repoRisk.updateRiskGroupItem(row);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {

            this.interaction.showSuccess('对象已添加');
            this.reloadItems(row.groupId);
        }
        else {
            this.interaction.showError(`保存失败：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {UIRiskGroup} group 
     */
    async deleteGroup(group) {

        let resp = await repoRisk.deleteRiskGroup(group.id);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            this.groups.remove(x => x instanceof UIRiskGroup && x.id == group.id);
        }
        else {
            this.interaction.showError(`删除失败：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {UIRiskGroupItem} row 
     */
    async deleteGroupItem(row) {

        let isnew = this.helper.isNone(row.id);
        if (isnew) {
            
            let group = this.groups.find(x => x.id == row.groupId);
            group.items.remove(x => x === row);
            return;
        }

        let resp = await repoRisk.deleteRiskGroupItem(row.id);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {

            let group = this.groups.find(x => x.id == row.groupId);
            group.remove(x => x instanceof UIRiskGroupItem && x.id == row.id);
        }
        else {
            this.interaction.showError(`删除失败：${errorCode}/${errorMsg}`);
        }
    }

    async reloadItems(group_id) {
        
        let resp = await repoRisk.getRiskGroupItems(group_id);
        let { errorCode, errorMsg, data } = resp;
        let matched = this.groups.find(x => x.id == group_id);
        
        if (matched == undefined) {
            console.error('group id is not matched', group_id);
            return;
        }

        /**
         * 默认展开
         */
        matched.collapsed = false;

        if (errorCode == 0) {
            matched.items.refill(data.map(x => new UIRiskGroupItem(x, false)));
        }
        else {
            this.interaction.showError(`分组详情查询失败：${errorCode}/${errorMsg}`);
        }
    }

    async requestGroups() {

        let resp = await repoRisk.getRiskGroups();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            this.data.groups = this.groups = data.map(x => new UIRiskGroup(x));
        }
        else {
            this.data.groups = this.groups = [];
            this.interaction.showError(`查询失败：${errorCode}/${errorMsg}`);
        }
    }

    async requestFunds() {

        let resp = await repoFund.getAll();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            this.data.funds = this.funds = data;
        }
    }

    async requestStrategies() {

        let resp = await repoStrategy.getStrategyAll();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            this.data.strategies = this.strategies = data;
        }
    }

    async requestAccounts() {

        let resp = await repoAccount.getAll();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            this.data.accounts = this.accounts = data;
        }
    }

    filterIdentities(identity_type) {
        return this.identities.filter(x => x.type == identity_type);
    }

    getIdentityTypeName(identity_type) {
        
        let matched = this.types.find(x => x.code == identity_type);
        return matched ? matched.mean : identity_type;
    }

    listIdentities() {

        let results = [{ label: null, value: null, type: 1 }].splice(1);
        let { types, funds, strategies, accounts } = this.data;
        results.push(...funds.map(x => ({ label: x.fundName, value: x.id, type: types.fund.code })));
        results.push(...strategies.map(x => ({ label: x.strategyName, value: x.strategyId, type: types.strategy.code })));
        results.push(...accounts.map(x => ({ label: x.accountName, value: x.id, type: types.account.code })));
        this.identities.refill(results);
    }

    async activate() {

        let loading = this.interaction.showLoading({ text: '请求风控分组...' });
        try {
            this.requestGroups();
            await Promise.all([this.requestFunds(), this.requestStrategies(), this.requestAccounts()]);
            this.listIdentities();
        }
        catch(ex) {
            console.error(ex);
        }
        finally {
            loading.close();
        }
    }
}

module.exports = View;
