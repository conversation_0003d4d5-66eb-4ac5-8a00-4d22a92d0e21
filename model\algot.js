/**
 * 任务状态（包含业务状态）
 */
const TaskEntrustStatus = { running: 0, completed: 1, canceled: 2 };

/**
 * 委托信息
 */
class AlgoEntrustInfo {

    constructor({
        accountId,
        accountName,
        afterAction,
        algoParam,
        algorithmMappingId,
        algorithmName,
        algorithmStatus,
        algorithmType,
        direction,
        effectiveTime,
        expireTime,
        fundId,
        fundName,
        id,
        identityId,
        instrument,
        instrumentName,
        limitAction,
        orderList,
        orderedVolume,
        remark,
        strategyId,
        strategyName,
        supplierName,
        taskId,
        taskName,
        tradeAmount,
        tradePrice,
        tradedVolume,
        userId,
        volume,
    }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.afterAction = afterAction;

        this.algoParam = {};
        if (typeof algoParam == 'string' && algoParam.length > 0) {
            try { this.algoParam = JSON.parse(algoParam); } catch(ex) {}
        }

        this.algorithmMappingId = algorithmMappingId;
        this.algorithmName = algorithmName;
        this.algorithmStatus = algorithmStatus;
        this.algorithmType = algorithmType;
        this.direction = direction;
        this.effectiveTime = effectiveTime;
        this.expireTime = expireTime;
        this.fundId = fundId;
        this.fundName = fundName;
        this.id = id;
        this.identityId = identityId;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.limitAction = limitAction;
        this.orderedVolume = orderedVolume;
        this.remark = remark;
        this.supplierName = supplierName;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.taskId = taskId;
        this.taskName = taskName;
        this.tradePrice = tradePrice;
        this.tradedVolume = tradedVolume;
        this.userId = userId;
        this.volume = volume;
        
        /** 市场成交均价 */
        this.marketPrice = null; // todo23
        this.orders = Array.isArray(orderList) ? orderList : [];
        this.tradeAmount = tradeAmount; // tradePrice * tradedVolume;
        this.cancelRate = null;
        this.pendingVolume = volume - tradedVolume;
    }
}

/**
 * 算法交易任务结构
 */
class AlgoTaskInfo {

    constructor({
        taskId,
        taskName,
        runningStock,
        totalTarget,
        totalBuyTarget,
        totalSellTarget,
        totalTraded,
        fundName,
        fundId,
        strategyName,
        strategyId,
        createTime,
        effectiveTime,
        expireTime,
        accountName,
        accountId,
        financeAccount,
        algoParam,
        algorithmMappingId,
        algorithmName,
        list
    }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.financeAccount = financeAccount;
        
        let aparam = {};
        if (typeof algoParam == 'string' && algoParam.length > 0) {
            try { aparam = JSON.parse(algoParam); } catch(ex) {}
        }
        else if (typeof algoParam == 'object') {
            aparam = algoParam || {};
        }

        this.algoParam = aparam;
        this.algoParams = [{ key: '', value: null }].splice(1);
        for (let key in aparam) {
            this.algoParams.push({ key, value: aparam[key] });
        }

        this.algoId = algorithmMappingId;
        this.algoName = algorithmName;
        this.fundId = fundId;
        this.fundName = fundName;
        this.runningStock = runningStock;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.taskId = taskId;
        this.taskName = taskName;
        this.totalBuyTarget = totalBuyTarget;
        this.totalSellTarget = totalSellTarget;
        this.totalTarget = totalTarget;
        this.totalTraded = totalTraded;

        /** 起效时间，目前母单上无字段，由子单反向设置 */
        this.effectiveTime = effectiveTime;
        /** 过期时间，目前母单上无字段，由子单反向设置 */
        this.expireTime = expireTime;
        /** 创建时间 */
        this.createTime = createTime;

        /** 实际发生委托列表（此处为母单委托，并非原有算法母单再下挂的交易层面委托） */
        const tasks = this.entrusts = Array.isArray(list) ? list.map(x => new AlgoEntrustInfo(x)): [];

        /**
         * 母单状态
         */
        this.status = runningStock == 0
                    || totalTraded == totalTarget
                    || this.expireTime <= Date.now() ? TaskEntrustStatus.completed : TaskEntrustStatus.running;
    }
}

/**
 * 算法交易任务数据包（多层次树形结构）
 */
class AlgoTaskPackage {

    constructor({
        runningStock,
        totalTarget, 
        totalBuyTarget, 
        totalSellTarget, 
        totalTraded, 
        taskMap,
    }) {

        this.runningStock = runningStock;
        this.totalBuyTarget = totalBuyTarget;
        this.totalSellTarget = totalSellTarget;
        this.totalTarget = totalTarget;
        this.totalTraded = totalTraded;

        /** 母单任务列表 */
        this.tasks = Array.isArray(taskMap) ? taskMap.map(x => new AlgoTaskInfo(x)): [];
    }
}

/**
 * 任务包含仓位（适用创建视图，非查询回显子任务视图）
 */
class AlgoPositionInfo {

    constructor({ 
        instrument, 
        instrumentName, 
        direction, 
        price,
        num, 
        totalPosition, 
        closablePosition, 
        targetPosition,
    }) {

        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.direction = direction || '';
        this.price = price;
        /** 委托下单次数 */
        this.num = num;
        /** 总仓位（可平 + 不可平） */
        this.totalPosition = totalPosition;
        /** 可平仓位 */
        this.closablePosition = closablePosition;
        /** 目标仓位 */
        this.targetPosition = targetPosition;
    }
}

module.exports = {

    TaskEntrustStatus,
    AlgoTaskPackage,
    AlgoTaskInfo,
    AlgoEntrustInfo,
    AlgoPositionInfo,
};