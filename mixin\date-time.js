function toDate(input) {
	try {
		return new Date(input).format('yyyy-MM-dd');
	} catch (ex) {
		return input;
	}
}

function toTime(input) {
	try {
		return new Date(input).format('hh:mm:ss');
	} catch (ex) {
		return input;
	}
}

function toTimems(input) {
	try {
		let date = new Date(input);
		let ms = date.getMilliseconds();
		if (ms < 10) {
			ms = '00' + ms;
		} else if (ms < 100) {
			ms = '0' + ms;
		}
		return date.format('hh:mm:ss') + ':' + ms;
	} catch (ex) {
		return input;
	}
}

function toDatetime(input) {
	try {
		return new Date(input).format('yyyy-MM-dd hh:mm:ss');
	} catch (ex) {
		return input;
	}
}

const DatetimeMixin = {
	filters: {
		toDate: toDate,
		toTime: toTime,
		toTimems: toTimems,
		toDatetime: toDatetime,
	},

	methods: {
		toDate: toDate,
		toTime: toTime,
		toTimems: toTimems,
		toDatetime: toDatetime,
	},
};

module.exports = { DatetimeMixin };
