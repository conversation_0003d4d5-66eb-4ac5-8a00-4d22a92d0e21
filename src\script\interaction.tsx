import type { ElMessageBoxOptions } from 'element-plus';

/**
 * 删除确认对话框
 */
export const deleteConfirm = async (title: string, message: string): Promise<boolean> => {
  return new Promise(resolve => {
    import('element-plus').then(({ ElMessageBox }) => {
      ElMessageBox({
        title,
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete',
        type: 'error',
        showClose: false,
        message,
        buttonSize: 'large',
        dangerouslyUseHTMLString: true,
        draggable: true,
        icon: () => <i class="fs-24 iconfont icon-warning-circle-fill" />,
      })
        .then(() => resolve(true))
        .catch(() => resolve(false));
    });
  });
};

export type ChooseConfirmResult = 'confirm' | 'cancel' | 'close';

/**
 * 选择确认对话框
 */
export const chooseConfirm = async (
  title: string,
  message: string,
  options: ElMessageBoxOptions,
): Promise<{ result: ChooseConfirmResult; value?: string | null | undefined }> => {
  return new Promise(resolve => {
    import('element-plus').then(({ ElMessageBox }) => {
      const defaults: ElMessageBoxOptions = {
        title,
        message,
        confirmButtonText: '确定',
        confirmButtonClass: 'primary',
        showCancelButton: true,
        cancelButtonText: '取消',
        type: 'warning',
        buttonSize: 'large',
        showClose: true,
        dangerouslyUseHTMLString: true,
        draggable: true,
        distinguishCancelAndClose: true,
        icon: () => <i class="fs-36 iconfont icon-warning-filling" />,
      };

      Object.assign(defaults, options);
      ElMessageBox(defaults)
        .then(function (feedback: any) {
          const action = typeof feedback === 'string' ? feedback : (feedback || {}).action;
          const data = { result: action as ChooseConfirmResult, value: (feedback || {}).value };
          resolve(data);
        })
        .catch(function (reason: ChooseConfirmResult) {
          resolve({ result: reason });
        });
    });
  });
};
