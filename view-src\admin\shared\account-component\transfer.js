const { IView } = require('../../../../component/iview');
const { repoAccount } = require('../../../../repository/account');

module.exports = class AccountTransferView extends IView {

    constructor(view_name) {

        super(view_name, false, '双中心账户资金划转');
        this.accounts = [{ accountId: null, accountName: null }].splice(1);

        /**
         * 账号转账方向
         */
        this.direction = {
            
            sh2sz: { label: '上海转深圳', value: 0 },
            sz2sh: { label: '深圳转上海', value: 1 },
        };

        /**
         * 柜台间划转方向
         */
        this.counters = {
            
            normal_to_fast: { label: '普通柜台 > 急速柜台', value: 1 },
            fast_2tonormal: { label: '急速柜台 > 普通柜台', value: 2 },
        };

        /**
         * 账号转账金额乘数
         */
        this.multiples = {

            tenk: { label: '万元', value: 10000  },
            one: { label: '元', value: 1 },
        };

        this.states = {

            visible: false,
            tab: 'internal',
        };

        this.internal = {

            accountId: null,
            accountName: null,
            direction: this.direction.sh2sz.value,
            times: this.multiples.tenk.value,
            value: null,
        };

        this.counter = {

            accountId: null,
            accountName: null,
            direction: this.counters.normal_to_fast.value,
            times: this.multiples.tenk.value,
            value: null,
        };
    }

    allocate() {
        
        let { accountId, accountName, direction, times, value } = this.internal;
        let message = '';

        if (this.helper.isNone(accountId) || this.helper.isNone(accountName)) {
            message = '请选择账号';
        }
        else if (this.helper.isNone(direction)) {
            message = '请选择方向';
        }
        else if (typeof value != 'number' || value <= 0) {
            message = '请填写划拨金额';
        }

        if (message) {
            return this.interaction.showError(message);
        }

        value = value * times;
        const dir = Object.values(this.direction).find(x => x.value == direction);
        const mentions = [

            ['账号', accountName],
            ['方向', dir.label],
            ['金额', value.thousandsDecimal(), 's-color-red'],
        ];
        const confirm_message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');

        this.interaction.showConfirm({

            title: '资金划拨确认',
            message: confirm_message,
            confirmed: () => {

                const data = {

                    accountId,
                    accountName, 
                    direction: dir.value,
                    value,
                };

                this.loggerTrading.info(`cash transfer function: to transfer cash = ${JSON.stringify(data)}`);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.transferCash, data);
                this.internal.value = 0;
                this.hide();
                this.interaction.showSuccess('资金划拨指令，已发送');
            },
        });
    }

    allocateBetweenCounters() {
        
        let { accountId, accountName, direction, times, value } = this.counter;
        let message = '';

        if (this.helper.isNone(accountId) || this.helper.isNone(accountName)) {
            message = '请选择账号';
        }
        else if (this.helper.isNone(direction)) {
            message = '请选择方向';
        }
        else if (typeof value != 'number' || value <= 0) {
            message = '请填写划拨金额';
        }

        if (message) {
            return this.interaction.showError(message);
        }

        value = value * times;
        const dir = Object.values(this.counters).find(x => x.value == direction);
        const mentions = [

            ['账号', accountName],
            ['方向', dir.label],
            ['金额', value.thousandsDecimal(), 's-color-red'],
        ];
        const confirm_message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');

        this.interaction.showConfirm({

            title: '柜台间资金划转确认',
            message: confirm_message,
            confirmed: async () => {

                const data = {

                    account_id: accountId,
                    trans_type: dir.value,
                    trans_amount: value,
                    account_name: encodeURIComponent(accountName), 
                };

                this.loggerTrading.info(`cash transfer between counters: to transfer cash = ${JSON.stringify(data)}`);
                const resp = await repoAccount.transferBetweenCounters(data);
                const { errorCode, errorMsg } = resp;

                if (errorCode != 0) {

                    this.interaction.showHttpError(`柜台间转账失败，错误信息：${errorCode}/${errorMsg}`);
                    return;
                }

                this.internal.value = 0;
                this.hide();
                this.interaction.showSuccess('柜台间资金划转指令，已发送');
            },
        });
    }

    cancel() {
        this.hide();
    }

    show() {

        super.show();
        this.states.visible = true;
    }

    hide() {
        
        super.hide();
        this.states.visible = false;
    }

    setContext(accountId, accountName) {

        this.internal.accountId = accountId;
        this.internal.accountName = accountName;
        this.counter.accountId = accountId;
        this.counter.accountName = accountName;
        this.accounts.refill([{ accountId, accountName }]);
        this.loggerTrading.info(`cash transfer function: set context = ${{ accountId, accountName }}`);
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container,
            data: {
                
                states: this.states,
                internal: this.internal,
                counter: this.counter,
                directions: this.direction,
                counters: this.counters,
                multiples: this.multiples,
                accounts: this.accounts,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.allocate,
                this.allocateBetweenCounters,
                this.cancel,
            ]),
        });
    }

    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
    }
};