class AlgorithmInfo {

    constructor({ algorithmName, algorithmType, createTime, englishName, id, param, strategyType, supplierId, supplierName, updateTime }) {

        this.algorithmName = algorithmName;
        this.algorithmType = algorithmType;
        this.createTime = createTime;
        this.englishName = englishName;
        this.id = id;

        /** 1. 算法携带的参数（原始内容）；
         * 2. json结构的字符串，key：字段变量名称，value：字段中文展示名称；
         * 3. 预期各个字段输入值，均为数值类型；
         */
        this.param = param;

        /** 算法携带的参数（参数数组形式） */
        this.params = AlgorithmInfo.Convert(param);
        
        this.strategyType = strategyType;
        this.supplierId = supplierId;
        this.supplierName = supplierName;
        this.updateTime = updateTime;
    }

    static Convert(param) {

        let data = {};
        if (typeof param == 'string' && param.length > 0) {
            data = JSON.parse(param);
        }

        return Object.entries(data).map(x => ({ prop: x[0], name: x[1] }));
    }
}

class AlgorithmGroup {

    /**
     * @param {String} name 
     * @param {Array<AlgorithmInfo>} members 
     */
    constructor(name, members) {
        
        this.name = name;
        this.algoes = members;
    }
}

module.exports = { AlgorithmInfo, AlgorithmGroup };