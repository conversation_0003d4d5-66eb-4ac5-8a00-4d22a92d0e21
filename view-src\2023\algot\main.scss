.main-view {

    box-sizing: border-box;
    padding-top: 60px;
    display: flex;
    flex-direction: column;
}

.top-box {

    margin-top: -60px;
    height: 60px;
    box-sizing: border-box;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .create-button {

        width: 209px;
        height: 36px;
        line-height: 36px;
        border-radius: 6px;
        font-size: 18px;
        font-weight: 500;
    }

    .summary-block {

        display: flex;
        flex-grow: 1;
        justify-content: flex-end;
        font-size: 16px;
        font-weight: 500;

        .sum-unit {
            margin-left: 20px;
        }

        .item-label {
            color: #aaa;
        }
    }
}

.control-box {

    margin-top: 10px;
    height: 40px;
    padding: 8px 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    .control-unit {
        margin-right: 20px;
    }

    .ctr-label {
        padding-right: 5px;
        color: #aaa;
    }

    .el-radio-button__inner {
        height: 36px;
        line-height: 18px;
        font-size: 16px;
    }
}

.morder-list {

    flex-grow: 1;
    flex-shrink: 0;
    max-height: 400px;
    overflow-y: auto;
}

.each-morder {

    margin-top: 10px;
    display: flex;
    align-items: center;
    padding: 8px 15px;
    font-size: 16px;
    background-color: #2f2f2f;
    
    &.selected {
        background-color: #294e83;
    }

    .morder-name {

        color: #c7a664;
        .morder-name-inner {
            width: 200px;
        }
    }

    .morder-aspects {

        display: flex;
        flex-grow: 1;
    }

    .aspect-block {
        width: 25%;
    }

    .upper-one,
    .lower-one {
        line-height: 24px;
    }

    .field-name {
        padding-right: 5px;
        color: #aaa;
    }

    .morder-operation {
        width: 95px;
    }
}

.child-task-title {
    
    padding-left: 10px;
    font-size: 14px;
    height: 36px;

    > span {
        line-height: 40px;
    }
}

.child-task-list {

    flex-grow: 1;
    flex-shrink: 1;
}