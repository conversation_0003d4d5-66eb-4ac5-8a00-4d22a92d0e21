const { IView } = require('../../../../component/iview');
const { repoApp } = require('../../../../repository/app');

class View extends IView {

    constructor(view_name, is_standalone_window, title) {
        super(view_name, is_standalone_window, '业绩分析');
    }

    refresh() {
        this.$webview.reload();
    }

    setup() {
        
        let $root = this.$container.querySelector('.template-root');
        let $webview = document.createElement('webview');
        $webview.classList.add('s-full-size');
        $root.appendChild($webview);
        this.$webview = $webview;
    }

    async autoLogin2Fof() {

        if (this.fofuser && (new Date().getTime() - this.fofuser.update) < 1000 * 60 * 60) {
            return this.fofuser;
        }

        let resp = await repoApp.login2Fof();
        let { errorCode, errorMsg, data } = resp;
        let { id, userName, token } = data || {};
        return (this.fofuser = { id, userName, token, update: new Date().getTime() });
    }

    getReportId() {
        return '168233047007461';
    }

    getFundCode() {
        return this.helper.isNotNone(this.amacCode) ? this.amacCode : '';
    }

    async visit() {

        let kvs = [

            // 产品代码
            { key: 'code', value: this.getFundCode() },
            { key: 'xtrade_user_id', value: this.userInfo.userId },
            { key: 'user_id', value: 127 }, // gaoyu_jyy
            { key: 'reportId', value: this.getReportId() },
            { key: 'tabKey', value: '2147483702' },
            { key: 'menuId', value: '2147483702' },
            { key: 'componentName', value: 'ReportPreviewRoute' },
            { key: 'pageHasMenu', value: 'false' },
            { key: 'label', value: '研究报告' },
            { key: 'title', value: '研究报告' },
            // 从哪里打开的fof页面
            { key: 'from', value: 'xtrade' },
            // 页面内部数据，静默刷新，间隔毫秒数
            { key: 'interval', value: 20000 },
        ];

        console.log(kvs);
        let fofusr = await this.autoLogin2Fof();
        console.log(JSON.stringify(fofusr));
        let targetUrl = `https://yitou.gaoyusoft.com/#/independentPage?${kvs.map(x => `${x.key}=${x.value}`).join('&')}`;
        let bridgeUrl = `https://yitou.gaoyusoft.com/#/tokenLogin?token=${fofusr.token}&user_id=${fofusr.id}&url=${encodeURIComponent(targetUrl)}`;
        console.log('target url = ' + targetUrl);
        console.log('token redirect url = ' + bridgeUrl);
        this.$webview.src = bridgeUrl;
    }

    handleProductChange(product_id, amac_code) {

        if (this.productId == product_id) {
            return;
        }

        console.log('context product changed: ' + JSON.stringify({ product_id, amac_code }));
        this.productId = product_id;
        this.amacCode = amac_code;
        this.visit();
    }

    build($container) {

        super.build($container);
        this.setup();
        this.registerEvent('set-context-identity', this.handleProductChange.bind(this));
    }
}

module.exports = View;