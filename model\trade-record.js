
const BizHelper = require('../libs/helper-biz').BizHelper;

/**
 * 成交
 */
class TradeRecord {

    constructor(struc) {

        this.id = struc.id;
        this.tradeId = struc.tradeId;
        this.exchangeOrderId = struc.exchangeOrderId;
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.assetType = struc.assetType;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.tradingDay = struc.tradingDay;
        this.today = struc.today;
        this.userId = struc.userId;
        this.userName = struc.userName;
        this.tradeTime = struc.tradeTime;
        this.tradeTime = struc.tradeTime;
        this.adjustFlag = !!struc.adjustFlag;        
        this.commission = struc.commission;
        this.direction = struc.direction;
        this.positionEffect = struc.positionEffect;        
        this.tradedPrice = struc.tradedPrice;
        this.volume = struc.volume;
        this.isToday = struc.isToday;
        
        this._enrich(struc);
    }

    _enrich(struc) {

        /** 最小价格变动，如：股票0.01，某期货合约0.5 */
        this.priceTick = BizHelper.getPriceTick(struc.instrument);
        /** 标准价格精度（价格小数点后，小数位数），如：股票2，某期货合约0，某期权1 */
        this.pricePrecision = BizHelper.getPricePrecision(struc.instrument);
    }
}

module.exports = { TradeRecord };