<template>
  <div>
    <el-form :model="formData" label-width="120px">
      <el-form-item label="账号名称">
        <el-input :model-value="accountName" disabled />
      </el-form-item>
      <el-form-item label="划转类型">
        <el-radio-group v-model="formData.transferType">
          <el-radio value="internal">账号内划转</el-radio>
          <el-radio value="counter">柜台间划转</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.transferType === 'internal'" label="划转方向">
        <el-select v-model="formData.direction" style="width: 100%">
          <el-option label="上海转深圳" :value="0" />
          <el-option label="深圳转上海" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.transferType === 'counter'" label="柜台方向">
        <el-select v-model="formData.counterDirection" style="width: 100%">
          <el-option label="普通柜台 > 急速柜台" :value="1" />
          <el-option label="急速柜台 > 普通柜台" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="划转金额" required>
        <el-input-number v-model="formData.amount" :precision="2" :min="0" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="executeTransfer">确定划转</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef } from 'vue';
import { ElMessage } from 'element-plus';
import { Repos } from '../../../../xtrade-sdk/dist';

interface TransferFormData {
  direction: number; // 0: 上海转深圳, 1: 深圳转上海
  amount: number;
  transferType: 'internal' | 'counter'; // internal: 账号内划转, counter: 柜台间划转
  counterDirection: number; // 1: 普通柜台 > 急速柜台, 2: 急速柜台 > 普通柜台
}

const repoInstance = new Repos.GovernanceRepo();

const { accountName } = defineProps<{
  accountName: string;
}>();

const emitter = defineEmits<{
  save: [];
  cancel: [];
}>();

const accountId = shallowRef('');
const formData = ref<TransferFormData>({
  direction: 0,
  amount: 0,
  transferType: 'internal',
  counterDirection: 1,
});

const reset = (id: string, name: string) => {
  accountId.value = id;
  formData.value.direction = 0;
  formData.value.amount = 0;
  formData.value.transferType = 'internal';
  formData.value.counterDirection = 1;
};

const cancel = () => {
  emitter('cancel');
};

const executeTransfer = async () => {
  const { amount, transferType, counterDirection } = formData.value;

  if (amount <= 0) {
    ElMessage.error('请输入有效的划转金额');
    return;
  }

  if (transferType === 'internal') {
    // 账号内划转 - 需要发送到交易服务器
    ElMessage.info('账号内资金划转功能需要连接交易服务器，暂未实现');
  } else {
    // 柜台间划转
    const data = {
      account_id: accountId.value,
      trans_type: counterDirection,
      trans_amount: amount,
      account_name: accountName,
    };

    const { errorCode, errorMsg } = await repoInstance.TransferBetweenCounters(data);

    if (errorCode === 0) {
      ElMessage.success('柜台间资金划转成功');
    } else {
      ElMessage.error(errorMsg || '柜台间资金划转失败');
    }
  }

  emitter('save');
};

defineExpose({
  reset,
});
</script>
