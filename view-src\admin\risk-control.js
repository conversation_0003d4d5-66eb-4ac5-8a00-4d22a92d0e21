const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const RiskDialog = require('./risk-dialog');
const drag = require('../../directives/drag');
const BizHelper = require('../../libs/helper-biz').BizHelper;
const xlsx = require('node-xlsx');
const RiskControlGroup = require('./risk-control-group');
const GlobalRiskControlView = require('./global-risk-control');

class View extends BaseAdminView {

    get repoUser() {
        return require('../../repository/user').repoUser;
    }

    get repoRisk() {
        return require('../../repository/risk').repoRisk;
    }

    get repoClassification() {
        return require('../../repository/classification').repoClassification;
    }

    get exportCtx() {
        let Ex = require('../../libs/el-table-to-excel');
        return new Ex();
    }

    constructor(view_name) {

        super(view_name, '风控管理');
        this.rules = [];
        this.rulesHash = {};

        this.PRIMARYCLASS = '一级分类';
        this.SECONDARYCLASS = '二级分类';
        this.CONSTNAME = '合约名称';
        this.CONSTINSTRUMENT = '合约代码';
        this.SELF_CLASSIFICATION_ID = 5;
        this.list = [];
        this.listHash = {};
        this.dialog = {
            main: {
                visible: false,
                mode: 'edit',
            },
            create: {
                visible: false,
            },
        };
        this.userList = [];
        this.userHash = {};
        this.constant = {
            ClassificationMethods: [],
            cached: false,
            mode: {
                READONLY: 'r',
                READWRITE: 'rw',
            },
        };
        this.classification = this.createEmptyClassification();
        this.riskDialog = null;
        this.$container = null;
        this.rcg = new RiskControlGroup(this);
    }

    resizeWindow() {
        
        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.maxHeight = winHeight - 190;
    }

    assembleGlobalRiskControl() {

        const $root = this.$container.querySelector('.global-risk-setting');
        const tbview = new GlobalRiskControlView('@admin/global-risk-control', false);
        tbview.loadBuild($root);
    }

    loadBuildHtml(contextData) {
        this.riskDialog = new RiskDialog('@admin/risk-dialog', true, {
            title: '风控设置',
        });
        this.riskDialog.registerEvent('save', this.updateRiskTemplateList.bind(this));
        let eleCtx = this.vueApp.$refs.riskDialog.$el;
        this.riskDialog.loadBuild(eleCtx.querySelector('.context-container'));
        //等待risk-dialog上下文加载完成再进行操作
        setTimeout(() => {
            this.riskDialog.thisWindow.webContents.send('set-context-data', contextData);
        }, 500);
    }

    createApp() {
        let controller = this;
        this.tableProps = { maxHeight: 300, ...this.systemSetting.tableProps };
        this.vueApp = new Vue({
            el: this.$container.querySelector('.risk-control-view-root'),
            components: {
                DataTables: DataTables.DataTables,
            },
            mixins: [],
            data: {
                activeTab: 'global-risk-control',
                tableProps: this.tableProps,
                paginationDef: this.systemSetting.tablePagination,
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'el-icon-search',
                    },
                },
                Rules: this.rules,
                classesList: this.list,
                dialog: this.dialog,
                classification: this.classification,
                rules: {
                    classificationName: [{ type: 'string', required: true, message: '请输入分类名称' }],
                    classMethodId: [{ required: true, message: '请选择分类方法' }],
                },
                constant: this.constant,
                riskDialog: {
                    visible: false,
                },
                groupData: this.rcg.data,
                receiverData: this.rcg.msgReceiver,
            },
            directives: {
                drag,
            },
            computed: {

                riskGroups: () => {

                    let results = [];
                    this.rcg.data.groups.forEach(each => {
                        results.push(each);
                        if (!each.collapsed) {
                            results.push(...each.items);
                        }
                    });

                    return results;
                }
            },
            methods: {
                saveRiskTemplate() {
                    controller.riskDialog.want2SaveTemplate();
                },
                handleClose() {
                    controller.riskDialog.dispose();
                    this.riskDialog.visible = !this.riskDialog.visible;
                },
                createTemplate() {
                    this.riskDialog.visible = true;
                    this.$nextTick(() => {
                        controller.loadBuildHtml({
                            action: 'template_create',
                            identity: null,
                            name: null,
                        });
                    });
                },
                updateTabChange: async tab => {
                    let tabName = tab.name;
                    switch (tabName) {
                        case 'template':
                            this.getRuleTemplateList();
                            break;
                        case 'classification':
                            await this.getUserList();
                            this.reqClassificationList();
                            break;
                        case 'risk-group':
                            await this.rcg.activate();
                            break;
                        default:
                            console.log('default');
                            break;
                    }
                },
                getTemplateType: this_template => {
                    return this.getTemplateType(this_template);
                },
                viewTemplate: this_template => {
                    this.viewTemplate(this_template);
                },
                editTemplate(this_template) {
                    this.riskDialog.visible = true;
                    this.$nextTick(() => {
                        controller.loadBuildHtml({
                            action: 'template_edit',
                            identityType: this_template.identityType,
                            identity: this_template.templateId,
                            name: this_template.templateName,
                        });
                    });
                },
                removeTemplate: this_template => {
                    this.interaction.showConfirm({
                        title: '提示',
                        message: '确定要移除当前的风控模板?',
                        confirmed: () => {
                            this.removeTemplate(this_template);
                        },
                        canceled: () => {
                            console.log('canceled');
                        },
                    });
                },
                downloadClassification: this_classification => {
                    if (!this_classification.loaded) {
                        this.editClassification(this_classification, () => {
                            this.exportClassification(this_classification);
                        });
                    } else {
                        this.exportClassification(this_classification);
                    }
                },
                importClassification: this_classification => {
                    this.classification = this.helper.extend(this.classification, this_classification);
                    // this.dialog.main.visible = true;
                    this.vueApp.$refs.input.click();
                },
                processImport: () => {
                    this.processImport();
                },
                openCreateDialog: () => {
                    this.getRiskClassification().then(() => {
                        this.dialog.create.visible = true;
                        this.vueApp.$nextTick(() => {
                            this.initializeCreate();
                        });
                    });
                },
                closeCreateDialog: () => {
                    this.initializeCreate();
                    this.dialog.create.visible = false;
                },
                createClassification: () => {
                    this.createClassification();
                },
                saveInstruments: () => {
                    if (this.classification.instruments.some(x => x.editable)) {
                        this.interaction.showError('请先保存您正在编辑的合约信息!');
                        return;
                    }
                    this.saveInstruments();
                },
                addInstrument: () => {
                    this.putupInstrument();
                },
                deleteInstrument: instrument => {
                    if (this.isEmptyInstrument(instrument)) {
                        this.deleteInstrument(instrument);
                    } else {
                        this.interaction.showConfirm({
                            title: '提示',
                            message: '确定要删除当前选中的股票吗?',
                            confirmed: () => {
                                this.deleteInstrument(instrument);
                            },
                            canceled: () => {
                                console.log('error delete instrument');
                            },
                        });
                    }
                },
                editInstrument: instrument => {
                    instrument.editable = true;
                },
                cacheInstrument: instrument => {
                    this.saveEditInstrument(instrument);
                },
                deleteClassification: this_classification => {
                    this.interaction.showConfirm({
                        title: '提示',
                        message: '确定要删除当前选中的分类吗?',
                        confirmed: () => {
                            this.deleteClassification(this_classification);
                        },
                        canceled: () => {
                            console.log('delete error');
                        },
                    });
                },
                editClassification: this_classification => {
                    this.editClassification(this_classification, () => {
                        this.dialog.main.visible = true;
                        this.dialog.main.mode = 'edit';
                    });
                },
                viewClassification: this_classification => {
                    this.editClassification(this_classification, () => {
                        this.dialog.main.mode = 'view';
                        this.dialog.main.visible = true;
                    });
                },
                closeDetailDialog: () => {
                    this.closeDetailDialog();
                },
                downloadTemplate: () => {
                    this.downloadTemplate();
                },

                isGroup: (row) => { return this.rcg.isGroup(row); },
                createGroup: () => { this.rcg.createGroup(); },
                editGroup: (row) => { this.rcg.editGroup(row); },
                editGroupItem: (row) => { this.rcg.editGroupItem(row); },
                saveGroup: () => { this.rcg.saveGroup(); },
                closeDialog: () => { this.rcg.closeDialog(); },
                saveReceiver: () => { this.rcg.saveReceiver(); },
                closeReceiverDialog: () => { this.rcg.closeReceiverDialog(); },
                toggleCollapse: (row) => { this.rcg.toggleCollapse(row); },
                addGroupItem: (row) => { this.rcg.addGroupItem(row); },
                openRiskSetting: (row) => { this.rcg.openRiskSetting(row); },
                openBehaviorSetting: (row) => { this.rcg.openBehaviorSetting(row); },
                openReceiverDialog: (row) => { this.rcg.openReceiverDialog(row); },
                deleteGroup: (row) => { this.rcg.deleteGroup(row); },
                deleteGroupItem: (row) => { this.rcg.deleteGroupItem(row); },
                saveGroupItem: (row) => { this.rcg.saveGroupItem(row); },
                cancelEditGroupItem: (group_id) => { this.rcg.cancelEditGroupItem(group_id); },
                formatTime: (column, row, datetime) => { return this.rcg.formatTime(column, row, datetime); },
                identityTypeChange: (row) => { this.rcg.identityTypeChange(row); },
                identityChange: (row) => { this.rcg.identityChange(row); },
                filterIdentities: (identity_type) => { return this.rcg.filterIdentities(identity_type); },
                getIdentityTypeName: (identity_type) => { return this.rcg.getIdentityTypeName(identity_type); },
            },
        });
        this.vueApp.$nextTick(() => {
            this.resizeWindow();
            this.assembleGlobalRiskControl();
        });
    }

    createTemplate() {
        this.openRiskModal({
            identity: null,
            templateName: null,
            action: 'template_create',
        });
    }

    editTemplate(this_template) {
        this.openRiskModal({
            identity: this_template.templateId,
            templateName: this_template.templateName,
            action: 'template_edit',
            identityType: this_template.identityType,
        });
    }

    viewTemplate(this_template) {
        // {
        //     identity: this_template.templateId,
        //     templateName: this_template.templateName,
        //     action: 'template_readonly'
        // }
    }

    getTemplateType(this_template) {
        let type =
            this.helper.dict2Array(this.systemEnum.identityType).first(x => x.code === this_template.identityType) ||
            {};
        return type.mean ? type.mean + '模板' : '未知类型';
    }

    async getRuleTemplateList() {
        let loading = this.interaction.showLoading({
            text: '请求风控模板列表...',
        });
        this.rules.clear();
        this.rulesHash = {};
        try {
            let resp = await this.repoRisk.getTemplateList();
            if (resp.errorCode === 0) {
                this.rules.merge(resp.data || []);
                this.rules.orderByDesc(x => x.templateId);
                if (resp.data instanceof Array) {
                    resp.data.forEach(risk_rule => {
                        let id = risk_rule.templateId;
                        if (!this.rulesHash[id]) {
                            this.rulesHash[id] = risk_rule;
                        }
                    });
                }
            } else {
                this.interaction.showError('获取风控模板列表失败!');
            }
        } catch (e) {
            this.interaction.showError('获取风控模板列表失败!');
        } finally {
            loading.close();
        }
    }

    async removeTemplate(this_template) {
        let loading = this.interaction.showLoading({
            text: '操作进行中...',
        });
        let templateId = this_template.templateId;
        try {
            let resp = await this.repoRisk.deleteTemplate(templateId);
            if (resp.errorCode === 0) {
                this.interaction.showSuccess('删除风控模板成功!');
                this.rules.remove(x => x.templateId === templateId);
                delete this.rulesHash[templateId];
            } else {
                this.interaction.showError('删除风控模板失败!');
            }
        } catch (e) {
            this.interaction.showError('删除风控模板失败!');
        } finally {
            loading.close();
        }
    }

    createEmptyClassification() {
        return {
            id: null,
            classificationName: null,
            classificationIds: [],
            classMethodId: null,
            classMethodName: null,
            createUser: null,
            fullName: null,
            classifyType: this.constant.mode.READWRITE,
            loaded: false,
            instruments: [],
            instrumentHash: {},
        };
    }

    createEmptyInstrument() {
        return {
            id: null,
            primaryClass: null,
            secondaryClass: null,
            instrument: null,
            instrumentName: null,
        };
    }

    downloadTemplate() {
        this.renderProcess.send(
            this.systemEvent.downloadTemplate,
            `file://${__dirname}/../../data-template/自定义分类导入模板.xlsx`,
        );
    }

    closeCreateDialog() {
        if (this.dialog.main.mode == 'view') {
            this.dialog.main.mode = 'edit';
        }
        this.initializeModal();
        this.dialog.visible = false;
    }

    closeDetailDialog() {
        this.classification = this.helper.extend(this.classification, this.createEmptyClassification());
        this.dialog.main.mode = 'edit';
        this.dialog.main.visible = false;
    }

    exportClassification(this_classification) {
        let data_source = this.constructClassification(this.helper.deepClone(this_classification));
        let destination = data_source.every(x => x.parentClassName && x.parentClassName !== '')
            ? data_source.orderBy(x => x.parentClassName)
            : data_source;
        // alert('export todo');
        this.exportCtx
            .build({
                columns: [
                    { label: '一级分类', property: 'parentClassName' },
                    { label: '二级分类', property: 'className' },
                    { label: '合约名称', property: 'instrumentName' },
                    { label: '合约代码', property: 'instrument' },
                ],
                filters: [],
                data: destination,
                filename: this_classification.classificationName,
                success: () => {
                    this.interaction.showSuccess('导出成功!');
                },
                failed: () => {
                    console.error('failed');
                },
            })
            .exportSingleSheet();
    }

    createClassification() {
        this.vueApp.$refs.classification.validate(valid => {
            if (valid) {
                this.cacheClassification();
                this.interaction.showSuccess('创建分类成功!');
                this.initializeCreate();
                this.dialog.create.visible = false;
                console.log(this.list);
            } else {
                console.log('input error');
            }
        });
    }

    //把数据插入到列表中，但是还没有真正保存
    cacheClassification() {
        let createUser = this.userHash[this.userInfo.userId];
        let classification = this.helper.deepClone(this.classification);
        classification.fullName = createUser.fullName;
        classification.classMethodId = this.SELF_CLASSIFICATION_ID;
        classification.createUser = this.userInfo.userId;
        classification.timestamp = new Date().getTime().toString();
        let classId = classification.timestamp;
        this.list.unshift(classification);
        this.listHash[classId] = classification;
    }

    validUnique({ instrumentName, instrument }) {
        return this.classification.instrumentHash[instrument] === undefined;
    }

    validInputInstrument(toValid) {
        if (!toValid.instrument) {
            return false;
        }
        toValid.instrument = toValid.instrument.toUpperCase();
        let ins = BizHelper.stocksHash[toValid.instrument] || BizHelper.futuresHash[toValid.instrument];
        if (ins === undefined) {
            return false;
        }
        if (!toValid.primaryClass && toValid.secondaryClass) {
            return false;
        }
        if (
            this.classification.instruments.every(x => !x.primaryClass && !x.secondaryClass) &&
            (!toValid.primaryClass && !toValid.secondaryClass)
        ) {
            toValid.instrumentName = ins.instrumentName;
            return true;
        }
        if (!toValid.primaryClass && !toValid.secondaryClass) {
            return false;
        }
        toValid.instrumentName = ins.instrumentName;
        return true;
    }

    saveEditInstrument(instrument) {
        if (this.validInputInstrument(instrument.edit)) {
            instrument.editable = false;
            instrument = this.helper.extend(instrument, instrument.edit);
            let id = this.classification.id || this.classification.timestamp;
            let updateClassify = this.list.find(x => (x.id === id || x.timestamp === id) && x.id !== undefined) || {};
            //将新增的股票代码更新进去
            if (updateClassify.instruments instanceof Array) {
                updateClassify.instruments.push(instrument);
                updateClassify.instrumentHash[instrument.instrument] = instrument;
            }
            console.log(updateClassify);
        } else {
            this.interaction.showError('您输入的信息不满足要求!');
        }
    }

    putupInstrument() {
        if (this.classification.instruments.some(x => x.editable)) {
            this.interaction.showWarning('请先保存上一条数据!');
            return;
        }
        this.classification.instruments.unshift({
            editable: true,
            edit: this.createEmptyInstrument(),
        });
    }

    _addInstrument(ins) {
        console.log(ins);
        this.classification.instruments.push(ins);
        this.classification.instrumentHash[ins.instrument] = ins;
    }

    processImport() {
        if (this.vueApp.$refs.input && this.vueApp.$refs.input.files.length > 0) {
            let file = this.vueApp.$refs.input.files[0];
            const workExcel = xlsx.parse(file.path);
            try {
                workExcel.forEach(sheet => {
                    let header = sheet.data.shift();
                    let primaryIndex = header.findIndex(x => x === this.PRIMARYCLASS);
                    let secondaryIndex = header.findIndex(x => x === this.SECONDARYCLASS);
                    let codeIndex = header.findIndex(x => x === this.CONSTINSTRUMENT);
                    let nameIndex = header.findIndex(x => x === this.CONSTNAME);
                    sheet.data
                        .filter(x => !x.every(cdt => cdt === ''))
                        .forEach((row, index) => {
                            if (!row[codeIndex]) {
                                this.interaction.showError(
                                    `部分数据导入成功，数据完整性验证不通过，错误位置(工作表：${
                                        sheet.name
                                    }，行：${index + 1})，错误原因（未录入合约代码），导入强制终止!`,
                                );
                                return;
                            }
                            let record = {
                                primaryClass: row[primaryIndex],
                                secondaryClass: row[secondaryIndex],
                                instrument: row[codeIndex].toUpperCase(),
                                instrumentName: row[nameIndex],
                                edit: {
                                    primaryClass: row[primaryIndex],
                                    secondaryClass: row[secondaryIndex],
                                    instrument: row[codeIndex].toUpperCase(),
                                    instrumentName: row[nameIndex],
                                },
                                editable: false,
                            };
                            //如果在两个hash里面都找不到的话，说明这个合约代码不对
                            let ins =
                                BizHelper.stocksHash[record.instrument] || BizHelper.futuresHash[record.instrument];
                            if (ins === undefined) {
                                this.interaction.showError(
                                    `部分数据导入成功，数据完整性验证不通过，错误位置(工作表：${
                                        sheet.name
                                    }，行：${index + 1})，错误原因（录入的合约代码不存在），导入强制终止!`,
                                );
                                return;
                            }
                            record.instrumentName = ins.instrumentName;
                            if (this.validUnique(record)) {
                                this._addInstrument(record);
                            }
                        });
                });
            } catch (e) {
                this.interaction.showError('对不起，你上传的文件数据格式不符合要求，请联系管理员!');
            } finally {
                if (!this.dialog.main.visible) {
                    this.dialog.main.visible = true;
                }
            }
        }
        this.vueApp.$refs.input.value = '';
    }

    isEmptyInstrument(this_ins) {
        return (
            !this_ins.id &&
            this_ins.edit &&
            this_ins.edit.primaryClass === '' &&
            this_ins.edit.secondaryClass === '' &&
            this_ins.edit.instrument === '' &&
            this_ins.edit.instrumentName === ''
        );
    }

    deleteInstrument(this_ins) {
        let instrument = this_ins.instrument;
        this.classification.instruments.remove(x => x.instrument === instrument);
        delete this.classification.instrumentHash[instrument];
    }

    reshapeClassification(origin) {
        origin = this.helper.dict2Array(origin.groupBy(x => x.classificationName));
        origin = origin.map(x => {
            let obj = {};
            obj.instruments = [];
            obj.classificationIds = x.map(x => x.id);
            obj.instrumentHash = {};
            x.forEach(cdt => {
                cdt.primaryClass = cdt.parentClassName;
                cdt.secondaryClass = cdt.className;
                cdt.editable = false;
                cdt.edit = {
                    primaryClass: cdt.parentClassName,
                    secondaryClass: cdt.className,
                    instrument: cdt.instrument,
                    instrumentName: cdt.instrumentName,
                };
                obj.instrumentHash[cdt.instrument] = cdt;
            });
            if (x.length > 0) {
                obj.classifyType = x[0].orgId === 0 ? this.constant.mode.READONLY : this.constant.mode.READWRITE;
                obj.id = x[0].id;
                obj.classMethodId = x[0].classMethodId;
                obj.classMethodName = x[0].classMethodName;
                obj.classificationName = x[0].classificationName;
                obj.orgId = x[0].orgId;
                obj.loaded = false;
                obj.createUser = x[0].createUser;
                obj.fullName = this.userHash[x[0].createUser] ? this.userHash[x[0].createUser].fullName : '未知';
            }
            return obj;
        });
        return origin;
    }

    constructClassification(Model) {
        let classMethod = this.constant.ClassificationMethods.find(x => x.classMethodId === Model.classMethodId) || {};
        return Model.instruments.map(instrument => {
            return {
                id: instrument.id,
                classificationName: Model.classificationName,
                parentClassName: instrument.primaryClass,
                className: instrument.secondaryClass,
                classMethodId: classMethod.classMethodId,
                classMethodName: classMethod.classMethodName,
                orgId: this.userInfo.orgId,
                description: null,
                createUser: this.userInfo.userId,
                instrument: instrument.instrument,
                instrumentName: instrument.instrumentName,
            };
        });
    }

    saveInstruments() {
        this.interaction.showConfirm({
            title: '提示',
            message: '确定要保存当前录入的分类信息吗?',
            confirmed: async () => {
                let model = this.helper.deepClone(this.classification);
                let result = await this.reqCreateClassification(model);
                if (result.flag) {
                    this.dialog.main.visible = false;
                    this.interaction.showSuccess('保存分类信息成功!');
                    let id = this.classification.id || this.classification.timestamp;
                    let toUpdate = this.list.find(x => (x.id === id || x.timestamp === id) && id !== undefined);
                    result.data.forEach(ins => {
                        if (toUpdate && toUpdate.instrumentHash) {
                            let modify_instrument = toUpdate.instrumentHash[ins.instrument];
                            modify_instrument.id = ins.id;
                        }
                    });
                    toUpdate.loaded = true;
                    //将原来的清空
                    this.classification = this.helper.extend(this.classification, this.createEmptyClassification());
                } else {
                    this.interaction.showError('保存分类信息失败！');
                }
            },
            canceled: () => {},
        });
    }

    async reqClassificationList() {
        console.log('classification');
        let loading = this.interaction.showLoading({
            text: '正在获取...',
        });
        try {
            this.list.clear();
            let resp = await this.repoClassification.getClassificationList();
            if (resp.errorCode === 0) {
                this.list.merge(
                    this.reshapeClassification(
                        resp.data.filter(condition => condition.classificationName !== '') || [],
                    ),
                );
                this.list = this.list.orderByDesc(x => x.id);
                if (resp.data instanceof Array) {
                    resp.data.forEach(x => {
                        let id = x.id;
                        this.listHash[id] = x;
                    });
                }
            } else {
                this.interaction.showError('获取分类列表失败!');
            }
        } catch (e) {
            this.interaction.showError('获取分类列表失败!');
        } finally {
            loading.close();
        }
    }

    async loadDetailInstruments(classification) {
        let output = [];
        let loading = this.interaction.showLoading({
            text: '正在获取分类详情信息...',
        });
        try {
            if (classification.classificationIds instanceof Array) {
                let resps = await Promise.all(
                    classification.classificationIds.map(id => this.repoClassification.getClassificationDetail(id)),
                );
                if (resps.every(x => x.errorCode === 0)) {
                    // if (typeof callback === 'function') {
                    //     callback();
                    // }
                    resps.forEach(resp => {
                        output.merge(resp.data || []);
                    });
                } else {
                    this.interaction.showError('获取分类详情信息失败!');
                }
            }
        } catch (e) {
            this.interaction.showError('获取分类详情信息失败!');
        }
        loading.close();
        return output;
    }

    async reqCreateClassification(classModel) {
        let loading = this.interaction.showLoading({
            text: '正在添加...',
        });
        let result = {
            flag: false,
            data: null,
        };
        try {
            if (this.constant.ClassificationMethods.length <= 0) {
                await this.getRiskClassification();
            }
            let constructModel = this.constructClassification(classModel);
            let resp = await this.repoClassification.createClassification(constructModel);
            if (resp.errorCode === 0) {
                result.flag = true;
                result.data = resp.data;
            }
        } catch (e) {
            null;
        } finally {
            loading.close();
        }
        return result;
    }

    async reqModifyClassification(classModel) {
        let loading = this.interaction.showLoading({
            text: '正在更新...',
        });
        let result = {
            flag: false,
            data: null,
        };
        try {
            let constructModel = this.constructClassification(classModel);
            console.log(constructModel);
            let resp = await this.repoClassification.updateClassification(constructModel);
            if (resp.errorCode === 0) {
                result.flag = true;
                result.data = resp.data;
            }
        } catch (e) {
            null;
        } finally {
            loading.close();
        }
        return result;
    }

    editClassification(classification, callback) {
        let editModel = null;
        if (classification.loaded) {
            editModel = this.helper.deepClone(classification);
            typeof callback === 'function' ? callback() : '';
            this.classification = this.helper.extend(this.classification, editModel);
        } else {
            this.loadDetailInstruments(classification).then(instruments => {
                instruments.forEach(cdt => {
                    cdt.primaryClass = cdt.parentClassName;
                    cdt.secondaryClass = cdt.className;
                    cdt.editable = false;
                    cdt.edit = {
                        primaryClass: cdt.parentClassName,
                        secondaryClass: cdt.className,
                        instrument: cdt.instrument,
                        instrumentName: cdt.instrumentName,
                    };
                    classification.instrumentHash[cdt.instrument] = cdt;
                });
                classification.instruments = instruments;
                editModel = this.helper.deepClone(classification);
                this.classification = this.helper.extend(this.classification, editModel);
                classification.loaded = true;
                typeof callback === 'function' ? callback() : '';
            });
        }
    }

    setEmptyClassification() {
        this.classification.instruments.clear();
        this.classification = this.createEmptyClassification();
    }

    initializeCreate() {
        this.classification.classificationName = '';
        this.classification.classMethodId = '';
        this.vueApp.$refs.classification.resetFields();
        this.vueApp.$refs.classification.clearValidate();
    }

    initializeModal() {
        this.setEmptyClassification();
    }

    async deleteClassification(classification) {
        let loading = this.interaction.showLoading({
            text: '正在删除...',
        });
        let className = classification.classificationName;
        try {
            let resp = await this.repoClassification.deleteClassification(className);
            if (resp.errorCode === 0) {
                this.list.remove(x => {
                    return x.classificationName === className;
                });
                delete this.listHash[className];
                this.interaction.showSuccess('删除分类成功!');
            } else {
                this.interaction.showError('删除分类失败，您在风控规则中尚在使用当前分类!');
            }
        } catch (e) {
            this.interaction.showError('删除分类失败!');
        } finally {
            loading.close();
        }
    }

    async getRiskClassification() {
        if (this.constant.cached) {
            return Promise.resolve(true);
        }
        let loading = this.interaction.showLoading({
            text: '正在获取分类方法...',
        });
        try {
            this.constant.ClassificationMethods.clear();
            let resp = await this.repoRisk.getRiskClassification();
            if (resp.errorCode === 0) {
                let classMethods = resp.data.map(x => {
                    return {
                        classMethodId: x.classMethodId,
                        classMethodName: x.classMethodName,
                    };
                }); //.toUniqueArray();
                this.constant.ClassificationMethods.merge(classMethods);
                this.constant.cached = true;
            } else {
                this.interaction.showError('获取分类方法失败!');
            }
        } catch (e) {
            this.interaction.showError('获取分类方法失败!');
        } finally {
            loading.close();
        }
    }

    async getUserList() {
        let loading = this.interaction.showLoading({
            text: '请求用户列表...',
        });
        this.userList.clear();
        try {
            const resp = await this.repoUser.getAll();
            if (resp.errorCode == 0) {
                if (this.userInfo.isOrgAdmin) {
                    resp.data.push(this.helper.extend({ id: this.userInfo.userId }, this.userInfo));
                }
                let list = resp.data;
                this.userList.merge(list);
                resp.data.forEach(user => {
                    let id = user.id;
                    this.userHash[id] = user;
                });
            } else {
                console.log(resp);
                this.interaction.showHttpError('查询用户列表出错，详细信息："' + resp.errorMsg + '"');
            }
        } catch (error) {
            this.interaction.showError('查询用户列表出错!');
        } finally {
            loading.close();
        }
    }

    updateRiskTemplateList(params) {
        let templateId = params.templateId;
        let action_text = '创建';
        if (this.rulesHash[templateId] !== undefined) {
            let toUpdate = this.rules.find(x => x.templateId === templateId);
            this.helper.extend(toUpdate, params);
            action_text = '修改';
        } else {
            let data = this.helper.deepClone(params);
            this.rulesHash[templateId] = data;
            this.rules.unshift(data);
        }
        this.interaction.showSuccess(action_text + '模板成功!');
        this.vueApp.riskDialog.visible = !this.vueApp.riskDialog.visible;
        this.riskDialog.dispose();
    }

    refresh() {
        this.vueApp.updateTabChange({ name: this.vueApp.activeTab });
    }

    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    build($container) {

        this.$container = $container;
        this.createApp();
        this.getRuleTemplateList();
    }
}

module.exports = View;
