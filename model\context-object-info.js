
const systemEnum = require('../config/system-enum').systemEnum;
const assetsType = systemEnum.assetsType;
const helper = require('../libs/helper').helper;

class AccountInfo {

    constructor (struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.financeAccount = struc.financeAccount;
        this.financeAccountName = struc.financeAccountName;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.assetType = struc.assetType;
        this.isCredit = !!struc.isCredit;
    }
}

/**
 * 上下文对象信息（产品 | 策略 | 账号 | 母单）
 */
class ContextObjectInfo {

    constructor (struc) {

        let fund_id = struc.fundId;
        let strategy_id = struc.strategyId;
        let parent_id = struc.parentOrderId;
        let account_id = struc.accountId;

        let isAboutParent = !helper.isNone(parent_id);
        let isAboutStrategy = !isAboutParent && !helper.isNone(strategy_id);
        let isAboutFund = !isAboutStrategy && !helper.isNone(fund_id);
        let isAboutAccount = !isAboutParent && !isAboutStrategy && !isAboutFund;

        this.isAboutFund = isAboutFund;
        this.isAboutStrategy = isAboutStrategy;
        this.isAboutAccount = isAboutAccount;
        this.isAboutParent = isAboutParent;
        this.identityId = isAboutParent ? parent_id : isAboutStrategy ? strategy_id : isAboutFund ? fund_id : account_id;

        let accounts = struc.accounts;
        let accountInfos = accounts instanceof Array ? accounts.map(x => new AccountInfo(x)) : [];
        accountInfos.forEach(item => {

            delete item.fundName;
            delete item.accountName;
            delete item.strategyName;
        });
        
        this.containsStockAccounts = accountInfos.find(x => x.assetType == assetsType.stock.code) !== undefined;
        this.containsFutureAccounts = accountInfos.find(x => x.assetType == assetsType.future.code)  !== undefined;
        this.containsOptionAccounts = accountInfos.find(x => x.assetType == assetsType.option.code) !== undefined;
        this.accounts = accountInfos;
    }

    /**
     * 克隆一个具有完全相同类型的实例（结构 + 类型，完全匹配）
     * @param {*} same_struc 
     */
    static clone(same_struc) {

        if (helper.isNone(same_struc)) {
            return null;
        }
        else if (same_struc instanceof ContextObjectInfo) {
            return same_struc;
        }
        else {

            let cloned = new ContextObjectInfo({});
            for (let key in same_struc) {
                cloned[key] = same_struc[key];
            }

            return cloned;
        }
    }
}

module.exports = { ContextObjectInfo };