/**
 * 任务状态（包含业务状态）
 */
const TaskEntrustStatus = { running: 0, completed: 1, canceled: 2 };

/**
 * 委托信息
 */
class T0EntrustInfo {

    constructor({ id, taskId, algorithmStatus, stockCode, stockName, buyAmount, buyMoney, buyAvgPrice, sellAmount, sellMoney, sellAvgPrice, openAmount, openMoney, openAvgPrice, dealTimes, closePrice, preclosePrice, realMoney, floatMoney, preRealMoney, preFloatMoney, totalProfitLoss, targetVolume }) {
        
        this.id = id;
        this.task_id = taskId;
        this.status = algorithmStatus;
        this.stock_code = stockCode;
        this.stock_name = stockName;
        this.target_volume = targetVolume;

        this.buy_amount = buyAmount;
        this.buy_avg_price = buyAvgPrice;
        this.buy_money = buyMoney;

        this.sell_amount = sellAmount;
        this.sell_avg_price = sellAvgPrice;
        this.sell_money = sellMoney;

        this.open_amount = openAmount;
        this.open_avg_price = openAvgPrice;
        this.open_money = openMoney;
        
        this.pre_float_profit = preFloatMoney;
        this.pre_closed_profit = preRealMoney;
        this.pre_close_price = preclosePrice;

        this.closed_profit = realMoney;
        this.float_profit = floatMoney;
        this.total_profit = totalProfitLoss;

        this.close_price = closePrice;
        this.deal_times = dealTimes;
    }
}

/**
 * 日内回转任务结构
 */
class T0TaskInfo {

    constructor({ identityId, xtradeTaskId, fundName, fundId, xtradeStrategyName, xtradeStrategyId, accountName, accountId, strategyId, algorithmMappingId, taskId, taskName, status, strategyName, availableMoney, floatMoney, realMoney, totalProfitLoss, totalDealMoney, runningStock, directionBuy, directionSell, algoParam, list }) {

        this.xtrade = {

            /** xtrade identity id */
            identity_id: identityId,
            /** xtrade 任务id */
            task_id: xtradeTaskId,
            /** xtrade 算法id */
            algo_id: algorithmMappingId,

            /** xtrade 产品id */
            fund_id: fundId,
            /** xtrade 产品名称 */
            fund_name: fundName,
            /** xtrade 策略id */
            strategy_id: xtradeStrategyId,
            /** xtrade 策略名称 */
            strategy_name: xtradeStrategyName,
            /** xtrade 账号id */
            account_id: accountId,
            /** xtrade 账号名称 */
            account_name: accountName,
        };

        this.task_id = taskId;
        this.task_name = taskName;
        /** 该母单的状态 */
        this.status = runningStock > 0 ? TaskEntrustStatus.running : TaskEntrustStatus.completed;
        this.strategy_id = strategyId;
        this.strategy_name = strategyName;
        this.stock_count = Array.isArray(list) ? list.length : 0;

        this.closed_profit = realMoney;
        this.float_profit = floatMoney;
        this.total_profit = totalProfitLoss;
        this.total_deal = totalDealMoney;
        this.available = availableMoney;

        this.direction_buy = directionBuy;
        this.direction_sell = directionSell;

        let algop = this.algop = {

            /** 算法分配资金 */
            money: 0,
            /** 单日止损金额 */
            lose: 0,
            /** 平仓时间 */
            time: null,
            /** 策略有效期至 */
            end_date: null,
        };

        if (typeof algoParam == 'string' && algoParam.length > 0) {

            try {

                let { money, lose, time, end_time } = JSON.parse(algoParam);
                algop.money = money || 0;
                algop.lose = lose || 0;
                algop.time = time ? new Date(time) : new Date();
                algop.end_date = end_time ? new Date(end_time) : new Date();
            }
            catch(ex) {
                console.error(algoParam, ex);
            }
        }

        /** 实际发生委托列表 */
        this.entrusts = Array.isArray(list) ? list.map(x => new T0EntrustInfo(x)): [];
    }
}

/**
 * 日内回转任务数据包（多层次树形结构）
 */
class T0TaskPackage {

    constructor({ floatMoney, realMoney, totalProfitLoss, totalDealMoney, runningStock, taskTableData }) {

        this.stock_count = runningStock;
        this.closed_profit = realMoney;
        this.float_profit = floatMoney;
        this.total_profit = totalProfitLoss;
        this.total_deal = totalDealMoney;

        /** 母单任务列表 */
        this.tasks = Array.isArray(taskTableData) ? taskTableData.map(x => new T0TaskInfo(x)): [];
    }
}

/**
 * 任务包含仓位（适用创建视图，非查询回显子任务视图）
 */
class T0PositionInfo {

    constructor({ stock_code, stock_name, totalPosition, closablePosition, targetPosition }) {

        this.stock_code = stock_code;
        this.stock_name = stock_name;
        /** 总仓位（可平 + 不可平） */
        this.totalPosition = totalPosition;
        /** 可平仓位 */
        this.closablePosition = closablePosition;
        /** 目标仓位 */
        this.targetPosition = targetPosition;
    }
}

module.exports = {

    TaskEntrustStatus,
    T0TaskPackage,
    T0TaskInfo,
    T0EntrustInfo,
    T0PositionInfo,
};