import { GetEnv } from '../global-state';
import { HttpAssist } from './http-assit';
import { RuntimeEnvironment } from '../config/architecture';
import { CommunicationFactory } from '../communication/factory';
import { ICommunication } from '../communication/protocol';
import pako from 'pako';
import { ResponseType } from 'axios';
import { HttpResponseData } from '../types/common';

const ServerState = {
  /** 交易服务器 */
  TradeServer: { host: '', port: 0, instance: null as ICommunication | null },
  /** 行情服务器 */
  QuoteServer: { host: '', port: 0, instance: null as ICommunication | null },
};

/**
 * 设置交易服务器（全局唯一实例）
 */
async function SetTradeServer(host: string, port: number) {
  Object.assign(ServerState.TradeServer, { host, port });
  const env = GetEnv();
  ServerState.TradeServer.instance = await CommunicationFactory.CreateTradeServer(env, host, port);
}

/**
 * 获取交易服务器（全局唯一实例）
 */
function GetTradeServer() {
  const { host, port } = ServerState.TradeServer;
  return { host, port };
}

/**
 * 设置行情服务器（全局唯一实例）
 */
async function SetQuoteServer(host: string, port: number) {
  Object.assign(ServerState.QuoteServer, { host, port });
  const env = GetEnv();
  ServerState.QuoteServer.instance = await CommunicationFactory.CreateQuoteServer(env, host, port);
}

/**
 * 获取行情服务器（全局唯一实例）
 */
function GetQuoteServer() {
  const { host, port } = ServerState.QuoteServer;
  return { host, port };
}

/**
 * Socket服务器管理功能
 */
export const ServerManager = {
  SetTradeServer,
  GetTradeServer,
  SetQuoteServer,
  GetQuoteServer,
};

export class BaseRepo {
  protected binaryHeaders = {
    'Content-Type': 'application/json,charset=utf-8',
    Accept: 'application/octet-stream;charset=UTF-8',
  };
  protected binaryResponseType: ResponseType = 'arraybuffer';

  /**
   * HTTP助手
   */
  protected get assist() {
    return HttpAssist;
  }

  /**
   * 运行环境标识
   */
  protected get env() {
    return GetEnv();
  }

  /**
   * 是否纯WEB
   */
  protected get isWeb() {
    return this.env == RuntimeEnvironment.Web;
  }

  /**
   * 是否WebSocket环境
   */
  protected get isWebSocket() {
    return this.env == RuntimeEnvironment.WebSocket;
  }

  /**
   * 是否本地客户端环境
   */
  protected get isNativeSocket() {
    return this.env == RuntimeEnvironment.Native;
  }

  /**
   * 是否Socket环境
   */
  protected get isSocket() {
    return this.isWebSocket || this.isNativeSocket;
  }

  /**
   * 交易服务器（从任何Repo子类获取，均为同一服务器实例）
   */
  protected get tserver() {
    return ServerState.TradeServer.instance!;
  }

  /**
   * 行情服务器（从任何Repo子类获取，均为同一服务器实例）
   */
  protected get qserver() {
    return ServerState.QuoteServer.instance!;
  }

  constructor() {
    //
  }

  /**
   * 将二进制数据流转换成JSON格式的原始数据
   */
  translate<T>(resp: any) {
    /**
     * 将数据流转化为字符串, 兼容汉字
     * @param {Array} array
     */
    function Utf8ArrayToStr(array: Uint8Array<ArrayBuffer>) {
      var out = '',
        idx = 0,
        len = array.length,
        char1,
        char2,
        char3,
        char4;
      while (idx < len) {
        char1 = array[idx++];

        /**
         * 当单个字节时, 最大值 '01111111', 最小值 '00000000' 右移四位 07, 00
         * 当两个字节时, 最大值 '11011111', 最小值 '11000000' 右移四位 13, 12
         * 当三个字节时, 最大值 '11101111', 最小值 '11100000' 右移四位 14, 14
         */

        if (char1 >> 4 <= 7) {
          out += String.fromCharCode(char1);
        } else if (char1 >> 4 == 12 || char1 >> 4 == 13) {
          char2 = array[idx++];
          out += String.fromCharCode(((char1 & 0x1f) << 6) | (char2 & 0x3f));
        } else if (char1 >> 4 == 14) {
          char2 = array[idx++];
          char3 = array[idx++];
          char4 = ((char1 & 0x0f) << 12) | ((char2 & 0x3f) << 6);
          out += String.fromCharCode(char4 | ((char3 & 0x3f) << 0));
        }
      }

      return out;
    }

    var compressed = new Uint8Array(resp);
    var uncompressed = pako.ungzip(compressed);
    var raw = JSON.parse(Utf8ArrayToStr(uncompressed));
    return raw as T;
  }
}
