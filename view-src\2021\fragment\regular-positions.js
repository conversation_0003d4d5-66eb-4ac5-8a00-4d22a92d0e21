const { TypicalDataView } = require('../classcial/typical-data-view');
const { Position } = require('../../../model/position');
const { repoTrading } = require('../../../repository/trading');

class View extends TypicalDataView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '持仓记录');
        this.priceMap = {};
    }

    /**
     * @param {Position} record 
     */
    testRecords(record) {
        
        var kw = this.states.keywords;
        return this.tableObj.matchKeywords(record)
            || this.testPy(record.instrumentName, kw)
            || this.testPy(record.accountName, kw)
            || this.testPy(record.strategyName, kw)
            || this.testPy(record.fundName, kw);
    }

    handleContextChange(identityId) {

        if (identityId === this.identityId) {
            return;
        }

        this.identityId = identityId;
        this.tableObj.clear();

        if (this.helper.isNotNone(identityId)) {

            this.resetControls();
            this.requestRecords();
        }
    }

    async requestRecords() {

        var identityId = this.identityId;
        var resp = await repoTrading.getTodayPositions({

            fundId: this.is4Product ? identityId : null,
            strategyId: this.is4Strategy ? identityId : null,
            accountId: this.is4Account ? identityId : null,
        });

        var records = resp.data;
        var typeds = records instanceof Array ? records.map(x => new Position(x)) : [];
        var priceMap = await repoTrading.getAssetsLatestPrice();
        
        typeds.forEach(item => {

            let latest_price = priceMap[item.instrument] || 0;
            item.lastPrice = latest_price;
            item.floatProfit = (latest_price - item.avgPrice) * item.totalPosition * item.volumeMultiple * item.direction;
            item.profit = item.closeProfit + item.floatProfit;
        });

        this.tableObj.refill(typeds);
    }

    refresh() {
        
        if (this.helper.isNotNone(this.identityId)) {
            super.refresh();
        }
    }

    /**
     * @param {Array<Position>} records
     * @returns {Array<Position>}
     */
    typeRecords(records) {
        return records;
    }

    async updatePrice() {

        var map = await repoTrading.getAssetsLatestPrice();

        for (let key in map) {
            this.priceMap[key] = map[key] || 0;
        }

        const positions = this.typeRecords(this.tableObj.extractAllRecords());
        positions.forEach(pos => {
            
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let scale = pos.totalPosition * pos.direction * pos.volumeMultiple;
            let floatProfit = (lastPrice - pos.avgPrice) * scale;

            this.tableObj.updateRow({

                id: pos.id,
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
                marketValue: Math.abs(lastPrice * scale),
            });
        });
    }

    schedulePriceUpdate() {

        this.priceUpdateTimer = setInterval(async () => {
            
            if (this.isRequestingPrice) {
                return;
            }

            try {
                
                this.isRequestingPrice = true;
                await this.updatePrice();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isRequestingPrice = false;
            }

        }, 1000 * 10);
    }

    dispose() {

        super.dispose();
        clearInterval(this.priceUpdateTimer);
        delete this.priceUpdateTimer;
    }

    build($container, options) {

        super.build($container, options, {

            heightOffset: 119,
            tableName: 'smt-frp',
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
        });

        this.is4Product = !!this.voptions.is4Product;
        this.is4Strategy = !!this.voptions.is4Strategy;
        this.is4Account = !!this.voptions.is4Account;

        /** 监听上下文切换 */
        this.registerEvent('set-context-identity', this.handleContextChange.bind(this));
        this.schedulePriceUpdate();
    }
}

module.exports = View;