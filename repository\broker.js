const httpRequest = require('../libs/http').http;

class BrokerRepository {

    getAll() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/broker').then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    createBroker(broker) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/broker', broker).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    updateBroker(broker) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/broker', broker).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    removeBroker (brokerId) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/broker', { params: { broker_id: brokerId }}).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }
}

module.exports = { repoBroker: new BrokerRepository() };