<template>
  <div w-full>
    <el-select
      ref="selectRef"
      v-model="model"
      placeholder="请选择经纪商"
      filterable
      clearable
      :loading="loading"
      @change="handleChange"
    >
      <el-option
        v-for="broker in filteredBrokers"
        :key="broker.id"
        :label="`${broker.brokerName} (${broker.brokerId})`"
        :value="broker.id"
      />
      <template v-if="canCreate" #footer>
        <div p-2>
          <el-button type="primary" size="small" w-full @click="handleAddBroker">
            <i class="iconfont icon-add" mr-1></i>
            添加经纪商
          </el-button>
        </div>
      </template>
    </el-select>
    <!-- 新建经纪商对话框 -->
    <BrokerDialog
      ref="createBrokerFormRef"
      v-if="showCreateDialog"
      v-model="showCreateDialog"
      @success="handleCreateBrokerSaved"
    />
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  ref,
  shallowRef,
  watch,
  defineAsyncComponent,
  useTemplateRef,
} from 'vue';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';
import { type MomBroker, BrokerTypeEnum } from '../../../../xtrade-sdk/dist';
import { hasPermission } from '@/script';
import { MenuPermitAccountManagement } from '@/enum';

const BrokerDialog = defineAsyncComponent(() => import('../BrokerView/BrokerDialog.vue'));

interface Props {
  /** 需要排除的经纪商ID列表 */
  excludeIds?: number[];
  /** 来自哪个菜单路由，用于权限判断 */
  from?: string;
}

const { excludeIds = [], from } = defineProps<Props>();

const emit = defineEmits<{
  change: [value: number | undefined, broker?: MomBroker];
  save: [broker: MomBroker];
  refresh: [];
}>();

const model = defineModel<number | undefined>();
const selectRef = useTemplateRef('selectRef');

// 响应式数据
const loading = ref(false);
const allBrokers = shallowRef<MomBroker[]>([]);
const showCreateDialog = ref(false);

const canCreate = computed(() => {
  // 经纪商管理表单选择经纪商时，肯定有权限
  if (from == 'account') {
    return hasPermission(MenuPermitAccountManagement.创建经纪商);
  } else {
    console.warn('BrokerSelect: 未设置创建权限判断依据');
    return false;
  }
});

// 计算属性：过滤后的经纪商列表
const filteredBrokers = computed(() => {
  let brokers = allBrokers.value;

  // 排除指定的经纪商ID
  if (excludeIds.length > 0) {
    brokers = brokers.filter(b => !excludeIds.includes(b.id));
  }

  return brokers;
});

// 监听过滤条件变化，重新验证当前选择
watch(
  () => [excludeIds],
  () => {
    // 如果当前选择的经纪商不在新的过滤结果中，清空选择
    if (model.value) {
      const isCurrentBrokerValid = filteredBrokers.value.some(b => b.id === model.value);
      if (!isCurrentBrokerValid) {
        model.value = undefined;
      }
    }
  },
  { deep: true },
);

onMounted(() => {
  loadBrokers();
});

// 处理选择变化
const handleChange = (value: number | undefined) => {
  emit(
    'change',
    value,
    allBrokers.value.find(b => b.id === value),
  );
  selectRef.value?.blur();
};

// 加载经纪商列表
const loadBrokers = async () => {
  loading.value = true;
  try {
    const brokers = await AdminService.getBrokers();
    allBrokers.value = brokers || [];
  } catch (error) {
    console.error('加载经纪商列表失败:', error);
    ElMessage.error('加载经纪商列表失败');
  }
  loading.value = false;
};

// 处理添加经纪商
const handleAddBroker = () => {
  selectRef.value?.blur();
  showCreateDialog.value = true;
};

const handleCreateBrokerSaved = async (broker: MomBroker) => {
  await loadBrokers();
  model.value = broker.id;
  emit('refresh');
  selectRef.value?.blur();
};
</script>

<style scoped></style>
