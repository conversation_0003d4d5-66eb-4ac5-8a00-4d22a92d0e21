const httpRequest = require('../libs/http').http;
class OrgRepository {

    createOrg(org) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/organization', org).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    updateOrg(org) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/organization', org).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    getAll() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/organization').then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    deleteOrg(org_id) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/organization', {
                params: {
                    org_id: org_id
                }
            }).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }
}

module.exports = { repoOrg: new OrgRepository() };