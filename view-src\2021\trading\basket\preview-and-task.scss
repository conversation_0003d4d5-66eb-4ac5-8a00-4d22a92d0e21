.basket-preview-and-task {

    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 5px;
    gap: 5px;

    .tab-row {

        flex-shrink: 0;
        flex-grow: 0;
        display: flex;
        gap: 10px;

        .el-radio-button {

            box-sizing: border-box;
            .el-radio-button__inner {
                width: 100px;
            }
        }
    }

    .mother-list {

        box-sizing: border-box;
        max-height: 280px;
        flex-shrink: 0;
        flex-grow: 0;
        padding-right: 3px;
    }
    
    .child-list {
        
        box-sizing: border-box;
        flex: 1 1 100%;
        padding-right: 3px;
    }
}