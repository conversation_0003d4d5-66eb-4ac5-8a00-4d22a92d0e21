<script setup lang="tsx">
import { shallowRef, watch } from 'vue';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { ColumnDefinition } from '@/types';
import type { BasketOrderPreview } from '@/types/basket';
import { ASSET_TYPES, TradeDirectionEnum } from '@/enum/trade';
import { renderLabel } from '@/script';
import { usePreviewDataStore } from '@/stores/counter';

interface CellRenderParam {
  rowData: BasketOrderPreview;
  cellData: any;
}

function isBuy(direction: TradeDirectionEnum) {
  return direction === TradeDirectionEnum.买入;
}

function isSell(direction: TradeDirectionEnum) {
  return direction === TradeDirectionEnum.卖出;
}

const records = shallowRef<BasketOrderPreview[]>([]);
const store = usePreviewDataStore();
watch(
  () => store.previews,
  data => {
    records.value = data;
  },
);

// 定义列配置
const columns: ColumnDefinition<BasketOrderPreview> = [
  { key: 'instrumentName', title: '合约名称', width: 100, sortable: true, fixed: true },
  { key: 'instrument', title: '合约代码', width: 100, sortable: true },
  {
    key: 'assetType',
    title: '资产类型',
    width: 100,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      return <span>{renderLabel(params.cellData, ASSET_TYPES)}</span>;
    },
  },
  {
    key: 'direction',
    title: '买卖方向',
    width: 100,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      return (
        <span class={isBuy(params.cellData) ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]'}>
          {isBuy(params.cellData) ? '买入' : '卖出'}
        </span>
      );
    },
  },
  { key: 'volume', title: '委托数量', width: 100, sortable: true },
  { key: 'price', title: '委托价格', width: 100, sortable: true },
  { key: 'amount', title: '预估金额', width: 120, sortable: true },
  { key: 'ceilingPrice', title: '涨停价', width: 100, sortable: true },
  { key: 'floorPrice', title: '跌停价', width: 100, sortable: true },
  { key: 'yesterdayPosition', title: '昨持仓', width: 100, sortable: true },
  { key: 'todayPosition', title: '今持仓', width: 100, sortable: true },
  { key: 'closablePosition', title: '可平仓', width: 100, sortable: true },
];
</script>

<template>
  <div class="basket-order-preview" h-full flex flex-col>
    <VirtualizedTable :columns="columns" :data="records" fixed />
  </div>
</template>

<style scoped>
.basket-order-preview {
  height: 100%;
  padding: 10px;
}
</style>
