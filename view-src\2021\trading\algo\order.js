const IView = require('../../../../component/iview').IView;
const SmartTable = require('../../../../libs/table/smart-table').SmartTable;
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { AlgoOrder, MotherAlgoOrder } = require('../../../../model/algo-order');
const { repoAlgo } = require('../../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '算法订单');

        this.states = {

            /** 关键字 */
            keywords: null,
        };

        /** 本地设置 */
        this.settings = {

            /** 任务状态更新频率 */
            frequency: 1000 * 40,
        };

        /**
         * 批次展开与否map（key：mother_order.task_id，value： boolean）
         */
        this.openMap = {};
    }

    get orders() {
        return this.constructOrders(this.tableObj.extractAllRecords());
    }

    /**
     * @param {Array<AlgoOrder|MotherAlgoOrder>} orders 
     */
    constructOrders(orders) {
        return orders;
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} record
     */
    identity(record) {
        return record instanceof AlgoOrder ? record.id : record.taskId;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.filterOrders,
                this.hope2CancelCheckeds,
                this.hope2CancelAll,
                this.hope2Replace,
            ]),
        });
    }

    createTable() {

        this.helper.extend(this, ColumnCommonFunc);
        let $table = this.$container.querySelector('.data-list');
        let ref = new SmartTable($table, this.identity, this, {

            tableName: 'smt-tao',
            displayName: this.title,
            defaultSorting: { prop: 'sort_key', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
            rowSelected: this.handleRowSelected.bind(this),
            rowChecked: this.handleRowChecked.bind(this),
            allRowsChecked: this.handleAllRowsChecked.bind(this),
        });

        ref.hideColumns(['排序']);
        ref.setPageSize(999999);
        ref.setMaxHeight(351);
        this.tableObj = ref;
    }

    handleTableFiltered(total) {
        //
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} order 
     */
    handleRowSelected(order) {

        if (order instanceof AlgoOrder) {
            this.trigger('algo-order-selected', order);
        }
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} order 
     * @param {Boolean} checked 
     * @param {Number} totalChecked 
     */
    handleRowChecked(order, checked, totalChecked) {

        if (order instanceof MotherAlgoOrder) {
            this.qchildren(order.taskId).forEach(x => { this.tableObj.checkRow(this.identity(x), checked); });
        }
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} order 
     */
    handleAllRowsChecked(order) {

        if (order instanceof AlgoOrder) {
            this.trigger('algo-order-selected', order);
        }
    }

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.tradingDirection });
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} record
     */
    formatStatus(record) {

        if (record instanceof MotherAlgoOrder) {
            return '';
        }
        else {

            let status = { running: 0, done: 1, canceled: 2 };
            switch (record.algorithmStatus) {

                case status.running: return '进行中';
                case status.done: return '已完成';
                case status.canceled: return '已撤销';
                default: return record.algorithmStatus;
            }
        }
    }

    formatDirection2(record, direction, direction_name) {
        return record instanceof AlgoOrder ? ColumnCommonFunc.formatDirection(record, direction, direction_name) : '';
    }

    formatDirectionText2(record, direction, direction_name) {
        return record instanceof AlgoOrder ? ColumnCommonFunc.formatDirectionText(record, direction, direction_name) : '';
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} record
     */
    formatOpener(record) {

        if (record instanceof AlgoOrder) {
            return this.qmother(record.taskId) ? '<span class="child-flag"></span>' : '';
        }
        else {
            return record.isOpened ? '<div class="opener-icon" event.onclick="expand"><i class="el-icon-arrow-down"></i></div>'
                                    : '<div class="opener-icon" event.onclick="collapse"><i class="el-icon-arrow-right"></i></div>';
        }
        
    }

    isChild(target) {
        return target instanceof AlgoOrder;
    }

    isMother(target) {
        return target instanceof MotherAlgoOrder;
    }

    longer(something) {

        let str = (something || '').toString();
        while (str.length < 30) { str = '0' + str; }
        return str;
    }

    /**
     * @returns {Array<AlgoOrder>}
     */
    qchildren(taskId) {
        return this.orders.filter(x => x instanceof AlgoOrder && x.taskId == taskId);
    }

    /**
     * @returns {MotherAlgoOrder}
     */
    qmother(taskId) {
        return this.orders.find(x => x instanceof MotherAlgoOrder && x.taskId == taskId);
    }

    /**
     * @param {Boolean} isAll
     * @returns {Array<AlgoOrder>}
     */
    qcheckeds(isAll) {

        let ref = this.tableObj;
        let records = isAll ? ref.extractAllRecords() : ref.extractCheckedRecords();
        return records.filter(x => x instanceof AlgoOrder);
    }

    /**
     * @param {MotherAlgoOrder} record
     */
    expand(record) {

        this.qchildren(record.taskId).forEach(x => { this.tableObj.hideRow(this.identity(x)); });
        this.toggleMotherOpen(record);
    }

    /**
     * @param {MotherAlgoOrder} record
     */
    collapse(record) {

        this.qchildren(record.taskId).forEach(x => { this.tableObj.showRow(this.identity(x)); });
        this.toggleMotherOpen(record);
    }

    /**
     * @param {MotherAlgoOrder} record
     */
    toggleMotherOpen(record) {

        let snap = Object.assign({}, record);
        snap.isOpened = this.openMap[record.taskId] = !snap.isOpened;
        this.tableObj.updateRow(snap);
    }

    /**
     * @param {AlgoOrder|MotherAlgoOrder} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button class="danger" event.onclick="cancelSingle">撤销</button>';
    }

    filterOrders() {

        let ref = this.tableObj;
        let keywords = this.states.keywords;

        /**
         * @param {AlgoOrder|MotherAlgoOrder} record 
         */
        let testRecords = (record) => {
            return record instanceof AlgoOrder && ref.matchKeywords(record)
                    || record instanceof MotherAlgoOrder && this.qchildren(record.taskId).some(x => ref.matchKeywords(x));
        }

        ref.setPageIndex(1, false);
        ref.setKeywords(keywords, false);
        ref.customFilter(testRecords);
    }

    hope2CancelCheckeds() {

        if (this.tableObj.rowCount == 0) {
            return this.interaction.showMessage('当前无订单');
        }

        var checkeds = this.qcheckeds();
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要撤销的订单');
        }

        var progressings = checkeds.filter(item => !item.isCompleted);
        if (progressings.length == 0) {
            return this.interaction.showError(`勾选订单 = ${checkeds.length}，可撤销订单 = 0`);
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `勾选订单 = ${checkeds.length}，可撤销订单 = ${progressings.length}，是否确定？`,
            confirmed: () => {
                this.cancel(progressings.map(x => x.id));
            },
        });
    }

    /**
     * 单一撤销
     * @param {AlgoOrder|MotherAlgoOrder} order 
     */
    cancelSingle(order) {

        if (order instanceof AlgoOrder) {

            this.interaction.showConfirm({

                title: '撤单确认',
                message: `是否撤销该算法单？`,
                confirmed: () => {
                    this.cancel([order.id]);
                },
            });
        }
        else {
            
            let children = this.qchildren(order.taskId);
            if (children.length == 0) {
                return this.interaction.showMessage('该算法批次下，已无订单');
            }

            this.interaction.showConfirm({

                title: '撤单确认',
                message: `是否撤销该算法母单（按批次，数量 = ${children.length}）？`,
                confirmed: () => {
                    this.cancel([order.taskId]);
                },
            });
        }
    }

    /**
     * 撤销
     * @param {Array} order_ids 
     */
    async cancel(order_ids) {

        var resp = await repoAlgo.cancelOrder(order_ids);
        if (resp.errorCode != 0) {
            return this.interaction.showError(`撤单请求处理错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        setTimeout(() => { this.requestAlgoOrders(); }, 1000 * 2);
        this.interaction.showSuccess(`撤销请求已发出，数量 = ${order_ids.length}`);
    }

    hope2CancelAll() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无订单');
            return;
        }

        var all = this.qcheckeds(true);
        var progressings = all.filter(item => !item.isCompleted);
        if (progressings.length == 0) {

            this.interaction.showError(`所有订单 = ${all.length}，可撤销订单 = 0`);
            return;
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `所有订单 = ${all.length}，可撤销订单 = ${progressings.length}，是否确定？`,
            confirmed: () => {
                this.cancel(progressings.map(x => x.id));
            },
        });
    }

    hope2Replace() {
        
        let ref = this.tableObj;

        if (ref.rowCount == 0) {
            return this.interaction.showMessage('当前无订单');
        }
        else if (ref.filteredRowCount == 0) {
            return this.interaction.showMessage('筛选结果无订单');
        }

        var checkeds = ref.extractCheckedRecords();
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要追单的订单');
        }

        var cancellables = checkeds.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {
            return this.interaction.showError(`勾选订单 = ${checkeds.length}，可（撤）追单数 = 0`);
        }

        this.replaceTask(cancellables);
    }

    /**
     * 追单
     * @param {Array<AlgoOrder>} orders
     */
    replaceTask(orders) {
        
        if (this.dialogReplacing === undefined) {
            
            var DialogReplacing = require('./replacing');
            var dialog = new DialogReplacing('@2021/trading/algo/replacing');
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', orders); });
            this.dialogReplacing = dialog;
        }
        else {
            this.dialogReplacing.trigger('showup', orders);
        }
    }

    refresh() {

        this.interaction.showSuccess('算法母单：刷新请求已发送');
        this.requestAlgoOrders();
    }

    /**
     * @returns {AlgoOrder}
     */
    typeds(record) {
        return record;
    }

    /**
     * @returns {Array<AlgoOrder>}
     */
    typedsAll(records) {
        return records;
    }

    async requestAlgoOrders(is_auto_update) {

        let resp = await repoAlgo.getOrderList();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`算法单加载失败：${resp.errorCode}/${resp.errorMsg}`);
        }

        let records = resp.data;
        let orders = records instanceof Array ? records.map(item => new AlgoOrder(item)) : [];
        let default_name = 'absent-group';
        let map = orders.groupBy(x => this.typeds(x).taskId || default_name);
        let mothers = [];
        
        for (let task_id in map) {
            
            if (task_id == default_name) {
                continue;
            }

            let members = this.typedsAll(map[task_id]);
            if (members.length < 2) {
                continue;
            }

            let mother = new MotherAlgoOrder({

                taskId: task_id,
                strategyId: members[0].strategyId,
                strategyName: members[0].strategyName,
                orderedVolume: members.sum(x => this.typeds(x).orderedVolume),
                tradedVolume: members.sum(x => this.typeds(x).tradedVolume),
                volume: members.sum(x => this.typeds(x).volume),
            });

            mother.isCompleted = members.filter(x => !x.isCompleted).length == 0;
            mother.isOpened = !!this.openMap[mother.taskId];
            mothers.push(mother);
        }

        // 合并批次母单
        orders.merge(mothers);

        // // 进行排序

        // orders.sort((a, b) => {

        //     let va = a.taskId + (this.isMother(a) ? '9' : '1' + a.id);
        //     let vb = b.taskId + (this.isMother(b) ? '9' : '1' + b.id);
        //     return this.helper.compare(va, vb, false);
        // });

        // 新建排序列，利用表格本身的排序功能

        orders.forEach(each => {

            if (this.isMother(each)) {
                each.sort_key = each.taskId + '9';
            }
            else {
                each.sort_key = each.taskId + '1' + each.id;
            }
        });

        // console.log(orders.map(x => ({ id: x.id, taskId: x.taskId, taskName: x.taskName })));

        if (is_auto_update) {
            orders.forEach(item => this.tableObj.putRow(item));
        }
        else {
            this.tableObj.refill(orders);
        }

        // 恢复最近一次母单的展开、隐藏情况
        mothers.forEach(each => {

            if (each.isOpened == false) {
                this.qchildren(each.taskId).forEach(x => this.tableObj.hideRow(this.identity(x)));
            }
        });
    }

    listen2Events() {
        this.registerEvent('reload-algo-orders', () => { this.requestAlgoOrders(); });
    }

    keepRefreshed() {

        this.refreshJob = setInterval(async () => {
            
            if (this._isJobRunning) {
                return;
            }

            this._isJobRunning = true;
            await this.requestAlgoOrders(true);
            this._isJobRunning = false;

        }, this.settings.frequency);
    }

    build($container) {
        
        window.xyz = this;
        super.build($container);
        this.createToolbarApp();
        this.createTable();
        this.requestAlgoOrders();
        this.listen2Events();
        this.keepRefreshed();
    }
}

module.exports = View;