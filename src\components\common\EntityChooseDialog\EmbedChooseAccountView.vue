<script setup lang="ts">
import VirtualizedTable from '../../common/VirtualizedTable.vue';
import { computed, ref, watch } from 'vue';
import type { ColumnDefinition, AccountInfo, RowAction } from '@/types';
import { remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';
import { ElMessage } from 'element-plus';

import {
  Repos,
  TradeClassificationType,
  type TradeClassificationMember,
} from '../../../../../xtrade-sdk/dist';

const { classType, classId, className, least } = defineProps<{
  classType: TradeClassificationType;
  classId: number | null;
  className: string | null;
  // 至少选择多少个
  least?: number;
}>();

const repoInstance = new Repos.ClassificationRepo();
const repoGovenInstance = new Repos.GovernanceRepo();

watch([() => classType, () => classId], () => {
  handleContextChange();
});

const columns: ColumnDefinition<AccountInfo> = [
  { key: 'accountId', title: '账号ID', width: 150, minWidth: 150, sortable: true },
  { key: 'accountName', title: '账号名称', width: 300, minWidth: 300, sortable: true },
  // { key: 'brokerName', title: '经纪商名称', width: 100, minWidth: 100, sortable: true },
  { key: 'orgName', title: '机构名称', width: 300, minWidth: 300, sortable: true },
];

const rowActions: RowAction<AccountInfo>[] = [
  {
    label: '移除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const containedFundsAsMembers = ref<TradeClassificationMember[]>([]);
const allAccounts = ref<AccountInfo[]>([]);
const checkedAccounts = ref<AccountInfo[]>([]);
const selectedAccountId = ref<string>('');

const availables = computed(() => {
  return allAccounts.value.filter(x => !checkedAccounts.value.some(y => y.id == x.id));
});

// 删除分类成员
async function deleteRow(row: AccountInfo) {
  const result = await deleteConfirm('从账号组移除确认', `是否移除账号： ${row.accountName}？`);
  if (result !== true) {
    return;
  }

  const matched = containedFundsAsMembers.value.find(x => x.memberCode == row.id)!;
  const resp = await repoInstance.deleteTradeClassificationMember(matched.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已移除');
    remove(containedFundsAsMembers.value, x => x.memberCode == row.id);
    remove(checkedAccounts.value, x => x === row);
  } else {
    ElMessage.error(`移除失败：${errorCode}/${errorMsg}`);
  }
}

function add2Table() {
  const matched = checkedAccounts.value.find(x => x.id == selectedAccountId.value);
  if (!matched) {
    const matched2 = allAccounts.value.find(x => x.id == selectedAccountId.value);
    if (matched2) {
      add2Group(matched2);
    }
  }

  // reset to empty
  selectedAccountId.value = '';
}

async function add2Group(row: AccountInfo) {
  const member: TradeClassificationMember = {
    id: null as any,
    classificationId: classId!,
    memberCode: row.id,
    memberName: row.accountName,
    memberType: TradeClassificationType.ProductGroup,
    sortOrder: checkedAccounts.value.length + 2,
  };

  const resp = await repoInstance.createTradeClassificationMember(classId!, member);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success(`已保存`);
    await request();
    await requestAccounts();
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
}

async function handleContextChange() {
  console.log('account/input props changed', { classType, classId, className, least });
  if (!classId || classType != TradeClassificationType.AccountGroup) {
    containedFundsAsMembers.value = [];
    return;
  }
  await request();
  await requestAccounts();
}

async function request() {
  const list = (await repoInstance.getTradeClassificationMembers(classId!)).data || [];
  containedFundsAsMembers.value = list;
}

async function requestAccounts() {
  const list = (await repoGovenInstance.QueryAccounts()).data || [];
  allAccounts.value = list;
  const targetIds = containedFundsAsMembers.value.map(x => x.memberCode);
  checkedAccounts.value = list.filter(x => targetIds.some(id => id == x.id));
}

function getSelectedRows() {
  return checkedAccounts.value;
}

defineExpose({
  getSelectedRows,
});
</script>

<template>
  <div class="choose-panel" h-full of-y-hidden>
    <VirtualizedTable
      identity="id"
      style="height: 100%"
      :columns="columns"
      :data="checkedAccounts"
      :row-actions="rowActions"
      :row-action-width="80"
      :enable-search="false"
      select
      fixed
    >
      <template #actions>
        <div class="waiting-list" flex aic gap-10>
          <el-select
            v-model="selectedAccountId"
            @change="add2Table"
            placeholder="请选择要添加的账号"
            style="width: 200px"
            filterable
          >
            <el-option
              v-for="item in availables"
              :key="item.id"
              :label="item.accountName"
              :value="item.id"
            ></el-option>
          </el-select>
          <span class="c-[var(--g-text-color-1)]">剩余可选数量 = {{ availables.length }}</span>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
