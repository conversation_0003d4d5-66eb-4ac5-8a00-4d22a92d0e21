<script lang="ts" setup>
import SingleRuleRow from './SingleRuleRow.vue';
import CommonSharedIndicator from '../IndicatorConfig/CommonSharedIndicator.vue';
import { onMounted, ref, watch } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElMessage, ElTree } from 'element-plus';
import { buildTree, isLeafNode, type LeafNode, type Level1Node } from '../IndicatorTreeNodeLogic';
import type { ContextualIndicatorInfo, RiskRuleExt } from '@/types/riskc';
import { chooseConfirm, deleteConfirm } from '@/script/interaction';
import { deepClone, formatDateTime, isNone } from '@/script';
import { createEmptyRule, DefaultRiskParamCreator } from '../DefaultRiskParamCreator';

import {
  Repos,
  type EntityRiskTemplateInfo,
  type RiskIndicator,
  type RiskRule,
} from '../../../../../xtrade-sdk/dist';

const { identityId, identityType } = defineProps<{
  identityId: number | string;
  identityName: string;
  identityType: number;
}>();

// 添加上下文主体ID监听
watch(
  () => identityId,
  () => {
    request();
  },
);

const repoInstance = new Repos.RiskControlRepo();
const $treeRef = ref<TreeInstance>();
const treeNodes = ref<Level1Node[]>();
// 规则ID与其对应的VUE组件名称索引
const ruleId2ComponentNameMap = ref<{ [id: number]: string }>({});
// 当前主体（产品或账号）绑定的风控模板
const riskTemplates = ref<EntityRiskTemplateInfo[]>([]);
// 上层节点包含叶子节点的ID列表（排除叶子节点外的所有节点）
const parent2LeafNodeIdsMap = ref<{ [id: number]: number[] }>({});
// 叶子节点对应的规则列表
const leaf2RulesMap = ref<{ [id: number]: RiskRuleExt[] }>({});
// 选中的规则ID
const focusedRuleId = ref<number | null>(null);
// 当前选中的规则
const selectedRule = ref<RiskRule | null>(null);
// 控制对话框显示
const dialogVisible = ref(false);
// 当前选中的指标信息
const selectedIndicator = ref<ContextualIndicatorInfo | null>(null);

const emitter = defineEmits<{
  dialog: [visible: boolean];
}>();

function doesParentContainSomeRules(level_id: number) {
  const leafIds = parent2LeafNodeIdsMap.value[level_id];
  if (!Array.isArray(leafIds) || leafIds.length === 0) {
    return false;
  }

  return leafIds.some(id => {
    const rules = leaf2RulesMap.value[id];
    return Array.isArray(rules) && rules.length > 0;
  });
}

function constructMaps(indicators: RiskIndicator[]) {
  // 初始化每个指标对应的组件名称
  const map0 = {} as any;
  indicators.forEach(item => {
    map0[item.id] = item.clientName;
  });

  ruleId2ComponentNameMap.value = map0;

  // 初始化上层节点包含叶子节点的ID列表
  const map1 = {} as any;
  indicators.forEach(item => {
    const { id, firstLevelCode: f, secondLevelCode: s } = item;
    (map1[f] || (map1[f] = [])).push(id);
    (map1[s] || (map1[s] = [])).push(id);
  });

  parent2LeafNodeIdsMap.value = map1;

  // 初始化每个指标对应规则列表
  const map2 = {} as any;
  indicators.forEach(item => {
    map2[item.id] = [];
  });

  leaf2RulesMap.value = map2;
}

async function requestIdcTree() {
  const indicators = (await repoInstance.QueryIndicators()).data || [];
  treeNodes.value = buildTree(indicators);
  constructMaps(indicators);
}

async function requestAttachedTmpls() {
  const tmpl_list = (await repoInstance.QueryIdentityTemplates(identityId)).data || [];
  riskTemplates.value = tmpl_list;
  tmpl_list.forEach(tmpl => {
    tmpl.ruleList.forEach(rule => {
      leaf2RulesMap.value[rule.indicatorId].push({ ...rule, templateName: tmpl.templateName });
    });
  });
}

async function request() {
  if (!identityId) {
    const map = leaf2RulesMap.value;
    for (const key in map) {
      map[key] = [];
    }
    return;
  }

  await requestIdcTree();
  await requestAttachedTmpls();
}

/**
 * 处理规则行点击
 */
function handleFocused(rule: RiskRule) {
  focusedRuleId.value = rule.id;
  selectedRule.value = deepClone(rule);
}

/**
 * 直接在实体上添加风控规则
 */
function handleAddRule(leaf: LeafNode) {
  const { indicator } = leaf;
  const { id, indicatorName, clientName } = indicator;
  const rule = createEmptyRule(clientName, 0, id, indicatorName);
  const riskParam = DefaultRiskParamCreator[clientName]();
  const dateObj = new Date();

  rule.configuration.riskParam = riskParam;
  selectedRule.value = rule;
  // 构造指标信息对象
  selectedIndicator.value = {
    indicatorId: id,
    indicatorName: `${indicatorName}_${formatDateTime(dateObj, 'yyMMdd')}_${formatDateTime(dateObj, 'hhmmss')}`,
    componentName: clientName,
  };

  dialogVisible.value = true;
  emitter('dialog', true);
}

/**
 * 编辑一条风控规则（该条规则来源可能： 1. 风控模板； 2. 实体上的规则）
 */
function handleEditRule(rule: RiskRule) {
  selectedRule.value = deepClone(rule);
  // 构造指标信息对象
  selectedIndicator.value = {
    indicatorId: rule.indicatorId,
    indicatorName: '', // 这里需要根据实际指标信息填充
    componentName: ruleId2ComponentNameMap.value[rule.indicatorId] || '',
  };
  dialogVisible.value = true;
  emitter('dialog', true);
}

function isNewRule(rule: RiskRule) {
  return isNone(rule.id) || rule.id == 0;
}

function isIdentityRule(rule: RiskRule) {
  return rule.templateId == 0;
}

function isTmplRule(rule: RiskRule) {
  return rule.templateId > 0;
}

/**
 * 删除实体上的规则
 */
async function handleDeleteEntityRule(rule: RiskRule) {
  const result = await deleteConfirm('删除独立规则', `确定要删除独立规则 "${rule.ruleName}" 吗？`);
  if (!result) {
    return;
  }

  const resp = await repoInstance.DeleteRule(rule.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('独立规则，删除成功');
    request();
  } else {
    ElMessage.error(`独立规则，删除失败：${errorCode}/${errorMsg}`);
  }
}

/**
 * 解绑位于某个模板上的某条规则
 */
async function handleUnbindRule(rule: RiskRule) {
  const { result, value: newTmplName } = await ask4Name();
  if (result != 'confirm') {
    ElMessage.error(`您已放弃解绑规则`);
    return;
  }

  // 解绑 & 克隆 & 重新绑定
  const resp = await repoInstance.UnbindRuleAndReconnect2Entity(
    rule.templateId,
    newTmplName!,
    String(rule.id),
    Number(identityId),
  );
  const { errorCode, errorMsg } = resp;
  if (errorCode == 0) {
    ElMessage.success('规则解绑，保存成功');
    request();
  } else {
    ElMessage.error(`规则解绑，保存失败：${errorCode}/${errorMsg}`);
  }
}

/**
 * 来自于指标配置对话框的提交回调
 */
async function handleRuleSubmit(rule: RiskRule) {
  // 关闭指标配置对话框
  handleCloseDialog();

  if (isTmplRule(rule)) {
    // 来自模板的风控规则，仅有修改，没有创建（创建在模板管理）
    updateTmplRule(rule);
    return;
  }

  if (isIdentityRule(rule)) {
    // 来自当前主体的风控规则，可能为创建，或者修改
    saveEntityRule(rule);
    return;
  }
}

/**
 * 更新来自于模板上的规则（只有修改，没有创建）
 */
async function updateTmplRule(rule: RiskRule) {
  const result = await ask4RewriteChoice();
  const isRewriteOriginal = result == 'confirm';
  const isUnbindAndRebind = result == 'cancel';

  if (isRewriteOriginal) {
    // 直接重写原来的规则
    const resp = await repoInstance.UpdateRule(rule);
    const { errorCode, errorMsg } = resp;

    if (errorCode == 0) {
      ElMessage.success('规则重写，保存成功');
      request();
    } else {
      ElMessage.error(`规则重写，保存失败：${errorCode}/${errorMsg}`);
    }
    return;
  }

  if (isUnbindAndRebind) {
    const { result, value: newTmplName } = await ask4Name();
    if (result != 'confirm') {
      ElMessage.error(`您已放弃解绑重建`);
      return;
    }

    // 解绑 & 克隆 & 重新绑定
    const resp = await repoInstance.SaveRuleAsNewAndReBindTemplate(
      rule.templateId,
      newTmplName!,
      Number(identityId),
      rule,
    );
    const { errorCode, errorMsg } = resp;
    if (errorCode == 0) {
      ElMessage.success('规则解绑重建，保存成功');
      request();
    } else {
      ElMessage.error(`规则解绑重建，保存失败：${errorCode}/${errorMsg}`);
    }
    return;
  }

  ElMessage.info('您已放弃修改');
}

/**
 * 询问模板规则保存选项
 */
async function ask4RewriteChoice() {
  const confirmButtonText = '更新此模版，同时更新关联产品的风控设置';
  const cancelButtonText = '解绑模版，只更新当前产品';
  const title = '修改风控规则确认';
  const message = `此模板绑定了其他产品，如仅修改当前产品，请点击 “${cancelButtonText}” ？`;
  const { result } = await chooseConfirm(title, message, { confirmButtonText, cancelButtonText });
  return result;
}

/**
 * 录入模板名称
 */
async function ask4Name(name?: string) {
  const confirmButtonText = '使用该名称创建新模板';
  const cancelButtonText = '取消操作';
  const title = '请为新增的模板命名';
  const message = `请输入名称`;
  const { result, value } = await chooseConfirm(title, message, {
    confirmButtonText,
    cancelButtonText,
    showInput: true,
    inputPlaceholder: '请输入名称',
    inputValue: name || '模板名称_' + Date.now(),
    inputPattern: /^[\\u4e00-\\u9fa5a-zA-Z0-9_.-]+$/,
    inputErrorMessage: '请输入正确的名称（可包含：中文、英文、数字、下划线、中划线、点）',
  });

  return { result, value };
}

/**
 * 保存实体上的规则（包含创建、修改）
 */
function saveEntityRule(rule: RiskRule) {
  if (isNewRule(rule)) {
    createEntityOnRule(rule);
  } else {
    updateEntityOnRule(rule);
  }
}

/**
 * 在实体上创建规则
 */
async function createEntityOnRule(rule: RiskRule) {
  const resp = await repoInstance.CreateRuleOnEntity(Number(identityId), identityType, rule);
  const { errorCode, errorMsg } = resp;
  if (errorCode == 0) {
    ElMessage.success('独立规则，创建成功');
    request();
  } else {
    ElMessage.error(`独立规则，创建失败：${errorCode}/${errorMsg}`);
  }
}

/**
 * 修改位于实体上的规则
 */
async function updateEntityOnRule(rule: RiskRule) {
  const resp = await repoInstance.UpdateRule(rule);
  const { errorCode, errorMsg } = resp;
  if (errorCode == 0) {
    ElMessage.success('独立规则，修改成功');
    request();
  } else {
    ElMessage.error(`独立规则，修改失败：${errorCode}/${errorMsg}`);
  }
}

// 关闭对话框
function handleCloseDialog() {
  dialogVisible.value = false;
  selectedRule.value = null;
  selectedIndicator.value = null;
  emitter('dialog', false);
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="tree-control" flex flex-col h-500 p-10>
    <!-- 标签栏 -->
    <div class="tree-control-header" w-full h-32 flex gap-10>
      <div w-200 pl-15>指标名称</div>
      <div w-200 pl-15>来源</div>
      <div w-250>控制环节</div>
      <div w-190>执行日期区间</div>
      <div w-160>执行时间区间</div>
      <div w-100>操作</div>
    </div>
    <!-- 树形结构 -->
    <div class="tree-control-body" w-full h-500 flex-1 of-y-auto>
      <el-tree
        ref="$treeRef"
        empty-text="无指标数据"
        node-key="id"
        :props="{ label: 'name', children: 'children' }"
        :data="treeNodes"
        :show-checkbox="false"
        highlight-current
        default-expand-all
      >
        <template #default="{ data }">
          <div
            v-if="isLeafNode(data)"
            class="leaf-node"
            :class="{ is_invisible: leaf2RulesMap[data.id].length == 0 }"
            w-full
            of-x-hidden
          >
            <div class="leaf-row" w-full flex jcsb aic pr-13>
              <span class="leaf-name">{{ data.name }}</span>
              <el-link @click="handleAddRule(data)" underline>添加</el-link>
            </div>
            <template v-if="leaf2RulesMap[data.id].length > 0">
              <div class="idc-rule-box">
                <SingleRuleRow
                  :rules="leaf2RulesMap[data.id]"
                  :focused-rule-id="focusedRuleId"
                  :component-map="ruleId2ComponentNameMap"
                  @focused="handleFocused"
                  @edit="handleEditRule"
                  @delete="handleDeleteEntityRule"
                  @unbind="handleUnbindRule"
                ></SingleRuleRow>
              </div>
            </template>
          </div>
          <div
            v-else
            class="parent-node"
            :class="{ is_invisible: !doesParentContainSomeRules(data.id) }"
          >
            {{ data.name }}
          </div>
        </template>
      </el-tree>
    </div>
  </div>

  <!-- 风控规则修改对话框 -->
  <el-dialog v-model="dialogVisible" title="风控指标配置" width="1300px" @close="handleCloseDialog">
    <CommonSharedIndicator
      v-if="selectedRule && selectedIndicator"
      :context-rule="selectedRule"
      :context-indicator="selectedIndicator"
      :is-vendor="true"
      @submit="handleRuleSubmit"
      append-to-body
    />
  </el-dialog>
</template>

<style scoped>
.tree-control {
  .tree-control-header {
    > div {
      text-align: center;
      flex-shrink: 1;
      flex-grow: 1;
    }
  }
  :deep() {
    .el-tree-node__content {
      height: unset !important;
      cursor: default !important;
    }

    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: unset !important;
      .leaf-row {
        background-color: var(--g-text-color-2);
      }
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: unset !important;
        }
      }
    }

    .el-tree-node__content {
      display: flex;
      align-items: center;
    }
  }
}
</style>
