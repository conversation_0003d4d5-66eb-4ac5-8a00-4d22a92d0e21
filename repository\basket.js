const http = require('../libs/http').http;

class BasketRepository {
    
    constructor() {

        this.baseUrl = {

            basket: '/basket',
            account: '/accountgroup',
            algorithm: '/algorithm',
            fixedAbs: 'http://182.150.112.115:10350/v1',
        };
    }

    getBasket() {
        return new Promise((resolve, reject) => {
            http(this.baseUrl.basket).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getEtfBasket() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/etf`).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getBasketDetail(basket_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/detail`, { params: { basket_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    // getEtfBasketDetail(etf_code) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.baseUrl.basket}/etf/members`, { params: { etf_code }}).then(
    //             resp => { resolve(resp.data); },
    //             error => { reject(error); },
    //         );
    //     });
    // }

    saveBasket(data) {
        return new Promise((resolve, reject) => {
            http(this.baseUrl.basket, { method: 'post', data }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    updateBasket(data) {
        return new Promise((resolve, reject) => {
            http(this.baseUrl.basket, { method: 'put', data }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    deleteBasket(basket_id) {
        return new Promise((resolve, reject) => {
            http(this.baseUrl.basket, { method: 'delete', params: { basket_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getTaskList(executor_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/task`, { params: { executor_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
    
    getBasketTask() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/task/total`, {}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getBasketUserTask() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/task/user`, {}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getTaskOrder(task_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/task/order`, { params: { task_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getTaskTrade(task_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/task/trade`, { params: { task_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getTaskDetail({ task_id, fund_id, account_id, strategy_id }) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/task/detail`, { params: { task_id, fund_id, account_id, strategy_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getAlgorithmTask(user_name) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/executor`, { params: { user_name }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getAccountGroup() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.account}`).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    saveAccountGroup(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.account}`, { method: 'post', data }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    calculateAlorithm(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.algorithm}`, { method: 'post', data }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    executor(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/executor`, { method: 'post', data  }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    changeExecutor(executor_id, operation) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/executor`, { method: 'put', data: { executor_id, operation }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    updateAccountGroup(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.account}`, { method: 'put', data }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    deleteAccountGroup(group_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.account}`, { method: 'delete', params: { group_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    previewMothers(queryData) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/order/preview`, { method: 'post', data: queryData }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    previewChilds(task_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/order/detail/preview`, { method: 'get', params: { task_id } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    queryMothers() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.basket}/trade/task`, { method: 'get' }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    queryChildTasks(task_id) {

        return new Promise((resolve, reject) => {

            http(`${this.baseUrl.basket}/trade/task/detail`, { method: 'get', params: { task_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    getAlgorithmTaskOrder(batch_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/batch/order`, { params: { batch_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    changeBatch(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/batch`, { method: 'post', data }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getOrderList(stock_code, executor_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl.fixedAbs}/order`, { params: { stock_code, executor_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
}

module.exports = new BasketRepository();
