<template>
  <div w-full>
    <el-select
      ref="selectRef"
      v-model="model"
      placeholder="请选择账号"
      filterable
      clearable
      :loading="loading"
      @change="handleChange"
    >
      <el-option
        v-for="account in filteredAccounts"
        :key="account.accountId"
        :label="`${account.accountName} (${account.financeAccountName})`"
        :value="account.accountId"
      />
      <template v-if="canCreate" #footer>
        <div p-2>
          <el-button type="primary" size="small" w-full @click="handleAddAccount">
            <i class="iconfont icon-add" mr-1></i>
            添加账号
          </el-button>
        </div>
      </template>
    </el-select>
    <!-- 新建账号对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建账号"
      width="800px"
      draggable
      class="typical-dialog"
      destroy-on-close
      append-to-body
      top="10vh"
    >
      <AccountBasicInfoForm
        ref="basicForm"
        :context-product="contextProduct"
        v-if="showCreateDialog"
        @save="handleCreateAccountSaved"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  shallowRef,
  watch,
  defineAsyncComponent,
  useTemplateRef,
  onMounted,
} from 'vue';
import { ElMessage } from 'element-plus';
import { Repos, type SimpleAccountInfo, type LegacyAccountInfo } from '../../../../xtrade-sdk/dist';
import { getUser, hasPermission, sleep } from '@/script';
import { MenuPermitProductManagement, FundTypeEnum } from '@/enum';
import type { ProductInfo } from '@/types';

const AccountBasicInfoForm = defineAsyncComponent(
  () => import('../AccountView/AccountBasicInfoForm.vue'),
);

interface Props {
  /** 机构ID，用于按机构过滤账号列表 */
  orgId?: number;
  /** 需要排除的账号ID列表 */
  excludeIds?: string[];
  /** 只显示未绑定的账号 */
  useUnboundOnly?: boolean;
  /** 来自哪个菜单路由，用于权限判断 */
  from?: string;
  /** 是否按用户所属机构过滤账号列表 */
  filterByUserOrg?: boolean;
  /** 关联的产品，用于账号创建时回填 */
  contextProduct?: ProductInfo | null;
}

const {
  excludeIds = [],
  useUnboundOnly = false,
  orgId,
  filterByUserOrg = false,
  from,
  contextProduct,
} = defineProps<Props>();

const emit = defineEmits<{
  change: [value: string | undefined];
}>();

const model = defineModel<string | undefined>();
const selectRef = useTemplateRef('selectRef');
const basicForm = useTemplateRef('basicForm');

// 响应式数据
const loading = ref(false);
const allAccounts = shallowRef<Array<SimpleAccountInfo & { orgId: number }>>([]);
const showCreateDialog = ref(false);

// 仓库实例
const governanceRepo = new Repos.GovernanceRepo();
const currentUser = getUser()!;

const canCreate = computed(() => {
  if (from == 'product') {
    return hasPermission(MenuPermitProductManagement.创建账号);
  } else {
    console.warn('AccountSelect: 未设置创建权限判断依据');
    return false;
  }
});

// 计算属性：过滤后的账号列表
const filteredAccounts = computed(() => {
  let accounts = allAccounts.value;

  // 按机构过滤
  if (orgId) {
    accounts = accounts.filter(a => a.orgId === orgId);
  } else if (filterByUserOrg) {
    accounts = accounts.filter(a => a.orgId === currentUser.orgId);
  }

  // 排除指定的账号ID
  if (excludeIds.length > 0) {
    accounts = accounts.filter(a => !excludeIds.includes(a.accountId));
  }

  return accounts;
});

// 监听orgId变化，清空当前选择
watch(
  () => orgId,
  () => {
    if (model.value) {
      const isCurrentAccountValid = filteredAccounts.value.some(a => a.accountId === model.value);
      if (!isCurrentAccountValid) {
        model.value = undefined;
      }
    }
    loadAccounts();
  },
);

// 监听contextProduct变化，重新加载账号列表
watch(
  () => [contextProduct?.fundType, contextProduct?.identity],
  () => {
    if (orgId) {
      loadAccounts();
    }
  },
  { deep: true },
);

onMounted(() => {
  loadAccounts();
});

// 处理选择变化
const handleChange = (value: string | undefined) => {
  emit('change', value);
};

// 加载账号列表
const loadAccounts = async () => {
  if (!orgId) return;

  loading.value = true;

  // 如果是Mom子基金且有关联产品，则获取关联产品下的已绑定账号
  if (contextProduct?.fundType === FundTypeEnum.MOM子基金 && contextProduct?.identity) {
    try {
      // 首先获取所有产品信息
      const {
        errorCode: productsErrorCode,
        errorMsg: productsErrorMsg,
        data: allProducts,
      } = await governanceRepo.QueryProducts();

      if (productsErrorCode === 0 && Array.isArray(allProducts)) {
        // 找到关联产品
        const relatedProduct = allProducts.find(product => product.id === contextProduct.identity);

        if (relatedProduct && relatedProduct.accounts && Array.isArray(relatedProduct.accounts)) {
          // 获取关联产品的已绑定账号
          allAccounts.value = relatedProduct.accounts.map(
            account =>
              ({
                accountId: account.accountId,
                accountName: account.accountName,
                financeAccountName: account.financeAccountName,
                assetType: account.assetType,
                orgId,
              }) as SimpleAccountInfo & { orgId: number },
          );
        } else {
          allAccounts.value = [];
        }
      } else {
        ElMessage.error(productsErrorMsg || '获取产品列表失败');
        allAccounts.value = [];
      }
    } catch (error) {
      console.error('获取关联产品账号失败:', error);
      ElMessage.error('获取关联产品账号失败');
      allAccounts.value = [];
    }
  } else if (useUnboundOnly) {
    // 使用QueryUnboundAccounts接口获取未绑定的账号
    const { errorCode, errorMsg, data } = await governanceRepo.QueryUnboundAccounts(orgId);
    if (errorCode === 0 && Array.isArray(data)) {
      allAccounts.value = data.map(account => ({
        ...account,
        orgId: orgId,
      }));
    } else {
      ElMessage.error(errorMsg || '加载账号列表失败');
    }
  } else {
    // 使用QueryAccounts接口获取所有账号，然后按机构过滤
    const { errorCode, errorMsg, data } = await governanceRepo.QueryAccounts();
    if (errorCode === 0) {
      allAccounts.value = (data || [])
        .filter(account => account.orgId === orgId)
        .map(account => ({
          accountId: account.id,
          accountName: account.accountName,
          financeAccountName: account.financeAccount,
          assetType: account.assetType,
          orgId,
        }));
    } else {
      ElMessage.error(errorMsg || '加载账号列表失败');
    }
  }
  loading.value = false;
};

// 处理添加账号
const handleAddAccount = async () => {
  selectRef.value?.blur();
  showCreateDialog.value = true;
  await sleep(100);
  basicForm.value?.reset(null);
};

const handleCreateAccountSaved = async (account: LegacyAccountInfo) => {
  showCreateDialog.value = false;
  await loadAccounts();
  model.value = String(account.id);
  emit('change', String(account.id));
  selectRef.value?.blur();
};

defineExpose({
  loadAccounts,
});
</script>

<style scoped></style>
