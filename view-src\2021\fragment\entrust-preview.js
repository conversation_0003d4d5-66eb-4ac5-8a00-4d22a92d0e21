const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const { WeightedAccountDetail, OrderPreview, BasketOrderPreview } = require('../model/account');
const { TradeChannel, InstrumentInfo } = require('../model/message');
const { repoTrading } = require('../../../repository/trading');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '订单预览');

        this.states = {
            keywords: null,
        };

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: 50,
            layout: paging.layout,
            total: 0,
            page: 0,
        };
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.filterRecords]),
        });
    }

    createFooterRowApp() {
        
        new Vue({

            el: this.$container.querySelector('.user-footer'),
            data: {
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    filterRecords() {
        this.tableObj.setKeywords(this.states.keywords);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize, true);
        this.tableObj.setPageIndex(1);
    }

    /**
     * @param {OrderPreview} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    createTable() {

        this.helper.extend(this, ColumnCommonFunc);
        const $table = this.$container.querySelector('.data-list');
        const tableObj = this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-fep',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        const basketColumns = ['预估金额'];
        const batchColumns = ['委托价', '委托金额', '每手保证金', '占用保证金', '已用保证金', '最大可开', '备注'];

        if (this.isBasket) {
            tableObj.hideColumns(batchColumns);
        }
        else if (this.isBatch) {
            tableObj.hideColumns(basketColumns);
        }

        tableObj.setPageSize(999999999);
    }

    /**
     * @param {OrderPreview} record
     */
    formatRemark(record) {

        if (!record.instrument) {
            return '目标合约未指定';
        }
        else if (isNaN(record.orderPrice)) {
            return '委托价格无效';
        }
        else if (record.orderPrice == 0) {
            return '委托价格=0';
        }
        else {
            return `<span class="${record.remark.clsname || ''}">${record.remark.content || ''}</span>`;
        }
    }

    /**
     * @param {Boolean} isReset 
     * @param {Array<OrderPreview | BasketOrderPreview>} orders 
     */
    async renderOrders(isReset, orders) {

        let map = await repoTrading.getAssetsLatestPrice();
        orders.forEach(x => { x.latestPrice = map[x.instrument] || 0; });

        if (isReset) {
            this.tableObj.refill(orders);
        }
        else {
            orders.forEach(item => { this.tableObj.updateRow(item); });
        }
    }

    /**
     * @param {InstrumentInfo} insInfo 
     */
    setAsInstrument(insInfo) {

        var thisObj = this;
        var isOk = !!insInfo;
        var records = (/** @returns {Array<OrderPreview>} */ function() { return thisObj.tableObj.extractAllRecords(); })();

        records.forEach(item => {

            this.tableObj.updateRow({

                id: item.id,
                shortInstrument: isOk ? insInfo.shortInstrument : null,
                instrument: isOk ? insInfo.instrument : null,
                instrumentName: isOk ? insInfo.instrumentName : null,
                assetType: isOk ? insInfo.assetType : null,
                price: 0,
                orderPrice: 0,
                latestPrice: 0,
                volumeOriginal: 0,
                volume: 0,
                amount: 0,
                maxCanOpen: 0,
                remark: { content: null, clsname: null },
            });
        });
    }

    /**
     * @param {WeightedAccountDetail} account 
     */
    handleAccountRowSelect(account) {

        var pk = WeightedAccountDetail.MakeId(account);
        if (this.tableObj.hasRow(pk)) {
            this.tableObj.selectRow(pk);
        }
    }

    adjustTableHeight(win_width, win_height, is_maximized) {
        this.tableObj.setMaxHeight(win_height - (is_maximized ? 560 : 548));
    }

    /**
     * 处理tab获得焦点事件
     */
    handleActivated(tab) {
        this.tableObj.fitColumnWidth();
    }

    /**
     * @param {TradeChannel} channel 
     */
    handleChannelChange(channel) {

        // if (channel.options.isFuture || channel.options.isOption) {
        //     this.tableObj.hideColumns(['委托金额']);
        // }
        // else {
        //     this.tableObj.showColumns(['委托金额']);
        // }
    }

    listen2Events() {

        /** 监听TAB激活 */
        this.registerEvent(this.systemEvent.tabActivated, this.handleActivated.bind(this));
        /** 监听交易渠道变化 */
        this.registerEvent('set-channel', this.handleChannelChange.bind(this));
        /** 监听账号组变更 & 下单面板调整，碰撞结果推送 */
        this.registerEvent('set-as-order-preview', this.renderOrders.bind(this));
        /** 监听对预览订单的合约设置 */
        this.registerEvent('set-as-instrument', this.setAsInstrument.bind(this));
        /** 监听账号选择变化 */
        this.registerEvent('account-row-selected', this.handleAccountRowSelect.bind(this));
        /** 监听窗口尺寸调整 */
        this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
    }

    build($container, { isBasket, isBatch }) {

        super.build($container);
        this.isBasket = !!isBasket;
        this.isBatch = !!isBatch;
        this.createToolbarApp();
        this.createFooterRowApp();
        this.createTable();
        this.listen2Events();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;