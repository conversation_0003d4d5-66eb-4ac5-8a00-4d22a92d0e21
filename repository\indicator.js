const httpRequest = require('../libs/http').http;

class indicatorRepository {

    constructor() {
        //
    }

    shareReportDocument(reportId, shareUserIds, share_type) {
        return new Promise((resolve, reject) => {
            httpRequest
                .post('/report/share', shareUserIds, { params: { report_id: reportId, share_type: share_type }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    createReportDocument(model) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/report', model)
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    updateReportDocument(model) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/report', model)
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    removeReportDocument(rId) {

        return new Promise((resolve, reject) => {
            httpRequest.delete('/report', { params: { report_id: rId }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getReportDocumentList() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/report')
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getReportTemplateList({ key_word, page_no, page_size }) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/template', { params: { key_word, page_no, page_size }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    removeReportTemplate(id) {
        return new Promise((resolve, reject) => {
            httpRequest
                .delete('/template', { params: { template_id: id }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getTemplateDetail(templateId) {
        return new Promise((resolve, reject) => {
            httpRequest
                .get('/template_detail', { params: { template_id: templateId }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    createTemplate(templateModel) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/template', templateModel)
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    updateTemplate(templateId, templateModel) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/template', templateModel, { params: { template_id: templateId }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getIndicatorList() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/indicator_library')
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    removeIndicatorList(id) {
        return new Promise((resolve, reject) => {
            httpRequest
                .delete('/indicator_library', { params: { indicator_library_id: id }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    saveIndicatorList(model) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/indicator_library', model)
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    updateIndicatorList(model) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/indicator_library', model)
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    getIndicatorData(identity_id, indicator_codes) {
        return new Promise((resolve, reject) => {
            httpRequest
                .get('/indicator', { params: { identity_id, indicator_codes }})
                .then(resp => { resolve(resp.data); }, err => { reject(err); });
        });
    }

    bindTemplate(identityId, templateIdsMap){
        return new Promise((resolve, reject) => {
            httpRequest.post('/template/bind', templateIdsMap, {
                params: {
                    identity_id: identityId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }
}

module.exports = { repoIndicator:  new indicatorRepository() };