const BaseComponent = require('../BaseComponent');
const draggable = require('vuedraggable');
const interaction = require('../../../../../libs/interaction').interaction;
module.exports = class DragWidget extends BaseComponent {
    constructor() {
        super(__dirname);
        this.options = {
            components: { draggable },
            props: {
                currentItem: {
                    type: Object,
                    default: null,
                },
                templateDetail: {
                    type: Object,
                },
            },
            data() {
                return {
                    form: {
                        templateName: '',
                        interval: 0,
                    },
                    list: [],
                    options: {
                        group: {
                            name: 'list',
                        },
                        // disabled: true,
                    },
                    types: [{ value: 'line', label: '折线图' }, { value: 'bar', label: '柱形图' }, { value: 'area', label: '面积图' }, { value: 'scatter', label: '散点图' }],
                    floatList: [{ value: -1, label: '默认' }, { value: 0, label: '0' }, { value: 1, label: '1' }, { value: 2, label: '2' }, { value: 3, label: '3' }, { value: 4, label: '4' }],
                    deleting: false,
                };
            },
            computed: {
                multi() {
                    return this.currentItem && this.currentItem.valueType == '2';
                },
            },
            watch: {
                templateDetail() {
                    this.form.templateName = this.templateDetail.report_info.title_format;
                    this.form.interval = this.templateDetail.layout.interval || 0;
                },
            },
            methods: {
                handleAdd(e) {
                    const itemIndex = e.newIndex;
                    const oldIndex = e.oldIndex;
                    let item = this.list[itemIndex];
                    if (item.relatedIndex !== 0) {
                        let previewArea = this.$parent.$refs.previewArea;
                        previewArea.recoverBox(item, oldIndex);
                    }
                    this.list.splice(itemIndex, 1);
                },
            },
        };
    }
};
