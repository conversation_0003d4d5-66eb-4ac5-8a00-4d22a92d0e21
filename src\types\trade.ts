import { AssetTypeEnum } from '@/enum/trade';
import type { BusinessFlagEnum } from '../../../xtrade-sdk/dist';
export interface TradeChannel {
  label: string;
  name: string;
  assetTypes: AssetTypeEnum[];
  credit?: boolean;
}

export interface TradeButton {
  label: string;
  businessFlag?: BusinessFlagEnum;
}

/**
 * 标准TICK行情数据结构
 */
export interface StandardTick {
  /**
   * 卖1 ~ 卖10 的价格数组（小到大）
   */
  askPrice: number[];

  /**
   * 卖1 ~ 卖10 的挂单量数组（小到大）
   */
  askVolume: number[];

  /**
   * 买1 ~ 买10 的价格数组（大到小）
   */
  bidPrice: number[];

  /**
   * 买1 ~ 买10 的挂单量数组（大到小）
   */
  bidVolume: number[];

  /**
   * 交易所名称
   */
  exchange: string;

  /**
   * 当日最高价
   */
  highPrice: number;

  /**
   * 合约代码
   */
  instrumentID: string;

  /**
   * 最新成交价
   */
  lastPrice: number;

  /**
   * 当日最低价
   */
  lowPrice: number;

  /**
   * 涨停价
   */
  upperLimitPrice: number;

  /**
   * 跌停价
   */
  lowerLimitPrice: number;

  /**
   * 开盘价
   */
  openPrice: number;

  /**
   * 当前持仓量
   */
  position: number;

  /**
   * 昨收盘价
   */
  preClosePrice: number;

  /**
   * 结算价
   */
  settlePrice: number;

  /**
   * 时间字符串（格式化后的时间）
   */
  strTime: string;

  /**
   * 成交额
   */
  turnover: number;

  /**
   * 数据更新时间戳或字符串
   */
  updateTime: string;

  /**
   * 成交量
   */
  volume: number;
}
