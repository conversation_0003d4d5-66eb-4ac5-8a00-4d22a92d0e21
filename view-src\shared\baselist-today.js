const os = require('os');
const fs = require('fs');
const path = require('path');
const { LocalSetting } = require('../../config/system-setting.local');
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const { BaseList } = require('./baselist');
const { ContextObjectInfo } = require('../../model/context-object-info');
const { ServerBatchData } = require('../../model/server-batch-data');
const repoPosition = require('../../repository/position').repoPosition;
const repoOrder = require('../../repository/order').repoOrder;
const repoTradeRecord = require('../../repository/traderecord').repoTradeRecord;

/** 行结束符 */
const LineSplitter = os.EOL;
/** 文件内容编码 */
const FileContentEncoding = 'utf8';
/** 文件最新写入信息描述长度 */
const VersionInfoMessageLength = 200;

/**
 * 推送数据写入到本地化文件的设置
 */
const JobSetting = {

    /** 每一次常规检测，间隔毫秒数 */
    Interval: 1000 * 40,
    /** 距上一次实际执行，最大间隔毫秒数后，无论已累积推送数量多少，均需写入一次 */
    LeastMs: 1000 * 60 * 10,
    /** 距上一次实际执行，当前已累积最小推送数据量，方可写入一次 */
    MinSize: 1000,
};

/**
 * 已经写入到本地化文件，的最新一笔数据版本信息
 */
class RecordVersionInfo {

    constructor(struc) {

        /** 记录ID字段 */
        this.id = struc.id;
        /** 记录最新更新时间 */
        this.updateTime = struc.updateTime;

        /** 对版本信息，字段结构缺失进行容错 */
        if (this.updateTime === undefined) {
            this.updateTime = 0;
        }
    }
}

class TodayBaseList extends BaseList {

    /**
     * 本地化数据文件完整路径
     */
    get dataFilePath() {
        return this._makeDataPath(this.identityId);
    }

    /**
     * id字段，在一维数据中的索引位置
     */
    get RecordIdIdx() {
        return this.titles2IdxMap['id'];
    }

    /**
     * updateTime字段，在一维数据中的索引位置
     */
    get RecordUpdateTimeIdx() {
        return this.titles2IdxMap['updateTime'];
    }

    /**
     * 生成上下文对象预期的本地化文件地址
     * @param {*} identity_id
     */
    _makeDataPath(identity_id) {

        if (this.todayStr === undefined) {
            this.todayStr = new Date().format('yyyyMMdd');
        }

        return path.join(LocalSetting.userDataPath, `${this.todayStr}-${identity_id}-${this.dataType}`);
    }

    /**
     * 当前视图，是否为今日订单
     */
    get isOrder() {
        return this.title == TodayBaseList.ViewTitles.order;
    }

    /**
     * 当前视图，是否为今日持仓
     */
    get isPosition() {
        return this.title == TodayBaseList.ViewTitles.position;
    }

    /**
     * 当前视图，是否为今日成交
     */
    get isTradeRecord() {
        return this.title == TodayBaseList.ViewTitles.tradeRecord;
    }

    /**
     * 是否禁用本地缓存文件写入
     */
    get isLocalCacheDisabled() {
        return true;
    }

    /**
     * 视图名称识别字串
     */
    static get ViewTitles() {

        return {

            order: '今日订单',
            position: '今日持仓',
            tradeRecord: '今日成交',
        };
    }

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);

        /**
         * 实时推送数据，缓冲区
         * 1. 未完成接收服务器批量推送时，实时推送的数据，存放于该缓冲区
         * 2. 已完成接收服务器批量推送时，如果该缓冲区存在数据，则一次性将内部所有数据渲染至TABLE
         */
        this.hotChanges = [];

        /**
         * 实时推送 & 需要被本地化的，增量数据，缓冲区
         */
        this.incrChanges = [];

        /**
         * 数据消费者，是否处于正常可接收，数据推送的状态
         */
        this.isConsumerReady = true;

        /**
         * 拼音样本字典
         */
        this.pinyinMap = {};

        /** 今日数据模块，公用数据 */
        this.sharedCondition = {

            /** 是否显示暂停实时数据渲染按钮 */
            showPause: true,

            /** 是否已暂停实时数据的渲染（数据接收正常进行） */
            isPaused: false,

            /** 是否展示添加今日数据的按钮（仅机构管理员试用） */
            canChangeRecord: false,
        };
    }

    setDataFunCodeType(func_code, data_type) {

        /**
         * 今日批量数据：订单/持仓/成交，的功能代码
         */
        this.dataFunctionCode = func_code;

        /**
         * 今日数据种类（用于文件名标识等用途）
         */
        this.dataType = data_type;
    }

    logEvent(message) {
        // console.log(`${ Date.now() } > ${ this.dataType } > ${ this.context.identityId } > ${ message }`);
    }

    /**
     * 设置本地化数据标题
     * @param {Array<String>} titles 标题，标题字段列表的顺序、数量，与服务器推送数据，完全一致（如遇服务器端调整，需同步调整）
     */
    setTitle(titles) {

        this.titles = titles;
        let map = {};
        titles.forEach((name, idx) => { map[name] = idx; });
        this.titles2IdxMap = map;
    }

    /**
     * 识别是否有本地化的当日数据
     * @param {*} another_path 特定的本地化文件地址（默认null）
     */
    _hasLocalized(another_path) {
        return fs.existsSync(another_path || this.dataFilePath);
    }

    /**
     * 读取已经被本地化的数据
     * @param {*} another_path 特定的本地化文件地址（默认null）
     * @returns {Array}
     */
    _readLocalized(another_path = null) {
        
        let results = [];
        if (!this._hasLocalized(another_path)) {
            return [];
        }

        try {

            let content = fs.readFileSync(another_path || this.dataFilePath, { encoding: FileContentEncoding });

            if (typeof content == 'string' && content.length > 0) {

                let lines = content.split(LineSplitter);

                /**
                 * 1. 第一行为数据版本信息，故需要将第一行数据抛掉
                 * 2. 推送数据各个字段，为固定顺序（已通过编码中 setTitle 保持一致顺序，如遇服务器端调整，需同步调整）
                 */
                lines.shift(0);

                let data_lines = lines.filter((x) => x.length > 0);
                results = data_lines.map((line_content) => JSON.parse(line_content));
            }
        } 
        catch (ex) {

            console.error(ex);
            try {

                /**
                 * 读取文件内容产生异常，则将该文件删除
                 */
                fs.unlinkSync(another_path || this.dataFilePath);
            } 
            catch (ex2) {
                console.error(ex2);
            }
        }

        return results;
    }

    /**
     * 读取本地化数据的最新版本
     * @param {Function} handler 回调函数
     */
    _readLatestRecordVersion(handler) {

        if (!this._hasLocalized()) {

            handler();
            return;
        }

        try {

            let thisObj = this;
            let readLine = require('readline');
            let firstLine = null;
            let lineReader = readLine.createInterface({ input: fs.createReadStream(this.dataFilePath) });

            lineReader.on('line', function (line_content) {

                firstLine = line_content;
                /** 获取第一行内容后，立即移除行读取监听 */
                lineReader.removeAllListeners('line');
                lineReader.close();
            });

            lineReader.on('close', function () {

                let has_content = typeof firstLine == 'string' && firstLine.trim().length > 0;
                if (!has_content) {

                    handler(false, 'empty file');
                    return;
                }

                try {

                    let struc = JSON.parse(firstLine.substr(0, VersionInfoMessageLength));
                    if (thisObj.helper.isJson(struc) && struc.id !== undefined && struc.updateTime !== undefined) {
                        handler(new RecordVersionInfo(struc), `version(${firstLine}) fields [id] or [updateTime] is not present`);
                    } 
                    else {
                        handler(false, `version(${firstLine}) text content is invalid`);
                    }
                } 
                catch (ex) {

                    console.error(ex);
                    handler(false, 'version text content parsing error');
                }
            });
        } 
        catch (ex) {

            console.error(ex);
            handler(false, 'version extracting error');
        }
    }

    /**
     * 根据最新的写入情况，采取增量的方式请求数据
     * @param {RecordVersionInfo} version_info
     */
    _requestDepends(version_info) {

        let version_mark = !(version_info instanceof RecordVersionInfo) ? undefined : this.isTradeRecord ? version_info.id : version_info.updateTime;
        this.standardSend(this.dataFunctionCode, this.context, version_mark);
    }

    /**
     * 向服务器动态请求批量数据
     */
    flexibleRequest() {

        if (this.isLocalCacheDisabled) {
            
            this._requestDepends();
            return;
        }

        this._readLatestRecordVersion((latest_info, failure_reason) => {

            if (latest_info === false) {

                try {
                    console.error('version info not read properly & localized file removed: ' + failure_reason);
                    fs.unlinkSync(this.dataFilePath);
                } 
                catch (ex) {
                    console.error(ex);
                }
            }

            this.logEvent(`to request with funcode = ${this.dataFunctionCode}, version info = ${JSON.stringify(latest_info)}`);
            this._requestDepends(latest_info);
        });
    }

    /**
     * 寻找批次当中最新的记录
     * @param {Array<Array>} contents
     */
    _seekLatest(contents) {

        let id_idx = this.RecordIdIdx;
        let ut_idx = this.RecordUpdateTimeIdx;
        let matched = contents[0];

        for (let idx = 1; idx < contents.length; idx++) {

            let values = contents[idx];
            if (matched[ut_idx] < values[ut_idx]) {
                matched = values;
            }
        }

        return { id: matched[id_idx], updateTime: matched[ut_idx] };
    }

    /**
     * 将推送数据，转换为title所确定顺序的二维数组数据
     * @param {Array} records
     */
    _convertObjects2Values(records) {

        let titles = this.titles;
        return records.map((item) => {

            let values = [];
            titles.forEach((name) => { values.push(item[name]); });
            return values;
        });
    }

    /**
     * 本地化合并后的完整数据
     * @param {Array<Array>} mergeds 要写入本地化数据文件，的完整数据集
     * @param {String} another_path 特定的本地化文件地址（默认null）
     */
    localize(mergeds, another_path = null) {

        if (mergeds.length == 0) {
            return;
        }

        let lines = mergeds.map((values) => JSON.stringify(values));
        let version_info = { id: null, updateTime: 0 };

        /**
         * 寻找最新的一笔记录，用以设置为，当前本地化的数据版本
         */

        if (mergeds.length > 0) {

            let latest = this._seekLatest(mergeds);
            version_info.id = latest.id;
            version_info.updateTime = latest.updateTime;

            if (version_info.updateTime === undefined) {
                version_info.updateTime = 0;
            }
        }

        /**
         * 本地化文件，首行放置版本信息
         */
        lines.unshift(JSON.stringify(version_info));
        this.logEvent(`to localize, data length = ${mergeds.length} & version = ${JSON.stringify(version_info)}`);

        /**
         * 将：首行版本信息 + 合并后的完整数据，写入本地化文件
         */
        fs.writeFileSync(another_path || this.dataFilePath, lines.join(LineSplitter) + LineSplitter, { encoding: FileContentEncoding });
    }

    /**
     * 本地化增量数据（适用于，本地化文件已存在）
     */
    localizeChanges() {

        if (this.isLocalCacheDisabled) {
            return;
        }

        let changes = this.incrChanges;
        if (changes.length == 0) {
            return;
        }

        let locals = this._readLocalized();
        let newers = this._convertObjects2Values(changes);
        let mergeds = locals.length == 0 ? newers : this.mergeAll(locals, newers);
        this.logEvent(`to localize incremental hot changes, changes.length = ${changes.length} mergeds.length = ${mergeds.length}`);
        this.localize(mergeds);
        this._clearIncrChanges();
    }

    /**
     * 发生上下文切换后，立即对上一主体的增量数据，进行本地化写入
     * @param { ContextObjectInfo } previous_context
     */
    localizePreviousChanges(previous_context) {

        if (this.isLocalCacheDisabled) {
            return;
        }

        let changes = this.incrChanges;
        if (changes.length == 0) {
            return;
        }

        let fpath = this._makeDataPath(previous_context.identityId);
        if (!fs.existsSync(fpath)) {

            /**
             * 本地化数据文件当存在，如不存在，则直接抛弃上一主体的增量数据
             */
            return;
        }

        let locals = this._readLocalized(fpath);
        let newers = this._convertObjects2Values(changes);
        let mergeds = locals.length == 0 ? newers : this.mergeAll(locals, newers);
        this.logEvent(`to localize previous change, pre-left = ${changes.length}, pre-mergeds.length = ${mergeds.length}`);
        this.localize(mergeds, fpath);
    }

    /**
     * 将对象数组数据格式化为字符串数组
     * @param {Array} typed_records 要写入本地化数据文件，的数据集
     */
    _formatRecords(typed_records) {

        let titles = this.titles;
        let dataLines = typed_records.map((typed_obj) => {

            let values = [];
            /** 按标题顺序，将对象实例转换成，一维数组 */
            titles.forEach((prop_name) => { values.push(typed_obj[prop_name]); });
            return JSON.stringify(values);
        });

        return dataLines;
    }

    /**
     * 清空实时数据缓冲区
     */
    _clearBuffereds() {
        this.hotChanges.clear();
    }

    /**
     * 清空增量数据缓冲区
     */
    _clearIncrChanges() {
        this.incrChanges.clear();
    }

    /**
     * 处理消费者重启推送请求，将所有缓冲区的数据一次性全部推送
     */
    _handleFlushRequest() {

        this._showLoading();
        setTimeout(() => {

            this._flushAll();
            this._hideLoading();
        }, 0);
    }

    /**
     * 将，上下文切换，至批量数据返回，此期间羁押的实时推送数据，一次性推送给消费者
     */
    _flushAll() {

        /** 将羁押的实时推送，全部传送至消费处 */
        this.hotChanges.forEach((struc) => { this.consumeRealtimePush(struc); });
        /** 清空实时数据缓冲区 */
        this._clearBuffereds();
    }

    _isNotAboutContextIdentity(identity_id) {
        return identity_id != this.context.identityId;
    }

    /**
     * 设置快速数据获取操作完成标识
     * @param {Boolean} isCompleted 
     */
    _setQuickRequestCompletement(isCompleted) {

        /**
         * 首屏数据快速获取操作是否已完成
         */
        this.isQuickRequestCompleted = isCompleted;
    }

    /**
     * 快速获取首页数据
     */
    async quickRequest() {

        this._setQuickRequestCompletement(false);

        let identityId = this.identityId;
        let pageSize = this.paging.pageSize;
        let resp;

        if (this.isPosition) {
            resp = await repoPosition.requestFirstPage(identityId, pageSize);
        } 
        else if (this.isOrder) {
            resp = await repoOrder.requestFirstPage(identityId, pageSize);
        } 
        else if (this.isTradeRecord) {
            resp = await repoTradeRecord.requestFirstPage(identityId, pageSize);
        }

        if (resp.errorCode != 0) {
            this.interaction.showHttpError(`${this.title} > 首页数据获取错误 > ${resp.errorMsg}`);
        }
        else if (this.isDataPushReceived) {

            /**
             * 批量推送数据，已先于首屏数据完成接收处理，故此处的首屏数据，作废弃处理
             */
        }
        else {

            /**
             
            sample = {
                contents
                identityId
                pageNo
                pageSize
                totalPages
                totalSize
            };

             */

            let bean = resp.data || [];
            let { contents, totalSize } = bean;
            /** 将首行标题栏抛掉 */
            let titles = (contents || []).shift();
            this.consumeBatchPush(contents || []);
            /** 首屏数据，携带了全量数据条数，使用该值进行重置 */
            this.paging.total = totalSize;

            /**
             * 1. 首屏数据获取、渲染完成后，即将加载效果隐藏
             * 2. 加载效果，由context change事件源头启动展示，可由2个事件进行关闭：
             *    A. 全量（增量）数据推送接收处理完成，进行关闭
             *    B. 首屏数据获取、渲染完成，进行关闭
             */
            this._hideLoading();
        }

        this._setQuickRequestCompletement(true);
    }

    /**
     * 消耗完整的服务器推送数据（本地化数据 + 推送的增量数据）
     * @param { ServerBatchData } sbdata_meta 系类实例经主进程序列化后的结果 - 具有相同的结构，但不具有类型
     */
    handleBatchPush(sbdata_meta) {

        let sbdata = new ServerBatchData({});
        this.helper.extend(sbdata, sbdata_meta);
        let newers = sbdata.contents;

        this.logEvent(`received data push, length = ${newers.length}`);

        /**
         * 刨除标题元素
         * 1. 仅当存在至少1条数据时，后端才给出具体的标题信息
         * 2. 如果没有数据，则标题信息也不存在
         */
        let titles = newers.shift();

        // /**
        //  * 设置标题（已通过子类初始设置，此处无需再进行）
        //  */
        // if (this.titles === undefined && titles instanceof Array) {
        //     this.setTitle(titles);
        // }

        if (this.isLocalCacheDisabled) {
            this.consumeBatchPush(newers);
        }
        else {

            /**
             * 1. 尚未本地化情况下，首次的批量数据，进行完整本地化
             * 2. 已发生本地化情况下，增量的数据，与本地化版本的数据，进行合并（子类实现）后，对合并结果再进行本地化
             */

            let locals = this._readLocalized();
            let mergeds = newers.length == 0 ? locals : locals.length == 0 ? newers : this.mergeAll(locals, newers);
            this.logEvent(`data merged: locals = ${locals.length}, newers = ${newers.length}, mergeds = ${mergeds.length}`);
            this.localize(mergeds);
            this.consumeBatchPush(mergeds);
        }

        /** 隐藏数据加载效果 */
        this._hideLoading();
        /** 一次性将批次数据推送到消费处 */
        this._flushAll();
        /** 标识数据推送已完成接收 */
        this._setBatchDataReadyState(true);
    }

    handleRealtimeChange(struc) {

        if (this._isNotAboutContextIdentity(struc.strategyId || struc.fundId || struc.accountId)) {

            console.error(struc);
            return;
        }

        /**
         * 无论是否，批量数据已完成接收和处理，都将实时推送放入增量队列

         * 1. 从服务器开始推送，批量数据的第一个字节开始，到主进程完成接收 & 原始数据解压 & 跨进程传输 & 反序列化等，耗时较长
         * 2. 步骤1耗时较长，此过程中可能存在实时数据推送
         * 3. 避免存在全量 & 增量之间的遗漏偏差（也许，还存在数据重叠的可能性 ？）
         */
        this.incrChanges.push(struc);

        /**
         * 根据批量数据接收情况，决定是否将短时间内的实时推送数据放入缓冲区
         */

        if (this.isDataPushReceived && this.isConsumerReady) {
            this.consumeRealtimePush(struc);
        } 
        else {
            this.hotChanges.push(struc);
        }
    }

    /**
     * 合并本地数据 + 新接收的推送数据（涉及可能的数据去重）
     * @param {Array<Array>} locals
     * @param {Array<Array>} newers
     * @returns {Array<Array>}
     */
    mergeAll(locals, newers) {
        throw new Error('not implemented');
    }

    /**
     * 消耗完整的服务器推送数据（本地化数据 + 推送的增量数据）
     * @param {Array<Array>} contents
     */
    consumeBatchPush(contents) {

        this.paging.page = 1;
        this.paging.total = contents.length;
    }

    /**
     * 消耗服务器实时推送数据
     * @param {*} struc
     */
    consumeRealtimePush(struc) {
        throw new Error('not implemented');
    }

    /**
     * 设置标识：批量数据，是否已就位
     * 1. 后续的数据，将根据该状态，决定是放入推送缓冲区，还是追加到表格组件
     * 2. 对首屏数据的处理，也将根据批量数据是否已返回，决定是否需要：A. 进行实际渲染，或 B. 丢弃首屏数据
     * @param {Boolean} flag
     */
    _setBatchDataReadyState(flag) {

        /**
         * 批量数据是否已完成推送接收
         */
        this.isDataPushReceived = flag;
    }

    _startIntervalWrite() {

        this._lastJobExecuteTime = Date.now();

        if (this._intervalJob !== undefined) {

            /**
             * 无论是否处于暂停状态，直接置为允许状态
             */
            this._isTimelyWriteDisabled = false;
            return;
        }

        this._intervalJob = setInterval(() => {

            if (this._isTimelyWriteDisabled) {
                return;
            }

            let totalSize = this.incrChanges.length;
            if (totalSize == 0) {
                return;
            }

            let last = this._lastJobExecuteTime;
            let now = Date.now();
            let ellapsed = now - last;
            let shouldRun = totalSize >= JobSetting.MinSize || ellapsed >= JobSetting.LeastMs;

            if (shouldRun) {

                this.logEvent(`incremental change flushed due to size = ${totalSize} & time = ${ellapsed}, last time = ${last} & now = ${now}`);
                this._lastJobExecuteTime = now;
                this.localizeChanges();
            }

        }, JobSetting.Interval);
    }

    _stopIntervalWrite() {
        this._isTimelyWriteDisabled = true;
    }

    /**
     * @param {ContextObjectInfo} previous
     */
    handleContextChanged(previous) {

        this.logEvent(`context changed from ${previous ? previous.identityId : '[undefined]'} to ${this.identityId}`);

        /**
         * 判断是否是账号，是账号且为管理员，则可以修改：今日持仓 / 今日订单 / 今日成交
         */
        this.sharedCondition.canChangeRecord = !!this.context && this.context.isAboutAccount && this.userInfo.isOrgAdmin;

        /**
         * 快速获取用于首屏展示的数据
         */
        this.quickRequest();

        /**
         * 对上一主体，退订实时推送 + 本地化已羁押的数据
         */
        if (previous instanceof ContextObjectInfo) {

            /** 退订之前上下文的数据推送 */
            this.unsubChange(previous);

            /** 立即将可能存在的待本地化增量数据，进行持久化 */
            this.localizePreviousChanges(previous);
        }

        /** 重置控件状态 */
        this.resetControls();
        /** 停止周期性的推送数据写入 */
        this._stopIntervalWrite();
        /** 重置为，批量数据推送 & 接收，尚未就位 */
        this._setBatchDataReadyState(false);
        /** 清空实时数据缓冲区 */
        this._clearBuffereds();
        /** 清空实时数据缓冲区 */
        this._clearIncrChanges();
        /** 请求当前上下文的全量数据 */
        this.turn2Request();
        /** 订阅当前上下文的实时推送数据 */
        this.resubChange();
        /** 开始周期性的推送数据写入 */
        this._startIntervalWrite();
    }

    handleConnectionRecovered() {
        this.turn2Request();
    }

    handleRefresh() {

        this.interaction.showSuccess('刷新请求已发送');
        this.resetControls();
        this.quickRequest();
        this.turn2Request();
    }

    toggleRealtimePush() {

        var is2Pause = !this.sharedCondition.isPaused;
        this.sharedCondition.isPaused = is2Pause;

        if ((this.isConsumerReady = !is2Pause)) {
            this._handleFlushRequest();
        }
    }

    openAddRecordDialog() {
        this._displayRecordEditDialog();
    }

    updateRecord(record) {
        this._displayRecordEditDialog(record);
    }

    _displayRecordEditDialog(record) {

        let thisObj = this;

        function notifyChange() {

            thisObj.dialogEditRecord.getNotify({

                record,
                account: thisObj.context.accounts[0],
                isOrder: thisObj.isOrder,
                isPosition: thisObj.isPosition,
                isTradeRecord: thisObj.isTradeRecord,
                callback: thisObj.handleRefresh.bind(thisObj),
            });
        }

        if (this.dialogEditRecord) {

            notifyChange();
            return;
        }

        let EditTodayRecordView = require('./edit-today-record-view').EditTodayRecordView;
        let viewIns = (this.dialogEditRecord = new EditTodayRecordView('@shared/edit-today-record-view', this.title));
        viewIns.loadBuild(this.$container, undefined, () => { notifyChange(); });
    }

    deleteRecord(record) {

        this.interaction.showConfirm({

            message: `是否确认删除记录：${this.title} / ${this.identifyRecord(record)} / ${record.instrument} / ${record.instrumentName}`,
            confirmed: () => {
                this.doDeleteRecord(record);
            },
        });
    }

    async doDeleteRecord(record) {

        let resp;
        if (this.isPosition) {
            resp = await repoPosition.delete([record]);
        } 
        else if (this.isOrder) {
            resp = await repoOrder.delete([record]);
        } 
        else if (this.isTradeRecord) {
            resp = await repoTradeRecord.delete([record]);
        }

        if (resp.errorCode == 0) {

            this.interaction.showSuccess('删除成功');
            this.tableObj.deleteRow(this.identifyRecord(record));
        } 
        else {
            this.interaction.showHttpError('删除失败:' + resp.errorMsg);
        }
    }

    makeFilters(records, propName, translators) {
        return SmartTable.MakeColFilters(records, propName, { translators: translators });
    }

    /** 重置控件状态 */
    resetControls() {
        this.tableObj.removeAllColFilters();
    }

    /**
     * 退订，之前的，上下文对象的实时数据
     * @param { ContextObjectInfo } previous_context
     */
    unsubChange(previous_context) {
        throw new Error('not implemented');
    }

    /**
     * 订阅，当前的，上下文对象的实时数据
     */
    resubChange() {
        throw new Error('not implemented');
    }

    /**
     * 重新请求数据
     */
    turn2Request() {
        this.flexibleRequest();
    }

    /**
     * 测试样本内容是否能被关键字匹配到
     * @param {String} content
     * @param {String} keywords
     */
    testKeywords(sample, keywords) {

        let matched_py = this.pinyinMap[sample];
        if (matched_py === undefined) {
            matched_py = this.pinyinMap[sample] = this.helper.pinyin(sample);
        }

        return typeof keywords == 'string' && keywords.length >= 1 && matched_py.indexOf(keywords) >= 0;
    }

    dispose() {

        clearInterval(this._intervalJob);
        delete this._intervalJob;
        console.log('the internal job cleared for > today order/position/exchange base class');
    }

    _handlePauseRender(disabled) {
        this.sharedCondition.showPause = !disabled;
    }

    build($container) {
        
        super.build($container);
        this.registerEvent('pauseRender', this._handlePauseRender.bind(this));
    }
}

module.exports = { TodayBaseList };
