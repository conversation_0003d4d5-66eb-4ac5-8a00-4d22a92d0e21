//sdfdsfdsfs

const ValueAbsentHolder = '---';

function fixedN(val, precision) {
    return typeof val == 'number' ? val.toFixed(precision) : val !== null && val !== undefined ? val : ValueAbsentHolder;
}

function fixed2(val) {
    return fixedN(val, 2);
}
function fixed3(val) {
    return fixedN(val, 3);
}
function fixed4(val) {
    return fixedN(val, 4);
}
function thousands(val, to_integer, precision) {
    return typeof val == 'number' ? val.thousands(to_integer, precision) : val !== null && val !== undefined ? val : ValueAbsentHolder;
}
function thousandsDecimal(val, precision) {
    return typeof val == 'number' ? val.thousandsDecimal(precision) : val !== null && val !== undefined ? val : ValueAbsentHolder;
}
function thousandsInt(val) {
    return thousands(val, true, 0);
}
function percentage(val, by100, precision) {
    if (val === undefined || val === null || val === '') {
        return ValueAbsentHolder;
    } else if (typeof val != 'number') {
        return val;
    }

    var new_val = val * (by100 === true ? 100 : 1);
    return fixedN(new_val, precision >= 0 ? precision : 2) + '%';
}

function integer(num) {
    return parseInt(num);
}

const NumberMixin = {
    filters: {
        fixed2: fixed2,
        fixed3: fixed3,
        fixed4: fixed4,
        integer: integer,
        thousandsInt,
        thousandsDecimal,
        thousands: (val) => { return thousands(val, false, 2); },
        percentage: (val) => { return percentage(val, false, 2); },
        percentageBy100: (val) => { return percentage(val, true, 2); },
    },

    methods: {
        fixed2: fixed2,
        fixed3: fixed3,
        fixed4: fixed4,
        fixedN: fixedN,
        thousands,
        thousandsDecimal,
        thousandsInt,
        percentage: (val) => { return percentage(val, false, 2); },
        percentageBy100: (val) => { return percentage(val, true, 2); },
        classBenefit: (value, threshold = undefined) => {
            if (typeof threshold != 'number') {
                threshold = 0;
            }
            return value > threshold ? 's-color-red' : value < threshold ? 's-color-green' : '';
        },
    },
};

module.exports = { NumberMixin };
