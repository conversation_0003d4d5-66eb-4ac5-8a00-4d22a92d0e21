<div class="nav-chart-view">

    <div class="user-toolbar themed-box">

        <el-select placeholder="选择参考基准"
                    v-model="states.benchmark" 
                    @change="handleBenchmarkChange" filterable>

            <el-option v-for="(item, item_idx) in benchmarks" 
                        :key="item_idx" 
                        :label="item.mean"
                        :value="item.code"></el-option>
        </el-select>

    </div>

    <div class="chart">
        <!-- placeholder -->
    </div>

</div>