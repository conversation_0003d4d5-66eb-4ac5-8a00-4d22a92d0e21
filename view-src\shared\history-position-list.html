<div class="summary-history-position">

	<div class="user-toolbar themed-box">

		<el-date-picker size="mini" type="daterange" v-model="condition.dateRange" start-placeholder="开始日期" range-separator="至" end-placeholder="结束日期"></el-date-picker>
        <el-select size="mini" placeholder="账号" v-model="condition.accountId" class="s-mgl-10 s-w-240" filterable clearable>
            <el-option v-for="(item, item_idx) in condition.accounts" :key="item_idx" :value="item.accountId" :label="formatSelectAccountName(item)"></el-option>
        </el-select>
        <el-input size="mini" placeholder="完整合约代码" v-model.trim="condition.keywords" class="s-mgl-10 s-w-120" clearable></el-input>
        <el-button size="mini" type="primary" size="small" @click="search" class="s-mgl-10">查询</el-button>
		
		<el-pagination :page-sizes="paging.pageSizes" 
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total" 
					   :current-page.sync="paging.page"
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange" 
					   @current-change="handlePageChange"></el-pagination>

	</div>

	<div class="table-control">
		<table>
            <tr>
                <th label="代码" fixed-width="100" prop="instrument" overflowt sortable searchable></th>
				<th label="名称" fixed-width="80" prop="instrumentName" overflowt sortable searchable></th>
                <th label="账号名称" min-width="202.0" prop="accountName" formatter="formatAccountName" overflowt sortable searchable></th>
                <th label="交易日" fixed-width="90" prop="tradingDay" sortable></th>

                <th type="program" 
                    label="资产类型" 
                    fixed-width="100" 
                    prop="assetType" 
                    watch="assetType" 
                    formatter="formatAssetType" sortable></th>

                <th type="program" 
                    label="方向" 
                    fixed-width="70" 
                    prop="direction" 
                    watch="direction" 
                    formatter="formatDirection" 
                    export-formatter="formatDirectionText" sortable></th>
                
                <th label="总仓" fixed-width="80" prop="totalPosition" align="right" sortable thousands-int></th>
				<th label="昨仓" fixed-width="80" prop="yesterdayPosition" align="right" sortable thousands-int></th>
                <th label="今仓" fixed-width="80" prop="todayPosition" align="right" sortable thousands-int></th>
                <th label="持仓均价" fixed-width="80" prop="avgPrice" align="right" formatter="formatPrice"></th>
                <th label="持仓市值" min-width="80" prop="marketValue" align="right" sortable thousands></th>
                
                <th label="平仓盈亏" 
                    min-width="80" 
                    prop="closeProfit" 
                    align="right" 
                    class-maker="makeBenefitClass" 
                    footer-class-maker="makeBenefitClass" overflowt thousands sortable summarizable></th>

                <th label="手续费" min-width="80" prop="usedCommission" align="right" overflowt thousands sortable summarizable></th>
                <th label="保证金" min-width="80" prop="usedMargin" align="right" sortable overflowt thousands summarizable></th>
                
                <th label="盈亏" 
                    min-width="80" 
                    prop="profit" 
                    align="right" 
                    class-maker="makeBenefitClass" 
                    footer-class-maker="makeBenefitClass" overflowt thousands sortable summarizable></th>
            </tr>
        </table>
	</div>

</div>