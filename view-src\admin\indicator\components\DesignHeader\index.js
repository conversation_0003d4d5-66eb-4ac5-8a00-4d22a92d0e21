const BaseComponent = require('../BaseComponent');
const interaction = require('../../../../../libs/interaction').interaction;
module.exports = class DragWidget extends BaseComponent {
    constructor() {
        super(__dirname);
        this.options = {
            components: {  },
            props: {

            },
            data() {
                return {
                };
            },
            computed: {
                
            },
            methods: {
                handleSave() {
                    if (this.check()) {
                        this.saveTemplate();
                    }
                },
                check() {
                    if (!this.$parent.$refs.configArea.form.templateName) {
                        interaction.showWarning('请填写模板名称');
                        return false;
                    }
                    const previewList = this.$parent.$refs.previewArea.previewList;
                    if (previewList.length == 0) {
                        interaction.showWarning('请至少添加一个指标');
                        return false;
                    }
                    return true;
                },
                saveTemplate() {
                    // console.log(this.$parent.$refs.previewArea.previewList);
                    const content = this.$parent.$refs.previewArea.previewList.map(item => {
                        let obj = { ...item, rawData: [], data: [], columns: [], value: '---' };
                        return obj;
                    });
                    this.$emit('save-template', content, this.$parent.$refs.configArea.form);
                },
            },
        };
    }
};
