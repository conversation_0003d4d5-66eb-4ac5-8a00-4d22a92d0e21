const BatchChildView = require('./batch-child');
const { AccountDetail } = require('../../../../model/account');
const { repoAccount } = require('../../../../repository/account');

class AccountProperty {

    /**
     * @param {String} name 属性名称
     * @param {Number} value 属性值
     */
    constructor(name, value, options = {

        isPercent: false,
        isCredit: false, 
        isFuture: false, 
        isOption: false, 
        showTitle: false 
    }) {

        this.name = name;
        this.value = value;
        this.isPercent = options.isPercent;
        this.isCredit = options.isCredit;
        this.isFuture = options.isFuture;
        this.isOption = options.isOption;
        this.showTitle = options.showTitle;
    }
}

class View extends BatchChildView {

    constructor(view_name) {

        super(view_name);

        this.states = {
            
            accountId: null, 
            accountName: null,
        };

        this.property = {

            balance: new AccountProperty('权益', 0),
            marketValue: new AccountProperty('市值', 0),
            available: new AccountProperty('可用资金', 0),
            preBalance: new AccountProperty('昨日权益', 0),
            nav: new AccountProperty('净值', 0),
            risePercent: new AccountProperty('涨跌幅', 0, { isPercent: true }),
            frozenMargin: new AccountProperty('冻结资金', 0),
            closeProfit: new AccountProperty('平仓盈亏', 0),
            positionProfit: new AccountProperty('浮动盈亏', 0),
            loanBuyBalance: new AccountProperty('融资买入金额', 0, { isCredit: true, showTitle: true }),
            loanSellBalance: new AccountProperty('可用融券卖出资金', 0, { isCredit: true, showTitle: true }),
            loanSellQuota: new AccountProperty('融券卖出金额', 0, { isCredit: true, showTitle: true }),
            commission: new AccountProperty('手续费', 0),
            margin: new AccountProperty('占用保证金', 0, { isFuture: true, isOption: true, showTitle: true }),
            takeRisk: new AccountProperty('风险度', 0, { isPercent: true, isFuture: true, isOption: true }),
        };

        this.registerEvent('set-context-account', this.setAsAccount.bind(this));
    }

    resetProperties() {
        Object.values(this.property).forEach(item => { item.value = 0; });
    }

    /**
     * @param {AccountDetail} account 
     * @returns 
     */
    async setAsAccount(account) {

        if (this.helper.isNone(account)) {

            this.states.accountId = null;
            this.states.accountName = null;
            this.resetProperties();
            return;
        }
        
        var accountId = this.helperUi.getProperAccountId(account);
        this.states.accountId = accountId;
        this.states.accountName = this.helperUi.formatSelectAccountName(account);
        var resp = await repoAccount.getAccountDetail(accountId);

        if (resp.errorCode != 0) {

            this.resetProperties();
            return;
        }
        
        var struc = resp.data;
        if (!struc) {
            return;
        }

        var prop = this.property;
        var balance = struc.balance;

        prop.balance.value = struc.balance;
        prop.marketValue.value = struc.marketValue;
        prop.available.value = struc.available;
        prop.preBalance.value = struc.preBalance;
        prop.nav.value = struc.nav;
        prop.risePercent.value = typeof struc.risePercent == 'number' ? struc.risePercent.toFixed(2) : struc.risePercent;
        prop.frozenMargin.value = struc.frozenMargin;
        prop.closeProfit.value = struc.closeProfit;
        prop.positionProfit.value = struc.positionProfit;
        prop.loanBuyBalance.value = struc.loanBuyBalance;
        prop.loanSellBalance.value = struc.loanSellBalance;
        prop.loanSellQuota.value = struc.loanSellQuota;
        prop.commission.value = struc.commission;
        prop.margin.value = struc.margin;
        prop.takeRisk.value = typeof balance == 'number' && balance >= 0 ? (100 * struc.marketValue / balance).toFixed(2) : 0;
    }

    /**
     * @param {AccountProperty} prop 
     */
    isApplicable(prop) {

        if (this.isCredit) {
            return prop.isCredit || !prop.isFuture && !prop.isOption;
        }
        else if (this.isFuture || this.isOption) {
            return prop.isFuture || prop.isOption || !prop.isCredit;
        }
        else {
            return !prop.isCredit && !prop.isFuture && !prop.isOption;
        }
    }

    createApp() {

        new Vue({

            el: this.$container.querySelector('.account-detail-frame'),
            data: {

                states: this.states,
                properties: Object.values(this.property),
            },
            mixins: [this.NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [this.isApplicable]),
        });
    }

    scheduleUpdate() {

        this.timer = setInterval(async () => {
            
            if (this.isRequesting) {
                return;
            }

            this.isRequesting = true;
            var { accountId, accountName } = this.states;
            if (accountId) {

                try {
                    await this.setAsAccount({ accountId, accountName });
                }
                catch(ex) {}
            }

            this.isRequesting = false;

        }, 1000 * 10);
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.scheduleUpdate();
    }
}

module.exports = View;