const { IView } = require('../../../../component/iview');
const { NumberMixin } = require('../../../../mixin/number');
const { AlgoEntrustInfo, TaskEntrustStatus } = require('../../../../model/algot');
const { BasketMotherTask, BasketChildTask } = require('../../../../model/basket-task');
const { SmartTable } = require('../../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { repoAlgo } = require('../../../../repository/algorithm');
const repoBasket = require('../../../../repository/basket');

class View extends IView {

    constructor(view_name) {

        super(view_name, false, '算法交易');
        this.taskStatus = TaskEntrustStatus;
        this.cached = { mtasks: [new BasketMotherTask({})].splice(1) };
        this.mcolumns = ['运行状态', '母单进度', '分配资金', '创建时间', '操作'];
        this.childCols = {
            preview: ['委托数量'],
            task: ['进度', '成交均价', '已成交额', '未成交量', '操作'],
        };
    }

    /**
     * @returns {BasketMotherTask}
     */
    asMotherTask(data) {
        return data;
    }

    /**
     * @param {BasketMotherTask} task 
     */
    exportUnclosedEntrusts(task) {
        
        let table = this.table4UnclosedEntrust;
        if (table == undefined) {

            let $table = this.$container.querySelector('.table-unclosed-entrust');
            table = this.table4UnclosedEntrust = new SmartTable($table, this.identifyChild, this);
        }
        
        let entrusts = task.entrusts.map(ent => this.helper.deepClone(ent));
        table.refill(entrusts);
        table.exportAllRecords(`篮子算法剩余委托-${new Date().format('yyyy-MM-dd')}`);
    }

    /**
     * @param {BasketMotherTask} data 
     */
    isTaskRunning(data) {
        return data.status == this.taskStatus.running;
    }

    /**
     * @param {BasketMotherTask} data 
     */
    isTaskStopped(data) {
        return data.status == this.taskStatus.completed || data.status == this.taskStatus.canceled;
    }

    /**
     * @param {BasketMotherTask} data 
     */
    hasNotCompleted(data) {
        return data.totalTarget > 0 && data.totalTraded < data.totalTarget;
    }

    /**
     * @param {BasketMotherTask} task 
     */
    async stopm(task) {

        let resp = await repoAlgo.cancelOrder([task.taskId]);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess('母单已撤单');
            task.status = this.taskStatus.canceled;
            this.tmother.selectRow(this.identifyMother(task));
        }
        else {
            this.interaction.showError(`任务撤单错误：${errorCode}/${errorMsg}`);
        }
    }

    isPreview() {
        return this.states.tab == this.tabs.preview.value;
    }

    isTask() {
        return this.states.tab == this.tabs.task.value;
    }

    tabChanged() {

        this.resetTables();

        if (this.isTask()) {
            this.requestMothers();
        }
        else if (this.cached.mtasks.length > 0) {

            let first = this.cached.mtasks[0];
            this.tmother.refill(this.cached.mtasks);
            this.tmother.selectRow(this.identifyMother(first));
        }
    }

    resetTables() {
        
        if (this.isPreview()) {

            this.tmother.hideColumns(this.mcolumns);
            this.tchild.hideColumns(this.childCols.task);
            this.tchild.showColumns(this.childCols.preview);
        }
        else {

            this.tmother.showColumns(this.mcolumns);
            this.tchild.hideColumns(this.childCols.preview);
            this.tchild.showColumns(this.childCols.task);
        }

        this.tmother.clear();
        this.tmother.fitColumnWidth();
        this.tchild.clear();
        this.tchild.fitColumnWidth();
    }

    setupTabView() {

        this.tabs = {
            preview: { label: '预览', value: 'preview' },
            task: { label: '任务', value: 'task' },
        };

        this.states = { tab: this.tabs.preview.value };
        this.tabApp = new Vue({
            
            el: this.$tab,
            data: {
                tabs: this.tabs,
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.tabChanged,
                this.isTask,
                this.refreshTask,
            ]),
        });
    }

    listen2Events() {

        this.registerEvent('to-preview', this.handlePreviewRequest.bind(this));
        this.registerEvent('reload-basket-tasks', () => {
            // todo24
        });
    }

    /**
     * @param {BasketMotherTask} record 
     */
    identifyMother(record) {
        return record.taskId;
    }

    /**
     * @param {BasketMotherTask} record 
     */
    handleRowSelect(record) {
        this.requestChildren(record);
    }

    createMotherTable() {

        this.tmother = new SmartTable(this.$mother, this.identifyMother, this, {

            tableName: 'smt-hg2b', 
            displayName: '篮子母单',
            rowSelected: this.handleRowSelect.bind(this),
        });

        this.tmother.setPageSize(9999);
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    identifyChild(record) {
        return record.id;
    }

    createChildTable() {

        this.tchild = new SmartTable(this.$child, this.identifyChild, this, { tableName: 'smt-vb28', displayName: '篮子子单' });
        this.tchild.setPageSize(9999);
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    formatCodeName(record) {
        return `${record.instrument} / ${record.instrumentName}`;
    }

    /**
     * @param {BasketMotherTask} record 
     */
    formatMotherStatus(record) {
        return `<span class="${this.isTaskRunning(record) ? 's-color-green' : 's-color-red'}">${this.formatTaskStatus(record)}</span>`;
    }

    /**
     * @param {BasketMotherTask} record 
     */
    formatMotherProgress(record) {

        let { totalTarget, totalTraded } = record;
        return totalTarget > 0 ? (totalTraded * 100 / totalTarget).toFixed(2) + '%' : '';
    }

    /**
     * @param {BasketMotherTask} record 
     */
    formatMoney(record) {

        let money = (record.algoParam || {}).money;
        return typeof money == 'number' ? NumberMixin.methods.thousandsInt(money) : '';
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    formatChildProgress(record) {

        let method = NumberMixin.methods.thousandsInt;
        return `${method(record.tradedVolume)} / ${method(record.volume)}`;
    }

    /**
     * @param {BasketMotherTask} task 
     */
    formatTaskStatus(task) {
        return this.isTaskRunning(task) ? '运行中' : '已停止';
    }

    /**
     * @param {BasketMotherTask} task 
     */
    formatTaskProgress(task) {

        let { totalTarget, totalTraded } = task;
        return (totalTarget > 0 ? totalTraded * 100 / totalTarget : 0).toFixed(2) + '%';
    }

    /**
     * @param {BasketMotherTask} task 
     */
    formatTaskMoney(task) {
        return (task.algoParam || {}).money || '--';
    }

    /**
     * @param {AlgoEntrustInfo} data 
     */
    isEntrustStopped(data) {
        return data.algorithmStatus == this.taskStatus.completed || data.algorithmStatus == this.taskStatus.canceled;
    }

    /**
     * @param {BasketMotherTask} record 
     */
    formatMotherActions(record) {

        let buttons = [];

        if (this.isTaskRunning(record)) {

            buttons.push(`<button class="danger" event.onclick="stopm">
                            <i class="el-icon-video-pause"></i>
                            <span>停止</span>
                        </button>`);
        }

        if (this.isTaskStopped(record) && this.hasNotCompleted(record)) {

            buttons.push(`<button class="danger" event.onclick="exportUnclosedEntrusts">
                            <i class="el-icon-download"></i>
                            <span>导出剩余量</span>
                        </button>`);
        }

        return buttons.length > 0 ? buttons.join('') : '';
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    formatChildActions(record) {
        return this.isEntrustStopped(record) ? '<span>已停止</span>' : '<button class="danger" event.onclick="stopc">停止</button>';
    }

    /**
     * @param {AlgoEntrustInfo} record 
     */
    async stopc(record) {

        let resp = await repoAlgo.cancelOrder([record.id]);
        let { errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.tchild.updateRow({ id: record.id, algorithmStatus: this.taskStatus.canceled });
            this.interaction.showSuccess(`${record.instrument} / ${record.instrumentName}，委托已撤单`);
        }
        else {
            this.interaction.showError(`任务撤单错误：${errorCode}/${errorMsg}`);
        }
    }

    refreshTask() {
        this.requestMothers();
    }

    /**
     * 预览（篮子算法母单）
     * @param {*} queryd
     */
    handlePreviewRequest(queryd) {

        this.states.tab = this.tabs.preview.value;
        this.requestMothers(queryd);
    }

    /**
     * 预览（篮子算法母单）
     * @param {*} queryd
     */
    async requestMothers(queryd) {
        
        let resp = this.isPreview() ? await repoBasket.previewMothers(queryd) : await repoBasket.queryMothers();
        let { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {

            this.interaction.showError(`篮子下单预览失败：${errorCode}/${errorMsg}`);
            return;
        }
        
        this.interaction.showSuccess('母单已刷新');
        let { taskMap } = data;
        let tasks = Object.values(taskMap || {}).map(x => new BasketMotherTask(x));
        this.tmother.refill(tasks);

        if (tasks.length > 0) {

            let first = tasks[0];
            this.tmother.selectRow(this.identifyMother(first));
        }
        else {
            this.tchild.clear();
        }

        /**
         * 缓存当次预览母单
         */

        if (this.isPreview()) {
            this.cached.mtasks = tasks;
        }
    }

    /**
     * @param {BasketMotherTask} record 
     */
    async requestChildren(record) {
        
        let resp = this.isPreview() ? await repoBasket.previewChilds(record.taskId) : await repoBasket.queryChildTasks(record.taskId);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {

            this.tchild.clear();
            this.interaction.showError(`篮子任务预览失败：${errorCode}/${errorMsg}`);
            return;
        }

        let tasks = data instanceof Array ? data.map(x => new BasketChildTask(x)) : [];
        if (this.isPreview()) {
            tasks.forEach((tsk, tsk_idx) => { tsk.id = tsk_idx + 1; });
        }
        
        this.tchild.refill(tasks);
    }

    build($container) {

        super.build($container);
        this.helper.extend(this, ColumnCommonFunc);
        this.$tab = this.$container.querySelector('.tab-row');
        this.$mother = this.$container.querySelector('.mother-list');
        this.$child = this.$container.querySelector('.child-list');

        this.setupTabView();
        this.createMotherTable();
        this.createChildTable();
        this.listen2Events();
        this.resetTables();
    }
}

module.exports = View;