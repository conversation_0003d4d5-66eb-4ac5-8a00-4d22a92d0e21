import Utils from '../modules/utils';
import { AxiosXHR } from '../modules/http-assit';
import { HttpResponseData, StandardTick } from '../types/common';
import { InstrumentRepo } from './instrument';

/**
 * 通过HTTP方式模拟的行情订阅者（仅支持标准TICK）
 */
const HttpSubscribeMap: { [instrument: string]: any } = {};
const InsRepo = new InstrumentRepo();

type TickDataHandler = (resp: HttpResponseData<StandardTick[]>) => void;

/**
 * HTTP方式模拟的行情订阅者（仅支持标准TICK）
 */
export class TickHttpHelper {
  private handler: TickDataHandler;

  constructor(handler: TickDataHandler) {
    this.handler = handler;
  }

  /**
   * WEBSOCKET通信的行情服务器暂未就绪，使用HTTP方式替代订阅行情
   */
  Subscribe(instrument: string) {
    this.Unsubscribe(instrument);
    HttpSubscribeMap[instrument] = Utils.safeSetInterval(1000 * 3, async () => {
      await this.Request(instrument);
    });
    this.Request(instrument);
  }

  /**
   * WEBSOCKET通信的行情服务器暂未就绪，使用HTTP方式替代退订行情
   */
  Unsubscribe(instrument: string) {
    clearInterval(HttpSubscribeMap[instrument]);
  }

  async Request(instrument: string) {
    const resp = await InsRepo.RequestLastTick(instrument);
    this.handler(resp);
  }
}
