/**
 * 软件运行环境
 */
export enum RuntimeEnvironment {

    /** 网页 */
    Web = 1,
    /** 网页 */
    WebSocket = 2,
    /** 客户端 */
    Native = 3,
}

/**
 * 日志级别枚举
 * 级别从低到高依次为：ALL < TRACE < DEBUG < INFO < WARN < ERROR < FATAL < OFF
 */
export enum LogLevel {
    
    /**
     * 最低级别，表示记录所有日志消息（包括自定义级别）。
     */
    ALL = 0,
  
    /**
     * 比 DEBUG 更详细的调试信息，通常用于追踪程序执行的每一步。
     */
    TRACE = 1,
  
    /**
     * 调试信息，主要用于开发阶段排查问题。
     */
    DEBUG = 2,
  
    /**
     * 一般信息，表示应用程序正常运行时的关键事件。
     */
    INFO = 3,
  
    /**
     * 警告信息，表示可能有问题但不会影响程序继续运行。
     */
    WARN = 4,
  
    /**
     * 错误信息，表示发生了错误，但程序仍然可以继续运行。
     */
    ERROR = 5,
  
    /**
     * 致命错误信息，表示非常严重的错误，可能导致程序崩溃。
     */
    FATAL = 6,
  
    /**
     * 最高级别，表示关闭所有日志记录。
     */
    OFF = 7,
}

/**
 * Socket服务器种类
 */
export const SocketServerType = {
    
    TradeServer: 'Trade-Server', 
    QuoteServer: 'Quote-Server',
};

/**
 * 客户端与服务器往来的数据包，消息BODY数据类型
 */
export enum MessageDataType {

    /**
     * 缺省默认
     */
    defaulted = 0,

    /**
     * 字符串
     */
    text = 1,

    /**
     * JSON对象
     */
    json = 2,

    /**
     * 二进制数据
     */
    buffer = 3,

    /**
     * 交易服务器登录消息
     */
    quoteServerLogon = 5,

    /**
     * 交易服务器登录消息
     */
    tradeServerLogon = 6,
}