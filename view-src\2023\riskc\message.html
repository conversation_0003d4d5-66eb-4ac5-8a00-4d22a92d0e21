<div class="riskc-message-root s-fdc">
    <div class="query-toolbar s-pd-5 s-flex themed-box">

        <el-select v-model="istates.productId" placeholder="请选择产品" @change="handleProductChange" filterable clearable>
            <el-option v-for="(item, item_idx) in funds" :key="item_idx" :label="item.label" :value="item.value"></el-option>
        </el-select>

        <el-select v-model="istates.accountId" placeholder="请选择账号" @change="handleAccountChange" filterable clearable>
            <el-option v-for="(item, item_idx) in accounts" :key="item_idx" :label="formatSelectAccountName(item)" :value="item.value"></el-option>
        </el-select>

        <el-date-picker 
            type="daterange" 
            value-format="yyyy-MM-dd"
            style="width: 300px;"
            :picker-options="states.pickerOptions" 
            v-model="states.date" 
            range-separator="至" 
            start-placeholder="开始日期" 
            end-placeholder="结束日期"
            @change="dateTimechange"
        >
        </el-date-picker>

        <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>

    </div>
    <div class="message-box s-fdc">
        <div class="table-alert">
            <table>
                <tr>
                    <th label="消息ID" prop="id" width="80" overflowt></th>
                    <th label="风控类型" prop="identityType" formatter="formatIdentityType" width="80" overflowt></th>
                    <th label="风控主体" prop="identityName" width="200" overflowt></th>
                    <th label="预警类型" prop="warningType" formatter="formatWarningType" width="60" overflowt></th>
                    <th label="风控信息" prop="content" class="content-cell" width="500" format-as-html overflowt></th>
                    <th label="记录时间" prop="createTime" formatter="formatDateTime" width="100" overflowt></th>
                </tr>
            </table>
        </div>
        <div class="paging-row">
            <el-pagination 
                :page-sizes="pageSizes"
                :page-size.sync="pageSize" 
                :total="total"
                :current-page.sync="pageNo" 
                :layout="layout" 
                @size-change="handlePageSizeChange"
                @current-change="handlePageChange"
            >
            </el-pagination>
        </div>
    </div>
</div>