<template>
  <div class="form-container" overflow-y-auto overflow-x-hidden>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="130px"
      w-full
      mt-10
    >
      <div class="half-col">
        <el-form-item label="过户费（上海）" prop="shTransferFee">
          <el-input-number
            :controls="false"
            :precision="5"
            :min="0"
            :step="0.00001"
            :maxlength="10"
            v-model="formData.shTransferFee"
            placeholder="例如 0.00001"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="过户费（深圳）" prop="szTransferFee">
          <el-input-number
            :controls="false"
            :precision="5"
            :min="0"
            :step="0.00001"
            :maxlength="10"
            v-model="formData.szTransferFee"
            placeholder="例如 0.00001"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-building"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="交易佣金" prop="commission">
          <el-input-number
            :controls="false"
            :precision="2"
            :min="0"
            :step="0.01"
            :maxlength="10"
            v-model="formData.commission"
            placeholder="例如 0.01"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-document-code"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="最低佣金（元）" prop="commissionLeast">
          <el-input-number
            :controls="false"
            :precision="2"
            :min="0"
            :step="0.01"
            :maxlength="10"
            v-model="formData.commissionLeast"
            placeholder="例如 5.0"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-document-code"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="其他费用（上海）" prop="shOther">
          <el-input-number
            :controls="false"
            :precision="3"
            :min="0"
            :step="0.001"
            :maxlength="10"
            v-model="formData.shOther"
            placeholder="例如 0.001"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="其他费用（深圳）" prop="szOther">
          <el-input-number
            :controls="false"
            :precision="3"
            :min="0"
            :step="0.001"
            :maxlength="10"
            v-model="formData.szOther"
            placeholder="例如 0.001"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="印花税" prop="stampDuty">
          <el-input-number
            :controls="false"
            :precision="3"
            :min="0"
            :step="0.001"
            :maxlength="10"
            v-model="formData.stampDuty"
            placeholder="例如 0.001"
            clearable
            disabled-scientific
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
    </el-form>
  </div>
  <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button type="primary" @click="check" w-220>确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue';
import { isJson, isNone } from '@/script';
import { ElMessage } from 'element-plus';
import { Repos, type AccountFeeInfo } from '../../../../xtrade-sdk/dist';

const repoInstance = new Repos.GovernanceRepo();
const formRef = useTemplateRef('formRef');
const formData = ref<AccountFeeInfo>({} as any);
const context = { accountId: null as any, accountName: null as any };

const rules = {
  stampDuty: [{ required: true, message: '请输入印花税' }],
  shTransferFee: [{ required: true, message: '请输入过户费（上海）' }],
  szTransferFee: [{ required: true, message: '请输入过户费（深圳）' }],
  commission: [{ required: true, message: '请输入交易佣金' }],
  commissionLeast: [{ required: true, message: '请输入最低佣金' }],
  shOther: [{ required: true, message: '请输入其他费用（上海）' }],
  szOther: [{ required: true, message: '请输入其他费用（深圳）' }],
};

const emitter = defineEmits<{
  cancel: [];
  save: [];
}>();

const cancel = () => {
  emitter('cancel');
  formRef.value?.clearValidate();
};

const check = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      save();
    }
  });
};

async function save() {
  const obj = formData.value!;
  const isCreate = isNone(obj.id);
  const { errorCode, errorMsg } = isCreate
    ? await repoInstance.CreateAccountFee(obj)
    : await repoInstance.UpdateAccountFee(obj);
  if (errorCode === 0) {
    ElMessage.success('操作成功');
    emitter('save');
  } else {
    ElMessage.error(errorMsg || '操作失败');
  }
}

async function request() {
  const { errorCode, errorMsg, data } = await repoInstance.QueryAccountFee(context.accountId);
  if (errorCode === 0 && data && isJson(data)) {
    formData.value = data;
  } else {
    formData.value = {
      id: null as any,
      accountId: context.accountId,
      accountName: context.accountName,
      commission: 0,
      commissionLeast: 0,
      shOther: 0,
      shTransferFee: 0,
      stampDuty: 0,
      szOther: 0,
      szTransferFee: 0,
      createTime: 0,
      updateTime: 0,
    };
    // ElMessage.error(errorMsg || '查询已有费用设置出错');
  }
}

function reset(accountId: string, accountName: string) {
  context.accountId = accountId;
  context.accountName = accountName;
  request();
}

defineExpose({
  reset,
});

onMounted(() => {
  //
});
</script>

<style scoped>
.form-container {
  .half-col {
    float: left;
    width: 50%;
  }
  :deep() {
    .el-form-item {
      width: 95%;
      margin-right: 10px !important;
    }

    .el-form-item__label {
      position: relative;
    }

    .el-input-number {
      width: 100%;
    }
  }
}
</style>
