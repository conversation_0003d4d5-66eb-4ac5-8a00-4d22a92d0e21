const httpRequest = require('../libs/http').http;

class ApplyRepository {

    /**
     * 账号申购信息
     * @returns {Promise<unknown>}
     */
    getAccountIpoInfo() {
        return new Promise((resolve, reject) => {
            httpRequest.get('../v4/ipo/account').then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    /**
     * 账号申购额度
     * @param broker
     * @returns {Promise<unknown>}
     */
    getAccountQuota() {
        return new Promise((resolve, reject) => {
            httpRequest.get('../v4/ipo/account/quota').then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    apply(accounts) {
        return new Promise((resolve, reject) => {
            httpRequest.post('../v4/ipo/purchase', accounts).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }


}

module.exports = { repoApply: new ApplyRepository() };