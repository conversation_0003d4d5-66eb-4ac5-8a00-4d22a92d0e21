const { CreditBaseView } = require('./credit-base');
const { repoCredit } = require('../../../../repository/credit');

class View extends CreditBaseView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '两融负债');
    }

    async requestCreditData(criteria) {
        return await repoCredit.getTargetInfo(criteria);
    }

    build($container, options) {
        super.build($container, options, { isDebted: true }, 'table-credit-debt');
    }
}

module.exports = View;