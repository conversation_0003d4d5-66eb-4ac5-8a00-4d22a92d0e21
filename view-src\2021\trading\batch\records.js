const IView = require('../../../../component/iview').IView;
const Tab = require('../../../../component/tab').Tab;
const TabList = require('../../../../component/tab-list').TabList;
const { TradeChannel } = require('../../model/message');

class View extends IView {

    /**
     * @param {*} view_name 
     * @param {*} is_standalone_window 
     * @param {TradeChannel} defaultChannel 
     */
    constructor(view_name, is_standalone_window, defaultChannel) {

        super(view_name, is_standalone_window, '批量交易数据');
        this.setAsChannel(defaultChannel);
    }

    createDataViews() {

        var $tab = this.$container.querySelector('.category-tab');
        var $content = this.$container.querySelector('.data-views');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/fragment/entrust-preview', '预览', { isBatch: true });
        tabs.openTab(true, '@2021/trading/batch/user-orders', '委托');
        tabs.openTab(true, '@2021/trading/batch/user-positions', '持仓');
        tabs.openTab(true, '@2021/trading/batch/user-exchanges', '成交');
        tabs.openTab(true, '@2021/fragment/credit/contract', '两融合约');
        tabs.openTab(true, '@2021/fragment/credit/debt', '两融标的');

        this.tabPreview = tabs.tabs[0];
        this.tabCreditContract = tabs.tabs[4];
        this.tabCreditDebt = tabs.tabs[5];
        this.tabList = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        /**
         * 交易数据的各个视图，默认为延迟加载，故：当TAB主体被实际构建时，通知一次渠道变化
         */
        
        tab.viewEngine.trigger('set-channel', this.currentChannel);
        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    /**
     * @param {Tab} tab 
     */
     handleTabFocused(tab) {
        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    /**
     * 设置为当前的交易渠道
     * @param {TradeChannel} channel 
     */
    setAsChannel(channel) {
        this.currentChannel = channel;
    }

    /**
     * 将渠道切换事件，通知到交易数据的各个视图
     * @param {TradeChannel} channel 
     */
    showProperTabs(channel) {
        
        var tabCtr = this.tabList;

        if (channel.options.isCredit) {

            tabCtr.show(this.tabCreditContract);
            tabCtr.show(this.tabCreditDebt);
        }
        else {

            if (tabCtr.isFocused(this.tabCreditContract) || tabCtr.isFocused(this.tabCreditDebt)) {
                tabCtr.setFocus(this.tabPreview);
            }

            tabCtr.hide(this.tabCreditContract);
            tabCtr.hide(this.tabCreditDebt);
        }
    }

    listen2Events() {

        var EventSetChannel = 'set-channel';
        var EventSetPreview = 'set-as-order-preview';
        var EventSetInstru = 'set-as-instrument';
        var EventAccountSelected = 'account-row-selected';
        var EventBatchOrderMade = 'batch-order-made';
        var tabCtr = this.tabList;
        var viewPreview = tabCtr.tabs[0].viewEngine;
        var viewOrder = tabCtr.tabs[1].viewEngine;
        var viewPosition = tabCtr.tabs[2].viewEngine;
        var viewExchange = tabCtr.tabs[3].viewEngine;

        this.registerEvent(EventSetChannel, (channel) => {

            this.setAsChannel(channel);

            /**
             * 将渠道切换事件，通知到交易数据的各个视图
             */

            viewPreview.trigger(EventSetChannel, channel);
            viewOrder.trigger(EventSetChannel, channel);
            viewPosition.trigger(EventSetChannel, channel);
            viewExchange.trigger(EventSetChannel, channel);

            /**
             * 根据交易渠道，动态调整需要展示的数据视图
             */

            this.showProperTabs(channel);
        });

        this.registerEvent(EventSetPreview, (...args) => { viewPreview.trigger(EventSetPreview, ...args); });
        this.registerEvent(EventSetInstru, (...args) => { viewPreview.trigger(EventSetInstru, ...args); });
        this.registerEvent(EventAccountSelected, (...args) => { viewPreview.trigger(EventAccountSelected, ...args); });
        this.registerEvent(EventBatchOrderMade, _ => { viewPosition.trigger(EventBatchOrderMade); });

        /** 监听窗口尺寸调整 */
        this.lisen2WinSizeChange((width, height, isMaximized) => {

            this.tabList.fireEventOnFocusedTab('table-max-height', height - (isMaximized ? 564 : 549));
            this.tabList.fireEventOnFocusedTab('table-scroll-2-left');
        });
    }

    build($container) {

        super.build($container);
        this.createDataViews();
        this.showProperTabs(this.currentChannel);
        this.listen2Events();
    }
}

module.exports = View;