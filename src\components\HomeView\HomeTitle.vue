<script setup lang="ts">
const emit = defineEmits<{
  toggle: [];
}>();

function toggleShowMenu() {
  emit('toggle');
}
</script>

<template>
  <div class="home-toolkit" flex aic gap-8>
    <img src="../../assets/image/logo.svg" w-30 h-30 />
    <label fs-20 fw-600 lh-14>高御科技量化交易平台</label>
    <div
      class="toggle-button"
      flex
      aic
      jcc
      w-50
      h-32
      rd-6
      bg="[--g-block-bg-1]"
      @click="toggleShowMenu"
    >
      <i class="iconfont icon-list" thb1 fs-18></i>
    </div>
  </div>
</template>

<style scoped>
.home-toolkit {
  .toggle-button {
    cursor: pointer;
    &:hover {
      background-color: var(--g-bg-hover-1);
    }
  }
}
</style>
