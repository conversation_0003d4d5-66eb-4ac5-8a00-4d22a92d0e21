<script setup lang="ts">
import { ref } from 'vue';
import { type MomMenuTree } from '../../../../xtrade-sdk/dist';

const { activeMenu, menus } = defineProps<{
  activeMenu: MomMenuTree | null;
  menus: MomMenuTree[];
}>();

const emit = defineEmits<{
  clickMenu: [item: MomMenuTree];
}>();

const defaultOpeneds: string[] = [];
const getMenuNames = (items: MomMenuTree[]) => {
  items.forEach(item => {
    defaultOpeneds.push(item.menuName);
    if (item.children?.length) {
      getMenuNames(item.children);
    }
  });
};
getMenuNames(menus);

const collapse = ref(false);

const handleClickMenu = (item: MomMenuTree) => {
  emit('clickMenu', item);
};
</script>

<template>
  <div class="home-menu" bg="[--g-bg]" h-full py-20>
    <el-scrollbar>
      <div v-for="(group, group_idx) in menus" :key="group_idx" class="group-box">
        <div class="group-title c-[--g-text-color-1]" px-40 fs-14 fw-400 lh-16>
          {{ group.menuName }}
        </div>
        <el-menu
          w-200
          class="el-menu-home"
          :default-active="activeMenu?.menuRoute"
          :default-openeds="defaultOpeneds"
          :collapse="collapse"
        >
          <template v-for="item in group.children">
            <el-sub-menu v-if="item.children" :key="item.menuName" :index="item.menuName">
              <template #title>
                <div flex aic gap-8>
                  <i fs-14 block pr-5 pt-2 class="iconfont" :class="`icon-${item.menuIcon}`"></i>
                  {{ item.menuName }}
                </div>
              </template>
              <el-menu-item
                v-for="sub in item.children"
                :key="sub.menuRoute"
                :index="sub.menuRoute"
                @click="handleClickMenu(sub)"
              >
                {{ sub.menuName }}
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item
              v-else
              :key="item.menuRoute"
              :index="item.menuRoute"
              @click="handleClickMenu(item)"
            >
              <div flex aic gap-8>
                <i fs-14 block pr-5 pt-2 class="iconfont" :class="`icon-${item.menuIcon}`"></i>
                {{ item.menuName }}
              </div>
            </el-menu-item>
          </template>
        </el-menu>
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped>
.home-menu {
  width: 288px;
  .group-box {
    &:not(:first-child) {
      .group-title {
        margin-top: 48px;
      }
    }
  }

  :deep() {
    .el-menu-home {
      width: 100%;
      --el-menu-base-level-padding: 24px;
    }
    .el-menu {
      .el-menu-item {
        margin-top: 16px;
        padding-left: 40px;
        padding-right: 40px;
        font-size: 16px;
        font-weight: 600px;

        .iconfont {
          position: relative;
          top: -2px;
          font-size: 24px;
        }
      }
    }
  }
}
</style>
