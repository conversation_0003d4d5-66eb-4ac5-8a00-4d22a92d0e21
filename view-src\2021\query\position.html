<div>

    <div class="channel-container"></div>
    <div class="user-toolbar themed-box"></div>

    <div class="data-list">
		<table>
            <tr>
                <th label="交易日" 
                    fixed-width="90" 
                    prop="tradingDay" sortable></th>
                    
                <th label="代码" 
                    fixed-width="100" 
                    prop="instrument" overflowt sortable searchable></th>

                <th label="名称" 
                    fixed-width="80" 
                    prop="instrumentName" overflowt sortable searchable></th>

                <th label="产品" 
                    min-width="150" 
                    prop="fundName" sortable overflowt searchable></th>

                <th label="账号名称" 
                    min-width="202.0" 
                    prop="accountName" 
					formatter="formatAccountName" overflowt sortable searchable></th>

                <th label="方向" 
                    fixed-width="70" 
                    prop="direction"
                    formatter="formatDirection" 
                    export-formatter="formatDirectionText" sortable></th>

                <th label="总仓" 
                    fixed-width="80" 
                    prop="totalPosition" 
                    align="right" sortable thousands-int></th>

                <th label="昨仓" 
                    fixed-width="80"
                    prop="yesterdayPosition" 
                    align="right" sortable thousands-int></th>

                <th label="今仓" 
                    fixed-width="80" 
                    prop="todayPosition" 
                    align="right" sortable thousands-int></th>

                <th label="持仓市值" 
                    min-width="80" 
                    prop="marketValue" 
                    align="right" sortable thousands></th>

                <th label="盈亏"
                    min-width="80" 
                    prop="profit" 
                    align="right" 
                    class-maker="makeBenefitClass"
                    footer-class-maker="makeBenefitClass" overflowt thousands sortable summarizable></th>

                <th label="平仓盈亏" 
                    min-width="80" 
                    prop="closeProfit" 
                    align="right" 
                    class-maker="makeBenefitClass"
                    footer-class-maker="makeBenefitClass" overflowt thousands sortable summarizable></th>

                <th label="持仓均价" 
                    fixed-width="80" 
                    prop="avgPrice" 
                    align="right" 
                    formatter="formatPrice"></th>

                <th label="手续费" 
                    min-width="80" 
                    prop="usedCommission" 
                    align="right" overflowt thousands sortable summarizable></th>

                <th label="保证金" 
                    min-width="80" 
                    prop="usedMargin" 
                    align="right" sortable overflowt thousands summarizable></th>

                <th label="资产类型" 
                    fixed-width="100" 
                    prop="assetType"
                    formatter="formatAssetType" sortable></th>

            </tr>

        </table>

    </div>

    <div class="user-footer themed-box">

        <el-pagination class="s-pull-right"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="currentPage" 
                       :page-sizes="pageSizes" 
                       :page-size="pageSize"
                       :total="total"
                       layout="prev, pager, next, sizes, total">
        </el-pagination>
    </div>
</div>