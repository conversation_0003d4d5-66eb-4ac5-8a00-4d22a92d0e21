<script setup lang="tsx">
import VirtualizedTable from './VirtualizedTable.vue';
import { shallowRef, computed, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition } from '@/types';
import { AdminService } from '@/api';
import {
  assetTypeCol,
  accountNameCol,
  brokerNameCol,
} from './ComponentTabs/shared/columnDefinitions';
import {
  type LegacyAccountInfo,
  type MomTerminal,
  type MomBroker,
} from '../../../../xtrade-sdk/dist';

type DialogType = 'terminal' | 'broker';

const { type, entity } = defineProps<{
  type: DialogType;
  entity?: MomTerminal | MomBroker;
}>();

const visible = defineModel<boolean>();

// 列定义
const columns = [
  assetTypeCol,
  accountNameCol,
  brokerNameCol,
  {
    key: 'connectionStatus',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return (
        <span class={cellData ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]'}>
          {cellData ? '在线' : '离线'}
        </span>
      );
    },
  },
  {
    key: 'id',
    title: '连接成功时间(无字段)',
    width: 180,
    sortable: true,
    cellRenderer: ({ rowData }) => {
      return <span>--</span>;
    },
  },
] as ColumnDefinition<LegacyAccountInfo>;

const records = shallowRef<LegacyAccountInfo[]>([]);

// 对话框标题
const dialogTitle = computed(() => {
  if (!entity) return '账号列表';

  if (type === 'terminal') {
    const terminal = entity as MomTerminal;
    return `${terminal.terminalName} - 账号列表`;
  } else {
    const broker = entity as MomBroker;
    return `${broker.brokerName} - 账号列表`;
  }
});

// 监听visible变化，加载数据
watch(visible, async val => {
  if (val && entity) {
    await loadAccounts();
  }
});

// 加载账号数据
async function loadAccounts() {
  if (!entity) return;

  try {
    if (type === 'terminal') {
      const terminal = entity as MomTerminal;
      records.value = await AdminService.getTerminalAccounts(terminal.id);
    } else {
      const broker = entity as MomBroker;
      records.value = await AdminService.getBrokerAccounts(broker.id);
    }
  } catch (error) {
    console.error('获取账号列表失败:', error);
    records.value = [];
  }
}

// 关闭对话框
function handleClose() {
  visible.value = false;
}
</script>

<template>
  <el-dialog
    destroy-on-close
    draggable
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    @close="handleClose"
  >
    <div h-500>
      <VirtualizedTable
        :sort="{ key: 'connectTime', order: TableV2SortOrder.DESC }"
        :columns="columns"
        :data="records"
        fixed
      />
    </div>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
