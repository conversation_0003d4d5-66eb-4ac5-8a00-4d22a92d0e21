.basket-root {

	height: 100%;
	display: flex;
	flex-direction: column;

	.basket-trading-channel {

		height: 24px;
		flex: 0 0;
	}

	.basket-user-input {
	
		box-sizing: border-box;
		flex-grow: 0;
		flex-shrink: 0;
		height: 386.1px;
		padding: 2px 0;
		overflow: hidden;
	
		> table.layout-table {
	
			table-layout: fixed;
			width: 100%;
			height: 100%;
	
			td.layout-cell {
				
				padding: 0;
				vertical-align: top;
	
				> * {
					height: 381.1px;
				}
			}
		}
	}

	.data-records {
		
		height: 100px;
		overflow: hidden;
		flex-shrink: 0;
		flex-grow: 1;
	}
}
