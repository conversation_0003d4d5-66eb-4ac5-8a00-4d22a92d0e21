﻿/**
 * 产品/策略/账号 > 数据服务
 */

const SnappyJS = require('snappyjs');
const electron = require('electron');
const { BrowserWindow } = electron;
const { ServerEnvMainModule } = require('./main-module');
const { ServerBatchData } = require('../model/server-batch-data');
const { ContextObjectInfo } = require('../model/context-object-info');

/**
 * 订阅交易数据的订阅者（一个窗口可对应多个视图）
 */
class Subscriber {

    /**
     * @param {*} windowId 来源窗口ID
     * @param {*} viewId 来源视图ID
     * @param {*} identityId 数据订阅主体
     */
    constructor(windowId, viewId, identityId) {

        this.windowId = windowId;
        this.viewId = viewId;
        
        /**
         * 该订阅者针对的主体ID（作反向映射用）
         */
        this.belongId = identityId;
    }
}

class AccountServiceModule extends ServerEnvMainModule {

    /**
     * 除开产品、策略层面的订阅，归属于用户自己的默认订阅（订阅者identity id）
     * 1. 作为get属性，而不作为构造函数数据成员，防止代码初始加载时，用户信息尚未设置
     */
    get UserAsIdentityId() {
        return this._uid || (this._uid = `user:${this.userInfo.userId}`);
    }

    /**
     * @param {String} module_name 
     */
    constructor(module_name) {

        super(module_name);

        /**
         * 实时变化数据订阅者map
         */
        this.bigMap = {};

        /**
         * 全量（增量）批次数据订阅者map
         */
        this.bigMap2 = {};

        /**
         * 状态字典
         */
        this.states = {};
    }

    /**
     * 使用主体id，生成map key
     */
    makeMapKey(identityId) {
        return `identity.${identityId}`;
    }

    /**
     * @returns {Array<Subscriber>}
     */
    ensureGet(key) {
        return this.bigMap[key] || (this.bigMap[key] = []);
    }

    /**
     * 注册，实时数据订阅者
     */
    register(win_id, view_id, identity_id) {

        let matched_set = this.ensureGet(this.makeMapKey(identity_id));
        let matched = matched_set.find(item => item.windowId === win_id && item.viewId === view_id);
        let not_registered = matched === undefined;

        if (not_registered) {
            matched_set.push(new Subscriber(win_id, view_id, identity_id));
        }
    }

    /**
     * 在最小粒度级别（视图级别），进行订阅
     * @param {*} event
     * @param {Number} win_id 物理窗口ID
     * @param {String} view_id 窗口内视图ID
     * @param {Number|String} identity_id 基金ID | 策略ID | 账号ID（无论何种ID，彼此之间，业务上不会发生重复）
     * @param {Array<Number>} data_types 需要订阅的数据变化种类（可选种类有：订单、持仓、成交）
     */
    subscribe(event, win_id, view_id, identity_id, data_types) {

        /**
         * 订阅账号数据变化必须显示提供要订阅的数据种类
         */

        if (!Array.isArray(data_types) || data_types.length == 0) {
            return;
        }

        var event_ord_change = this.serverEvent.orderChanged;
        var event_pos_change = this.serverEvent.positionChanged;
        var event_exch_change = this.serverEvent.exchangeChanged;

        /**
         * 仅对交易服务器开启唯一（订单变化）监听入口
         */
        if (this.hasListen2OrderChange === undefined && data_types.includes(event_ord_change)) {

            this.hasListen2OrderChange = true;
            this.tradingServer.listen2Event(event_ord_change, (message) => {
                this.handleRealtimeChange(event_ord_change, message, true);
            });
        }

        /**
         * 仅对交易服务器开启唯一（持仓变化）监听入口
         */
        if (this.hasListen2PositionChange === undefined && data_types.includes(event_pos_change)) {

            this.hasListen2PositionChange = true;
            this.tradingServer.listen2Event(event_pos_change, (message) => {
                this.handleRealtimeChange(event_pos_change, message, false);
            });
        }

        /**
         * 仅对交易服务器开启唯一（成交变化）监听入口
         */
        if (this.hasListen2ExchangeChange === undefined && data_types.includes(event_exch_change)) {

            this.hasListen2ExchangeChange = true;
            this.tradingServer.listen2Event(event_exch_change, (message) => {
                this.handleRealtimeChange(event_exch_change, message, true);
            });
        }

        /**
         * 1. 当未提供主体ID时，将默认为订阅本人相关的数据变化
         * 2. 而本人直接相关的数据，无需订阅的情况下自动推送
         * 3. 对本人的订阅，只进行订阅者注册，不发生实际的服务器订阅
         */

        if (this.helper.isNone(identity_id)) {
            this.register(win_id, view_id, this.UserAsIdentityId);
        } 
        else {

            this.register(win_id, view_id, identity_id);
            var message = { fc: this.serverFunction.subAccountChange, reqId: 0, dataType: 1, body: [identity_id] };
            this.tradingServer.send(message);
        }
    }

    /**
     * 获取某一键值下的，注册视图订阅者列表（该列表当中，每一个订阅者，都订阅至同一主体）
     * @returns {Array<Subscriber>}
     */
    readSet(key) {
        return this.bigMap[key];
    }

    /**
     * 删除已记录的订阅，并返回订阅者map
     * @param {*} win_id <required> 物理窗口ID
     * @param {*} view_id <optional>，不指定则代表取消所有来自window id所框定范围的订阅
     * @param {*} identity_id <optional>，不指定则默认为取消window id + view id所框定范围内的全部
     * @returns {Array<Number>} 本次，从完整订阅map中，需要剔除的主体 id 列表（代表其中每一个，都需要退订），返回空集合则无主体需要退订数据
     */
    unregister(win_id, view_id, identity_id) {

        // 是否指定了主体
        var isSpecified = this.helper.isNotNone(identity_id);

        /**
         * 指定了具体的主体
         */

        if (isSpecified) {

            let matched_set = this.ensureGet(this.makeMapKey(identity_id));
            if (matched_set.length == 0) {

                /**
                 * 要退订的主体，没有订阅过或已完全退订，故无需退订
                 */
                return [];
            }

            /**
             * 将订阅该主体 & 限定范围内的视图删除
             */
            matched_set.remove(item => item instanceof Subscriber && item.windowId === win_id && (view_id === null || item.viewId === view_id));

            /**
             * 删除过后，如果已经没有剩下任何视图监听该主体，才执行退订
             */
            if (matched_set.length == 0) {

                /**
                 * 将当前主体加入到待退订列表
                 */
                return [identity_id];
            } 
            else {

                /**
                 * 还有剩下的视图订阅者，不能取消该主体的订阅
                 */
                return [];
            }
        }

        /**
         * 1. 未指定具体主体，则默认取消由window id + view id 所框定的范围内，所有的主体
         * 2. 如view id未提供，则框定范围为window id所确定的所有视图
         */

        /**
         * 待取消订阅的主体名单
         */
        var targetSet = [];

        /**
         * 扫描整个map，确定需要参与退订的主体
         */
        for (let key in this.bigMap) {

            let eachSet = this.readSet(key);
            if (eachSet.length == 0) {
                continue;
            }

            /**
             * 该集合内部，每个订阅者都订阅于，的相同主体ID（取第一个即可）
             */
            let belongId = eachSet[0].belongId;

            /**
             * 将限定范围内的视图删除
             */
            eachSet.remove(item => item instanceof Subscriber && item.windowId === win_id && (view_id === null || item.viewId === view_id));

            /**
             * 如果已经没有任何视图监听当前主体，才执行退订
             */
            if (eachSet.length == 0) {

                /**
                 * 将该主体加入到待取消订阅集合中
                 */
                targetSet.push(belongId);
            }
        }

        return targetSet;
    }

    /**
     * 在最小粒度级别（视图级别），取消订阅
     * @param {*} event
     * @param {Number} win_id 物理窗口ID
     * @param {String} view_id 窗口内视图ID
     * @param {Number|String} identity_id 基金策略账号ID
     * @param {Array<Number>} data_types 需要退订的数据变化种类（订单推送、持仓推送、成交推送）
     */
    unsubscribe(event, win_id, view_id, identity_id, data_types) {

        var target_set = this.unregister(win_id, view_id, identity_id);
        if (target_set.length == 0) {
            return;
        }

        var message = { fc: this.serverFunction.unsubAccountChange, reqId: 0, dataType: 1, body: target_set };
        this.tradingServer.send(message);
    }

    /**
     * 处理账户实时数据推送
     * @param {Number} event_code 服务器数据类型代码
     * @param {Object} message_package 数据包
     * @param {Boolean} is_multiple_brocast 是否多播
     */
    handleRealtimeChange(event_code, message_package, is_multiple_brocast) {

        var data;
        var body = message_package.body;

        try {
            data = JSON.parse(body);
        }
        catch(ex) {

            /**
             * 将产生异常的数据包信息，写入日志文件
             */
            this.loggerSys.fatal(`push data with json parse exception/${event_code}/${JSON.stringify(message_package)}/${body.toString()}`);
            throw ex;
        }

        if (this.helper.isNone(data)) {
            return;
        }

        /**
         * 推送数据，分发到账号维度
         */
        this.feedConsumers(event_code.toString(), data.identityId, data);

        /**
         * remark：以下代码，为原有分发逻辑，新版本设计已调整（2021-03-10 by Steven）
         */

        // var event_code_str = event_code.toString();
        // var is_with_strategy = this.helper.isNotNone(data.strategyId);

        // if (is_multiple_brocast) {

        //     this.feedConsumers(event_code_str, data.fundId, data);
        //     if (is_with_strategy) {
        //         this.feedConsumers(event_code_str, data.strategyId, data);
        //     }
        // }
        // else {

        //     if (is_with_strategy) {
        //         this.feedConsumers(event_code_str, data.strategyId, data);
        //     }
        //     else {
        //         this.feedConsumers(event_code_str, data.fundId, data);
        //     }
        // }
    }

    /**
     * @param {*} server_event 事件代码
     * @param {*} identity_id 订阅该事件的主体ID
     * @param {*} data 推送数据
     */
    feedConsumers(server_event, identity_id, data) {

        /**
         * @param {Array<Subscriber>} subscribers
         * @param {*} data
         */
        function feed(subscribers, data) {

            subscribers.forEach(item => {

                let target_win = BrowserWindow.fromId(item.windowId);
                if (target_win && !target_win.isDestroyed()) {
                    target_win.webContents.send(server_event, item.viewId, data);
                }
            });
        };

        /**
         * 归属于账号层面的订阅
         */

        var subscribers = this.ensureGet(this.makeMapKey(identity_id));
        if (subscribers.length > 0) {
            feed(subscribers, data);
        }

        /**
         * 归属于用户层面的订阅
         */

        var user_subscribers = this.ensureGet(this.makeMapKey(this.UserAsIdentityId));
        if (user_subscribers.length > 0) {
            feed(user_subscribers, data);
        }
    }

    makeMapKey2(identity_id, fun_code) {
        return `${identity_id}.${fun_code}`;
    }

    /**
     * @returns {Array<Subscriber>}
     */
    ensureGet2(key) {
        return this.bigMap2[key] || (this.bigMap2[key] = []);
    }

    /**
     * 注册全量（增量）批次数据订阅者
     */
    register2(win_id, view_id, identity_id, fun_code) {

        let matched_set = this.ensureGet2(this.makeMapKey2(identity_id, fun_code));
        let matched = matched_set.find(item => item.windowId === win_id && item.viewId === view_id);
        let not_registered = matched === undefined;

        if (not_registered) {
            matched_set.push(new Subscriber(win_id, view_id, identity_id));
        }
    }

    ConsumeBatchPush(fun_code, server_event_code, message_package) {

        try {

            let body = message_package.body;
            let decompressed_body = SnappyJS.uncompress(body);
            let wrapper = JSON.parse(decompressed_body);
            let sbdata = new ServerBatchData(wrapper);
            let identity_id = sbdata.identityId;

            if (this.helper.isNotNone(sbdata.parentOrderId)) {
                identity_id = sbdata.parentOrderId;
            } 
            else if (sbdata.accountIds.length > 0) {
                identity_id = sbdata.accountIds[0];
            }

            let subscribers = this.ensureGet2(this.makeMapKey2(identity_id, fun_code));
            subscribers.forEach(subr => {

                let target_win = BrowserWindow.fromId(subr.windowId);
                if (target_win && !target_win.isDestroyed()) {
                    target_win.webContents.send(server_event_code, subr.viewId, sbdata);
                }
            });

            this.logServerPush('completed to push data to client ui', fun_code, server_event_code);

            /**
             * 清除所有，订阅该数据类型，的所有订阅者
             */
            subscribers.clear();
        } 
        catch (ex) {

            this.loggerSys.debug(ex);
            this.loggerConsole.debug(ex);
        }
    }

    /**
     * 统一的方式，处理订单、持仓、成交各个数据请求
     * @param { Number } function_code 请求服务器数据，功能代码
     * @param { Number } event_code 服务器推送数据，事件代码
     * @param { Number } win_id 窗口ID
     * @param { String } view_id 窗口内视图ID
     * @param { ContextObjectInfo } context_obj
     * @param { String } incremental_key
     * @param {*} incremental_value
     */
    handleDataRequest(function_code, event_code, win_id, view_id, context_obj, incremental_key, incremental_value) {

        if (!this.helper.isJson(context_obj)) {

            this.loggerSys.fatal(`data request aborted/${JSON.stringify({ function_code, event_code, win_id, view_id, context_obj, incremental_key, incremental_value })}`);
            return;
        }

        let { isAboutParent, isAboutAccount, identityId, accounts } = context_obj;
        let data_type_key = function_code + '/' + event_code;

        if (this.states[data_type_key] === undefined) {

            this.states[data_type_key] = true;
            this.tradingServer.listen2Event(event_code, (message) => {
                this.ConsumeBatchPush(function_code, event_code.toString(), message);
            });
        }

        this.register2(win_id, view_id, identityId, function_code);

        let body = {
            [incremental_key]: incremental_value,
        };

        if (isAboutParent) {
            body.parentOrderId = identityId;
        }
        else if (isAboutAccount) {

            /**
             * 当前identity为账号口径时，必定有且仅有，一个账号
             */
            // body.accountIds = accounts.length > 0 ? [accounts[0].accountId] : [];
            
            if (accounts.length == 0) {
                body.identityId = null;
            }
            else if (accounts.length == 1) {
                body.identityId = accounts[0].accountId;
            }
            else {
                body.accountIds = accounts.map(x => x.accountId);
            }
        } 
        else {
            body.identityId = identityId;
        }

        this.logClientRequest(function_code, win_id, view_id, context_obj, body);
        this.tradingServer.send({ fc: function_code, reqId: 0, dataType: 1, body: body });
    }

    handleOrderRequest(event, win_id, view_id, context_obj, last_update_time) {
        this.handleDataRequest(this.serverFunction.requestTodayOrder, this.serverEvent.todayOrderPush, win_id, view_id, context_obj, 'updateTime', last_update_time);
    }

    handlePositionRequest(event, win_id, view_id, context_obj, last_update_time) {
        this.handleDataRequest(this.serverFunction.requestTodayPosition, this.serverEvent.todayPositionPush, win_id, view_id, context_obj, 'updateTime', last_update_time);
    }

    handleTradeRecordRequest(event, win_id, view_id, context_obj, latest_record_id) {
        this.handleDataRequest(this.serverFunction.requestTodayTradeRecord, this.serverEvent.todayTradeRecordPush, win_id, view_id, context_obj, 'id', latest_record_id);
    }

    listen2Commands() {

        /**
         * 监听，全量订单数据，一次性消费请求
         */
        this.mainProcess.on(this.serverFunction.requestTodayOrder.toString(), this.handleOrderRequest.bind(this));

        /**
         * 监听，全量持仓数据，一次性消费请求
         */
        this.mainProcess.on(this.serverFunction.requestTodayPosition.toString(), this.handlePositionRequest.bind(this));

        /**
         * 监听，全量成交数据，一次性消费请求
         */
        this.mainProcess.on(this.serverFunction.requestTodayTradeRecord.toString(), this.handleTradeRecordRequest.bind(this));

        /**
         * 监听来自各处，对主体数据实时变化，的订阅
         */

        this.mainProcess.on(this.systemEvent.subscribeAccountChange, this.subscribe.bind(this));

        /**
         * 监听来自各处，对主体数据实时变化，的取消订阅
         */

        this.mainProcess.on(this.systemEvent.unsubscribeAccountChange, this.unsubscribe.bind(this));
    }

    logClientRequest(fun_code, win_id, view_id, context_obj, message_body) {

        this.loggerConsole.info('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
        const msg = `batch data requesting > funcode/${fun_code}, win/${win_id}, view/${view_id}, context/${JSON.stringify(context_obj)}, body/${JSON.stringify(message_body)}`;
        this.loggerSys.debug(msg);
        this.loggerConsole.debug(msg);
        this.loggerConsole.info('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');
    }

    logServerPush(operation, fun_code, server_event_code) {

        const msg = `diagnosing batch data push > ${operation} > funcode/${fun_code}, event/${server_event_code}`;
        this.loggerSys.debug(msg);
        this.loggerConsole.debug(msg);
    }

    run() {

        this.loggerSys.info('load module account-service > begin');
        this.listen2Commands();
        this.loggerSys.info('load module account-service > end');
    }
}

module.exports = { AccountServiceModule };