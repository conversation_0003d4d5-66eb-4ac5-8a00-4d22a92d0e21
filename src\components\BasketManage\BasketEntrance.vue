<script setup lang="tsx">
import BasketCreation from './BasketCreation.vue';
import { deleteConfirm } from '@/script/interaction';
import { onMounted, reactive, ref } from 'vue';
import { Repos, type BasketItem } from '../../../../xtrade-sdk/dist';
import { remove } from '@/script';
import { ElMessage } from 'element-plus';
import type { AssetTypeEnum } from '@/enum';

const { assetType, targetId } = defineProps<{
  assetType: AssetTypeEnum;
  targetId: number | null;
}>();

const records = ref<BasketItem[]>([]);

// 控制篮子维护对话框的显示
const dialog = reactive({
  visible: false,
  basket: null as BasketItem | null,
  onlyUploading: false,
});

async function deleteBasket(item: BasketItem) {
  const result = await deleteConfirm('删除篮子', `确定要删除该篮子：${item.basketName}？`);
  if (result !== true) {
    return;
  }
  const resp = await repoInstance.DeleteBasket(item.basketId);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    remove(records.value, x => x.basketId == item.basketId);
    ElMessage.success('已删除');
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

function editBasket(item: BasketItem) {
  dialog.basket = item;
  dialog.visible = true;
  dialog.onlyUploading = false;
}

// 新建篮子
function createBasket() {
  dialog.basket = null;
  dialog.visible = true;
  dialog.onlyUploading = false;
}

// 导入篮子
async function importBasket() {
  if (!targetId) {
    ElMessage.warning('请选择需要导入的篮子');
    return;
  }

  let matched = records.value.find(x => x.basketId == targetId);
  if (!matched) {
    await request();
    matched = records.value.find(x => x.basketId == targetId);
  }

  if (!matched) {
    ElMessage.warning('当前篮子不存在');
    return;
  }

  dialog.basket = matched;
  dialog.visible = true;
  dialog.onlyUploading = true;
}

const repoInstance = new Repos.BasketRepo();
async function request() {
  records.value = ((await repoInstance.QueryBaskets()) || {}).data || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <div>
    <el-tooltip placement="bottom" popper-class="basket-selector-tooltip">
      <template #content>
        <div flex flex-col w-200 :style="{ height: records.length > 0 ? '280px' : 'unset' }">
          <div
            v-if="records.length > 0"
            class="basket-list"
            h-100
            flex-1
            of-y-auto
            p-8
            flex
            flex-col
            gap-5
          >
            <div
              v-for="(basket, idx) in records"
              :key="basket.basketId"
              class="basket-item"
              w-full
              h-32
              lh-24
              flex
              aic
            >
              <div w-100 flex-1>{{ idx + 1 }}. {{ basket.basketName }}</div>
              <div w-40 flex jcsb>
                <i class="iconfont icon-edit" @click.stop="editBasket(basket)" />
                <i class="iconfont icon-remove" @click.stop="deleteBasket(basket)" />
              </div>
            </div>
          </div>
          <div v-else>
            <div text-center lh-36>您尚未创建交易篮子！</div>
          </div>
          <div h-42 p-t-6 flex jcc aic>
            <el-button link type="primary" @click="createBasket">
              <i class="iconfont icon-add" />
              <span p-l-5>新建篮子</span>
            </el-button>
          </div>
          <div h-36 flex jcc aic>
            <el-button link type="primary" @click="importBasket">
              <i class="iconfont icon-upload" />
              <span p-l-5>导入篮子</span>
            </el-button>
          </div>
        </div>
      </template>
      <i class="iconfont icon-setting" />
    </el-tooltip>

    <!-- 交易篮子维护对话框 -->
    <el-dialog
      :title="dialog.basket ? '编辑交易篮子' : '新建交易篮子'"
      width="900px"
      top="50px"
      v-model="dialog.visible"
      @close="dialog.visible = false"
      :close-on-click-modal="false"
      append-to-body
      draggable
    >
      <BasketCreation
        :asset-type="assetType"
        :basket="dialog.basket"
        :only-uploading="dialog.onlyUploading"
        @close="dialog.visible = false"
      />
    </el-dialog>
  </div>
</template>

<style scoped>
.basket-list {
  .basket-item {
    padding: 4px 8px;
    border-radius: 4px;
    background-color: var(--g-table-odd-row-bg);
    &:hover {
      background-color: var(--g-active-row);
    }
  }
}
</style>
