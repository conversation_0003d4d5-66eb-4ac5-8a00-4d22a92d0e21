﻿/*
    deal with core system messages
*/

const electron = require('electron');
const BrowserWindow = electron.BrowserWindow;
const MainModule = require('./main-module').MainModule;
const UserInfo = require('../model/user-info').UserInfo;
const ServerManager = require('./module-comm/server-manager').ServerManager;

class CommModule extends MainModule {

    get tradingServer() {
        return this.serverManager.tradingServer;
    }

    get quoteServer() {
        return this.serverManager.quoteServer;
    }

    constructor(module_name) {

        super(module_name);

        this.states = {

            // once an user login request validated [ok], this flag is marked as [true] forever, until the app exits.
            loginSucceededOnce: null,
            maxReconnectTimes: 60,
            requestLatency: 2000,
            // should not delay to block ui input once a disconnection happens
            delayReconnectTrading: false,
            delayReconnectQuote: false,
            // times counter will be zeroed once established a connection
            reconnect2TradingTimes: 0,
            reconnect2QuoteTimes: 0,
            isRetryConnecting2TradingServer: false,
            isRetryConnecting2QuoteServer: false,
        };
    }

    allow2TryReconnect(system_event_name) {
        return this.states.loginSucceededOnce && system_event_name === this.systemEvent.connClosed && !this.isDisconnectedByLogout;
    }

    closeAllOtherWindows() {

        var all_windows = BrowserWindow.getAllWindows();

        for (var idx = 0; idx < all_windows.length; idx++) {
            
            let the_win = all_windows[idx];
            if (the_win === this.loginWindow) {
                continue;
            }

            the_win.close();
        }
    }

    doSysAutoLogout() {

        this.centralWindow.webContents.send(this.systemEvent.simulatedLogout);
        setTimeout(() => { electron.dialog.showErrorBox('网络错误提示', 'PB终端无法正常重连到服务器，您可以稍后重试登录。'); }, 300);
    }

    dealWithTradingServerConnectionError(system_event_name, error_message) {

        var error_info = `${system_event_name}/${error_message}`;
        var msg = `comm > [trading server] connection error > ${error_info}`;
        this.loggerConsole.error(msg);
        this.loggerSys.error(msg);

        var last_cfg = this.tradingServer.serverConfig;
        var current_cfg = this.getTradingServerInfo();

        if (last_cfg.ip != current_cfg.ip || last_cfg.port != current_cfg.port) {
            this.loggerSys.info(
                `comm > user chooses 2 connect another trading server: ${JSON.stringify(current_cfg)}, and the last: ${JSON.stringify(last_cfg)} is disconnected before establishing to the new`,
            );
            return;
        }

        if (this.allow2TryReconnect(system_event_name)) {
            this.states.isRetryConnecting2TradingServer = true;
            this.tryReconnect2TradingServer();
        } 
        else {
            let msg2 = `comm > not allowed to reconnect to [trading server], the error message goes to sign-in window, disconnected by event ${system_event_name}`;
            this.loggerConsole.info(msg2);
            this.loggerSys.info(msg2);
            this.loginWindow.webContents.send(system_event_name, error_message);
        }
    }

    tryReconnect2TradingServer() {

        let msg = 'comm > once succeeded to login, to try to auto re-connect to [trading server]';
        this.loggerConsole.info(msg);
        this.loggerSys.info(msg);

        if (this.states.reconnect2TradingTimes >= this.states.maxReconnectTimes) {
            this.loggerSys.info('comm > [trading server] tried exceeded the max times to re-connect but finally failed, direct UI 2 sign-in window');
            this.doSysAutoLogout();
            return;
        }

        this.states.reconnect2TradingTimes++;
        this.loggerSys.debug(`comm > retry-connect to [trading server] for ${this.states.reconnect2TradingTimes} times`);

        if (!this.states.delayReconnectTrading) {
            this.states.delayReconnectTrading = true;
            this.logonTradingServer();
        } 
        else {
            setTimeout(() => {
                this.logonTradingServer();
            }, this.states.requestLatency);
        }
    }

    dealWithQuoteServerConnectionError(system_event_name, error_message) {

        var error_info = `${system_event_name}/${error_message}`;
        var msg = `comm > [quote server] connection error > ${error_info}`;
        this.loggerConsole.error(msg);
        this.loggerSys.error(msg);

        var last_cfg = this.quoteServer.serverConfig;
        var current_cfg = this.getQuoteServerInfo();

        if (last_cfg.ip != current_cfg.ip || last_cfg.port != current_cfg.port) {
            this.loggerSys.info(
                `comm > user chooses 2 connect another quote server: ${JSON.stringify(current_cfg)}, and the last: ${JSON.stringify(last_cfg)} is disconnected before establishing to the new`,
            );
            return;
        }

        if (this.allow2TryReconnect(system_event_name)) {
            this.states.isRetryConnecting2QuoteServer = true;
            this.tryReconnect2QuoteServer();
        } 
        else {
            let msg2 = `comm > not allowed to reconnect to [quote server], the error message goes to sign-in window, disconnected by event ${system_event_name}`;
            this.loggerConsole.info(msg2);
            this.loggerSys.info(msg2);
            if (this.loginWindow && !this.loginWindow.isDestroyed()) {
				this.loginWindow.webContents.send(system_event_name, error_message);
			}
        }
    }

    tryReconnect2QuoteServer() {

        let msg = 'comm > once succeeded to login, to try to auto re-connect to [quote server]';
        this.loggerConsole.info(msg);
        this.loggerSys.info(msg);

        if (this.states.reconnect2QuoteTimes >= this.states.maxReconnectTimes) {
            this.loggerSys.info('comm > [quote server] tried exceeded the max times to re-connect but finally failed, direct UI 2 sign-in window');
            return;
        }

        this.states.reconnect2QuoteTimes++;
        this.loggerSys.debug(`comm > retry-connect to [quote server] for ${this.states.reconnect2QuoteTimes} times`);

        if (!this.states.delayReconnectQuote) {
            this.states.delayReconnectQuote = true;
            this.logonQuoteServer();
        } 
        else {
            setTimeout(() => {
                this.logonQuoteServer();
            }, this.states.requestLatency);
        }
    }

    setUserInfo(signin_response) {

        let input = this.getLoginUserInput();
        this.setContextDataItem(this.dataKey.userInfo, new UserInfo({ ...signin_response, password: input.passCode }));
        this.setContextDataItem(this.dataKey.stockAccounts, signin_response.accounts || []);
    }

    brocastNetworkGotConnected(server_reconnected_event_name) {

        var all_windows = BrowserWindow.getAllWindows();
        for (var idx = 0; idx < all_windows.length; idx++) {
            all_windows[idx] ? all_windows[idx].webContents.send(server_reconnected_event_name) : null;
        }
    }

    digestTradingServerLoginResponse(data_package) {

        var login_resp = JSON.parse(data_package.body);
        var response_log = 'comm > user info set to context = ' + JSON.stringify(login_resp);
        this.loggerConsole.log(response_log);
        this.loggerSys.log(response_log);

        if (login_resp.errorCode == undefined) {
            login_resp.errorCode = login_resp.errorId;
        }

        if (login_resp.errorCode === this.systemUserEnum.loginResult.ok.code) {

            this.loggerSys.debug(`comm > login [trading server] ok with response: ${JSON.stringify(login_resp)}`);
            this.tradingServer.startHeartBeating();
            this.loggerSys.info('comm > start [trading server] heart beating');
            this.setUserInfo(login_resp);
        }
        else {

            if(this.tradingServer.isConnected) {
				this.tradingServer.disconnect(this.systemEvent.appInitiativeDisconnect);
            }
            this.loggerSys.error(`comm > login [trading server] failed with response: ${JSON.stringify(login_resp)}`);
        }

        if (this.states.isRetryConnecting2TradingServer) {

            this.loggerSys.info('comm > reconnected to [trading server], all on-reconnected listeners will be notified');
            this.states.isRetryConnecting2TradingServer = false;
            this.brocastNetworkGotConnected(this.systemEvent.tradingServerReestablished);
        } 
        else {
            this.loggerSys.info('comm > to tell sign-in window the [trading server] login result via ipc');
            this.loginWindow.webContents.send(this.systemEvent.loginTradingServerCompleted, login_resp);
        }
    }

    digestQuoteServerLoginResponse(data_package) {

        var login_resp = JSON.parse(data_package.body);
        if (login_resp.errorCode == undefined) {
            login_resp.errorCode = login_resp.errorId;
        }

        if (login_resp.errorCode === this.systemUserEnum.loginResult.ok.code) {
            this.loggerSys.debug(`comm > login [quote server] ok with response: ${JSON.stringify(login_resp)}`);
            this.quoteServer.startHeartBeating();
        } 
        else {
            if(this.quoteServer.isConnected) {
				this.quoteServer.disconnect(this.systemEvent.appInitiativeDisconnect);
            }
            this.loggerSys.error(`comm > login [quote server] failed with response: ${JSON.stringify(login_resp)}`);
        }

        if (this.states.isRetryConnecting2QuoteServer) {
            this.loggerSys.info('comm > reconnected to [quote server], all on-reconnected listeners will be notified');
            this.states.isRetryConnecting2QuoteServer = false;
            this.brocastNetworkGotConnected(this.systemEvent.quoteServerReestablished);
        } 
        else {
            this.loggerSys.info('comm > to tell sign-in window the [quote server] login result via ipc');
            this.loginWindow.webContents.send(this.systemEvent.loginQuoteServerCompleted, login_resp);
        }
    }

    getTypicalTradingServerEventHandlers() {

        var ssyse = this.systemEvent;
        var svre = this.serverEvent;
        var handlers = {};

        handlers[ssyse.connEstablished] = () => {
            this.doLogonTradingServer();
        };
        handlers[ssyse.connTimedOut] = () => {
            this.dealWithTradingServerConnectionError(ssyse.connTimedOut, '交易服务器连接超时');
        };
        handlers[ssyse.connError] = () => {
            this.dealWithTradingServerConnectionError(ssyse.connError, '交易服务器连接错误');
        };
        handlers[ssyse.connClosed] = () => {
            this.dealWithTradingServerConnectionError(ssyse.connClosed, '交易服务器未连接');
        };
        handlers[svre.heartBeat] = () => {};
        handlers[svre.logoutTradingServerAnswered] = () => {
            this.loggerSys.info('comm > log out from [trading server]got answered');
        };
        handlers[svre.serverError] = message => {
            this.loggerSys.error('comm > [trading server] error: ' + message.body);
        };
        handlers[svre.loginTradingServerAnswered] = signin_response => {
            this.loggerSys.info('comm > [trading server] to call [login-answered] callback');
            this.digestTradingServerLoginResponse(signin_response);
        };

        return handlers;
    }

    updateToken(message_package) {

        var message = JSON.parse(message_package.body);
        this.loggerSys.debug(`comm > [trading server] token updated/${JSON.stringify(message)}`);
        this.userInfo.token = message.token;
    }

    doLogonTradingServer() {

        this.states.delayReconnectTrading = false;
        this.states.reconnect2TradingTimes = 0;

        this.loggerSys.info('comm > [trading server] connected > before doing login');
        this.serverManager.logonTradingServer();
        this.loggerSys.info('comm > [trading server] connected > after doing login');
    }

    logonTradingServer() {

        var server_info = this.getTradingServerInfo();

        if (!this.tradingServer) {

            this.serverManager.createNewTradingServer(server_info, this.getTypicalTradingServerEventHandlers());
            // listen to token generation
            this.tradingServer.listen2Event(this.serverEvent.tokenGenerated, this.updateToken.bind(this));
        }

        var server_cfg = this.tradingServer.serverConfig;
        var server_changed = server_info.ip !== server_cfg.ip || server_info.port !== server_cfg.port;

        if (server_changed) {

            this.loggerSys.debug(`comm > [trading server] changed to another, to establish connect to ${JSON.stringify(server_info)}`);
            this.serverManager.connect2TradingServer(server_info);
        } 
        else {

            if (this.serverManager.isTradingServerConnected) {
                this.loggerSys.info('comm > [trading server] still connected, do login directly');
                this.doLogonTradingServer();
            }
            else {
                this.loggerSys.info('comm > [trading server] not connected, to establish connect');
                this.serverManager.connect2TradingServer();
            }
        }
    }

    getTypicalQuoteServerEventHandlers() {

        var ssyse = this.systemEvent;
        var svre = this.serverEvent;
        var handlers = {};

        handlers[ssyse.connEstablished] = () => {
            this.doLogonQuoteServer();
        };
        handlers[ssyse.connTimedOut] = () => {
            this.dealWithQuoteServerConnectionError(ssyse.connTimedOut, '行情服务器连接超时');
        };
        handlers[ssyse.connError] = () => {
            this.dealWithQuoteServerConnectionError(ssyse.connError, '行情服务器连接错误');
        };
        handlers[ssyse.connClosed] = () => {
            this.dealWithQuoteServerConnectionError(ssyse.connClosed, '行情服务器未连接');
        };
        handlers[svre.heartBeat] = () => {};
        handlers[svre.serverError] = message => {
            this.loggerSys.error('comm > [quote server] error: ' + message.body);
        };
        handlers[svre.loginQuoteServerAnswered] = signin_response => {
            this.digestQuoteServerLoginResponse(signin_response);
        };

        return handlers;
    }

    doLogonQuoteServer() {

        this.states.delayReconnectQuote = false;
        this.states.reconnect2QuoteTimes = 0;

        this.loggerSys.info('comm > before doing login onto [quote server]');
        this.serverManager.logonQuoteServer();
        this.loggerSys.info('comm > after doing login onto [quote server]');
    }

    logonQuoteServer() {

        var server_info = this.getQuoteServerInfo();

        if (!this.quoteServer) {

            this.loggerConsole.log('======================================================');
            this.loggerConsole.info('to create quote server > ' + JSON.stringify(server_info));
            this.serverManager.createNewQuoteServer(server_info, this.getTypicalQuoteServerEventHandlers());
            this.loggerConsole.log('======================================================');
        }

        var server_cfg = this.quoteServer.serverConfig;
        var server_changed = server_info.ip !== server_cfg.ip || server_info.port !== server_cfg.port;

        if (server_changed) {

            this.loggerSys.debug(`comm > [quote server] changed to another, to establish connect to ${JSON.stringify(server_info)}`);
            this.serverManager.connect2QuoteServer(server_info);
        } 
        else {

            if (this.serverManager.isQuoteServerConnected) {

                this.loggerSys.info('comm > [quote server] still connected, do login directly');
                this.doLogonQuoteServer();
            } 
            else {
                
                this.loggerSys.info('comm > [quote server] not connected, to establish connect');
                this.serverManager.connect2QuoteServer();
            }
        }
    }

    refreshToken() {

        if (this.tradingServer.isConnected && !this.tradingServer.isLogedOut) {

            this.loggerSys.info('comm > to send out token refresh request');
            this.tradingServer.send({ fc: this.serverFunction.refreshToken, reqId: 0, dataType: 1, body: null });
        }
        else {
            this.loggerSys.error('comm > server is not connected, token refresh refused');
        }
    }

    scheduleTokenRefresh() {

        if (this.tokenRefreshTask) {
            clearInterval(this.tokenRefreshTask);
        }
        this.tokenRefreshTask = setInterval(() => { this.refreshToken(); }, 1000 * 60 * 30);
    }

    finishLoginProcess() {

        this.loggerSys.info('comm > the main process is informed the user has loged in successfully');
        this.states.loginSucceededOnce = true;
        this.scheduleTokenRefresh();
    }

    setAppDataItem(key, value) {

        if (typeof key != 'string' && typeof key != 'number') {
            console.error(`app data key [${key}] is not an valid key`);
            return false;
        }
        this.app[key] = value;
        return true;
    }

    setupTopComponents() {

        // communication proxy consisting of trading/quote servers lifecycle management
        this.serverManager = new ServerManager();
        // 设置APP顶级对象
        this.app.serverManager = this.serverManager;
    }

    /**
     * 转发窗口欲发送到交易服务器的数据
     * @param {*} event 
     * @param {Number} server_function_code 
     * @param {*} body_data 
     * @param {*} reqId 
     */
    transmit2TradingServer(event, server_function_code, body_data, reqId) {

        this.loggerSys.info(`com > client asks to send data to trading server > func code/${ server_function_code }, data = ${ JSON.stringify(body_data) }`);
        var message = { fc: server_function_code, reqId: typeof reqId == 'number' ? reqId : 0, dataType: 1, body: body_data === null || body_data === undefined ? '' : body_data };
        this.tradingServer.send(message);
    }

    /**
     * 转发窗口欲发送到行情服务器的数据
     * @param {*} event 
     * @param {Number} server_function_code 
     * @param {*} body_data 
     * @param {*} reqId 
     */
    transmit2QuoteServer(event, server_function_code, body_data, reqId) {

        var message = { fc: server_function_code, reqId: typeof reqId == 'number' ? reqId : 0, dataType: 1, body: body_data === null || body_data === undefined ? '' : body_data };
        this.quoteServer.send(message);
    }

    listen2Events() {

        // monitor request to login <trading> server
        this.mainProcess.on(this.systemEvent.toLoginTradingServer, this.logonTradingServer.bind(this));
        // monitor request to login <quote> server
        this.mainProcess.on(this.systemEvent.toLoginQuoteServer, this.logonQuoteServer.bind(this));
        // monitor signal expressing a login request is validated as [OK]
        this.mainProcess.on(this.systemEvent.loginRequestValidatedOk, this.finishLoginProcess.bind(this));
        // transmit data from render process(es) to trading server
        this.mainProcess.on(this.systemEvent.transmitMsg2TradingServer, this.transmit2TradingServer.bind(this));
        // transmit data from render process(es) to quote server
        this.mainProcess.on(this.systemEvent.transmitMsg2QuoteServer, this.transmit2QuoteServer.bind(this));

        /**
         * 注销时，清除已分配的相关未释放资源
         */
        this.mainProcess.on(this.systemEvent.toLogout, event => {

            /**
             * 清除曾经登录成功标识
             */
            this.states.loginSucceededOnce = false;

            /**
             * 清除可能存在已经的token刷新定时服务
             */
            if (this.tokenRefreshTask) {
                clearInterval(this.tokenRefreshTask);
                delete this.tokenRefreshTask;
            }
        });
    }

    run() {
        this.setupTopComponents();
        this.listen2Events();
    }
}

module.exports = { CommModule };
