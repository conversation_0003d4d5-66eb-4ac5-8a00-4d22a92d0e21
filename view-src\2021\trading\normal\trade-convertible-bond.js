const NormalTradeView = require('./trade-view-normal');

class View extends NormalTradeView {

    constructor(view_name) {
        super(view_name, { isBond: true });
    }

    sendOutOrder() {

        var uistates = this.uistates;
        var { fundId, strategyId, accountId } = this.account;
        var dict = this.systemTrdEnum;

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

            strategyId: strategyId || fundId,
            accountId: accountId,
            userId: this.userInfo.userId,
            price: uistates.price,
            volume: uistates.scale,
            instrument: this.states.instrument,
            priceType: dict.pricingType.fixedPrice.code,
            bsFlag: uistates.direction,
            businessFlag: 0,
            positionEffect: this.isSpot ? 0 : this.uistates.effect,
            customId: 'normal-spot-' + this.helper.makeToken(),
            orderTime: null,
            hedgeFlag: uistates.checkOpt.hedgeFlag,
        });
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                channel: this.channel,
                directions: this.sdirections,
                accounts: this.saccounts,
                effects: this.effects,
                shortcuts: this.shortcuts,
                states: this.states,
                uistates: this.uistates,
                localStates: this.localStates,
            },

            computed: {
                
                isEffectApplicable: () => { return this.isEffectApplicable(); },
                isCloseTodayNotApplicable: () => { return this.isCloseTodayNotApplicable(); },
                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                scaleStep: () => { return this.decideScaleStep(); },
                maxScale: () => { return this.decideMaxScale(); },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.setAsPrice,
                this.precisePrice,
                this.handleUserInput,
                this.handleSelect,
                this.suggest,
                this.handleClearIns,
                this.handleDirectionChange,
                this.handlePositionEffectChange,
                this.handleAccountChange,
                this.handlePriceChange,
                this.handleScaleChange,
                this.hope2Entrust,
                this.handleShortcutClick,
                this.makeDisplayUnit,
                this.makeBuyText,
                this.makeSellText,
                this.formatSelectAccountName,
                this.getProperAccountId,
            ]),
        });
    }

    handleScaleChange() {
        
        const value = this.uistates.scale;
        if (typeof value == 'number') {
            if (value % 10 != 0) {
                this.uistates.scale = Math.floor(value / 10) * 10;
            }
        }

        super.handleScaleChange();
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;