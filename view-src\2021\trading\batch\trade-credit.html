<div class="trade-form btcform s-full-height">
	<div class="trade-form-inner btcform-internal themed-box s-scroll-bar s-full-height">
		<div class="xtcontainer s-border-box s-full-height">

			<template>
				<div class="xtheader themed-header">
					<span>{{ channel.mean }}</span>
					<el-popover placement="bottom"
								title="交易设置"
								width="130"
								trigger="click">

						<a slot="reference" class="s-pull-right themed-color themed-hover-color" title="设置">
							<i class="iconfont icon-shezhi11"></i>
						</a>

						<div class="xtpop-body">
							<div class="switch-item">
								<span>校验资金</span>
								<el-switch v-model="uistates.checkOpt.cash"></el-switch>
							</div>
							<div class="switch-item">
								<span>校验持仓</span>
								<el-switch v-model="uistates.checkOpt.position"></el-switch>
							</div>
						</div>

					</el-popover>
				</div>
			</template>

			<template>

				<div class="form-external s-unselectable">
	
					<form class="xtform">

						<div class="xtinput">
							<span class="xtlabel themed-color">模式</span>
							<span class="mode-box">
								<el-radio-group v-model="uistates.mode">
									<el-radio v-for="(item, item_idx) in modes"
											  :key="item_idx"
											  :label="item.code"
											  @change="handleModeChange">{{ item.mean }}</el-radio>
								</el-radio-group>
							</span>
						</div>
	
						<div class="direction-row">

							<el-radio-group v-model="localStates.creditType" 
											@change="handleCreditTypeChange" 
											class="s-full-width">

								<el-radio-button v-for="(item, item_idx) in creditTypes"
												 :key="item_idx"
												 :label="item.code"
												 :style="{ width: 100 / creditTypes.length + '%' }">{{ item.mean }}</el-radio-button>
							</el-radio-group>

						</div>

						<div v-if="isBusinessApplicable" class="xtinput">
							<span class="xtlabel themed-color">开平</span>
							<span class="effect-box" :class="localStates.effectBoxClass">
								<el-radio-group v-model="localStates.businessCode" @change="handleBusinessChange">
									<el-radio v-for="(item, item_idx) in businesses"
											  :key="item_idx"
											  :label="item.code">{{ item.mean }}</el-radio>
								</el-radio-group>
							</span>
						</div>
	
						<div class="xtinput">
							<span class="xtlabel themed-color">代码</span>
							<el-autocomplete v-model="uistates.keywords"
											 :fetch-suggestions="suggest"
											 @keydown.native="handleUserInput"
											 @clear="handleClearIns"
											 @select="handleSelect" clearable>
	
								<template slot-scope="{ item: ins }">
									<span class="item-code">[{{ ins.instrument }}] </span>
									<span class="item-name">{{ ins.instrumentName }}</span>
								</template>

							</el-autocomplete>
						</div>
	
						<div class="xtinput shorten">
							<el-tooltip placement="right" content="调整限价/市价">
								<span class="limit-btn themed-bg-harder themed-bright s-not-allowed" title="调整价格模式">限</span>
							</el-tooltip>
							<span class="xtlabel themed-color">价格</span>
							<el-input-number placeholder="委托价格"
											 v-model="uistates.price"
											 :min="uistates.floor" 
											 :max="uistates.ceiling > 0 ? uistates.ceiling : 999999999"
											 :step="uistates.priceStep"
											 @change="handlePriceChange"></el-input-number>
						</div>

						<div class="xtinput subsidary">
							<span class="xtlabel"></span>
							<span class="prices-box">
								<span class="price-item themed-bright">
									跌停<a class="s-color-green s-hover-underline" 
											@click="setAsPrice(uistates.floor)">{{ precisePrice(uistates.floor) }}</a>
								</span>
								<span class="price-item themed-bright">
									涨停<a class="s-color-red s-hover-underline" 
											@click="setAsPrice(uistates.ceiling)">{{ precisePrice(uistates.ceiling) }}</a>
								</span>
							</span>
						</div>
	
						<div class="xtinput shorten">
							<span class="unit-txt themed-color" title="单位">({{ makeDisplayUnit() }})</span>
							<span class="xtlabel themed-color">{{ uistates.method.mean }}</span>
							<el-input-number :placeholder="'按' + uistates.method.mean"
											 :min="0"
											 :max="maxScale"
											 :step="scaleStep"
											 v-model="uistates.scale"
											 @change="handleScaleChange"></el-input-number>
						</div>
	
						<div class="xtinput subsidary">
							<span class="xtlabel"></span>
							<span class="methods-box">
								<a v-for="(item, item_idx) in methods"
								   class="method-item s-hover-underline s-opacity-7 s-opacity-hover"
								   :class="uistates.method.code == item.code ? 'themed-selected-member' : null"
								   :style="{ width: (item.mean.length * 100 / 9) + '%', 'text-align': item.align }"
								   :title="item.tooltip"
								   @click="handleMethodChange(item)">{{ item.mean }}</a>
							</span>
						</div>
	
						<div class="xtinput button-row">
							<el-button :type="localStates.buttonType" @click="hope2Entrust">{{ localStates.buttonText }}</el-button>
						</div>
	
					</form>

				</div>

			</template>
		</div>		
	</div>
</div>