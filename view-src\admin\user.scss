.user-dialog {

    .user-form {
        padding: 5px 10px;
    }
}

.user-view-root {

    height: 100%;

    .vueapp-node {

        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .toolbar {

        flex-grow: 0;
        flex-shrink: 0;
        height: 30px;
        line-height: 30px;
        overflow: hidden;
    }

    .table-box {
        
        flex-grow: 1;
        flex-shrink: 1;
        height: 100px;
    }

    .sc-table {

        height: 100%;
        
        .el-table {

            height: 100%;
            box-sizing: border-box;
            padding-top: 27px;
            padding-bottom: 10px;

            .el-table__header-wrapper {

                margin-top: -27px;
                height: 27px;
            }

            .el-table__body-wrapper {

                height: 100% !important;
                overflow-y: auto;
            }
        }
    }

    .pagination-wrap {

        display: block;
        float: right;
        margin-top: -28px;
    }

    .el-dialog {

        .pagination-bar {
            height: 32px;
        }
    
        .pagination-wrap {
            
            margin-top: 0;
            padding-top: 4px;
        }
    }
}