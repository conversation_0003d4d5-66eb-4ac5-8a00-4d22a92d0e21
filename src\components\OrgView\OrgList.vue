<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import OrgDialog from './OrgDialog.vue';
import { onMounted, shallowRef, useTemplateRef, computed } from 'vue';
import { TableV2SortOrder, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { formatDateTime, hasPermission, timestampCol } from '@/script';
import { AdminService } from '@/api';
import type { MomOrganization } from '../../../../xtrade-sdk/dist';
import { deleteConfirm } from '@/script/interaction';
import { MenuPermitOrganizationManagement } from '@/enum';

// 权限判断
const canCreate = computed(() => hasPermission(MenuPermitOrganizationManagement.创建));
const canEdit = computed(() => hasPermission(MenuPermitOrganizationManagement.编辑));
const canDelete = computed(() => hasPermission(MenuPermitOrganizationManagement.删除));
const canUpdateStatus = computed(() => hasPermission(MenuPermitOrganizationManagement.机构状态));

// 基础列定义
const columns: ColumnDefinition<MomOrganization> = [
  { key: 'orgName', title: '机构名', width: 200, sortable: true },
  { key: 'contract', title: '联系人', width: 150, sortable: true },
  { key: 'phone', title: '电话', width: 150, sortable: true },
  {
    key: 'status',
    title: '状态',
    sortable: true,
    width: 100,
    cellRenderer: ({ rowData }: { rowData: MomOrganization }) => {
      return canUpdateStatus.value ? (
        <el-switch
          modelValue={rowData.status}
          active-value={1}
          inactive-value={0}
          before-change={() => beforeChange(rowData)}
        />
      ) : (
        <span class={rowData.status === 1 ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]'}>
          {rowData.status === 1 ? '启用' : '禁用'}
        </span>
      );
    },
  },
  { key: 'email', title: '邮箱', width: 200, sortable: true },
  { key: 'domain', title: '域名', width: 200, sortable: true },
  // { key: 'introduction', title: '机构简介', width: 200, sortable: true },
  { key: 'id', title: 'ID', width: 100, sortable: true },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: params => timestampCol(params, 'yyyy-MM-dd'),
    textRenderer: val => formatDateTime(val, 'yyyy-MM-dd'),
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: params => timestampCol(params, 'yyyy-MM-dd'),
    textRenderer: val => formatDateTime(val, 'yyyy-MM-dd'),
  },
];

// 行操作（根据权限动态生成）
const rowActions = computed<RowAction<MomOrganization>[]>(() => {
  const actions: RowAction<MomOrganization>[] = [];

  if (canEdit.value) {
    actions.push({
      label: '编辑',
      icon: 'edit',
      onClick: row => {
        editRow(row);
      },
    });
  }

  if (canDelete.value) {
    actions.push({
      label: '删除',
      icon: 'remove',
      onClick: row => {
        deleteRow(row);
      },
    });
  }

  return actions;
});

const records = shallowRef<MomOrganization[]>([]);
const tableRef = useTemplateRef('tableRef');

// 对话框相关
const dialogVisible = shallowRef(false);
const editingOrg = shallowRef<MomOrganization | undefined>();

// 切换机构状态
const beforeChange = async (rowData: MomOrganization) => {
  if (!canUpdateStatus.value) {
    ElMessage.warning('您没有修改机构状态的权限');
    return false;
  }

  const { errorCode, errorMsg } = await AdminService.updateOrg({
    ...rowData,
    status: rowData.status == 1 ? 0 : 1,
  });
  if (errorCode === 0) {
    rowData.status = rowData.status == 1 ? 0 : 1;
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};

// 新建机构
const handleCreate = () => {
  editingOrg.value = undefined;
  dialogVisible.value = true;
};

// 编辑机构
function editRow(row: MomOrganization) {
  editingOrg.value = row;
  dialogVisible.value = true;
}

// 删除机构
async function deleteRow(row: MomOrganization) {
  const result = await deleteConfirm('删除机构', `确定要删除机构"${row.orgName}"吗？`);
  if (!result) return;
  const { errorCode, errorMsg } = await AdminService.deleteOrg(row.id);
  if (errorCode === 0) {
    ElMessage.success('删除成功');
    await request();
  } else {
    ElMessage.error(errorMsg || '删除失败');
  }
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  try {
    records.value = await AdminService.getOrgs();
  } catch (error) {
    console.error('获取机构列表失败:', error);
    ElMessage.error('获取机构列表失败');
  }
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    show-index
  >
    <template #actions>
      <div class="actions" flex aic>
        <!-- <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button> -->
        <el-button v-if="canCreate" type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建机构</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 机构编辑对话框 -->
  <OrgDialog v-model="dialogVisible" :org="editingOrg" @success="handleDialogSuccess" />
</template>

<style scoped></style>
