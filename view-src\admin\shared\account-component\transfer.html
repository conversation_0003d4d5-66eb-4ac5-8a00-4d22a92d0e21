<div class="view-account-transfer">

    <el-dialog
        width="780px"
        title="资金划转"
        :visible="states.visible"
        :close-on-click-modal="false"
        :show-close="false"
    >
        <div class="cash-transfer-box">

            <el-tabs v-model="states.tab">

                <el-tab-pane name="internal" label="账号资金划转">

                    <el-select v-model="internal.accountId" placeholder="账号" class="ctr-account" style="width: 240px;" disabled>
                        <el-option v-for="(item, item_idx) in accounts" :key="item_idx" :value="item.accountId" :label="item.accountName"></el-option>
                    </el-select>

                    <el-select v-model="internal.direction" placeholder="方向" class="ctr-direction s-mgl-10" style="width: 130px;">
                        <el-option v-for="(item, item_idx) in directions" :key="item_idx" :value="item.value" :label="item.label"></el-option>
                    </el-select>

                    <el-input-number 
                        placeholder="划转金额(万元)" 
                        class="ctr-cash s-mgl-10"
                        :min="0"
                        :max="*********" 
                        :step="0.01"
                        :precision="2"
                        :controls="false"
                        v-model="internal.value"
                        style="width: 100px;"
                        clearable
                    >
                    </el-input-number>

                    <el-select v-model="internal.times" class="ctr-times s-mgl-10" :class="{ highlighted: internal.times == multiples.tenk.value }">
                        <el-option v-for="(item, item_idx) in multiples" :key="item_idx" :value="item.value" :label="item.label"></el-option>
                    </el-select>

                </el-tab-pane>

                <el-tab-pane name="counter" label="柜台间资金划转">
                    
                    <el-select v-model="counter.accountId" placeholder="账号" class="ctr-account" style="width: 240px;" disabled>
                        <el-option v-for="(item, item_idx) in accounts" :key="item_idx" :value="item.accountId" :label="item.accountName"></el-option>
                    </el-select>

                    <el-select v-model="counter.direction" placeholder="方向" class="ctr-direction s-mgl-10" style="width: 160px;">
                        <el-option v-for="(item, item_idx) in counters" :key="item_idx" :value="item.value" :label="item.label"></el-option>
                    </el-select>

                    <el-input-number 
                        placeholder="划转金额(万元)" 
                        class="ctr-cash s-mgl-10"
                        :min="0"
                        :max="*********" 
                        :step="0.01"
                        :precision="2"
                        :controls="false"
                        v-model="counter.value"
                        style="width: 100px;"
                        clearable
                    >
                    </el-input-number>

                    <el-select v-model="counter.times" class="ctr-times s-mgl-10" :class="{ highlighted: counter.times == multiples.tenk.value }">
                        <el-option v-for="(item, item_idx) in multiples" :key="item_idx" :value="item.value" :label="item.label"></el-option>
                    </el-select>

                </el-tab-pane>

            </el-tabs>

        </div>

        <div slot="footer">
            <el-button v-if="states.tab == 'internal'" type="primary" @click="allocate">划拨资金</el-button>
            <el-button v-else type="primary" @click="allocateBetweenCounters" style="width: 120px;">柜台间资金划转</el-button>
			<el-button type="default" @click="cancel">取消</el-button>
		</div>

    </el-dialog>

</div>
