import { PositionDirectionEnum } from '../src/enum';

const positions = [
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '600036',
    instrumentName: '招商银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '600519',
    instrumentName: '贵州茅台',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601398',
    instrumentName: '工商银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601288',
    instrumentName: '农业银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: 1824.72,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '601988',
    instrumentName: '中国银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601628',
    instrumentName: '中国人寿',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '银河期货1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '601318',
    instrumentName: '中国平安',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
];

export default positions;
