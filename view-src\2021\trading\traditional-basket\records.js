const { IView } = require('../../../../component/iview');
const { Tab } = require('../../../../component/tab');
const { TabList } = require('../../../../component/tab-list');
const { TradeChannel } = require('../../model/message');

class View extends IView {

    /**
     * @param {*} view_name 
     * @param {*} is_standalone_window 
     * @param {TradeChannel} fixedChannel 
     */
    constructor(view_name, is_standalone_window, fixedChannel) {

        super(view_name, is_standalone_window, '篮子交易数据');
        this.setAsChannel(fixedChannel);
    }

    createDataViews() {

        var $tab = this.$container.querySelector('.category-tab');
        var $content = this.$container.querySelector('.data-views');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/fragment/entrust-preview', '预览', { isBasket: true });
        tabs.openTab(true, '@2021/trading/traditional-basket/task', '任务');

        this.tabPreview = tabs.tabs[0];
        this.tabTask = tabs.tabs[1];
        this.tabList = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        /**
         * 交易数据的各个视图，默认为延迟加载，故：当TAB主体被实际构建时，通知一次渠道变化
         */
        
        tab.viewEngine.trigger('set-channel', this.fixedChannel);
    }

    /**
     * @param {Tab} tab 
     */
     handleTabFocused(tab) {
        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    /**
     * 设置为当前的交易渠道
     * @param {TradeChannel} channel 
     */
    setAsChannel(channel) {
        this.fixedChannel = channel;
    }

    listen2Events() {

        var EventSetPreview = 'set-as-order-preview';
        var EventReloadTask = 'reload-basket-tasks';
        
        this.registerEvent(EventSetPreview, (...args) => {

            this.tabList.setFocus(this.tabPreview);
            this.tabPreview.viewEngine.trigger(EventSetPreview, ...args);
        });

        this.registerEvent(EventReloadTask, (...args) => {

            this.tabList.setFocus(this.tabTask);
            this.tabTask.viewEngine.trigger(EventReloadTask, ...args);
        });

        /** 监听窗口尺寸调整 */
        this.lisen2WinSizeChange((width, height, isMaximized) => {

            this.tabList.fireEventOnFocusedTab('table-max-height', height - (isMaximized ? 564 : 549));
            this.tabList.fireEventOnFocusedTab('table-scroll-2-left');
        });
    }

    build($container) {

        super.build($container);
        this.createDataViews();
        this.listen2Events();
    }
}

module.exports = View;