
/**
 * 系统级策略
 */
class SysStrategy {

    constructor({ available, balance, closeProfit, commission, connectCount, description, diffBalance, frozenCommission, frozenMargin, fundId, fundName, id, loanBuyBalance, loanSellBalance, loanSellQuota, margin, marketValue, positionProfit, preBalance, reportTemplates, risePercent, status, strategyName, withdrawQuota, preNav, nav, strategyAccounts = [], users = [] }) {

        this.available = available;
        this.balance = balance;
        this.closeProfit = closeProfit;
        this.commission = commission;
        this.connectCount = connectCount;
        this.description = description;
        this.diffBalance = diffBalance;
        this.frozenCommission = frozenCommission;
        this.frozenMargin = frozenMargin;
        this.fundId = fundId;
        this.fundName = fundName;
        this.id = id;
        this.loanBuyBalance = loanBuyBalance;
        this.loanSellBalance = loanSellBalance;
        this.loanSellQuota = loanSellQuota;
        this.margin = margin;
        this.marketValue = marketValue;
        this.positionProfit = positionProfit;
        this.preBalance = preBalance;
        this.reportTemplates = reportTemplates;
        this.risePercent = risePercent;
        this.status = status;
        this.strategyName = strategyName;
        this.withdrawQuota = withdrawQuota;
        this.preNav = preNav;
        this.nav = nav;
        
        this.strategyAccounts = strategyAccounts;
        this.users = users;
    }
}

module.exports = { SysStrategy };