<div class="view-root">
    
    <div class="toolbar">

        <el-input v-model="searching.keywords" class="input-searching" placeholder="请输入关键词" @change="filterRecords" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>

        <div class="s-pull-right">
            <el-pagination :page-sizes="paging.pageSizes"
                           :page-size.sync="paging.pageSize"
                           :total="paging.total"
                           :current-page.sync="paging.page"
                           :layout="paging.layout"
                           @size-change="handlePageSizeChange"
                           @current-change="handlePageChange"></el-pagination>
        </div>
        
    </div>

    <table>
        <tr>
            <th 
                label="账号名称" 
                min-width="180"
                prop="accountName" 
                formatter="formatAccountName" 
                searchable 
                sortable 
                overflowt
            ></th>
            <th 
                label="产品" 
                min-width="150" 
                prop="fundName" 
                searchable 
                sortable 
                overflowt
            ></th>
            <th 
                label="工作流" 
                min-width="100" 
                prop="workFlowName" 
                searchable 
                sortable 
                overflowt
            ></th>
            <th 
                label="来源" 
                min-width="100" 
                prop="sourceUserName" 
                searchable 
                sortable 
                overflowt
            ></th>
            <th 
                type="program" 
                label="止损" 
                fixed-width="60" 
                prop="stopLoss" 
                formatter="formatStoploss" 
                export-formatter="formatStoplossText" 
                sortable 
                overflowt
            ></th>
            <th 
                type="program" 
                label="指令类型" 
                fixed-width="90" 
                prop="instructionType" 
                formatter="formatOrderType" 
                sortable 
                overflowt
            ></th>
            <th 
                label="代码" 
                fixed-width="100" 
                prop="instrument" 
                overflowt 
                sortable 
                searchable
            ></th>
            <th 
                label="合约" 
                fixed-width="80" 
                prop="instrumentName" 
                overflowt 
                sortable 
                searchable
            ></th>
            <th 
                type="program" 
                label="方向" 
                fixed-width="60" 
                prop="direction" 
                watch="direction" 
                formatter="formatDirection" 
                export-formatter="formatDirectionText" 
                sortable
            ></th>
            <th 
                label="委托量" 
                min-width="80" 
                prop="volumeOriginal" 
                align="right" 
                thousands-int
            ></th>
            <th 
                type="program" 
                label="指令状态" 
                fixed-width="80" 
                prop="instructionStatus" 
                watch="instructionStatus" 
                formatter="formatApproveStatus" 
                export-formatter="formatApproveStatusText" 
                sortable 
                overflowt
            ></th>
            <th 
                type="program" 
                label="执行状态" 
                fixed-width="80" 
                prop="executeStatus" 
                watch="executeStatus" 
                formatter="formatExecuteStatus" 
                export-formatter="formatExecuteStatusText" 
                sortable 
                overflowt
            ></th>
            <th 
                label="执行人" 
                fixed-width="80" 
                prop="auditName"
                sortable 
                overflowt
            ></th>
            <th 
                label="执行通过时间" 
                fixed-width="80" 
                prop="auditTime"
                formatter="formatDateTime"
                sortable 
                overflowt
            ></th>
            <th 
                label="执行方式" 
                fixed-width="80" 
                prop="approvalType"
                formatter="formatApprovalType"
                sortable 
                overflowt
            ></th>
            <th 
                label="来源MAC地址" 
                fixed-width="160" 
                prop="sourceIpMac"
                formatter="formatIpMac"
                sortable 
                overflowt
            ></th>
            <th 
                label="执行MAC地址" 
                fixed-width="160" 
                prop="ipMac"
                formatter="formatIpMac"
                sortable 
                overflowt
            ></th>
            <th 
                label="开始时间" 
                fixed-width="160"
                prop="startTime" 
                formatter="formatDateTime" 
                sortable
            ></th>
            <th 
                label="工作流ID" 
                fixed-width="80" 
                prop="workFlowId" 
                overflowt
            ></th>
        </tr>
    </table>
</div>