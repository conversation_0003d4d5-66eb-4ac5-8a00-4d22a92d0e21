const BatchChildView = require('./batch-child');
const { CodeMeanItem, LevelMessage, TradeChannel, InstrumentInfo, TickData } = require('../../model/message');
const { WeightedAccountDetail, OrderPreview } = require('../../model/account');
const { BizHelper } = require('../../../../libs/helper-biz');

class TradeMethod {

    /**
     * @param {Number} code 
     * @param {String} mean 
     * @param {String} unit 
     * @param {String} tooltip 
     * @param {String} align 
     */
    constructor(code, mean, unit, tooltip, align, {

                isByQuantity = false, 
                isByMoney = false, 
                isByAvailableCash = false, 
                isByAvailablePos = false, 
                isByNetAsset = false }) {

        this.code = code;
        this.mean = mean;
        this.unit = unit;
        this.tooltip = tooltip;
        this.align = align;

        this.isByQuantity = !!isByQuantity;
        this.isByMoney = !!isByMoney;
        this.isByAvailableCash = !!isByAvailableCash;
        this.isByAvailablePos = !!isByAvailablePos;
        this.isByNetAsset = !!isByNetAsset;
    }
}

class TradeView extends BatchChildView {

    /**
     * 是否需要检测可用资金
     */
    get isCashCheckRequired() {
        return this.uistates.checkOpt.cash;
    }

    /**
     * 是否需要检测可用持仓
     */
    get isPositionCheckRequired() {
        return this.uistates.checkOpt.position;
    }

    /**
     * 当前选择方向是否为：买入
     */
    get isBuy() {
        return this.uistates.direction == this.directions.buy.code;
    }

    /**
     * 当前选择方向是否为：卖出
     */
    get isSell() {
        return this.uistates.direction == this.directions.sell.code;
    }

    /**
     * 当前选择方向是否为：调仓
     */
    get isAdjust() {
        return this.uistates.direction == this.directions.adjust.code;
    }

    /**
     * 当前选择模式是否为：复制模式
     */
    get isDuplicate() {
        return this.uistates.mode == this.modes.duplicate.code;
    }

    /**
     * 当前选择模式是否为：分摊模式
     */
    get isDevide() {
        return this.uistates.mode == this.modes.devide.code;
    }

    /**
     * 当前交易方法是否为：按数量
     */
    get isMethodVolume() {
        return this.uistates.method === this.methods.volume;
    }

    /**
     * 当前交易方法是否为：按金额
     * 
     */
    get isMethodAmount() {
        return this.uistates.method === this.methods.amount;
    }

    /**
     * 当前交易方法是否为：可用现金（比例）~ 仅复制模式 + 买入，具有该方法
     */
    get isMethodCash() {
        return this.uistates.method === this.methods.acash;
    }

    /**
     * 当前交易方法是否为：持仓（比例）~ 仅复制模式 + 卖出，具有该方法
     */
    get isMethodPos() {
        return this.uistates.method === this.methods.apos;
    }

    /**
     * 当前交易方法是否为：净资产（比例）~ 仅复制模式 + 买入/卖出，具有该方法
     */
    get isMethodAsset() {
        return this.uistates.method === this.methods.asset;
    }

    constructor(view_name) {

        super(view_name);

        /**
         * 交易有关的默认设置
         */
        this.thresholds = {

            /** 价格小数位数精度 */
            precision: 2,
            /** 价格变化最小步长 */
            priceStep: 0.01,
            /** 数量变化最小步长 */
            volumeStep: 100,
            /** 合约乘数 */
            multiple: 1,
            /** 比例步长 */
            percentStep: 1,
            /** 金额步长 */
            amountStep: 10000,
            /** 最大涨停价 */
            maxCeiling: 1000000000,
            /** 最大允许金额 */
            maxAmount: 9999999999,
            /** 最大允许数量 */
            maxVolume: 9999999999,
        };

        var dirs = this.systemTrdEnum.tradingDirection;

        /**
         * 委托方向
         */
        this.directions = {

            buy: new CodeMeanItem(dirs.buy.code, dirs.buy.mean),
            sell: new CodeMeanItem(dirs.sell.code, dirs.sell.mean),
            adjust: new CodeMeanItem(0, '调仓'),
        };

        var effects = this.systemTrdEnum.positionEffect;

        /**
         * 开平仓标识
         */
        this.effects = {

            open: new CodeMeanItem(effects.open.code, effects.open.mean),
            close: new CodeMeanItem(effects.close.code, effects.close.mean),
            closeToday: new CodeMeanItem(effects.closeToday.code, effects.closeToday.mean),
        };

        var hdges = this.systemTrdEnum.hedgeFlag;
        var { hedge, Speculate} = hdges;
        this.hedgeFlags = { hedge, Speculate };

        /**
         * 适配当前场景的交易方向
         */
        this.sdirections = (/** @returns {Array<CodeMeanItem>} */ () => { return this.helper.dict2Array(this.directions); })();

        /**
         * 委托方式
         */
        this.methods = {

            volume: new TradeMethod(1, '数量', '股', '按绝对数量交易', 'center', { isByQuantity: true }),
            amount: new TradeMethod(2, '金额', '元', '按绝对金额交易', 'center', { isByMoney: true }),
            acash: new TradeMethod(3, '资金', '%', '按可用现金比例交易', 'center', { isByAvailableCash: true }),
            apos: new TradeMethod(4, '持仓', '%', '按可用持仓比例交易', 'center', { isByAvailablePos: true }),
            asset: new TradeMethod(5, '净资产', '%', '按净资产总额比例交易', 'center', { isByNetAsset: true }),
        };

        /**
         * 适配当前场景的交易方法
         */
        this.smethods = (/** @returns {Array<TradeMethod>} */ () => { return this.helper.dict2Array(this.methods); })();

        /**
         * 交易模式
         */
        this.modes = {

            duplicate: new CodeMeanItem(1, '复制模式'),
            devide: new CodeMeanItem(2, '摊派模式'),
        };

        /**
         * 下单面板通用核心状态
         */
        this.uistates = {

            mode: this.modes.duplicate.code,
            direction: this.directions.buy.code,
            effect: this.effects.open.code,
            method: this.methods.volume,
            
            /** 搜索关键字 */
            keywords: null,
            /** 价格 */
            price: 0,
            /** 最小价差 */
            priceStep: this.thresholds.priceStep,
            /** 价格小数精度 */
            precision: this.thresholds.precision,
            /** 跌停价 */
            floor: 0,
            /** 涨停价 */
            ceiling: 0,
            /** 委托数量 */
            scale: 0,
            /** 数量步长 */
            volumeStep: this.thresholds.volumeStep,
            /** 合约乘数 */
            multiple: this.thresholds.multiple,
            /** 业务类型 */
            businessFlag: 0,

            checkOpt: {

                /** 下单时，是否检测可用资金 */
                cash: true,
                /** 下单时，是否检测可用持仓 */
                position: true,
                /** 投机与套保 */
                hedgeFlag: 0,
            }
        };

        /**
         * 待交易目标账号列表
        */
        this.accounts = (/** @returns {Array<WeightedAccountDetail>} */ function() { return []; })();

        /**
         * 待委托预览列表
        */
        this.previews = (/** @returns {Array<OrderPreview>} */ function() { return []; })();

        /**
         * 启动核心事件监听
         */
        this.listen2Events();
    }

    listen2Events() {

        /**
         * 监听价格设置
         */
        this.registerEvent('set-as-price', this.setAsPrice.bind(this));

        /**
         * 监听LEVEL1的报价档位递交的：价格 & 方向 & 触发下单设置
         */
        this.registerEvent('set-as-price-and-direction', this.setAsPriceAndDirection.bind(this));

        /**
         * 监听首个tick数据
         */
        this.registerEvent('set-first-tick', this.digestTick.bind(this));

        /**
         * 监听目标交易账号变化
         */
        this.registerEvent('set-as-accounts', this.setAsAccounts.bind(this));

        /**
         * 监听由交易数据双击引发的合约指定
         */
        this.registerEvent('pick-up-instrument-by-record', this.handleInstrumentPickup.bind(this));
    }

    /**
     * 当前是否为开仓
     */
    isOpen() {
        return this.isEffectApplicable() && this.uistates.effect == this.effects.open.code;
    }

    /**
     * 当前是否为平仓
     */
    isClose() {
        return this.isEffectApplicable() && (this.uistates.effect == this.effects.close.code || this.uistates.effect == this.effects.closeToday.code);
    }

    /**
     * 对于当前交易单元，开平仓标识是否适用
     * @returns {Boolean}
     */
    isEffectApplicable() {
        return (this.isFuture || this.isOption) && !this.isAdjust;
    }

    isCloseTodayNotApplicable() {
        return !this.isShfeInstrument(this.states.instrument);
    }

    isRegardAsOpen() {
        return this.isBuy && !this.isEffectApplicable() || this.isOpen();
    }

    isRegardAsClose() {
        return this.isSell && !this.isEffectApplicable() || this.isClose();
    }

    isShfeInstrument(instru) {
        return !instru || typeof instru == 'string' && instru.toUpperCase().indexOf(this.systemTrdEnum.market.shfe.code) >= 0;
    }

    /**
     * @param {Number} price 
     */
    setAsPrice(price) {

        if (this.uistates.price == price) {
            return;
        }
        else if (price >= this.thresholds.maxCeiling) {
            return;
        }

        this.uistates.price = +price.toFixed(this.uistates.precision);

        /**
         * 由LEVEL1引起的价格、方向变更，进行重新摊派
         */
        this.allocate();
    }

    /**
     * @param {LevelMessage} message 
     */
    setAsPriceAndDirection(message) {

        this.uistates.price = +message.price.toFixed(this.uistates.precision);
        var dirs = this.directions;

        if (message.options.changeDirection) {

            if (message.isBuy) {

                if (this.isBuy) {
                    this.setAsDirection(dirs.sell.code);
                }
            }
            else if (message.isSell) {
                
                if (this.isSell) {
                    this.setAsDirection(dirs.buy.code);
                }
            }
        }

        /**
         * 由LEVEL1引起的价格、方向变更，进行重新摊派
         */

        this.allocate();

        if (message.options.promptConfirm) {
            this.hope2Entrust();
        }
    }

    setAsDirection(direction) {
        this.uistates.direction = direction;
    }

    /**
     * @param {TickData} tick 
     */
    digestTick(tick) {

        var uistates = this.uistates;
        uistates.price = tick.latest;
        uistates.floor = tick.floor;
        uistates.ceiling = tick.ceiling;
        this.allocate();
    }

    /**
     * @param {Number} price 
     */
    precisePrice(price) {

        if (price >= this.thresholds.maxCeiling) {
            return '---'
        }
        else {
            return typeof price == 'number' ? price.toFixed(this.states.precision) : price;
        }
    }

    /**
     * 在渠道切换时，将合约进行重置
     * @param {TradeChannel} channel
     */
    handleChannelChange(channel) {

        super.handleChannelChange(channel);
        this.uistates.keywords = null;
        this.handleClearIns();
        this.specialize2Channel(channel);
        this.resetBusinessFlag();
        this.filterMethods();
    }

    resetBusinessFlag(){};
    
    /**
     * @param {TradeChannel} channel
     */
    specialize2Channel(channel) {

        if (channel.options.isFuture || channel.options.isOption) {
            
            this.methods.amount.mean = '市值';
            this.methods.amount.tooltip = '按市值交易';
        }
        else {
            
            this.methods.amount.mean = '金额';
            this.methods.amount.tooltip = '按绝对金额交易';
        }
    }

    handleDirectionChange() {

        this.filterMethods();
        this.allocate();
    }

    handlePositionEffectChange() {
        this.allocate();
    }

    handlePriceChange() {
        this.allocate();
    }

    handleScaleChange() {
        this.allocate();
    }

    /**
     * @param {Array<WeightedAccountDetail>} accounts
     */
    setAsAccounts(accounts) {

        this.accounts.refill(accounts);
        /** 目标交易账号列表是否已发生变更 */
        this.isAccountRefilled = true;
        this.allocate();
    }

    /**
     * 响应交易数据选中动作
     * @param {*} instrument 
     * @param {*} direction 
     */
    handleInstrumentPickup(instrument, direction) {

        /**
         * 各交易单元对交易方向，处理规则不同，无法统一作处理，暂不强制自动预设
         */

        var matches = BizHelper.filterInstruments(this.channel.assetType, instrument);
        if (matches.length > 0) {

            /**
             * 模拟合约选中操作
             */
            this.handleSelect(matches[0]);
        }
        else {
            console.error('cannot find instrument info with code = ' + instrument);
        }
    }


    /**
     * @param {WeightedAccountDetail} account 
     * @param {Number} price 
     */
    calculateMaxCanBuy(account, price) {

        if (price <= 0) {
            return 0;
        }

        const multiple = this.uistates.multiple;
        const { available } = account;
        var max_can_open = 0;

        if (this.states.isFutureOrOption) {

            var one_hand_margin = 0;
            
            if (typeof multiple == 'number' && this.doesFutureHasMarginRate(account)) {

                const { longMarginRatioByMoney, longMarginRatioByVolume, shortMarginRatioByMoney, shortMarginRatioByVolume } = account;
                let long_margin_ratio = Math.max(longMarginRatioByMoney, longMarginRatioByVolume);
                let short_margin_ratio = Math.max(shortMarginRatioByMoney, shortMarginRatioByVolume);
                one_hand_margin = price * multiple * (this.isBuy ? long_margin_ratio : short_margin_ratio);
            }

            /** 最大可开手数 */
            max_can_open = one_hand_margin == 0 ? 0 : Math.floor(available / one_hand_margin);
        }
        else {
            max_can_open = Math.floor(available / price / 100) * 100;
        }

        return max_can_open;
    }

    /**
     * @param {WeightedAccountDetail} account 
     */
    doesFutureHasMarginRate(account) {

        if (!this.states.isFutureOrOption) {

            /**
             * 非期货期权，保证金率自然无从谈起
             */

            return true;
        }

        let {
            longMarginRatioByMoney, 
            longMarginRatioByVolume, 
            shortMarginRatioByMoney, 
            shortMarginRatioByVolume,
        } = account;

        return typeof longMarginRatioByMoney == 'number'
                && typeof longMarginRatioByVolume == 'number'
                && typeof shortMarginRatioByMoney == 'number'
                && typeof shortMarginRatioByVolume == 'number';
    }

    /**
     * @param {Number} volume 
     * @param {Number} volumeStep 
     */
    trim(volume, volumeStep) {
        return parseInt(volume / volumeStep) * volumeStep;
    }

    /**
     * @param {Number} expected 期望的交易量（很可能是，非整数）
     * @param {Number} price 期望的交易价格
     * @param {WeightedAccountDetail} account 发生交易目标账号
     */
    decide(expected, price, account) {

        function thousands(num) {
            return typeof num == 'number' ? num.thousands() : num;
        }
        
        var step = this.uistates.volumeStep;
        var maxCanLabel = '最大可';
        var decided = 0;
        var explain = null;
        var clsname = null;
        var okCls = 's-color-green';
        var errCls = 's-color-red';

        if (!this.doesFutureHasMarginRate(account)) {
            
            return {
                volume: 0,
                remark: { content: '保证金率缺失', clsname: errCls },
            };
        }

        if (this.isRegardAsOpen()) {

            let dirName = this.isOpen() ? '开仓' : '买入';
            let maxCanBuy = Math.max(0, this.trim(this.calculateMaxCanBuy(account, price), step));
            decided = this.trim(Math.min(expected, maxCanBuy), step);

            if (maxCanBuy == 0) {

                decided = 0;
                explain = `${maxCanLabel}${dirName} = 0`;
                clsname = errCls;
            }
            else if (expected > maxCanBuy) {

                explain = `${maxCanLabel}${dirName} = ${thousands(maxCanBuy)} / 期望${dirName} = ${thousands(expected)} / 实际${dirName} = ${thousands(decided)}`;
                clsname = errCls;
            }
            else if (expected < step) {

                decided = 0;
                explain = `最少需${dirName}${step}`;
                clsname = errCls;
            }
            else {

                // explain = `${maxCanLabel}${dirName} = ${thousands(maxCanBuy)} / 期望${dirName} = ${thousands(expected)}`;
                // clsname = okCls;
            }
        }
        else if (this.isRegardAsClose()) {

            let dirName = this.isClose() ? '平仓' : '卖出';
            let position = this.states.isFutureOrOption ? (this.isBuy ? account.shortPosition : account.longPosition) : account.longPosition;
            let maxCanSell = parseInt(position);

            if (maxCanSell <= 0) {
                
                decided = 0;
                explain = `${maxCanLabel}${dirName} = 0`;
                clsname = errCls;
            }
            else if (maxCanSell < step) {

                /**
                 * 1. 只剩下零散持仓时，只允许一次性全部交易
                 * 2. 否则，不可交易任何数量
                 */
                decided = expected >= maxCanSell ? maxCanSell : 0;

                if (expected > maxCanSell) {

                    explain = `${maxCanLabel}${dirName} = ${thousands(maxCanSell)} / 期望${dirName} = ${thousands(expected)} / 零散份额全部${dirName}`;
                    clsname = errCls;
                }
                else if (expected < maxCanSell) {

                    explain = `${maxCanLabel}${dirName} = ${thousands(maxCanSell)} / 期望${dirName} = ${thousands(expected)} / 零散份额，需一次性全部${dirName}`;
                    clsname = errCls;
                }
            }
            else if (maxCanSell == step) {
                
                decided = expected >= step ? step : 0;

                if (decided == 0) {

                    explain = `${maxCanLabel}${dirName} = ${step}，只能全部交易`;
                    clsname = errCls;
                }
            }
            else {

                if (expected > maxCanSell) {

                    decided = maxCanSell;
                    explain = `${maxCanLabel}${dirName} = ${thousands(maxCanSell)} / 期望${dirName} = ${thousands(expected)} / 实际${dirName} = ${thousands(decided)}`;
                    clsname = errCls;
                }
                else if (expected == maxCanSell) {
                    decided = expected;
                }
                else {
                    
                    if (expected % step == 0) {
                        decided = expected;
                    }
                    else {

                        let residue = maxCanSell % step;
                        let hasResidue = residue > 0;

                        if (hasResidue) {
                            decided = this.trim(expected, step) + (expected % step >= residue ? residue : 0);
                        }
                        else {
                            decided = this.trim(expected, step);
                        }
                    }

                    if (decided < expected) {

                        explain = `${maxCanLabel}${dirName} = ${thousands(maxCanSell)} / 期望${dirName} = ${thousands(expected)} / 实际${dirName} = ${thousands(decided)}`;
                        clsname = errCls;
                    }
                }
            }
        }

        return {

            volume: decided, 
            remark: { content: explain, clsname: clsname },
        };
    }

    /**
     * @param {WeightedAccountDetail} account 
     * @param {Number} base 总权重
     * @param {Number} price 委托价格
     * @param {Number} scale 期望委托规模
     */
    distribute(account, base, price, scale) {

        var isPriceOk = !isNaN(price) && price > 0;

        /** 针对账号下单，最终换算结果 */
        var result = { 
            
            volume: 0, 
            remark: { content: null, clsname: null } 
        };

        var aweight = account.multiple;
        var multiple = this.uistates.multiple;
        var volumeStep = this.uistates.volumeStep;

        /**
         * 将复制与分摊模式进行统一换算
         */
        var shared = this.isDuplicate ? 1 : aweight / base;

        if (this.isDevide && (isNaN(aweight) || aweight <= 0)) {
            
            /**
             * 未指定权重，无法摊派
             */

            result.volume = 0;
            result.remark.content = '请指定权重';
            result.remark.clsname = 's-color-red';
        }
        else if (this.isMethodVolume) {
            
            /**
             * 委托数量 》委托数量
             */

            result = this.decide(scale * shared, price, account);
        }
        else if (this.isMethodAmount) {

            /**
             * 委托金额 》委托数量
             */

            result = !isPriceOk ? 0 : this.decide(scale * shared / price / multiple, price, account);
        }
        else if (this.isMethodCash) {

            /**
             * 可用现金比例 》委托数量
             */

            result = !isPriceOk ? 0 : this.decide(account.available * scale * 0.01 / price / multiple, price, account);
        }
        else if (this.isMethodPos) {

            /**
             * 按【持仓比例】进行交易，仅在【股票】+【卖出】适用
             */
            let allPos = account.longPosition;
            let position = this.trim(allPos * scale * 0.01, volumeStep);

            /**
             * 可用持仓比例 》委托数量
             */

            result = this.decide(position, price, account);
        }
        else if (this.isMethodAsset) {

            /**
             * 净资产比例 》委托数量
             */

            result = !isPriceOk ? 0 : this.decide(account.balance * scale * 0.01 / price / multiple, price, account);
        }

        return { account, volume: result.volume, remark: result.remark };
    }

    /**
     * @param {WeightedAccountDetail} account 
     * @param {Number} volume 委托数量
     * @param {{ content, clsname }} remark 备注
     */
    makePreview(account, volume, remark) {
        
        var dirs = this.directions;
        var effect = this.isOpen() || this.isClose() ? this.uistates.effect : 0;
        var material = Object.assign(this.helper.deepClone(account), {

            id: WeightedAccountDetail.MakeId(account),
            businessFlag: this.channel.options.isSpot ? (account.isCredit ? 1 : 0) : this.uistates.businessFlag,
            direction: this.isBuy ? dirs.buy.code : this.isSell ? dirs.sell.code : dirs.adjust.code,
            positionEffect: effect,
            orderPrice: this.uistates.price,
            volumeOriginal: volume,
            remark: remark,
        });

        return new OrderPreview(material);
    }

    allocate() {

        /**
         * 将委托预览列表清空
         */
        this.previews.clear();

        var accounts = this.accounts;
        var scale = this.uistates.scale;
        var base = accounts.map(item => item.multiple).sum();
        var isScaleInvalid = isNaN(scale) || scale <= 0;
        var isBaseInvalid = this.isDevide && (isNaN(base) || base <= 0);
        var isInvalid = isScaleInvalid || isBaseInvalid;
        var price = this.uistates.price;
        var event2Account = 'allocate-volume-to-account';
        var event2Preview = 'set-as-order-preview';

        if (isInvalid) {

            let errorMsg = isScaleInvalid ? `${this.uistates.method.mean}/未指定` : '分摊模式下，权重基数未指定';
            let shares = accounts.map(account => ({ account: account, volume: 0 }));
            let orders = accounts.map(account => this.makePreview(account, 0, { content: errorMsg, clsname: 's-color-red' }));

            this.trigger(event2Account, shares);
            this.trigger(event2Preview, this.isAccountRefilled, orders);
        }
        else {
            
            let allocations = accounts.map(account => this.distribute(account, base, price, scale));
            let shares = allocations.map(item => ({ account: item.account, volume: item.volume }));
            let orders = allocations.map(item => this.makePreview(item.account, item.volume, item.remark));

            this.previews.merge(orders);
            this.trigger(event2Account, shares);
            this.trigger(event2Preview, this.isAccountRefilled, orders);
        }

        this.isAccountRefilled = false;
    }

    makeDisplayUnit() {
        
        if (this.isBond) {
            return '张';
        }
        else if (this.isFuture || this.isOption) {
            return '手';
        }
        else {
            return '股';
        }
    }

    formatDirName() {
        return this.isBuy ? this.directions.buy.mean : this.isSell ? this.directions.sell.mean : this.directions.adjust.mean;
    }

    hope2Entrust() {

        var uistates = this.uistates;

        if (!this.states.instrument) {

            this.interaction.showError('请输入合约');
            return;
        }
        
        if (!(uistates.price > 0)) {

            this.interaction.showError('请输入有效委托价格');
            return;
        }

        if (!Number.isInteger(this.helper.safeDevide(uistates.price, uistates.priceStep))) {

            this.interaction.showError(`委托价格${uistates.price}，非最小价差${uistates.priceStep}，的整数倍`);
            return;
        }

        if (uistates.price < uistates.floor && !this.states.isBuyBack) {

            this.interaction.showError(`委托价格${uistates.price} < 跌停价${uistates.floor}`);
            return;
        }

        if (uistates.ceiling > 0 && uistates.price > uistates.ceiling && !this.states.isBuyBack) {

            this.interaction.showError(`委托价格${uistates.price} > 涨停价${uistates.ceiling}`);
            return;
        }

        if (!(uistates.scale > 0)) {

            this.interaction.showError('请输入委托' + uistates.method.mean);
            return;
        }

        if (this.accounts.length == 0) {

            this.interaction.showError('请选择参与交易的账号');
            return;
        }
        
        var assets = this.accounts.map(item => item.assetType).distinct();
        if (assets.length > 1) {

            this.interaction.showError('包含了>=2种资产类型的账号');
            return;
        }

        var total_account = this.previews.length;
        var targets = this.previews.filter(item => item.volumeOriginal > 0);
        if (targets.length == 0) {

            this.interaction.showError('下方预览的委托, 下单数量均为0, 无法构建有效委托');
            return;
        }

        let dirName = this.formatDirName();
        let dirCls = this.isBuy ? 's-color-red' : this.isSell ? 's-color-green' : undefined;
        let multiple = BizHelper.getVolumeMultiple(this.states.instrument);
        let t_volume = 0, t_amount = 0, t_totalMargin = 0, t_margin = 0, t_maxCanOpen = 0;

        targets.forEach(item => {

            t_volume += item.volumeOriginal;
            t_amount += item.amount;
            t_totalMargin += item.totalMargin;
            t_margin += item.margin;
            t_maxCanOpen += item.maxCanOpen;
        });
        
        let mentions = [

            ['模式', this.isDuplicate ? this.modes.duplicate.mean : this.modes.devide.mean],
            ['方向', dirName],
            ['合约', `${this.states.instrument} / ${this.states.instrumentName}`],
            ['方式', uistates.method.tooltip],
            [null, '---------'],
            ['价格', uistates.price],
            ['预期', `账户数 = ${total_account}, 下单数量 = ${uistates.scale * (this.isDuplicate ? total_account : 1)} (${this.makeDisplayUnit()})`],
            ['实际', `账户数 = ${targets.length}, 下单数量 = ${t_volume.thousands()} (${this.makeDisplayUnit()})`],
            ['总金额', (t_amount * multiple).thousands()],
        ];

        if (this.isFuture || this.isOption) {

            mentions.push([null, '---------']);
            mentions.push(['占用保证金', t_totalMargin.thousands()]);
            mentions.push(['已用保证金', t_margin.thousands()]);
            mentions.push(['最大可开', t_maxCanOpen.thousands()]);
        }
        
        let message = mentions.map(item => `<div><span>${item[0] || '----'}${item[0] ? '： ' : ''}</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        this.interaction.showConfirm({

            title: '批量委托确认',
            message: message,
            confirmed: () => {

                this.sendOutOrder(targets);
                this.trigger('batch-order-made');
                this.uistates.scale = 0;
                this.handleScaleChange();
                this.interaction.showSuccess('委托已发出，数量 = ' + previews.length);
            },
        });
    }

    /**
     * @param {Array<OrderPreview>} previews 
     */
    sendOutOrder(previews) {
        throw new Error('not implemented');
    }

    /**
     * @param {TradeMethod} method 
     */
    handleMethodChange(method) {

        this.uistates.method = method;
        this.uistates.scale = 0;
        this.allocate();
    }

    handleModeChange() {

        this.filterDirs();
        this.filterMethods();
        this.allocate();
        this.trigger('trade-mode-change', this.isDevide);
    }

    filterDirs() {

        this.sdirections.clear();
        var dirs = this.directions;

        /**
         * 根据选择的模式，填充适配的交易方向
         */

        if (this.isDevide) {

            /**
             * 摊派模式，不支持调仓
             */
            
            this.sdirections.merge([dirs.buy, dirs.sell]);

            if (this.isAdjust) {
                this.uistates.direction = dirs.buy.code;
            }
        }
        else {

            /**
             * 复制模式，支持所有交易方向
             */

            this.sdirections.merge(this.helper.dict2Array(dirs));
        }
    }

    filterMethods() {

        const { volume, amount, acash, apos, asset } = this.methods;
        const smethods = this.smethods;
        smethods.clear();

        if (this.isDuplicate) {

            if (this.isBuy) {
                smethods.merge([volume, amount, acash, asset]);
            }
            else if (this.isSell) {

                if (this.states.isFutureOrOption) {
                    smethods.merge([volume, amount, asset]);
                }
                else {
                    smethods.merge([volume, amount, apos, asset]);
                }
            }
            else if (this.isAdjust) {
                smethods.merge([volume, amount]);
            }
        }
        else {

            if (this.isBuy || this.isSell) {
                smethods.merge([volume, amount]);
            }
        }

        if (smethods.length > 0) {
            
            /**
             * 检测是否因上一轮选中的交易方法，已不在当前支持的交易方法中
             */

            let matched = smethods.find(item => item == this.uistates.method);
            if (matched === undefined) {

                /** 重置为第一个，当前所支持的交易方法 */
                this.uistates.method = smethods[0];
                /** 交易方法本身发生变更，重置下单数量 */
                this.uistates.scale = 0;
            }
        }
    }

    saveSetting() {
        this.closeSetting();
    }

    closeSetting() {
        this.dialog.visible = false;
    }

    decideScaleStep() {

        var dict = this.methods;
        var mtd = this.uistates.method;

        if (mtd === dict.volume) {
            return (this.isFuture || this.isOption) ? 1 : this.isBond ? 10 : this.uistates.volumeStep;
        }
        else if (mtd === dict.amount) {

            if (this.uistates.price > 0) {
                return +(this.uistates.volumeStep * this.uistates.multiple * this.uistates.price).toFixed(2);
            }
            else {
                return this.thresholds.amountStep;
            }
        }
        else if (mtd === dict.acash || mtd === dict.apos || mtd === dict.asset) {
            return this.thresholds.percentStep;
        }
        else {
            return 0;
        }
    }

    decideMaxScale() {
        
        var dict = this.methods;
        switch (this.uistates.method) {

            case dict.volume : return this.thresholds.maxVolume;
            case dict.amount: return this.thresholds.maxAmount;
            case dict.acash:
            case dict.apos:
            case dict.asset: return 100;
            default: return 0;
        }
    }

    handleSelect(selected) {

        var insInfo = new InstrumentInfo({

            assetType: this.channel.assetType,
            exchangeId: selected.exchangeId,
            instrument: selected.instrument,
            instrumentName: selected.instrumentName,
            precision: selected.pricePrecision,
            priceStep: selected.priceTick || this.thresholds.priceStep,
            volumeStep: this.isFuture || this.isOption ? 1 : this.thresholds.volumeStep,
            multiple: selected.volumeMultiple,
        });

        var uistates = this.uistates;
        uistates.keywords = `${insInfo.instrumentName}-${insInfo.instrument}`;
        uistates.priceStep = insInfo.priceStep;
        uistates.precision = insInfo.precision;
        uistates.volumeStep = insInfo.volumeStep;
        uistates.multiple = insInfo.multiple;

        uistates.price = 0;
        uistates.floor = 0;
        uistates.ceiling = this.thresholds.maxCeiling;
        uistates.scale = 0;

        if (this.uistates.effect == this.effects.closeToday.code && !this.isShfeInstrument(selected.instrument)) {
            uistates.effect = this.effects.open.code;
        }

        this.trigger('selected-one-instrument', insInfo);
    }

    /**
     * @param {String} keywords 
     * @param {Function} callback 
     */
    suggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }
        
        let matches = BizHelper.filterInstruments(this.channel.assetType, keywords);

        /**
         * 当为现货时，将回购品种包含在搜索范围内
         */
        if (this.isSpot) {

            let matches2 = BizHelper.filterInstruments(this.systemEnum.assetsType.buyback.code, keywords);
            matches.push(...matches2);
        }

        /**
         * 对两次独立搜索结果进行合并，并去重
         */

        let map = {};
        matches.forEach(item => {

            if (map[item.instrument] == undefined) {
                map[item.instrument] = item;
            }
        });

        matches = Object.values(map);

        /**
         * 仅有一个搜索结果时，自动选择
         */

        if (matches.length == 1) {

            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleUserInput() {

        if (event.keyCode == 8) {

            event.returnValue = false;
            this.uistates.keywords = null;
            this.handleClearIns();
        }
        else if (typeof this.uistates.keywords == 'string' && this.uistates.keywords.trim().length == 0) {
            this.handleClearIns();
        }
    }

    handleClearIns() {

        this.uistates.priceStep = this.thresholds.priceStep;
        this.uistates.precision = this.thresholds.precision;
        this.uistates.volumeStep = this.thresholds.volumeStep;
        this.uistates.multiple = this.thresholds.multiple;
        this.uistates.price = 0;
        this.uistates.floor = 0;
        this.uistates.ceiling = this.thresholds.maxCeiling;
        this.uistates.scale = 0;
        this.trigger('selected-one-instrument');
    }

    build($container) {
        super.build($container);
    }
}

module.exports = { TradeView, TradeMethod };