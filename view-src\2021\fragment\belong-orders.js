const { TypicalDataView } = require('../classcial/typical-data-view');
const { ModelConverter } = require('../../../model/model-converter');
const { Order } = require('../../../model/order');
const { OrderTitles } = require('./bases/trade-record-title');
const { repoOrder } = require('../../../repository/order');

class View extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '委托记录');
        this.headers = OrderTitles;        
        this.statuses = {

            all: { code: 0, mean: '全部' },
            cancellable: { code: 1, mean: '可撤' },
            invalid: { code: 2, mean: '废单' },
        };

        this.states = {

            status: this.statuses.all.code,
            keywords: null,
        };

        var ordStatus = this.systemEnum.orderStatus;
        this.invalidStatus = ordStatus.invalid.code;
        this.cancellables = [{ code: null, mean: null }];
        this.cancellables.pop();
        this.cancellables.merge(this.helper.dict2Array(ordStatus).filter(x => !x.isCompleted));
    }

    createToolbar() {

        return new Vue({

            el: this.$toolbar,
            data: {

                states: this.states,
                statuses: this.statuses,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleSearch,
                this.hope2CancelCheckeds,
                this.hope2CancelAll,
                this.hope2Replace,
            ]),
        });
    }

    /**
     * @param {Order} record 
     */
    testRecords(record) {
        
        var statuses = this.statuses;
        var isAll = this.states.status == statuses.all.code;
        var isCancellable = this.states.status == statuses.cancellable.code;
        var isInvalid = this.states.status == statuses.invalid.code;

        return (isAll
            || isCancellable && this.cancellables.some(x => x.code == record.orderStatus) 
            || isInvalid && record.orderStatus == this.invalidStatus)
            && this.tableObj.matchKeywords(record);
    }

    hope2CancelCheckeds() {

        if (this.tableObj.rowCount == 0) {
            return this.interaction.showMessage('当前无委托');
        }

        var checkeds = this.typeRecords(this.extractRecords());
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要撤单的委托');
        }

        var cancellables = checkeds.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {
            return this.interaction.showError(`勾选委托 = ${checkeds.length}，可撤委托 = 0`);
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `勾选委托 = ${checkeds.length}，可撤委托 = ${cancellables.length}，是否确定？`,
            confirmed: () => {
                this.cancel(cancellables);
            },
        });
    }

    hope2CancelAll() {

        if (this.tableObj.rowCount == 0) {
            return this.interaction.showMessage('当前无委托');
        }

        var all = this.typeRecords(this.extractRecords(true));
        var cancellables = all.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {
            return this.interaction.showError(`所有委托 = ${all.length}，可撤委托 = 0`);
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `所有委托 = ${all.length}，可撤委托 = ${cancellables.length}，是否确定？`,
            confirmed: () => {
                this.cancel(cancellables);
            },
        });
    }

    hope2Replace() {
        
        if (this.tableObj.rowCount == 0) {
            return this.interaction.showMessage('当前无订单');
        }

        if (this.tableObj.filteredRowCount == 0) {
            return this.interaction.showMessage('筛选结果无订单');
        }

        var checkeds = this.typeRecords(this.extractRecords());
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要追单的合约');
        }

        var orders = checkeds.filter(item => !item.isCompleted);
        if (orders.length == 0) {

            this.interaction.showError(`勾选订单 = ${checkeds.length}，可（撤）追单数 = 0`);
            return;
        }

        this.replaceOrder(orders);
    }

    /**
     * @param {Array<Order>} records
     * @returns {Array<Order>}
     */
    typeRecords(records) {
        return records;
    }

    /**
     * 追单
     * @param {Array<Order>} orders 
     */
    replaceOrder(orders) {
        
        if (this.dialogReplacing === undefined) {
            
            var DialogReplaceOrder = require('./dialog-replace-orders');
            var dialog = new DialogReplaceOrder('@2021/fragment/dialog-replace-orders', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', orders); });
            this.dialogReplacing = dialog;
        }
        else {
            this.dialogReplacing.trigger('showup', orders, false);
        }
    }

    handleContextChange(belongId) {

        if (belongId === this.belongId) {
            return;
        }

        this.belongId = belongId;
        this.tableObj.clear();
        
        if (this.helper.isNone(belongId)) {
            this.stop2Refresh();
        }
        else {
            
            this.resetControls();
            this.requestRecords();
            this.resume2Refresh();
        }
    }

    async requestRecords() {

        var resp = await repoOrder.requestBelongOrders(this.belongId);
        var records = (resp.data || {}).contents || [];
        records.shift();
        var orders = ModelConverter.formalizeOrders(this.headers, records);
        this.tableObj.refill(orders);
    }

    resetControls() {

        this.states.status = this.statuses.all.code;
        this.states.keywords = null;
        this.tableObj.removeAllColFilters();
    }

    /**
     * @param {Order} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button event.onclick="cancelSingle">撤单</button>';
    }

    /**
     * 单一撤单
     * @param {Order} order 
     */
    cancelSingle(order) {

        this.interaction.showConfirm({

            title: '撤单确认',
            message: '是否撤销该委托？',
            confirmed: () => {
                this.cancel([order]);
            },
        });
    }

    /**
     * 撤单
     * @param {Array<Order>} orders 
     */
    cancel(orders) {

        orders.forEach(order => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: order.id });
        });

        this.interaction.showSuccess(`撤单请求已发出，数量 = ${orders.length}`);
    }

    refresh() {
        
        if (this.helper.isNotNone(this.belongId)) {
            super.refresh();
        }        
    }

    build($container, options) {

        super.build($container, options, {

            tableName: 'smt-fbo',
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
        });
        
        /** 监听上下文切换 */
        this.registerEvent('set-context', this.handleContextChange.bind(this));
        this.stop2Refresh();
        this.timelyRefresh(1000 * 30);
    }
}

module.exports = View;