<div class="drag-widget">
    <el-tabs v-model="activeName" class="type-tab" type="card"> <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.name"></el-tab-pane> </el-tabs>
    <el-input class="query" v-model="query" placeholder="搜索指标" clearable></el-input>
    <el-scrollbar class="sidebar-container" wrap-class="scrollbar-wrapper">
        <div :style="widgetStyle">
            <div class="type-box f-tac" v-for="element in types" :key="element.key" :style="element.wStyle" :data-id="element.id" @mousedown="handleDown">{{ element.name }}</div>
        </div>
    </el-scrollbar>
    <div v-if="deleting" class="delete-area iconfont"></div>
</div>
