import type { InjectionKey, Ref, Reactive } from 'vue';
import type { TableColumnConfigParam } from './types';

/** 选中合约依赖注入 */
export const INSTRUMENT_SELECT_KEY = Symbol() as InjectionKey<Ref<string>>;

/** 表格列配置依赖注入 */
export const TABLE_COLUMN_SELECT_KEY = Symbol() as InjectionKey<Reactive<TableColumnConfigParam>>;

/** 盘口点击价格依赖注入 */
export const TRADE_LEVEL_CLICK_KEY = Symbol() as InjectionKey<Ref<number>>;
