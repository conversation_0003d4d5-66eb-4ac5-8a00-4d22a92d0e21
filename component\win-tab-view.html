<!DOCTYPE html>
<html>
    <head>
        <title>云信KS主动资产管理交易系统</title>
        <meta http-equiv="pragma" content="no-cache" />
        <meta http-equiv="cache-control" content="no-cache" />
        <meta http-equiv="expires" content="0" />
        <meta charset="utf-8" />
        <link rel="stylesheet" href="../asset/css/3rd/iconfont/iconfont.css" />
        <link rel="stylesheet" href="../asset/css/3rd/element-ui.css" />
        <link rel="stylesheet" href="../asset/css/smart-table.css" />
        <link rel="stylesheet" href="../asset/css/common.css" />
        <link rel="stylesheet" href="../asset/css/common-old.css" />
        <link rel="stylesheet" href="../asset/css/module-elem-ui.css" />
        <link rel="stylesheet" href="../asset/css/module-splitter.css" />
        <link rel="stylesheet" href="../asset/css/module-business.css" />
        <link rel="stylesheet" href="../asset/css/module-tab.css" />
        <link rel="stylesheet" href="../asset/css/module-toolbar.css" />
        <link rel="stylesheet" href="../asset/css/themed-dark.css" id="link-theme-stylesheet" />
        <link rel="stylesheet" href="../asset/css/themed-dark-old.css" id="link-theme-stylesheet-2" />
        <link rel="stylesheet" href="./win-tab-view.css" />
    </head>
    <body class="themed-bg">
        <div class="body-layout">
            <div class="layout-header">
                <div class="layout-header-inner">
                    <div class="win-buttons button-group">
                        <template>
                            <span class="s-unselectable">
                                <a @click="minimize" class="s-opacity-7 s-opacity-hover iconfont icon-zuixiaohua" title="最小化"></a>
                                <a @click="maximize" v-show="showMaximize" class="s-opacity-7 s-opacity-hover iconfont icon-zuidahua" title="最大化"></a>
                                <a @click="unmaximize" v-show="showRestore" class="s-opacity-7 s-opacity-hover iconfont icon-huanyuan" title="还原"></a>
                                <a @click="close" class="s-opacity-7 s-opacity-hover iconfont icon-guanbi" title="关闭"></a>
                            </span>
                        </template>
                    </div>
                    <div class="win-title s-dragable s-unselectable" id="window-header-inner">云信KS主动资产管理交易系统</div>
                </div>
            </div>

            <div class="layout-content">
                <div class="win-top-tabs">
                    <!-- tabs -->
                </div>

                <div class="win-top-content">
                    <!-- tabs content -->
                </div>
            </div>
        </div>

        <script type="text/javascript" src="../libs/3rd/vue.js"></script>
        <script type="text/javascript" src="../libs/3rd/element-ui.js"></script>
        <script type="text/javascript" src="../libs/type-extension.js"></script>
        <script type="text/javascript" src="./win-tab-view-entry.js"></script>
    </body>
</html>
