const http = require('../libs/http').http;
const { FundInfo } = require('../model/fund');

class FundRepository {

    /**
     * @returns {{ errorCode: number, errorMsg: string, data: Array<FundInfo> } }}
     */
    getAll(condition) {
        return new Promise((resolve, reject) => {
            http.get('/fund', {
                params: {
                    account_id: condition ? condition.account_id : '',
                    strategy_id: condition ? condition.strategy_id : '',
                },
            }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    /**
     * @returns {{ errorCode: number, errorMsg: string, data: Array<{ fundId, fundName, fundAccounts: Array<{ accountId, accountName, financeAccount, assetType }> }> }}
     */
    getFundDetail(fundIds) {
        return new Promise((resolve, reject) => {
            http.post('/fund/detail', fundIds).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    createFund(fund) {
        return new Promise((resolve, reject) => {
            http.post('/fund', fund).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    updateFund(fund) {
        return new Promise((resolve, reject) => {
            http.put('/fund', fund).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    deleteFund(fund_id) {
        return new Promise((resolve, reject) => {
            http.delete('/fund', { params: { fund_id: fund_id } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    /**
     * 更新设置产品的场外资产与负债 
     */
    updateOffsite(id, offsiteDebt, offsiteAsset) {
        return new Promise((resolve, reject) => {
            http.put('../v4/fund/update/detail', null, { params: { id, offsiteDebt, offsiteAsset }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    // 扩展操作

    shareFund2User(fund_id, user_ids) {
        return new Promise((resolve, reject) => {
            http.post('/fund/share', user_ids, { params: { fund_id: fund_id, share_type: 1 } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    shareTradeAndRiskForFund(fund_id, users) {
        return new Promise((resolve, reject) => {
            http.post("/fund/permission/clone?fund_id="+fund_id,  { tradeUserIds: users} ).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    bindAccount2Fund(fund_id, account_ids) {
        return new Promise((resolve, reject) => {
            http.post('/account/bind-fund', account_ids, { params: { fund_id: fund_id } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    queryHistoryNav(identityType, identityId) {

        return new Promise((resolve, reject) => {

            http.get('/fund/nav', { params: { identity_type: identityType, identity: identityId } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    updateFundRefNav(fund_id, nav) {
        return new Promise((resolve, reject) => {
            http.put('/fund/nav', null, { params: { fund_id, nav } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    /**
     * 获取查询需要的产品信息
     */
    getFundAll(user_id) {

        return new Promise((resolve, reject) => {

            http.get('../v4/fund/strategy', { params: { user_id }}).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    /**
     * 获取查询需要的产品权益信息
     */
    getFundPosition(condition) {

        return new Promise((resolve, reject) => {
            http.get('../v4/fund', {
            // http.get('/fund', {
                params: {
                    user_id: condition ? condition.userId : '', 
                },
            }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getHistoryNav(identity_type, identity) {

        return new Promise((resolve, reject) => {

            http.get('../v4/fund/nav', { params: { identity_type, identity } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
}

module.exports = { repoFund: new FundRepository() };
