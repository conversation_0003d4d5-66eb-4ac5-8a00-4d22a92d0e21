<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElTree } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { Repos, type RiskIndicator } from '../../../../xtrade-sdk/dist';
import type { ContextualIndicatorInfo } from '@/types/riskc';

import {
  buildTree,
  filterNode,
  isLeafNodeAndNoComponent,
  type LeafNode,
  type Level1Node,
  type Level2Node,
} from './IndicatorTreeNodeLogic';
import { deepClone } from '@/script';

const repoInstance = new Repos.RiskControlRepo();
// 根据设计图生成树形数据
const treeNodes = ref<Level1Node[]>([]);
const nodeList = ref<RiskIndicator[]>([]);
const $treeRef = ref<TreeInstance>();

async function request() {
  const indicators = (await repoInstance.QueryIndicators()).data || [];
  treeNodes.value = buildTree(indicators);
  nodeList.value = indicators;
  const id2Names = indicators.map(x => ({
    indicatorId: x.id,
    indicatorName: x.indicatorName,
    componentName: x.clientName,
  }));
  emitter('report', id2Names);
}

const emitter = defineEmits<{
  report: [indicators: ContextualIndicatorInfo[]];
  select: [item: RiskIndicator | null, idc_ids: number[]];
}>();

const filterText = ref('');

watch(
  () => filterText.value,
  value => {
    $treeRef.value!.filter(value);
  },
);

function handleClick(item: LeafNode | Level2Node | Level1Node) {
  const is_leaf = !!(item as LeafNode).indicator;
  const is_level2 = !is_leaf && !!(item as Level2Node).children[0].indicator;
  const is_level1 = !is_leaf && !is_level2;
  const consist_idc_ids: number[] = [];

  if (is_leaf) {
    consist_idc_ids.push((item as LeafNode).indicator.id);
  } else if (is_level2) {
    const leafs = (item as Level2Node).children;
    const ids = leafs.map(x => x.indicator.id);
    consist_idc_ids.push(...ids);
  } else if (is_level1) {
    const level2s = (item as Level1Node).children;
    const leafs = level2s.map(x => x.children).flat();
    const ids = leafs.map(x => x.indicator.id);
    consist_idc_ids.push(...ids);
  }

  emitter('select', is_leaf ? (item as LeafNode).indicator : null, consist_idc_ids);
}

function getSelcteds() {
  return ($treeRef.value!.getCheckedNodes(true, false) as LeafNode[]).map(x => x.indicator);
}

function getCurrent() {
  const node = $treeRef.value!.getCurrentNode() as LeafNode;
  return node?.indicator || null;
}

const indicatorsCountMap = ref<Record<number, number>>({});

function setIndicatorsCount(statistics: { indicatorId: number; count: number }[]) {
  const countMap: Record<number | string, number> = {};
  statistics.forEach(item => {
    // 具体叶子节点的指标统计数量
    const total = item.count;
    countMap[item.indicatorId] = total;
    const matched_node = nodeList.value.find(x => x.id === item.indicatorId);
    if (matched_node) {
      // 父级节点的指标统计数量
      const { firstLevelCode: fcode, secondLevelCode: scode } = matched_node;
      countMap[fcode] = (countMap[fcode] || 0) + total;
      countMap[scode] = (countMap[scode] || 0) + total;
    }
  });
  indicatorsCountMap.value = countMap;
}

onMounted(() => {
  request();
});

defineExpose({
  getCurrent,
  getSelcteds,
  setIndicatorsCount,
});
</script>

<template>
  <div class="tree-control" p-10>
    <!-- 搜索框 -->
    <el-input v-model.trim="filterText" placeholder="搜索指标" :suffix-icon="Search" clearable />
    <!-- 树形结构 -->
    <el-tree
      ref="$treeRef"
      empty-text="无指标数据"
      node-key="id"
      :props="{ label: 'name', children: 'children' }"
      :data="treeNodes"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :show-checkbox="false"
      @node-click="handleClick"
      highlight-current
      default-expand-all
    >
      <template #default="{ data }">
        <span>
          <template v-if="isLeafNodeAndNoComponent(data)">
            <span c-red>[未开发]</span>
          </template>
          <span>{{ data.name }}</span>
          <template v-if="indicatorsCountMap[data.id] !== undefined">
            <span>({{ indicatorsCountMap[data.id] }})</span>
          </template>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<style scoped>
.tree-control {
  :deep() {
    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: var(--g-block-bg-6);
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: var(--g-block-bg-6) !important;
        }
      }
    }
  }
}
</style>
