﻿const MainModule = require('./main-module').MainModule;

class ExtensionModule extends MainModule {

    constructor(module_name) {
        super(module_name);
    }

    listen2DownloadTemplate() {

        this.mainProcess.on(this.systemEvent.downloadTemplate, (event, filePath) => {
            this.centralWindow.webContents.downloadURL(filePath);
        });
    }

    run() {
        this.listen2DownloadTemplate();
    }
}

module.exports = { ExtensionModule };