﻿/*
    deal with placing order(s) by file
*/

const fs = require('fs');
const path = require('path');
const xlsx = require('node-xlsx').default;
const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;
const { NormalUserEntrust, AlgoUserEntrust } = require('../model/file-scan');

class FileOrderModule extends ServerEnvMainModule {

    get repoAccount() {

        if (this._repo_account === undefined) {
            this._repo_account = require('../repository/account').repoAccount;
        }

        return this._repo_account;
    }

    get repoTrading() {

        if (this._repo_trading === undefined) {
            this._repo_trading = require('../repository/trading').repoTrading;
        }

        return this._repo_trading;
    }

    constructor(module_name) {

        super(module_name);

        this.today = new Date().toISOString().substr(0, 10).replace(/-/g, '');
        this.timestamp = new Date().getTime();

        this.states = {

            // 标识，指示当前网络是否处于：连接状态
            isServerConnected: true,

            // 是否正在同步【订单】明细
            isSyncingOrders: false,

            // 是否正在同步【成交】明细
            isSyncingExchanges: false,
        };

        // 每个账号对应的最新一笔订单的update time
        this.accountId2LatestOrderMap = {};

        // 每个账号对应的最新一笔成交的记录ID（本身保持增长）
        this.accountId2LatestExchangeMap = {};

        // 常量定义
        this.consts = {

            entrusts: 'entrusts', 
            results: 'results',
            downloads: 'downloads',
            cancels: 'cancels',
            history: this.today + '_history',
            entrustType: { normal: 'normal', algo: 'algo' },
            // entrust resolve flow
            entrustResult: {
                orderResult: this.today + '.normal-order.csv',
                algoReply: this.today + '.algo-reply.csv',
                algoResult: this.today + '.algo-order.csv',
                exchangeResult: this.today + '.exchange.csv',
            },
        };

        this.headers = {

            normalOrder: ['entrust_type', 'instrument', 'asset_type', 'direction', 'price', 'price_type', 'volume', 'position_effect', 'hedge_flag', 'product_id', 'account_id', 'customId', 'remark'],
            algoOrder: ['entrust_type', 'product_id','strategy_id','account_id','algo_id','instrument','instrument_name','direction','volume','start_time','end_time','params','remark', 'limited_on', 'expired_on'],
            downloadNormalOrder: ['accountId','accountName','adjustFlag','assetType','cancelledVolume','commission','coverFlag','createTime','customId','direction','errorCode','errorMsg','exchangeOrderId','financeAccount','forceClose','foreign','frozenCommission','frozenMargin','frozenVolume','fundId','fundName','hedgeFlag','id','instrument','instrumentName','orderPrice','orderPriceType','orderStatus','orderTime','positionEffect','remark','strategyId','strategyName','tradeTime','tradedAmount','tradedPrice','tradedVolume','tradingDay','userId','userName','volumeOriginal','updateTime'],
            downloadAlgoOrder: ['accountId', 'accountName', 'afterAction', 'algoParam', 'algorithmMappingId', 'algorithmName', 'algorithmStatus', 'algorithmType', 'direction', 'effectiveTime', 'expireTime', 'externalId', 'fundName', 'id', 'identityId', 'instrument', 'instrumentName', 'limitAction', 'orders_count', 'orderedVolume', 'supplierName', 'taskId', 'tradePrice', 'tradedVolume', 'userId', 'volume'],
            downloadExchange: ['accountId','accountName','assetType','commission','direction','exchangeOrderId','fundId','fundName','id','instrument','instrumentName','orderId','positionEffect','strategyId','strategyName','today','tradeId','tradeTime','tradedPrice','tradingDay','userId','userName','volume'],
        };

        this.orderFileNamePattern = {

            csv: /^order\.\d{8,11}\.([a-zA-Z0-9]){3,10}\.csv$/i,
            excel: /^order\.\d{8,11}\.([a-zA-Z0-9]){3,10}\.xls$/i,
            excelx: /^order\.\d{8,11}\.([a-zA-Z0-9]){3,10}\.xlsx$/i,
        };

        this.cancelFileNamePattern = {

            csv: /^cancel\.\d{8,11}\.([a-zA-Z0-9]){3,10}\.csv$/i,
            excel: /^cancel\.\d{8,11}\.([a-zA-Z0-9]){3,10}\.xls$/i,
            excelx: /^cancel\.\d{8,11}\.([a-zA-Z0-9]){3,10}\.xlsx$/i,
        };
        this.folder = null;
        this.entrustResolveFrequency = 10;
        this.resolvingCancelFileMap = {};
        // 读取时发生异常的文件，key：文件名，value： true
        this.errorFileMap = {};
        this.accountIdsMap = {};
        this.isScanRunning = false;
    }

    isInWhiteList(accountId) {
        return this.accountIdsMap[accountId] == true;
    }

    registerAsErrorFile(file_name) {
        this.errorFileMap[file_name] = true;
    }

    isErrorFile(file_name) {
        return this.errorFileMap[file_name] === true;
    }

    consumeCancelFiles(folder_exists) {

        if (!folder_exists) {

            this.loggerSys.fatal('module file-order > scan cancel files but found no folder (maybe deleted manually)');
            this.markCancelScanAsCompleted();
            return;
        }

        try {
            fs.readdir(this.folderCancels, (err, file_names) => {

                if (err) {
                    this.markCancelScanAsCompleted();
                    this.loggerSys.fatal('module file-order > reading cancel files folder with error > ' + err);
                    return;
                }
                else if (!(file_names instanceof Array) || file_names.length == 0) {
                    this.markCancelScanAsCompleted();
                    return;
                }

                var file_count = file_names.length;
                var completed_item = 0;
                // 每个文件处理完成，执行检查
                var single_complete_cbk = () => {

                    ++completed_item;
                    if (completed_item >= file_count) {
                        this.markCancelScanAsCompleted();
                    }
                };
    
                file_names.forEach(file_name => {
    
                    if (this.isErrorFile(file_name)) {
                        single_complete_cbk();
                        return;
                    }

                    let { csv, excel, excelx } = this.cancelFileNamePattern;
                    let is_one_of = csv.test(file_name) || excel.test(file_name) || excelx.test(file_name);
                    if (!is_one_of) {
                        single_complete_cbk();
                        return;
                    }

                    let full_path = path.join(this.folderCancels, file_name);
                    try {
                        fs.stat(full_path, (desc_err, descrip) => {

                            if (desc_err) {

                                this.registerAsErrorFile(file_name);
                                single_complete_cbk();
                                this.loggerSys.fatal('module file-order > state cancel file error > ' + desc_err);
                            }
                            else if (descrip.isFile()) {
                                this.consumeSingleFile(file_name, full_path, false, () => { single_complete_cbk(); });
                            }
                            else {
                                single_complete_cbk();
                            }
                        });
                    }
                    catch(ex) {

                        this.loggerSys.fatal(`module file-order > fs.state cancel file/${full_path} with unexpected exception > ${ex.message}/${ex.stack}`);
                        this.registerAsErrorFile(file_name);
                        single_complete_cbk();
                    }
                });
            });
        }
        catch(ex) {

            this.loggerSys.fatal(`module file-order > single round to scan cancel files with unexpected exception > ${ex.message}/${ex.stack}`);
            this.markCancelScanAsCompleted();
        }
    }

    consumeEntrustFiles(folder_exists) {
        
        if (!folder_exists) {

            this.loggerSys.fatal('module file-order > scan entrust files but found no folder (maybe deleted manually)');
            this.markOrderScanAsCompleted();
            return;
        }

        try {
            fs.readdir(this.folderEntrusts, (err, file_names) => {
            
                if (err) {
                    this.markOrderScanAsCompleted();
                    this.loggerSys.fatal('module file-order > reading entrust files folder with error > ' + err);
                    return;
                }
                else if (!(file_names instanceof Array) || file_names.length == 0) {
                    this.markOrderScanAsCompleted();
                    return;
                }

                var file_count = file_names.length;
                var completed_item = 0;
                // 每个文件处理完成，执行检查
                var single_complete_cbk = () => {

                    ++completed_item;
                    if (completed_item >= file_count) {
                        this.markOrderScanAsCompleted();
                    }
                };
    
                file_names.forEach(file_name => {
    
                    if (this.isErrorFile(file_name)) {
                        single_complete_cbk();
                        return;
                    }
                    
                    let { csv, excel, excelx } = this.orderFileNamePattern;
                    let is_one_of = csv.test(file_name) || excel.test(file_name) || excelx.test(file_name);
                    if (!is_one_of) {
                        single_complete_cbk();
                        return;
                    }
                    
                    let full_path = path.join(this.folderEntrusts, file_name);
                    try {
                        fs.stat(full_path, (desc_err, descrip) => {
                        
                            if (desc_err) {

                                this.registerAsErrorFile(file_name);
                                single_complete_cbk();
                                this.loggerSys.fatal('module file-order > state entrust file error > ' + desc_err);
                            }
                            else if (descrip.isFile()) {
                                this.consumeSingleFile(file_name, full_path, true, () => { single_complete_cbk(); });
                            }
                            else {
                                single_complete_cbk();
                            }
                        });
                    }
                    catch(ex) {

                        this.loggerSys.fatal(`module file-order > fs.state entrust file/${full_path} with unexpected exception > ${ex.message}/${ex.stack}`);
                        this.registerAsErrorFile(file_name);
                        single_complete_cbk();
                    }
                });
            });
        }
        catch(ex) {

            this.loggerSys.fatal(`module file-order > single round to scan entrust files with unexpected exception > ${ex.message}/${ex.stack}`);
            this.markOrderScanAsCompleted();
        }
    }

    /**
     * @param {Array<Array>} data 
     */
    typedAsMatrix(data) {
        return data;
    }

    consumeSingleFile(file_name, full_path, is_order, moving_callback) {

        let new_file_name = file_name;
        let new_full_path = path.join(is_order ? this.folderEntrustsHistory : this.folderCancelsHistory, file_name);

        try {

            if (fs.existsSync(new_full_path)) {
                new_file_name = file_name + '-2';
                new_full_path = path.join(is_order ? this.folderEntrustsHistory : this.folderCancelsHistory, new_file_name);
            }

            let sheets = xlsx.parse(full_path);
            if (!(sheets instanceof Array) || sheets.length == 0) {
                this.moveFile(file_name, full_path, new_full_path, moving_callback);
                return;
            }

            let data_lines = this.typedAsMatrix(sheets[0].data).filter(x => x.length > 0);
            let has_data = data_lines.length > 0;
            if (!has_data) {
                this.moveFile(file_name, full_path, new_full_path, moving_callback);
                return;
            }

            if (is_order) {

                let result = this.extractOrders(data_lines);

                if (result.good.length > 0) {

                    this.loggerTrading.info(`module file-order > send ${result.good.length} orders from entrust file ${file_name}`);
                    this.makeOrders(result.good);
                    this.loggerTrading.info(`module file-order > completed to send order`);
                }

                if (result.bad.length > 0) {

                    var error_order_path = path.join(this.folderEntrustsHistory, new_file_name + '.error');
                    var error_lines = result.bad.join('\n');
                    var content = Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary'), Buffer.from(error_lines)]);
                    try {
                        fs.appendFile(error_order_path, content, (write_err) => {
                            if(write_err) {
                                this.loggerTrading.fatal('module file-order > writing bad orders to history file with error > ' + write_err);
                            }
                        });
                    }
                    catch(ex) {
                        this.loggerSys.fatal(`module file-order > consuming file with deep error comes up > ${ex.message}/${ex.stack}`);
                    }
                }
            }
            else {

                let order_ids = data_lines.map(x => x[0]);
                this.loggerTrading.info(`module file-order > send ${order_ids.length} cancel commands from cancel file ${file_name}`);
                this.makeCancels(order_ids);
                this.loggerTrading.info(`module file-order > completed to send cancel commands`);
            }

            // finally move the file away
            this.moveFile(file_name, full_path, new_full_path, moving_callback);
        }
        catch(ex) {

            this.registerAsErrorFile(file_name);
            moving_callback();
            this.loggerSys.fatal('module file-order > writing bad orders to history file with exception > ' + (ex.message || ex));
        }
    }

    isAlgoType(entrust_type) {
        return entrust_type == this.consts.entrustType.algo;
    }

    isNormalType(entrust_type) {
        return entrust_type == this.consts.entrustType.normal || this.helper.isNone(entrust_type);
    }

    /**
     * @param {NormalUserEntrust} data 
     */
    typedsNormal(data) {
        return data;
    }

    /**
     * @param {AlgoUserEntrust} data 
     */
    typedsAlgo(data) {
        return data;
    }

    /**
     * @param {AlgoUserEntrust} data 
     */
    validateAlgo(data) {

        var dirs = this.systemTrdEnum.tradingDirection;
        var time_regx = /^([01]\d|2[0-3]):[0-5]\d:[0-5]\d$/;
        var start = data.start_time;
        var end = data.end_time;
        var result = { isOk: true, error: null };

        if (this.helper.isNone(data.product_id)) {
            result.isOk = false;
            result.error = 'product_id缺失';
        }
        else if (this.helper.isNone(data.account_id)) {
            result.isOk = false;
            result.error = 'account_id缺失';
        }
        else if (this.helper.isNone(data.algo_id)) {
            result.isOk = false;
            result.error = '';
        }
        else if ( this.helper.isNone(data.instrument)) {
            result.isOk = false;
            result.error = '股票合约缺失';
        }
        else if (!(data.direction == dirs.buy.code || data.direction == dirs.sell.code)) {
            result.isOk = false;
            result.error = '买卖方向标识错误';
        }
        else if (!(Number.isInteger(data.volume) && data.volume > 0)) {
            result.isOk = false;
            result.error = '委托数量无效';
        }
        else if (!time_regx.test(start.toString().trim())) {
            result.isOk = false;
            result.error = '开始时间格式错误';
        }
        else if (!time_regx.test(end.toString().trim())) {
            result.isOk = false;
            result.error = '结束时间格式错误';
        }
        else if (!(start <= end)) {
            result.isOk = false;
            result.error = '开始时间，不小于结束时间';
        }

        return result;
    }

    /**
     * @param {NormalUserEntrust} data 
     */
    validateNormal(data) {

        var { stock, future, option, bond } = this.systemEnum.assetsType;
        var { buy, sell } = this.systemTrdEnum.tradingDirection;
        var { fixedPrice, marketPrice, simulated } = this.systemTrdEnum.pricingType;

        var asset_types = [stock.code, future.code, option.code, bond.code];
        var directions = [buy.code, sell.code];
        var price_types = [fixedPrice.code, marketPrice.code, simulated.code];
        var result = { isOk: true, error: null };

        if (this.helper.isNone(data.product_id)) {
            result.isOk = false;
            result.error = 'product_id缺失';
        }
        else if (this.helper.isNone(data.account_id)) {
            result.isOk = false;
            result.error = 'account_id缺失';
        }
        else if (!(typeof data.instrument == 'string' && data.instrument.length > 0)) {
            result.isOk = false;
            result.error = '合约代码缺失';
        }
        else if (!asset_types.some(x => x == data.asset_type)) {
            result.isOk = false;
            result.error = `资产类型代码asset_type=${data.asset_type}，不能识别`;
        }
        else if (!directions.some(x => x == data.direction)) {
            result.isOk = false;
            result.error = `交易方向direction=${data.direction}，不能识别`;
        }
        else if (!price_types.some(x => x == data.price_type)) {
            result.isOk = false;
            result.error = `有效的价格类型代码price_type = ${JSON.stringify(this.systemTrdEnum.pricingType)}`;
        }
        else if (!(typeof data.price == 'number' && data.price > 0)) {
            result.isOk = false;
            result.error = '委托价格price无效';
        }
        else if (!(typeof data.volume == 'number' && data.volume > 0)) {
            result.isOk = false;
            result.error = '委托数量volume无效';
        }
        
        return result;
    }

    /**
     * @param {Array<Array>} data_lines 
     */
    extractOrders(data_lines) {

        var good_orders = [];
        var bad_lines = [];

        data_lines.forEach((data_line, line_idx) => {

            let is_algo = this.isAlgoType(data_line[0]);

            if (is_algo) {

                let only = this.helper.convertMatrix2Json(this.headers.algoOrder, [data_line])[0];
                let algo_entrust = this.typedsAlgo(only);
                let result = this.validateAlgo(algo_entrust);

                if (!result.isOk) {
                    bad_lines.push(JSON.stringify({ error: result.error, data: algo_entrust }));
                }
                else if (!this.isInWhiteList(algo_entrust.account_id)) {
                    bad_lines.push(JSON.stringify({ error: `account_id/${algo_entrust.account_id}，不是扫单账号`, data: algo_entrust }));
                }
                else {
                    good_orders.push(algo_entrust);
                }
            }
            else {
                
                let only = this.helper.convertMatrix2Json(this.headers.normalOrder, [data_line])[0];
                let normal_entrust = this.typedsNormal(only);
                
                // 对custom id进行缺失处理
                if (this.helper.isNone(normal_entrust.customId)) {
                    normal_entrust.customId = '2-' + (++this.timestamp).toString();
                }

                let result = this.validateNormal(normal_entrust);
                if (!result.isOk) {
                    bad_lines.push(JSON.stringify({ error: result.error, data: normal_entrust }));
                }
                else if (!this.isInWhiteList(normal_entrust.account_id)) {
                    bad_lines.push(JSON.stringify({ error: `account_id/${normal_entrust.account_id}，不是扫单账号`, data: normal_entrust }));
                }
                else {
                    good_orders.push(normal_entrust);
                }
            }
        });

        return { good: good_orders, bad: bad_lines };
    }

    /**
     * @param {Array<NormalUserEntrust|AlgoUserEntrust>} records 
     */
    makeOrders(records) {
        
        this.loggerTrading.info(`module file-order > to send ${records.length} orders one by one`);
        let algos = records.filter(x => this.isAlgoType(x.entrust_type));
        let normals = records.filter(x => this.isNormalType(x.entrust_type));

        if (algos.length > 0) {
            this.sendAlgo(algos);
        }
        
        if (normals.length > 0) {
            normals.forEach(data => { this.sendNormal(data); });
        }
    }

    /**
     * @param {Array<AlgoUserEntrust>} algos
     */
    sendAlgo(algos) {

        let permiteds = algos.filter(item => this.isInWhiteList(item.account_id));
        if (permiteds.length == 0) {
            return;
        }
        
        let today = new Date().format('yyyy-MM-ddd');
        let orders = permiteds.map(data => {

            return {

                direction: data.direction,
                identityId: data.strategy_id || data.product_id,
                accountId: data.account_id,
                algorithmMappingId: data.algo_id,
                instrument: data.instrument,
                volume: data.volume,
                effectiveTime: new Date(today + ' ' + data.start_time).getTime(),
                expireTime: new Date(today + ' ' + data.end_time).getTime(),
                limitAction: data.limited_on ? 1 : 0,
                afterAction: data.expired_on ? 1 : 0,
                userId: this.userInfo.userId,
                algoParam: data.params,
                taskName: data.remark,
            };
        });

        let message = { fc: this.serverFunction.sendAlgoOrder, reqId: 0, dataType: 1, body: orders };
        this.tradingServer.send(message);
    }

    /**
     * @param {NormalUserEntrust} data 
     */
    sendNormal(data) {

        if (!this.isInWhiteList(data.account_id)) {
            return;
        }

        /**
         * 无论是产品，还是策略，都作为策略id
         */

        let strategy_id = data.product_id;
        let order_info = {

            strategyId: strategy_id,
            accountId: data.account_id,
            userId: this.userInfo.userId,
            price: data.price,
            volume: data.volume,
            instrument: data.instrument,
            priceType: data.price_type,
            bsFlag: data.direction,
            positionEffect: data.position_effect || 0,
            assetType: data.asset_type,
            customId: data.customId,
            orderTime: null,
            hedgeFlag: data.hedge_flag,
            remark: data.remark,
        };

        let message = { fc: this.serverFunction.sendOrder, reqId: 0, dataType: 1, body: order_info };
        this.tradingServer.send(message);
    }

    /**
     * @param {Array} order_ids 
     */
    makeCancels(order_ids) {

        this.loggerTrading.info(`module file-order > to send ${order_ids.length} cancels one by one`);

        /**
         * 无法区分当前的ID是普通委托还是算法委托，所以两种撤单都进行发送
         */

        order_ids.forEach(order_id => {

            let order = { orderId: order_id };
            let message = { fc: this.serverFunction.cancelOrder, reqId: 0, dataType: 1, body: order };
            this.tradingServer.send(message);
        });
        
        let algo_message = { fc: this.serverFunction.cancelAlgoOrder, reqId: 0, dataType: 1, body: order_ids };
        this.tradingServer.send(algo_message);
    }

    moveFile(file_name, full_path, new_full_path, moving_callback) {

        try {
            fs.rename(full_path, new_full_path, (move_err) => {
                if (move_err) { 
                    this.registerAsErrorFile(file_name);
                    this.loggerSys.fatal(`module file-order > moving file(${full_path}/${new_full_path}) with error > ` + move_err);
                }
                moving_callback();
            });
        }
        catch(ex) {

            this.loggerSys.fatal(`module file-order > moving file(${full_path}/${new_full_path}) with exception > ${ex.message}/${ex.stack}`);
            this.registerAsErrorFile(file_name);
            moving_callback();
        }
    }

    markOrderScanAsCompleted() {
        
        this.isResolvingEntrustFiles = false;

        // 是否立即执行下一次
        if (this.instantOrderScanRequired === true) {
            this.instantOrderScanRequired = false;
            this.scanOrders();
        }
    }

    scanOrders() {

        if (this.states.isServerConnected !== true) {
            return;
        }
        
        if (this.isResolvingEntrustFiles !== true) {

            // 标识为处于处理状态
            this.isResolvingEntrustFiles = true;
            fs.exists(this.folderEntrusts, this.consumeEntrustFiles.bind(this));
        }
        else if (this.instantOrderScanRequired !== true) {

            // 标识当前批次结束后，应理解开启下一次执行
            this.instantOrderScanRequired = true;
        }
    }

    markCancelScanAsCompleted() {

        this.isResolvingCancelFiles = false;
        // 是否立即执行下一次
        if (this.instantCancelScanRequired === true) {
            this.instantCancelScanRequired = false;
            this.scanCancels();
        }
    }

    scanCancels() {

        if (this.states.isServerConnected !== true) {
            return;
        }
        
        if (this.isResolvingCancelFiles !== true) {

            // 标识为处于处理状态
            this.isResolvingCancelFiles = true;
            fs.exists(this.folderCancels, this.consumeCancelFiles.bind(this));
        }
        else if (this.instantCancelScanRequired !== true) {
            
            // 标识当前批次结束后，应理解开启下一次执行
            this.instantCancelScanRequired = true;
        }
    }

    formatSingleChange(fields, single_change) {
        
        var values = [];
        fields.forEach((field_name) => {
            let fval = single_change[field_name];
            if (this.helper.isNone(fval)) {
                values.push('');
            }
            else if (this.helper.isJson(fval)) {
                values.push(`"${JSON.stringify(fval).replace(/\"/g, "'")}"`); 
            }
            else if (typeof fval == 'string') {
                values.push(fval.indexOf(',') >= 0 || fval.indexOf('"') >= 0 ? `"${fval.replace(/\"/g, "'")}"` : fval);
            }
            else {
                values.push(fval);
            }
        });
        return values.join(',');
    }

    createOrderLogFile() {
        
        var header_line = this.headers.downloadNormalOrder.join(',') + '\n';
        var content = Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary'), Buffer.from(header_line)]);
        fs.appendFile(this.entrustOrderLogFilePath, content, (write_err) => {
            if (write_err) {
                this.loggerSys.fatal('module file-order > create order change log file with error > ' + write_err);
            } 
        });
    }

    lockOrderLogFile() {
        this.orderLogFileDescriptor = fs.openSync(this.entrustOrderLogFilePath, 'a+');
    }

    createAlgoOrderLogFile() {
        
        var header_line = this.headers.downloadAlgoOrder.join(',') + '\n';
        var content = Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary'), Buffer.from(header_line)]);
        fs.writeFile(this.algoOrderLogFilePath, content, (write_err) => {
            if (write_err) {
                this.loggerSys.fatal('module file-order > create algo order change log file with error > ' + write_err);
            } 
        });
    }

    // lockAlgoOrderLogFile() {
    //     this.algoOrderLogFileDescriptor = fs.openSync(this.algoOrderLogFilePath, 'w+');
    // }

    createAlgoOrderReplyLogFile() {
        
        var content = Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary')]);
        fs.appendFile(this.algoOrderReplyLogFile, content, (write_err) => {
            if (write_err) {
                this.loggerSys.fatal('module file-order > create algo order reply log file with error > ' + write_err);
            } 
        });
    }

    lockAlgoOrderReplyLogFile() {
        this.algoOrderReplyLogFileDescriptor = fs.openSync(this.algoOrderReplyLogFile, 'a+');
    }

    createExchangeLogFile() {
        
        var header_line = this.headers.downloadExchange.join(',') + '\n';
        var content = Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary'), Buffer.from(header_line)]);
        fs.appendFile(this.exchangeLogFilePath, content, (write_err) => {
            if (write_err) { 
                this.loggerSys.fatal('module file-order > create exchange log file with error > ' + write_err);
            }
        });
    }

    lockExchangeLogFile() {
        this.exchangeLogFileDescriptor = fs.openSync(this.exchangeLogFilePath, 'a+');
    }

    /**
     * @returns {Array}
     */
    async loadAccounts() {

        if (this.cachedUserAccounts) {
            return this.cachedUserAccounts;
        }
        
        try {
            var resp = await this.repoAccount.getAll();
            if (resp.errorCode != 0) {

                let error_msg = `module file-order > network disconnected & recovered, sync order operation encounter restful failure: ${JSON.stringify(resp)}`;
                this.loggerSys.fatal(error_msg);
                this.cachedUserAccounts = [];
            }
            else {
                this.cachedUserAccounts = resp.data;
                this.loggerSys.info(`module file-order > requested accounts with length = ${this.cachedUserAccounts.length}`);
            }
        }
        catch(ex) {
            //
        }

        return this.cachedUserAccounts;
    }

    setOrderSyncAsCompleted() {
        this.states.isSyncingOrders = false;
    }

    async syncOrders() {

        if (this.states.isSyncingOrders) {
            return;
        }

        this.states.isSyncingOrders = true;
        this.loggerSys.debug(`module file-order > start to sync orders with latest status = ${ JSON.stringify(this.accountId2LatestOrderMap) }`);

        /**
         * @param {Array} lost_orders 
         */
        var complete_sync = (lost_orders) => {

            if (lost_orders.length == 0) {
                return;
            }

            this.loggerSys.info(`module file-ordering > has ${lost_orders.length} lost orders to be synched`);
            // 将增量订单序列化
            var data_lines = lost_orders.map(the_order => { return this.formatSingleChange(this.headers.downloadNormalOrder, the_order); });
            var data_lines_str = data_lines.join('\n');

            try {

                // 将增量订单批量写入文件
                fs.appendFile(this.orderLogFileDescriptor, data_lines_str + '\n', (write_err) => {
                    if (write_err) {
                        this.loggerSys.fatal('module file-order > newer orders write to log file with error > ' + write_err);
                    }
                });

                this.loggerSys.info(`module file-ordering > has ${lost_orders.length} orders where synced to log file`);

                // 更新最新记录标识
                lost_orders.forEach(the_order => {
                    this.accountId2LatestOrderMap[the_order.accountId] = the_order.updateTime;
                });

                this.loggerSys.info(`module file-ordering > order map is overwritten/${JSON.stringify(this.accountId2LatestOrderMap)}`);
            }
            catch(ex) {
                this.loggerSys.fatal(`module file-order > unexpected exception occurs with sync orders to file > ${ex.message}/${ex.stack}`);
            }
        };

        var accounts = await this.loadAccounts();
        if (accounts.length == 0) {
            this.setOrderSyncAsCompleted();
            return;
        }

        // 根据账号列表，获得每个账号的订单数据（仅保留比写入回报文件更新的订单）
        var accountIds = accounts.map(x => x.id);
        var newer_orders = [];
        
        try {
            for (let account_id of accountIds) {
            
                let lastest_time = this.accountId2LatestOrderMap[account_id];
                let resp = await this.repoTrading.getTodayOrders({ accountId: account_id });
                let account_orders = resp.data;
                this.loggerSys.info(`module file-order > request orders of account/${ account_id } = ${ JSON.stringify(account_orders) }`);
    
                if (account_orders instanceof Array) {
                    newer_orders.merge(lastest_time !== undefined ? account_orders.filter(x => x.updateTime > lastest_time) : account_orders);
                }
            }
        }
        catch(ex) {
            this.loggerSys.fatal(`module file-order > unexpected exception occurs with requesting account orders > ${ex.message}/${ex.stack}`);
        }

        if (newer_orders.length == 0) {
            
            this.loggerSys.info(`module file-order > no newer orders found from restful service`);
            this.setOrderSyncAsCompleted();
            return;
        }

        // 预先确保原始序列已经有序
        newer_orders.orderBy(x => x.updateTime);
        this.loggerSys.info(`module file-order > totaly filtered ${ newer_orders.length } newer orders across all accounts, and they are: ${ JSON.stringify(newer_orders) }`);
        complete_sync(newer_orders);
        this.setOrderSyncAsCompleted();
    }

    async requestAlgoOrders() {

        const { repoAlgo } = require('../repository/algorithm');
        let resp = await repoAlgo.getOrderList();
        let { errorCode, data } = resp;
        if (errorCode != 0 || !Array.isArray(data) || data.length == 0) {
            return;
        }
        
        data.forEach(each => {

            let list = each.orderList;
            each.orders_count = Array.isArray(list) ? list.length : 0;
            delete each.orderList;
        });

        var header_line = this.headers.downloadAlgoOrder.join(',') + '\n';
        var lines = data.map(item => this.formatSingleChange(this.headers.downloadAlgoOrder, item)).join('\n') + '\n';
        var content = Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary'), Buffer.from(header_line), Buffer.from(lines)]);

        if (fs.existsSync(this.algoOrderLogFilePath)) {
            fs.unlinkSync(this.algoOrderLogFilePath);
        }

        fs.writeFile(this.algoOrderLogFilePath, content, (write_err) => {
            if(write_err) {
                this.loggerSys.fatal('module file-order > put up algo orders to log file with error > ' + write_err); 
            }
        });
    }

    haltAlgoUpdate() {
        clearInterval(this.algoUpdateJob);
        this._isAlgoJobRunning = this.running;
    }

    timelyUpdateAlgoOrders() {

        this.algoUpdateJob = setInterval(async () => {
            
            if (this._isAlgoJobRunning) {
                return;
            }

            this._isAlgoJobRunning = true;
            await this.requestAlgoOrders(true);
            this._isAlgoJobRunning = false;

        }, 1000 * 20);
    }

    logOrderChange(message_package) {

        var order_info = JSON.parse(message_package.body);
        var accountId = order_info.accountId;

        if (!this.isScanRunning || !this.isInWhiteList(accountId)) {
            return;
        }

        var log_msg = `module file-order > module file-ordering is notified with an order change > ${JSON.stringify(order_info)}`;
        this.loggerTrading.debug(log_msg);

        // 记录写入文件的最后一笔订单
        this.accountId2LatestOrderMap[accountId] = order_info.updateTime;
        var data_line = this.formatSingleChange(this.headers.downloadNormalOrder, order_info);
        fs.appendFile(this.orderLogFileDescriptor, data_line + '\n', (write_err) => {
            if(write_err) {
                this.loggerSys.fatal('module file-order > put order changes to log file with error > ' + write_err); 
            }
        });
    }

    setExchangeSyncAsCompleted() {
        this.states.isSyncingExchanges = false;
    }

    async syncExchanges() {

        if (this.states.isSyncingExchanges) {
            return;
        }

        this.states.isSyncingExchanges = true;
        this.loggerSys.info(`module file-order > start to sync exchanges with latest status = ${JSON.stringify(this.accountId2LatestExchangeMap)}`);

        /**
         * @param {Array} lost_exchanges 
         */
        var complete_sync = (lost_exchanges) => {

            if (lost_exchanges.length == 0) {
                return;
            }

            this.loggerSys.info(`module file-ordering > nhas ${lost_exchanges.length} lost exchanges to be synched`);
            // 将增量成交序列化
            var data_lines = lost_exchanges.map(the_exchange => { return this.formatSingleChange(this.headers.downloadExchange, the_exchange); });
            var data_lines_str = data_lines.join('\n');

            try {

                // 将增量成交批量写入文件
                fs.appendFile(this.exchangeLogFileDescriptor, data_lines_str + '\n', (write_err) => {
                    if(write_err) {
                        this.loggerSys.fatal('module file-order > newer exchanges write to log file with error > ' + write_err);
                    }
                });

                this.loggerSys.info(`module file-order > has ${lost_exchanges.length} exchanges where synced to log file`);

                // 更新最新记录标识
                lost_exchanges.forEach(the_exchange => {
                    this.accountId2LatestExchangeMap[the_exchange.accountId] = the_exchange.id;
                });

                this.loggerSys.info(`module file-order > exchange map is overwritten/${JSON.stringify(this.accountId2LatestExchangeMap)}`);
            }
            catch(ex) {
                this.loggerSys.fatal(`module file-order > unexpected exception occurs with sync exchanges to file > ${ex.message}/${ex.stack}`);
            }
        };
        
        var accounts = await this.loadAccounts();
        if(accounts.length == 0) {
            this.setExchangeSyncAsCompleted();
            return;
        }

        // 根据账号列表，获得每个账号的成交数据（仅保留比写入回报文件更新的成交）
        var accountIds = accounts.map(x => x.id);
        var newer_exchanges = [];

        try {
            for (let account_id of accountIds) {
                
                let lastest_record_id = this.accountId2LatestExchangeMap[account_id];
                let resp = await this.repoTrading.getTodayWaterflows({ accountId: account_id });
                let exchanges = resp.data;
                this.loggerSys.info(`module file-order > request exchanges of account/${ account_id } = ${ JSON.stringify(exchanges) }`);
    
                if (exchanges instanceof Array) {
                    newer_exchanges.merge(lastest_record_id !== undefined ? exchanges.filter(x => x.id > lastest_record_id) : exchanges);
                }
            }
        }
        catch(ex) {
            this.loggerSys.fatal(`module file-order > unexpected exception occurs with requesting account exchanges > ${ex.message}/${ex.stack}`);
        }

        if (newer_exchanges.length == 0) {
            
            this.loggerSys.info(`module file-order > no newer exchanges found from restful service`);
            this.setExchangeSyncAsCompleted();
            return;
        }

        // 预先确保原始序列已经有序
        newer_exchanges.orderBy(x => x.id);
        this.loggerSys.info(`module file-order > totaly filtered ${ newer_exchanges.length } exchanges across all accounts, and they are: ${ JSON.stringify(newer_exchanges) }`);
        complete_sync(newer_exchanges);
        this.setExchangeSyncAsCompleted();
    }

    logExchange(message_package) {

        var exchange_info = JSON.parse(message_package.body);
        var accountId = exchange_info.accountId;

        if (!this.isScanRunning || !this.isInWhiteList(accountId)) {
            return;
        }

        this.loggerTrading.debug('module file-order > exchange comes > ' + JSON.stringify(exchange_info));

        // 记录写入文件的最后一成交
        this.accountId2LatestExchangeMap[accountId] = exchange_info.id;
        var data_line = this.formatSingleChange(this.headers.downloadExchange, exchange_info);
        fs.appendFile(this.exchangeLogFileDescriptor, data_line + '\n', (write_err) => {
            if(write_err) { 
                this.loggerSys.fatal('module file-order > put exchange to log file with error > ' + write_err); 
            }
        });
    }

    handleAlgoOrderReply(message_package) {

        let reply = JSON.parse(message_package.body);
        let { errorCode, errorMsg, data } = reply;
        fs.appendFile(this.algoOrderReplyLogFileDescriptor, JSON.stringify(reply) + '\n', (write_err) => {
            if(write_err) {
                this.loggerSys.fatal('module file-order > put algo order reply to log file with error > ' + write_err); 
            }
        });
    }

    /**
     * @param {*} sender 
     * @param {Boolean} running 
     * @param {{ frequency: Number, folder: String }} setting 
     * @returns 
     */
    handleSetting(sender, running, setting) {

        if(this.running === running) {
            return;
        }

        this.running = running;
        if (running) {
            
            try {

                this.setupEnvironment(setting.frequency, setting.folder);
                sender.send(this.systemEvent.fileOrderSetting, { succeed: true });
            }
            catch(ex) {
                sender.send(this.systemEvent.fileOrderSetting, { succeed: false, message: ex.message });
            }
        }

        var ok_2_run = running && typeof this.entrustResolveFrequency == 'number' && this.entrustResolveFrequency > 0;

        // 下单文件        
        if (ok_2_run && fs.existsSync(this.folder) && !this.scanOrderTimer) {
            this.scanOrderTimer = setInterval(() => { this.scanOrders(); }, this.entrustResolveFrequency);
        }
        else if(!running && this.scanOrderTimer) {

            clearInterval(this.scanOrderTimer);
            this.scanOrderTimer = null;
        }
    
        // 撤单文件
        if (ok_2_run && fs.existsSync(this.folder) && !this.scanCancelTimer) {
            this.scanCancelTimer = setInterval(() => { this.scanCancels(); }, this.entrustResolveFrequency);
        }
        else if(!running && this.scanCancelTimer) {

            clearInterval(this.scanCancelTimer);
            this.scanCancelTimer = null;
        }
    }

    async handleTradingServerConnected() {

        this.loggerSys.info('module file-ordering > trading server connection established again');

        // 指示网络发生断开重连后，同步订单明细
        await this.syncOrders();

        // 指示网络发生断开重连后，同步成交明细
        await this.syncExchanges();

        // 设置标识，服务器已连接，使扫单任务可以正常进行
        this.states.isServerConnected = true;
    }

    handleTradingServerDisconnected() {

        this.states.isServerConnected = false;
        this.loggerSys.info('module file-order > trading server gets disconnected');
    }

    monitorNetworkStatus() {

        // 监听交易服务器连接恢复正常
        this.tradingServer.listen2Event(this.systemEvent.connEstablished, () => {
            this.handleTradingServerConnected();
            this.haltAlgoUpdate();
            this.timelyUpdateAlgoOrders();
        });

        // 监听交易服务器网络断线
        this.tradingServer.listen2Event(this.systemEvent.connClosed, () => {
            this.handleTradingServerDisconnected();
            this.haltAlgoUpdate();
        });
    }

    /**
     * @param {Number} frequency 
     * @param {String} folder 
     */
    setupEnvironment(frequency, folder) {

        this.entrustResolveFrequency = typeof frequency == 'number' && frequency > 0 ? frequency : null;
        this.folder = typeof folder == 'string' && folder.trim().length > 0 ? folder.trim() : null;
        this.folderResults = path.join(this.folder, this.consts.results);
        this.folderCancels = path.join(this.folder, this.consts.cancels);
        this.folderEntrusts = path.join(this.folder, this.consts.entrusts);
        this.folderEntrustsHistory = path.join(this.folderEntrusts, this.consts.history);
        this.folderCancelsHistory = path.join(this.folderCancels, this.consts.history);

        if(!fs.existsSync(this.folderEntrusts)) { fs.mkdirSync(this.folderEntrusts); }
        if(!fs.existsSync(this.folderEntrustsHistory)) { fs.mkdirSync(this.folderEntrustsHistory); }
        if(!fs.existsSync(this.folderResults)) { fs.mkdirSync(this.folderResults); }
        if(!fs.existsSync(this.folderCancels)) { fs.mkdirSync(this.folderCancels); }
        if(!fs.existsSync(this.folderCancelsHistory)) { fs.mkdirSync(this.folderCancelsHistory); }

        this.entrustOrderLogFilePath = path.join(this.folderResults, this.consts.entrustResult.orderResult);
        !fs.existsSync(this.entrustOrderLogFilePath) ? this.createOrderLogFile() : null;
        this.lockOrderLogFile();

        this.algoOrderLogFilePath = path.join(this.folderResults, this.consts.entrustResult.algoResult);
        !fs.existsSync(this.algoOrderLogFilePath) ? this.createAlgoOrderLogFile() : null;
        // this.lockAlgoOrderLogFile();

        this.algoOrderReplyLogFile = path.join(this.folderResults, this.consts.entrustResult.algoReply);
        !fs.existsSync(this.algoOrderReplyLogFile) ? this.createAlgoOrderReplyLogFile() : null;
        this.lockAlgoOrderReplyLogFile();

        this.exchangeLogFilePath = path.join(this.folderResults, this.consts.entrustResult.exchangeResult);
        !fs.existsSync(this.exchangeLogFilePath) ? this.createExchangeLogFile() : null;
        this.lockExchangeLogFile();
    }

    listen2Commands() {

        /**
         * listen to file order setting update
         */
        this.mainProcess.on(this.systemEvent.fileOrderSetting, (event, running, setting) => {

            if (setting == null || setting == undefined) {
                setting = {};
            }

            /**
             * 是否已经启动对网络状态的监控
             */
            
            if (this.hasListened2NetworkStatusChange === undefined) {

                this.hasListened2NetworkStatusChange = true;
                this.monitorNetworkStatus();
            }

            /**
             * 是否已经启动对订单变化、成交推送的监听
             */

            if (this.hasListened2DataChange === undefined) {

                this.hasListened2DataChange = true;
                this.tradingServer.listen2Event(this.serverEvent.orderChanged, this.logOrderChange.bind(this));
                this.tradingServer.listen2Event(this.serverEvent.exchangeChanged, this.logExchange.bind(this));
                this.tradingServer.listen2Event(this.serverEvent.algoOrderReceived, this.handleAlgoOrderReply.bind(this));
                this.timelyUpdateAlgoOrders();
            }

            this.helper.clearHash(this.accountIdsMap);
            var accountIds = setting.accountIds;

            if (accountIds instanceof Array && accountIds.length > 0) {

                accountIds.forEach(id => { this.accountIdsMap[id] = true; });
                this.tradingServer.send({ fc: this.serverFunction.subAccountChange, reqId: 0, dataType: 1, body: accountIds });
            }

            this.isScanRunning = running;
            this.handleSetting(event.sender, running, setting); 
        });
    }

    run() {

        this.loggerSys.info('load module file-order > begin');
        this.listen2Commands();
        this.loggerSys.info('load module file-order > end');
    }
}

module.exports = { FileOrderModule };
