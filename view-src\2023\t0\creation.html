<div class="creation-view">
    <el-dialog width="1300px" :title="title" :visible="visible" :show-close="false" :close-on-click-modal="false">
        <template>
            <div class="creation-body">
                <div class="header themed-header">
                    <div class="input-area">

                        <div class="input-sth">
                            <label class="ctr-label">账号</label>
                            <el-select 
                                placeholder="请选择账号" 
                                class="s-w-150" 
                                v-model="recordId" 
                                @change="handleAccountChange" 
                                :disabled="isEditing"
                                filterable 
                                clearable>
                                <el-option 
                                    v-for="(item, item_idx) in accounts" 
                                    :key="item_idx" 
                                    :value="getProperAccountId(item)"
                                    :label="formatSelectAccountName(item)"></el-option>
                            </el-select>
                        </div>

                        <div class="input-sth">
                            <label class="ctr-label">算法</label>
                            <el-select 
                                placeholder="请选择算法" 
                                v-model="algoId" 
                                @change="handleAlgoChange" 
                                :disabled="isEditing"
                                class="s-w-150" 
                                filterable 
                                clearable>								
                                <el-option-group v-for="(group, group_idx) in algoGrps" :key="group_idx" :label="group.name">
                                    <el-option v-for="(item, item_idx) in group.algoes" :key="item_idx" :label="item.algorithmName" :value="item.id"></el-option>
                                </el-option-group>
                            </el-select>
                        </div>

                        <div class="input-sth">
                            <label class="ctr-label">母单名称</label>
                            <el-input v-model="task_name" class="s-w-150" :disabled="isEditing" clearable></el-input>
                        </div>

                    </div>
                    <div class="button-area">
                        <el-button type="primary" @click="confirm">保存</el-button>
                        <el-button type="info" @click="giveup">取消</el-button>
                    </div>
                </div>
                <div class="param-body">
                    <div class="part-left">
                        <div class="part-title left-part-title themed-header ctr-label">
                            <span class="title-text">设置算法</span>
                            <span v-show="isAccountChoosed()" class="account-info s-color-red s-cd" @click="takeAll">
                                <span>可用资金</span>
                                <span class="max-can-use">{{ thousandsDecimal(totalCanUse) }}</span>
                            </span>
                        </div>
                        <div class="part-body s-pdt-10">
                            <div class="param-row">
                                <label class="row-label">
                                    <span class="s-color-red">*</span>
                                    <span>算法分配资金</span>
                                </label>
                                <span class="row-control">
                                    <el-input-number size="mini" :precision="2" :min="0" :max="totalCanUse" :controls="false" v-model="money" clearable></el-input-number>
                                    <span class="s-pdl-10">{{ displayPercentInfo() }}</span>
                                </span>
                            </div>
                            <!-- <div class="param-row">
                                <label class="row-label">
                                    <span class="s-color-red">*</span>
                                    <span>单日止损金额</span>
                                </label>
                                <span class="row-control">
                                    <el-input-number size="mini" :precision="2" :min="0" :controls="false" v-model="lose" clearable></el-input-number>
                                </span>
                            </div>
                            <div class="param-row">
                                <label class="row-label">
                                    <span class="s-color-red">*</span>
                                    <span>平仓时间</span>
                                </label>
                                <span class="row-control">
                                    <el-time-picker v-model="time" :picker-options="{ selectableRange: '09:30:00 - 15:00:00' }"></el-time-picker>
                                </span>
                            </div> -->
                            <div class="param-row">
                                <label class="row-label">
                                    <span class="s-color-red">*</span>
                                    <span>策略有效期至</span>
                                </label>
                                <span class="row-control">
                                    <el-date-picker v-model="end_date" type="date"></el-date-picker>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="part-right">
                        <div class="part-title themed-header">
                            <span class="ctr-label">标的池，</span>
                            <span class="ctr-label s-pdr-10">共 <span class="s-color-white">{{ choices.length }}</span> 支股票</span>
                            <el-select 
                                placeholder="添加持仓股票" 
                                class="s-mgr-10" 
                                style="width: 330px;" 
                                v-model="choices" 
                                @change="handleChoiceChange" 
                                multiple 
                                filterable 
                                clearable 
                                collapse-tags>
                                <el-option v-for="(item, item_idx) in pools" :key="item_idx" :value="item.stock_code" :label="formStockDisplay(item)"></el-option>
                            </el-select>
                            <el-autocomplete
                                    class="s-mgr-10"
                                    placeholder="搜索添加股票"
                                    v-model="keywords"
                                    :fetch-suggestions="suggest"
                                    @keydown.native="handleUserInput"
                                    @clear="handleClearIns"
                                    @select="handleSelect"
                                    clearable>
								<template slot-scope="{ item: ins }">
									<span class="item-code">[{{ ins.instrument }}] </span>
									<span class="item-name">{{ ins.instrumentName }}</span>
								</template>
							</el-autocomplete>
                            <el-popover v-model="importing.visible" @hide="hideImport" placement="bottom" title="批量导入股票" width="200" trigger="manual">
                                <el-button slot="reference" type="info" class="import-button" @click="importStocks">批量上传</el-button>
                                <div class="pasted-area s-pd-10">
                                    <div class="import-button-row s-pd-5">
                                        <el-button type="primary" size="mini" class="import-button" @click="extractStocks">导入</el-button>
                                        <el-button type="info" size="mini" class="import-button" @click="hideImport">关闭</el-button>
                                    </div>
                                    <el-input 
                                        v-model="importing.content" 
                                        type="textarea"
                                        :rows="10"
                                        placeholder="粘贴或输入【股票代码】或【股票名称】，如 “SHSE.600919, 600036 300058, 宁德时代 , 002826” ">
                                    </el-input>
                                </div>
                            </el-popover>
                        </div>
                        <div class="part-body">
                            <div class="stocks-list">
                                <table>
                                    <tr>
                                        <th label="代码/名称" min-width="140" prop="stock_code" formatter="formatCodeName" sortable overflowt></th>
                                        <th label="总仓位" min-width="80" prop="totalPosition" align="right" sortable thousands-int></th>
                                        <th label="可平仓位" min-width="80" prop="closablePosition" align="right" sortable thousands-int></th>
                                        <th label="目标数量" min-width="80" prop="targetPosition" align="right" formatter="formatTarget" sortable thousands-int></th>
                                        <th label="操作" min-width="60" fixed="right" formatter="formatActions"></th>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>    
    </el-dialog>
</div>