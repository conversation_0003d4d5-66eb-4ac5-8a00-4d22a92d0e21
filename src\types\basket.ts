import type {
  BasketTradeModeEnum,
  AlgorithmExecuteTimeEnum,
  ExpirationUnfinishedTreatmentEnum,
  TradeStyleEnum,
  TradeDirectionEnum,
  AssetTypeEnum,
} from '@/enum/trade';

/**
 * 篮子交易数据接口
 */
export interface BasketTradeInfo {
  /**
   * 篮子选择
   */
  basketId: number;

  /**
   * 交易模式（按数量1，权重2）
   */
  tradeMode: BasketTradeModeEnum;

  /**
   * 目标金额
   */
  targetAmount: number;

  /**
   * 篮子数量
   */
  targetBaskets: number;

  /**
   * 算法ID
   */
  algorithmId: number;

  /**
   * 时间类型
   */
  executionTime: AlgorithmExecuteTimeEnum;

  /**
   * 起止时间
   */
  timeRange: [Date, Date];

  /**
   * 量比比例
   */
  volumeRatio: number;

  /**
   * 开盘集合竞价
   */
  openingCallAuction: boolean;

  /**
   * 开盘集合竞价参与比例
   */
  openingCallAuctionParticipation: number;

  /**
   * 开盘集合竞价价格偏移
   */
  openingCallAuctionPriceOffset: number;

  /**
   * 触价
   */
  maxPrice: number;

  /**
   * 到期未成处理
   */
  unfinishedTreatment: ExpirationUnfinishedTreatmentEnum;

  /**
   * 交易风格
   */
  tradingStyle: TradeStyleEnum;

  /**
   * 撤单率
   */
  cancellationRate: number;

  /**
   * 单笔最小量
   */
  minSingleVolume: number;

  /**
   * 投资备注
   */
  investmentNotes: string;
}

/**
 * 篮子订单试算预览基础信息
 */
export interface BasketOrderPreviewBasic {
  /**
   * ID
   */
  id: number;

  /**
   * 合约名称
   */
  instrumentName: string;

  /**
   * 合约代码
   */
  instrument: string;

  /**
   * 资产类型
   */
  assetType: AssetTypeEnum;

  /**
   * 买卖方向
   */
  direction: TradeDirectionEnum;

  /**
   * 委托数量
   */
  volume: number;

  /**
   * 委托价格
   */
  price: number;

  /**
   * 预估金额
   */
  amount: number;
}

/**
 * 篮子订单试算预览接口
 */
export interface BasketOrderPreview extends BasketOrderPreviewBasic {
  /**
   * 涨停价
   */
  ceilingPrice: number;

  /**
   * 跌停价
   */
  floorPrice: number;

  /**
   * 昨日持仓数量
   */
  yesterdayPosition: number;

  /**
   * 今日持仓数量
   */
  todayPosition: number;

  /**
   * 可平仓位数量
   */
  closablePosition: number;
}
