const BaseWindow = require('./base-window').BaseWindow;
const TabList = require('./tab-list').TabList;
const Tab = require('./tab').Tab;
const Menu = require('../model/menu').Menu;
const { RiskMessage } = require('../model/risk-message');
const repoTradingDay = require('../repository/trading-day').repoTradingDay;
const { repoUser } = require('../repository/user');
const { consoleHelper } = require('../libs/console-helper');

class WinDashboard extends BaseWindow {

    constructor() {
        
        super('@win-dashboard', true);
        this.serverInfo = this.getContextDataItem(this.dataKey.serverInfo);
    }

    toggleShowMenu() {

        if (this.menuStates.expanded) {

            this.menuStates.expanded = false;
            this.$menuToggleCrumb.style.display = 'block';
            document.body.classList.remove(this.classes.menuOn);
        }
        else {

            this.menuStates.expanded = true;
            this.$menuToggleCrumb.style.display = 'none';
            document.body.classList.add(this.classes.menuOn);
        }
    }

    /**
     * @param {Menu} menu 
     */
    handleLevel1MenuClick(menu) {

        this.menuStates.selectedLevel1 = menu;
        if (menu.children.length == 0) {
            this.openTabViewByMenu(menu);
        }
        else {
            menu.expanded = !menu.expanded;
        }
    }

    /**
     * @param {Menu} sub_menu 
     */
    handleLevel2MenuClick(sub_menu) {

        this.menuStates.selectedLevel1 = this.menus.find(x => x.menuId == sub_menu.parentId);
        this.menuStates.selectedLevel2 = sub_menu;
        this.openTabViewByMenu(sub_menu);
    }

    createMenuApp() {

        var $side = document.querySelector('.win-side');
        this.menuApp = new Vue({

            el: $side,
            data: {
                menus: this.menus,
                states: this.menuStates,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.toggleShowMenu, this.handleLevel1MenuClick, this.handleLevel2MenuClick]),
        });
    }

    createWinButtonsApp() {

        this.winButtonsApp = new Vue({

            el: document.querySelector('.win-header > .win-buttons'),
            data: this.winStates,
            methods: this.helper.fakeVueInsMethod(this, [
                this.minimize, 
                this.maximize, 
                this.unmaximize, 
                this.lockWindow, 
                this.showUcDialog, 
                this.close,
            ]),
        });
    }

    selectMenu(id) {

        let matched = this.menus.find(x => x.menuId == id || x.children.some(y => y.menuId == id));
        if (matched == undefined) {
            return;
        }
        
        let is_level1 = matched.menuId == id;
        if (is_level1) {

            this.menuStates.selectedLevel1 = matched;
            this.menuStates.selectedLevel2 = null;
        }
        else {

            matched.expanded = true;
            let child = matched.children.find(x => x.menuId == id);
            this.menuStates.selectedLevel1 = matched;
            this.menuStates.selectedLevel2 = child;
        }
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        let { menuId, menuName } = tab.viewOptions;
        this.selectMenu(menuId);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {

        let { menuId, menuName } = tab.viewOptions;
        this.selectMenu(menuId);
        tab.viewEngine.trigger('on-view-visible');
    }

    /**
     * @param {Tab} tab 
     */
    handleTabUnfocused(tab) {
        //
    }

    /**
     * @param {Tab} tab 
     */
    handleTabClosed(tab) {
        //
    }

    setupTabCtr() {

        var $navi = document.querySelector('.win-content > .tab-placeholder > .win-top-tabs');
        var $content = document.querySelector('.win-content > .win-top-content');
        this.toptab = new TabList({

            $navi: $navi,
            $content: $content,
            lazyLoad: false,
            hideTab4OnlyOne: false,
            showToolkit: 'refresh,export',
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
            tabUnfocused: this.handleTabUnfocused.bind(this),
            tabClosed: this.handleTabClosed.bind(this),
        });
    }

    logout() {

        this.interaction.showConfirm({

            title: '操作确认',
            message: '确定要注销吗？',
            confirmed: () => {
                this.renderProcess.send(this.systemEvent.toLogout);
            },
        });
    }

    lockWindow() {
        this.locker.lock();
    }

    checkPasswordComplexity(rule, value, callback) {

        const regExp = /^(?=.*[A-Za-z])(?=.*\d).{8,12}$/;
        if (!regExp.test(value)) {
            callback(new Error('密码必须包含字母和数字，可包含中下划线，长度为8-12位'));
        } 
        else {
          callback();
        }
    }

    checkPasswordConfirm(rule, value, callback) {

        if (value !== this.ucform.new_password) {
          callback(new Error('两次输入的新密码不一致!'));
        } 
        else {
          callback();
        }
    }

    hideUcDialog() {
        this.ucdialog.visible = false;
    }

    changePwd() {
        
        this.ucdialogApp.$refs.$pwdform.validate((valid) => {

            if (valid) {
                this.sendPwdChange();
            } 
            else {
                return false;
            }
        });
    }

    async sendPwdChange() {
        
        var resp = await repoUser.changePwd(this.userInfo.userName, this.ucform.old_password, this.ucform.new_password);
        var { errorCode, errorMsg, data } = resp;
        
        if (errorCode == 0) {

            const usrInfo = this.getContextDataItem(this.dataKey.userInfo);
            usrInfo.password = this.helper.aesEncrypt(this.ucform.new_password);
            this.setContextDataItem(this.dataKey.userInfo, usrInfo);
            this.interaction.showSuccess('密码更改成功');
            this.ucdialog.visible = false;
        }
        else {
            this.interaction.showError(`密码更改异常：${errorCode}/${errorMsg}`);
        }
    }
    
    promptupUcDialog() {
        
        this.ucdialog.visible = true;
        this.ucform.old_password = '';
        this.ucform.new_password = '';
        this.ucform.confirm_password = '';
    }

    showUcDialog() {

        if (this.ucdialogApp) {

            this.promptupUcDialog();
            return;
        }
        
        this.ucdialog = { title: '用户中心', visible: false };
        this.ucform = { old_password: '', new_password: '', confirm_password: '' };
        this.ucrules = {

            old_password: [{ required: true, message: '请输入原始密码', trigger: 'blur' }],
            new_password: [
                { required: true, message: '请输入新密码', trigger: 'blur' },
                { validator: this.checkPasswordComplexity.bind(this), trigger: 'blur' },
            ],
            confirm_password: [
                { required: true, message: '请再次输入新密码', trigger: 'blur' },
                { validator: this.checkPasswordConfirm.bind(this), trigger: 'blur' }
            ]
        };

        this.ucdialogApp = new Vue({

            el: document.querySelector('.user-center'),
            data: {
                ucdialog: this.ucdialog,
                ucform: this.ucform,
                ucrules: this.ucrules,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.changePwd,
                this.hideUcDialog,
            ]),
        });

        this.promptupUcDialog();
    }

    close() {

        this.interaction.showConfirm({

            title: '操作确认',
            message: '是否确认退出应用程序？',
            confirmed: () => {
                this.thisWindow.close();
            },
        });
    }

    getVersion() {
        return this.app.contextData.appVersion;
    }

    createWinFooterApp() {

        this.about = {
            
            userName: this.userInfo.userName,
            fullName: this.userInfo.fullName,
            serverName: this.serverInfo.name,
            tradingDay: null,
            version: this.getVersion(),
        };

        this.network = { 

            type: 'success', 
            icon: 'el-icon-star-on', 
            content: '连接状态OK',
            pingpong: null,
            pingpongTitle: null,
        };

        this.messageApp = new Vue({

            el: document.querySelector('.win-footer'),
            data: {

                about: this.about,
                network: this.network,
            },

            methods: this.helper.fakeVueInsMethod(this, [this.logout]),
        });
    }

    createWinLockComponent() {

        this.locker = new WinLocker(this);
        this.locker.startup();
    }

    /**
     * @param {Boolean} is_ok 
     * @param {String} content 
     */
    handleNetworkStatusChange(is_ok, content) {

        if (is_ok) {

            this.network.type = 'success';
            this.network.icon = 'el-icon-star-on';
        }
        else {

            this.network.type = 'error';
            this.network.icon = 'el-icon-error';
        }

        this.network.content = content;
    }

    /**
     * @param {String} start_ts
     * @param {String} end_ts 
     */
    handlePingPong(start_ts, end_ts) {

        this.network.pingpongTitle = `发送: ${ new Date(start_ts).format('hh:mm:ss-S') } ~ 接收: ${ new Date(end_ts).format('hh:mm:ss-S') }`;
        this.network.pingpong = `网络延迟 ${ end_ts - start_ts }ms`;
    }

    /**
     * @param {Menu} menu 
     */
    openTabViewByMenu(menu) {

        this.setAsLastOpened(menu);
        let location = menu.viewLocation;
        let title = menu.menuName;
        let view_options = { permits: menu.blackPermit, menuId: menu.menuId, menuName: menu.menuName };

        if (this.helper.isNotNone(menu.menuTag)) {
            view_options.tag = menu.menuTag;
        }

        this.toptab.openTab(true, location, title, view_options);
    }

    /**
     * 打开一个新的顶级TAB
     * @param {boolean} singleton 是否为单例（相同 location 是否允许被新开）
     * @param {string} location 视图路径（如 @a/b/c，为view-src下的相对目录，且亿 “@” 打头）
     * @param {string} title 视图名称
     * @param {*} options 视图选项（可选）
     */
    openTab(singleton, location, title, options) {
        this.toptab.openTab(singleton, location, title, options);
    }

    /**
     * @param {Menu} menu 
     */
    setAsLastOpened(menu) {
        localStorage.setItem('last-opened-menu', JSON.stringify({ id: menu.menuId, name: menu.menuName }));
    }

    /**
     * @param {RiskMessage} message 
     */
    displayNotify(message) {

        this.interaction.notify({

            title: '风控提示',
            type: 'warning',
            position: 'bottom-right',
            dangerouslyUseHTMLString: true,
            duration: 8000,
            message: `<div>
                        <div>时间：${new Date(message.createTime).format('MM-dd hh:mm:ss')}</div>
                        <div>内容：${message.content}</div>
                        </div>`,
        });
    }

    listen2Events() {

        this.renderProcess.on(this.systemEvent.networkStatusChange, (event, { ok, content }) => {
            this.handleNetworkStatusChange(ok, content);
        });

        this.renderProcess.on(this.systemEvent.pingPong, (event, { start, end }) => {
            this.handlePingPong(start, end);
        });

        this.renderProcess.on('toggle-menu', () => {
            this.toggleShowMenu();
        });

        this.renderProcess.on(this.serverEvent.closePositionReply, (event, reply) => {
            this.interaction.showSuccess('平仓指令，已处理');
        });

        this.renderProcess.on(this.serverEvent.riskAlertReceived, (event, message) => {
            this.displayNotify(message);
        });

        /**
         * 打开一个顶级TAB（用于程序调用打开场景）
         */
        this.renderProcess.on('open-top-tab', (singleton, location, title, options) => {
            this.openTab(singleton, location, title, options);
        });
    }

    keepTradingDayUpdated() {

        let thisObj = this;
        async function execRequst() {

            let resp = await repoTradingDay.getCurrentTradingDay();
            if (resp.errorCode == 0) {
                thisObj.about.tradingDay = resp.data;
            }
        }

        setInterval(execRequst, 1000 * 60 * 3);
        execRequst();
    }

    handleViewRouting() {

        if (this.menus.length == 0) {
            return;
        }

        let last = localStorage.getItem('last-opened-menu');
        if (!last) {
            this.open1stMenu();
            return;
        }

        try {

            let { id, name } = JSON.parse(last);
            let matched = this.menus.find(x => x.menuId == id || x.children.some(y => y.menuId == id));
            if (matched == undefined) {
                this.open1stMenu();
                return;
            }
            
            let is_level1 = matched.menuId == id;
            if (is_level1) {

                this.menuStates.selectedLevel1 = matched;
                this.menuStates.selectedLevel2 = null;
                this.openTabViewByMenu(matched);
            }
            else {

                matched.expanded = true;
                let child = matched.children.find(x => x.menuId == id);
                this.menuStates.selectedLevel1 = matched;
                this.menuStates.selectedLevel2 = child;
                this.openTabViewByMenu(child);
            }
        }
        catch(ex) {
            this.open1stMenu();
        }
    }

    open1stMenu() {

        /**
         * 打开第一个菜单，作为默认视图
         */

        let first = this.menus[0];
        let has_child = first.children.length > 0;

        if (has_child) {

            first.expanded = true;
            this.menuStates.selectedLevel2 = first.children[0];
        }

        let focused = has_child ? first.children[0] : first;
        this.openTabViewByMenu(focused);
    }

    /**
     * 读取并构造系统菜单数据
     * @returns {Array<Menu>}
     */
    constructMenus() {

        var menus = this.app.contextData[this.dataKey.userMenus];
        if (!(menus instanceof Array) || menus.length == 0) {
            return [];
        }
        
        return menus.map(item => Menu.Duplicate(item));
    }

    prepare() {

        /**
         * 菜单列表
         */

        this.menus = this.constructMenus();

        if (this.userInfo.isSuperAdmin) {

            this.menus.push({
                menuId: 9999,
                menuName: '算法中心',
                menuIcon: 'iconfont icon-chanpin',
                isApp: false,
                expanded: false, 
                isChild: false, 
                viewLocation: '@2024/algo-design/index',
                children: [],
                parentId: null,
            });
        }

        this.menuStates = {

            selectedLevel1: this.menus[0],
            selectedLevel2: this.menus[0],
            expanded: true,
        };

        this.classes = { menuOn: 'with-menu-on' };        
        this.$menuToggleCrumb = document.querySelector('.win-content > .menu-expander');
        this.$menuToggleCrumb.onclick = this.toggleShowMenu.bind(this);
    }

    compatiblizeStyle() {

        var $common = document.getElementById('link-common-stylesheet');
        var $common_old = document.createElement('link');
        $common_old.rel = 'stylesheet';
        $common_old.href = '../asset/css/common-old.css';
        $common.parentElement.insertBefore($common_old, $common.nextElementSibling);

        var $theme = document.getElementById('link-theme-stylesheet');
        var $theme_old = document.createElement('link');
        $theme_old.rel = 'stylesheet';
        $theme_old.href = '../asset/css/themed-dark-old.css';
        $theme.parentElement.insertBefore($theme_old, $theme.nextElementSibling);
    }

    build() {

        this.compatiblizeStyle();
        this.prepare();
        this.setupTabCtr();
        this.brocastReady();
        this.createWinButtonsApp();
        this.createMenuApp();
        this.createWinFooterApp();
        this.createWinLockComponent();
        this.listen2Events();
        this.keepTradingDayUpdated();

        // 通知主进程，主窗口已准备就绪
        this.renderProcess.send(this.systemEvent.mainWindowReady);
        window.dashboardWindow = this;
        window.consoleHelper = consoleHelper;
    }
}

class WinLocker {

    get helper() {
        return this.dashboard.helper;
    }

    get systemEvent() {
        return this.dashboard.systemEvent;
    }

    get renderProcess() {
        return this.dashboard.renderProcess;
    }

    /**
     * @param {WinDashboard} dashboard 
     */
    constructor(dashboard) {
        this.dashboard = dashboard;
    }

    createApp() {

        this.data = { 
            
            passcode: null,
            displaying: false,
        };

        this.vueApp = new Vue({

            el: document.body.querySelector('.window-locker'),
            data: this.data,
            methods: this.helper.fakeVueInsMethod(this, [this.unlockScreen, this.exit]),
        });
    }

    unlockScreen() {

        let passcode = (this.data.passcode || '').trim();
        if (passcode.length == 0) {
            return;
        }

        this.renderProcess.send(this.systemEvent.unlockScreen, passcode);
        this.data.passcode = null;
    }

    exit() {

        this.dashboard.interaction.showConfirm({

			title: '操作确认',
			message: '<span>是否确认退出软件？</span>',
			confirmed: () => {
                this.renderProcess.send(this.systemEvent.exitApp);	
			},
		});
    }

    lock() {
        this.renderProcess.send(this.systemEvent.lockScreen);
    }

    putup() {
        this.data.displaying = true;
    }

    putdown() {
        this.data.displaying = false;
    }

    startup() {

        window.appendTableRows = appendTableRows;
        this.createApp();
        this.renderProcess.on(this.systemEvent.lockScreen, () => { this.putup(); });
        this.renderProcess.on(this.systemEvent.unlockScreen, () => { this.putdown(); });
    }
}

function appendTableRows(smart_table_css_selector, rows) {

    const $compnent = document.querySelector(smart_table_css_selector);
	const $body = $compnent.querySelector('.smart-table-body');
    const $tables = $body.querySelectorAll('.table-center, .table-fixed-left, .table-fixed-right');
	
    $tables.forEach($table => {

        const token = Math.random().toString().replace('.', '');
        const $tbody = $table.querySelector('tbody');
        const $last_row = $tbody.querySelector('tr');

        for (let i = 0; i < rows; i++) {

            const $tr = $last_row.cloneNode(true);
            $tr.id = `new-row-${token}-${i}`;
            $tbody.appendChild($tr);
        }
    });
}

module.exports = WinDashboard;
