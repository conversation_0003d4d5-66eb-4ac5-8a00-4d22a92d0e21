const { IView } = require('../../../../component/iview');
const TradeView = require('../algo/trade');
const AlgorithmOrderView = require('../algo/order');
const DataRecordsView = require('../algo/records');
const { AlgoOrderInfo } = require('../../model/message');
const { AlgoOrder } = require('../../../../model/algo-order');
const { repoAlgo } = require('../../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '算法交易');
    }

    createTradeView() {

        var $root = this.$tradeRoot = this.$container.querySelector('.block-trade');
        var tradeView = new TradeView('@2021/trading/algo/trade');
        tradeView.loadBuild($root);
        tradeView.registerEvent('place-algo-orders', this.handlePlaceRequest.bind(this));
        this.tradeView = tradeView;
    }

    /**
     * @param {Boolean} isPreview 
     * @param {AlgoOrderInfo|Array<AlgoOrderInfo>} algOrders 
     * @param {{ onlimited: Boolean, onexpired: Boolean }} options 
     */
    async handlePlaceRequest(algOrders, options) {

        var selecteds = [];
        options.onexpired && selecteds.push('超时');
        options.onlimited && selecteds.push('涨跌停');        
        var message = null;
        
        if (algOrders instanceof AlgoOrderInfo) {

            let mentions = [

                ['方向', algOrders.directionName],
                ['产品', algOrders.productName],
                ['策略', algOrders.strategyName || '[无]'],
                ['账号', algOrders.accountName],
                ['算法', `${algOrders.algoId} / ${algOrders.algoName}`],
                ['数量', algOrders.volume],
                ['期间', `${algOrders.startTime} ~ ${algOrders.endTime}`],
                ['备注', algOrders.remark],
                ['限制', selecteds.length == 0 ? '[无]' : selecteds.join('/') + '时，继续交易'],
            ];
    
            message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        }
        else {
            message = `篮子算法条数 = ${algOrders.length}`;
        }

        this.interaction.showConfirm({

            title: '算法下单确认',
            message: message,
            confirmed: async () => {
                
                var dtos = this.formOrders(algOrders instanceof AlgoOrderInfo ? [algOrders] : algOrders, options);
                var resp = await repoAlgo.order(dtos, 0);
                if (resp.errorCode == 0) {
                    
                    setTimeout(() => { this.algoOrderView.trigger('reload-algo-orders'); }, 1000 * 1);
                    this.interaction.showSuccess('算法单已发送');
                }
                else {
                    this.interaction.showError(`算法单处理失败：${resp.errorCode}/${resp.errorMsg}`);
                }
            },
        });
    }

    /**
     * @param {Array<AlgoOrderInfo>} orders 
     * @param {{ onlimited: Boolean, onexpired: Boolean }} options 
     */
    formOrders(orders, options) {

        var userId = this.userInfo.userId;
        var today = new Date().format('yyyy-MM-ddd');

        return orders.map(item => ({

            direction: item.direction,
            identityId: item.strategyId || item.productId,
            accountId: item.accountId,
            algorithmMappingId: item.algoId,
            instrument: item.instrument,
            volume: item.volume,
            effectiveTime: new Date(today + ' ' + item.startTime).getTime(),
            expireTime: new Date(today + ' ' + item.endTime).getTime(),
            limitAction: options.onlimited ? 1 : 0,
            afterAction: options.onexpired ? 1 : 0,
            userId: userId,
            algoParam: item.algoParam,
            taskName: item.remark,
        }));
    }

    createAlgoOrderView() {

        var $root = this.$container.querySelector('.block-algo-orders');
        var view = new AlgorithmOrderView('@2021/trading/algo/order');
        view.loadBuild($root);
        view.registerEvent('algo-order-selected', this.handleAlgoOrderSelected.bind(this));
        this.algoOrderView = view;
    }

    refresh() {
        this.algoOrderView.refresh();
    }

    /**
     * @param {AlgoOrder} algord
     */
    handleAlgoOrderSelected(algord) {

        /** 当前选中的算法母单 */
        this.algord = algord;
        this.recordView.trigger('set-context-algo-order', algord.id);
    }

    createDataRecords() {

        var $root = this.$container.querySelector('.data-records');
        var view = new DataRecordsView('@2021/trading/algo/records');
        view.loadBuild($root);
        this.recordView = view;
    }

    build($container) {

        super.build($container);
        this.createDataRecords();
        this.createTradeView();
        this.createAlgoOrderView();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;
