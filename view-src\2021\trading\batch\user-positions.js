const { TradeRecordView } = require('../module/trade-record-view');
const { Position } = require('../../../../model/position');
const { repoPosition } = require('../../../../repository/position');
const { repoAccount } = require('../../../../repository/account');
const { repoTrading } = require('../../../../repository/trading');
const { AccountDetail } = require('../../../../model/account');

class View extends TradeRecordView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '我的持仓');
        this.types = {

            all: { code: 0, mean: '全部' },
            product: { code: 1, mean: '产品持仓' },
            strategy: { code: 2, mean: '策略持仓' },
        };

        this.states = {

            type: this.types.all.code,
            keywords: null,
        };

        this.priceMap = {};
        this.accounts = [new AccountDetail({})].splice(1);
    }

    handleChannelChange() {

        super.handleChannelChange();

        if (this.isFuture || this.isOption) {
            this.tableObj.showColumns(['保证金']);
        }
        else {
            this.tableObj.hideColumns(['保证金']);
        }
    }

    async requestAllAccounts() {

        if (this.accounts.length == 0) {
            
            let resp = await repoAccount.batchGetAccountCash();
            let { errorCode, data } = resp;
            if (errorCode == 0 && data instanceof Array) {
                this.accounts = data.map(item => new AccountDetail(item));
            }
        }

        return this.accounts;
    }

    async getProperIds() {

        let accounts = await this.requestAllAccounts();
        let identityIds = accounts.map(x => this.helperUi.getProperAccountId(x)).join(';');
        return identityIds;
    }

    async queryFirstScreen() {

        let identityIds = await this.getProperIds();
        return await repoPosition.quickMemQuery({ identityIds, pageSize: this.paging.pageSize, pageNo: 1 });
    }

    async queryAll() {
        
        let identityIds = await this.getProperIds();
        return await repoPosition.batchMemQuery({ identityIds });
    }

    listen2DataChange() {
        this.standardListen(this.serverEvent.positionChanged, this.handlePositionChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.positionChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.positionChanged]);
    }

    async consumeBatchPush(titles, contents, totalSize) {

        super.consumeBatchPush(titles, contents, totalSize);
        var records = TradeRecordView.ModelConverter.formalizePositions(titles, contents);
        this.tableObj.refill(records);
        this.filterPositions();
        this.updatePrice();
    }

    async updatePrice() {

        var map = await repoTrading.getAssetsLatestPrice();

        for (let key in map) {
            this.priceMap[key] = map[key] || 0;
        }

        const positions = this.typeRecords(this.tableObj.extractAllRecords());
        positions.forEach(pos => {
            
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let scale = pos.totalPosition * pos.direction * pos.volumeMultiple;
            let floatProfit = (lastPrice - pos.avgPrice) * scale;

            this.tableObj.updateRow({

                id: pos.id,
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
                marketValue: Math.abs(lastPrice * scale),
            });
        });
    }

    schedulePriceUpdate() {

        this.priceUpdateTimer = setInterval(async () => {
            
            if (this.isRequestingPrice) {
                return;
            }

            try {
                
                this.isRequestingPrice = true;
                await this.updatePrice();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isRequestingPrice = false;
            }

        }, 1000 * 10);
    }

    /**
     * @param {*} struc
     */
    handlePositionChange(struc) {
        
        var pos = new Position(struc);
        if (this.isRecordAssetQualified(pos.assetType)) {

            this.tableObj.putRow(pos);
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let scale = pos.totalPosition * pos.direction * pos.volumeMultiple
            let floatProfit = (lastPrice - pos.avgPrice) * scale;

            this.tableObj.updateRow({ 
                
                id: pos.id, 
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
                marketValue: Math.abs(lastPrice * scale),
            });
        }
    }

    // resetControls() {

    //     super.resetControls();
    //     this.states.type = this.types.all.code;
    //     this.states.keywords = null;
    // }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                states: this.states,
                types: this.types,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterPositions,
                this.hope2CloseCheckeds,
                this.createAsBasket,
            ]),
        });
    }

    /**
     * @param {Position} record 
     */
    formatActions(record) {
        return '';
    }

    /**
     * @param {Position} record 
     */
    formatPosPct(record) {

        let { strategyId, accountId, totalPosition, lastPrice, volumeMultiple } = record;
        let account = this.helper.isNotNone(strategyId) 
                        ? this.accounts.find(x => x.accountId == accountId && x.strategyId == strategyId)
                        : this.accounts.find(x => x.accountId == accountId && this.helper.isNone(strategyId));

        if (account == undefined) {
            return '[账号未匹配]';
        }

        let { balance } = account;
        if (balance == 0) {
            return '[账号权益=0]';
        }
        else if (typeof lastPrice != 'number' || lastPrice == 0) {
            return '[最新价缺失]';
        }
        else {
            return `${(lastPrice * totalPosition * volumeMultiple * 100 / balance).toFixed(2)}%`;
        }
    }

    filterByChannel() {
        this.filterPositions();
    }

    filterPositions() {

        var thisObj = this;
        var keywords = this.states.keywords;
        var isAll = this.states.type == this.types.all.code;
        var isOfProduct = this.states.type == this.types.product.code;
        var isOfStrategy = this.states.type == this.types.strategy.code;

        /**
         * @param {Position} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Position} record 
         */
        function testRecords(record) {

            return thisObj.isRecordAssetQualified(record.assetType)
            && (isAll || isOfProduct && thisObj.helper.isNone(record.strategyId) || isOfStrategy && thisObj.helper.isNotNone(record.strategyId))
            && (thisObj.tableObj.matchKeywords(record) || filterByPinyin(record));

            // return thisObj.tableObj.matchKeywords(record) || filterByPinyin(record);
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    /**
     * @param {Array<Position>} records
     * @returns {Array<Position>}
     */
    typeRecords(records) {
        return records;
    }

    hope2CloseCheckeds() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无持仓');
            return;
        }

        if (this.tableObj.filteredRowCount == 0) {

            this.interaction.showMessage('筛选结果无持仓');
            return;
        }

        var checkeds = this.typeRecords(this.tableObj.extractCheckedRecords());
        if (checkeds.length == 0) {

            this.interaction.showMessage('请选择要平仓的合约');
            return;
        }

        var filtereds = this.typeRecords(this.tableObj.extractFilteredRecords());
        var intersecs = checkeds.filter(item => filtereds.some(item2 => this.identifyRecord(item2) == this.identifyRecord(item)));
        var closables = intersecs.filter(item => item.closableVolume > 0);
        if (closables.length == 0) {

            this.interaction.showError(`勾选持仓 = ${intersecs.length}，可平持仓 = 0`);
            return;
        }

        this.closePosition(closables);
    }

    /**
     * 平仓
     * @param {Array<Position>} positions 
     */
    closePosition(positions) {
        
        if (this.closeDialog === undefined) {
            
            const DialogClosePosition = require('../../fragment/dialog-close-positions');
            const dialog = new DialogClosePosition('@2021/fragment/dialog-close-positions', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', positions); });
            this.closeDialog = dialog;
        }
        else {
            this.closeDialog.trigger('showup', positions);
        }
    }

    createAsBasket() {
        this.interaction.showAlert('暂未实现篮子导入功能');
    }

    dispose() {

        super.dispose();
        clearInterval(this.priceUpdateTimer);
        delete this.priceUpdateTimer;
    }

    build($container) {

        super.build($container, 'smt-fup');
        this.subChange();
        this.turn2Request();
        this.registerEvent('batch-order-made', _ => {

            /**
             * 延迟些许刷新持仓，获得完整度更高的，最新持仓数据
             */
            setTimeout(() => { this.reloadRecords(); }, 1000 * 2);
        });

        this.schedulePriceUpdate();
    }
}

module.exports = View;