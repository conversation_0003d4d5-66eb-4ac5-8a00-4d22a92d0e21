const { SimpleAccountItem } = require('../../model/account');
const { TypicalDataView } = require('../../classcial/typical-data-view');
const { CreditToolbarView, ToolbarQueryData } = require('./toolbar');

class CreditBaseView extends TypicalDataView {

    /**
     * 是否为两融标的视图
     */
    get isDebted() {
        return !!this.voptions.isDebted;
    }

    /**
     * 是否为单账号模式
     */
    get isBySingleAccount() {
        return !!this.voptions.isBySingleAccount;
    }

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.registerEvent('set-context-account', this.setAccount.bind(this));
    }

    /**
     * @param {SimpleAccountItem} account 
     */
    setAccount(account) {

        this.contextAccount = account;

        if (this.isBuildCompleted) {
            this.requestRecords();
        }
        else {
            this.initializer = () => { this.requestRecords(); };
        }
    }

    createToolbar() {

        var toolbar = new CreditToolbarView('@2021/fragment/credit/toolbar', this.title, this.isDebted, this.isBySingleAccount, {

            showProduct: false,
            showStrategy: false,
            showAccount: false,
        });

        toolbar.registerEvent('do-search', this.handleSearch.bind(this));
        toolbar.registerEvent('reply-query-data', this.handleSearch.bind(this));
        toolbar.loadBuild(this.$toolbar);
        this.toolbarView = toolbar;
    }

    async requestCreditData(criteria) {
        throw new Error('not implemented');
    }

    /**
     * @param {ToolbarQueryData} toolbarData 
     */
    async handleSearch(toolbarData) {
        
        var criteria = {

            user_id: this.userInfo.userId,
            token: this.userInfo.token,

            compact_type: toolbarData.typeId,
            compact_status: toolbarData.subsistId,
            fund_id: null,
            strategy_id: null,
            account_id: null,
            instrument: toolbarData.keywords,

            pageNo: this.paging.page,
            pageSize: this.paging.pageSize,
            // occur_date: new Date().format('yyyyMMdd'),
        };

        var account = this.contextAccount;

        if (this.isBySingleAccount && account) {
            
            criteria.fund_id = account.fundId;
            criteria.account_id = account.accountId;
        }
        else {
            
            criteria.fund_id = toolbarData.productId;
            criteria.strategy_id = toolbarData.strategyId;
            criteria.account_id = toolbarData.accountId;
        }

        this.interaction.showSuccess('刷新请求已发出');
        var resp = await this.requestCreditData(criteria);
        if (resp.errorCode != 0) {
            return this.interaction.showError(`两融数据请求错误：${resp.errorCode}/${resp.errorMsg}`);
        }
        
        var records = resp.data.list;
        this.tableObj.refill(records);
        this.paging.total = resp.data.totalSize;
    }

    handlePageSizeChange() {
        
        this.paging.page = 1;
        this.tableObj.setPageSize(this.paging.pageSize, false);
        this.requestRecords();
    }

    handlePageChange() {
        this.requestRecords();
    }

    refresh() {
        
        this.resetControls();
        this.requestRecords();
    }

    async requestRecords() {
        this.toolbarView.trigger('pull-query-data');
    }

    formatTargetType(record, fieldValue) {

        switch (fieldValue) {

            case 0: return '融资';
            case 1: return '融券';
            case 2: return '担保品';
            case 3: return '专项';
            default: return fieldValue;
        }
    }

    formatStatus(record, fieldValue) {

        switch (fieldValue) {

            case 0: return '正常';
            case 1: return '暂停';
            case 2: return '作废';
            default: return fieldValue;
        }
    }

    formatCompactType(record, fieldValue) {

        switch (fieldValue) {

            case 0: return '融资';
            case 1: return '融券';
            default: return fieldValue;
        }
    }

    formatCompactStatus(record, fieldValue) {

        switch (fieldValue) {

            case 0: return '<a class="s-flag s-bg-red">未了结</a>';
            case 1: return '<a class="s-flag s-bg-green">已了结</a>';
            default: return fieldValue;
        }
    }

    build($container, options, toolbarOptions, tableName) {

        /**
         * 是否为两融标的视图
         */
        options.isDebted = toolbarOptions.isDebted;
        super.build($container, options, { tableName, maxHeight: 300 });

        /**
         * 标识页面加载完成
         */
        this.isBuildCompleted = true;

        /**
         * 账号变化事件，先于视图加载完成
         */
        if (typeof this.initializer == 'function') {
            
            this.initializer();
            delete this.initializer;
        }
    }
}

module.exports = { CreditBaseView };