/**
 * 算法参数项
 */
export interface AlgoParam {
  /**
   * 参数标签/显示名称
   */
  label: string;

  /**
   * 属性名称
   */
  prop: string;

  /**
   * 参数类型标识符
   */
  type: string;

  /**
   * 指示参数是否为必填项
   */
  required: boolean;

  /**
   * 参数默认值（数值、字符串、布尔值、时间、时间区间、日期、日期区间、数组、空值等，默认值不尽相同）
   */
  defaultValue: any;

  /**
   * 算法参数提供之可选项
   */
  uoptions: { label: string; value: any }[];

  /**
   * 选项标签
   */
  optionLabel: string;

  /**
   * 参数描述/备注
   */
  remark: string;

  /**
   * 指示参数是否应显示
   */
  display: boolean;
}

/**
 * 策略交易算法信息
 */
export interface TradingAlgorithm {
  /**
   * 算法ID（如果未分配则为null）
   */
  id: number;

  /**
   * 算法外部ID
   */
  externalId: number | string;

  /**
   * 算法名称
   */
  name: string;

  /**
   * 供应商标识符（算法提供商）
   */
  vendorId: number | string;

  /**
   * 券商标识符（通常为券商名称首字母）（适用于哪个券商）
   */
  brokerId: string;

  /**
   * 算法备注或描述
   */
  remark: string | null;

  /**
   * 算法参数数组
   */
  params: AlgoParam[];

  /**
   * 策略类型标识符
   */
  strategyType: number;

  /**
   * 创建用户ID
   */
  userId: number;
}
