const { AlgoParamPartView } = require('../../../2024/algo-param-part-view');
const { CodeMeanItem, BasketOrderParam } = require('../../model/message');
const { AlgorithmClasses } = require('../../../../repository/algorithm');

class View extends AlgoParamPartView {

    /**
     * 是否为ETF篮子
     */
    get isEtfBasket() {
        return this._isEtfBasket === true;
    }

    /**
     * 当前选择方向是否为：买入
     */
     get isBuy() {
        return this.uistates.direction == this.directions.buy.code;
    }

    /**
     * 当前选择方向是否为：卖出
     */
    get isSell() {
        return this.uistates.direction == this.directions.sell.code;
    }

    /**
     * 当前选择方向是否为：调仓
     */
    get isAdjust() {
        return this.uistates.direction == this.directions.adjust.code;
    }

    get theMethod() {
        return this.smethods.find(x => x.code == this.uistates.method);
    }

    constructor(view_name) {

        super(view_name, '篮子算法交易');

        var dirs = this.systemTrdEnum.tradingDirection;
        /**
         * 委托方向
         */
        this.directions = {

            buy: new CodeMeanItem(dirs.buy.code, dirs.buy.mean),
            sell: new CodeMeanItem(dirs.sell.code, dirs.sell.mean),
            adjust: new CodeMeanItem(0, '调仓'),
        };

        /*
        
        下单方式：

        VOLUME("数量篮子", 1),——按数量篮子模式下单，数量单位为 篮（executeVolume）——复制模式
        PROPORTION("权重篮子", 2),——按权重篮子模式下单，数量单位为 元——复制模式（executeVolume）
        PROPORTION_ACCOUNT_RATIO("权重篮子与账号比例", 3),——按权重篮子下单，账号比例放入TaskDetailBean中的multiple，executeVolume表示总金额——分派模式
        PROPORTION_ASSET_RATIO("权重篮子与资产比例", 4);,——按权重篮子下单，，executeVolume表示仓位，例如输入50，表示所有账号都操作资产比例的50%；——复制模式
        
        */

        var methods = this.systemTrdEnum.basketMethod;

        /**
         * 委托方式
         */
        this.methods = { 
            volume: methods.volume, 
            weight: methods.weight, 
        };

        this.smethods = Object.values(this.methods);
        this.stages = this.systemTrdEnum.bidingStages;
        /** 选择的目标交易账号（accountId列表） */
        this.accounts = [];

        /**
         * 下单面板通用核心状态
         */
        this.uistates = {

            direction: this.directions.buy.code,
            instrument: null,
            instrumentName: null,
            method: this.methods.volume.code,
            algoId: null,
            scale: 1,
        };

        this.exclude = {

            suspend: false,
            cash: false,
            ceiling: false,
            floor: false,
        };

        this.registerEvent('set-as-basket', this.handleBasketChange.bind(this));
        this.registerEvent('account-changed', this.handleAccountChange.bind(this));
    }

    /**
     * @param {Array} account_ids 
     */
    handleAccountChange(account_ids) {

        this.accounts = Array.isArray(account_ids) ? account_ids : [];
        this.uistates.algoId = !Array.isArray(account_ids) || account_ids.length == 0 ? null : account_ids[0];
        this.requestAlgoes();
    }

    /**
     * @param {Number} basketId 
     * @param {String} basketName 
     * @param {Boolean} isEtf 
     */
    handleBasketChange(basketId, basketName, isEtf) {

        this._isEtfBasket = isEtf;
        this.uistates.instrument = basketId || null;
        this.uistates.instrumentName = basketName || null;
        this.smethods.clear();
        this.smethods.merge(isEtf ? [this.methods.volume] : this.helper.dict2Array(this.methods));
        this.uistates.method = this.smethods[0].code;
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                directions: this.helper.dict2Array(this.directions),
                methods: this.smethods,
                stages: this.stages,
                algoParams: this.algoParams,
                algoGrps: this.algoGrps,
                uistates: this.uistates,
                exclude: this.exclude,
                validation: this.validation,
                uoptions: [{ label: '', value: '' }].splice(1),
            },

            computed: {

                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                theMethod: () => { return this.theMethod; },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.hope2Preview,
                this.hope2Entrust,
                this.handleMethodChange,
                this.handleAlgoChange,
                this.shortize,
                this.isIntegerParam,
                this.isDecimalParam,
                this.isTimeParam,
                this.isTimeRangeParam,
                this.isTextParam,
                this.isUserOptionParam,
            ]),
        });
    }

    handleMethodChange() {
        this.uistates.scale = 0;
    }

    handleAlgoChange() {
        this.alignAlgoParams(this.uistates.algoId);
    }

    shortize(label) {
        return typeof label == 'string' ? label.substring(0, 7) : label;
    }

    hope2Preview() {

        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        this.trigger('preview-basket-orders', this.formParams());
    }

    hope2Entrust() {

        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        this.trigger('place-basket-orders', this.formParams());
    }

    getAlgoName() {
        
        let matched = this.algoes.find(x => x.id == this.uistates.algoId);
        return matched ? matched.algorithmName : null;
    }

    formParams() {

        const states = this.uistates;
        const matchedMethod = this.theMethod;
        const result = this.collectAlgoParamsInput();
        const orderParams = {

            direction: states.direction,
            directionName: this.isBuy ? '买入' : this.isSell ? '卖出' : '调仓',
            
            basketId: states.instrument,
            basketName: states.instrumentName,

            method: states.method,
            methodName: matchedMethod.mean,
            methodLabel: matchedMethod.label,
            methodUnit: matchedMethod.unit,

            scale: states.scale,
            stage: 0,
            stageName: null,
            offset: 0,
            adjustType: 1,

            algoId: states.algoId,
            algoName: this.getAlgoName(),

            effectiveTime: result.effectiveTime.value,
            expireTime: result.expireTime.value,
            algoParam: JSON.stringify(result.params),
        };

        return new BasketOrderParam(orderParams, this.helper.deepClone(this.exclude));
    }

    areParamsOk() {

        var uistates = this.uistates;

        if (!uistates.instrument) {
            return '请选择篮子';
        }

        if (!(uistates.scale > 0)) {
            return '委托' + this.theMethod.label + '无效';
        }

        // if (this.theMethod.code == this.methods.weight2Asset.code && uistates.scale > 100) {
        //     return `${this.methods.weight2Asset.mean}，不能超过100%`;
        // }

        if (this.algoes.length == 0) {
            return '没有可用算法';
        }
        else if (this.helper.isNone(uistates.algoId)) {
            return '算法未选择';
        }
        else if (!this.areAlgoParamsApplicable()) {
            return '算法参数值，部分或全部缺失';
        }
        
        return true;
    }

    async requestAlgoes() {

        if (this.accounts.length == 0) {

            this.algoGrps.clear();
            this.algoes.clear();
        }
        else {

            await this.queryQualifiedAlgoes(AlgorithmClasses.normal, this.accounts.join(','));
            this.uistates.algoId = this.algoes.length > 0 ? this.algoes[0].id : null;
            this.handleAlgoChange();
        }
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;