
const { TodayBaseList } = require('./baselist-today');
const { ContextObjectInfo } = require('../../model/context-object-info');
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const NumberMixin = require('../../mixin/number').NumberMixin;
const ModelConverter = require('../../model/model-converter').ModelConverter;
const TradeRecord = require('../../model/trade-record').TradeRecord;
const BizHelper = require('../../libs/helper-biz').BizHelper;

class View extends TodayBaseList {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, TodayBaseList.ViewTitles.tradeRecord);

        this.setDataFunCodeType(this.serverFunction.requestTodayTradeRecord, 'tradeRecord');
        this.condition = {
            keywords: null,
        };

        this.setTitle([

            'id',
            'orderId',
            'userId',
            'userName',
            'accountId',
            'accountName',
            'fundId',
            'fundName',
            'assetType',
            'strategyId',
            'strategyName',
            'tradingDay',
            'direction',
            'positionEffect',
            'instrument',
            'instrumentName',
            'tradeTime',
            'isToday',
            'volume',
            'tradedPrice',
            'tradeId',
            'exchangeOrderId',
            'commission',
            'adjustFlag',
            'identityid',
            'updateTime',
            'algoUserId',
            'algoUserName',
            'financeAccount',
        ]);
    }

    /**
     * 退订，之前的，上下文对象的实时数据
     * @param { ContextObjectInfo } previous_context 
     */
    unsubChange(previous_context) {

        this.standardSend(this.systemEvent.unsubscribeAccountChange,
                          previous_context.identityId, 
                          [this.serverEvent.exchangeChanged]);
    }

    /**
     * 订阅，当前的，上下文对象的实时数据
     */
    resubChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, this.identityId, [this.serverEvent.exchangeChanged]);
    }

    resetControls() {
        
        super.resetControls();
        this.condition.keywords = null;
    }

    /**
     * @param {Array<Array>} locals 
     * @param {Array<Array>} newers 
     * @returns {Array<Array>}
     */
    mergeAll(locals, newers) {
        
        /**
         * 理论上，成交列表不存在重叠，实际场景中，可能在服务器推送的全量数据 & 实时推送，两者之间存在一定的处理时间差，导致存在数据重叠的可能性
         */

        let id_idx = this.RecordIdIdx;
        let map_loc = {};
        let map_new = {};

        locals.forEach(values => { map_loc[values[id_idx]] = values; });
        newers.forEach(values => { map_new[values[id_idx]] = values; });

        for (let ord_id in map_new) {
        
            let item_loc = map_loc[ord_id];
            let item_new = map_new[ord_id];

            if (item_loc === undefined) {
                locals.push(item_new);
            }
        }

        return locals;
    }

    /**
     * @param {Array<Array>} contents 
     */
    consumeBatchPush(contents) {

        var records = ModelConverter.formalizeTradeRecords(this.titles, contents);
        if (this.context.isAboutFund) {
            records.remove(x => x instanceof TradeRecord && x.adjustFlag);
        }

        super.consumeBatchPush(records);
        this.tableObj.refill(records);
    }

    /**
     * @param {*} struc
     */
    consumeRealtimePush(struc) {
        
        var trade_record = new TradeRecord(struc);
        if (this.context.isAboutFund && trade_record.adjustFlag) {
            return;
        }
        this.tableObj.putRow(trade_record);
    }

    createToolbarApp() {

        this.toolbarApp = new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                condition: this.condition,
                sharedCondition: this.sharedCondition,
                paging: this.paging,
            },

            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.toggleRealtimePush,
                this.openAddRecordDialog,
                this.filterRecords, 
                this.handlePageSizeChange, 
                this.handlePageChange,
            ]),
        });
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.condition.keywords;

        /**
         * @param {TradeRecord} record 
         */
        function filterByPinyin(record) {
            return thisObj.testKeywords(record.instrumentName, keywords);
        }
        
        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.mixedFilter(filterByPinyin, 'or');
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {

        if (!this.isDataPushReceived) {

            this.interaction.showMessage('数据未准备完整，请稍后翻页');
            return;
        }

        this.tableObj.setPageIndex(this.paging.page);
    }

    createTableComponent() {
        
        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.table-control');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-exl',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'tradeTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        return tableObj;
    }

    handleTableFiltered(filtered_count) {
        this.paging.total = filtered_count;
    }

    /**
     * @param {TradeRecord} row_data 
     */
    formatActions(row_data) {

        if (this.sharedCondition.canChangeRecord) {

            return `<button event.onclick="updateRecord"><i class="el-icon-edit"></i> 更改</button>
                    <button event.onclick="deleteRecord" class="danger"><i class="el-icon-delete"></i> 删除</button>`;
        }
        else {
            return '';
        }
    }

    listen2Events() {

        this.standardListen(this.serverEvent.todayTradeRecordPush, this.handleBatchPush.bind(this));
        this.standardListen(this.serverEvent.exchangeChanged, this.handleRealtimeChange.bind(this));
    }

    build($container) {

        super.build($container);
        this.listen2Events();
    }
}

module.exports = View;
