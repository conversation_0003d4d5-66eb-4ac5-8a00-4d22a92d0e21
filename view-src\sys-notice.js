
const IView = require('../component/iview').IView;

class View extends IView {

    constructor(view_name) {
        super(view_name, true, '系统通知');
    }
    
    createApp() {

        this.vueApp = new Vue({
            el: '#body-main',
            data: {
                icon_class: null,
                message: null
            },
            methods: {
                hideWindow: () => {
                    this.thisWindow.hide();
                }
            }
        });
    }

    build() {
        this.createApp();
    }
}

module.exports = View;