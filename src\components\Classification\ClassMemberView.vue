<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import AddMembersDialog from './AddMembersDialog.vue';
import { computed, ref, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder, ElMessage } from 'element-plus';
import { findNearestValue, formatDateTime, max, remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';
import type { ColumnDefinition, RowAction } from '@/types';

import {
  Repos,
  TradeClassificationType,
  type TradeClassificationMember,
} from '../../../../xtrade-sdk/dist';

interface CellRenderParam {
  rowData: TradeClassificationMember;
  cellData: any;
}

const { classType, classId, className, isMemberEditAllowed } = defineProps<{
  classType: TradeClassificationType;
  classId: number | null;
  className: string | null;
  isMemberEditAllowed: boolean;
}>();

const repoInstance = new Repos.ClassificationRepo();
const records = ref<TradeClassificationMember[]>([]);
const tableRef = useTemplateRef('tableRef');

// 对话框相关
const dialogVisible = ref(false);
const assignedOrder = ref(0);

watch(
  [() => classType, () => classId],
  () => {
    request();
  },
  { immediate: true },
);

// 基础列定义
const columns = computed<ColumnDefinition<TradeClassificationMember>>(() => {
  return [
    { key: 'memberName', title: '合约名称', width: 200, minWidth: 200 },
    { key: 'memberCode', title: '合约代码', width: 150, minWidth: 150 },
    { key: 'createTime' as any, title: '绑定时间', width: 100, cellRenderer: formatDate },
  ];
});

// 行操作
const rowActions: RowAction<TradeClassificationMember>[] = [
  {
    label: '上移',
    icon: 'arrow-up',
    show: isMovingAllowed,
    onClick: row => {
      moveUp(row);
    },
  },
  {
    label: '下移',
    icon: 'arrow-down',
    show: isMovingAllowed,
    onClick: row => {
      moveDown(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    show: isEditAllowed,
    onClick: row => {
      deleteRow(row);
    },
  },
];

function isMovingAllowed() {
  return isMemberEditAllowed && records.value.length > 1;
}

function isEditAllowed() {
  return isMemberEditAllowed;
}

function formatDate(params: CellRenderParam) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

// 新建分类成员
const handleCreate = () => {
  const current_max = records.value.length == 0 ? 0 : max(records.value.map(x => x.sortOrder));
  assignedOrder.value = current_max + 1;
  dialogVisible.value = true;
};

// 上移
function moveUp(row: TradeClassificationMember) {
  moveRow(row, 1);
}

// 下移
function moveDown(row: TradeClassificationMember) {
  moveRow(row, -1);
}

async function moveRow(row: TradeClassificationMember, direciton: number) {
  const beyonds =
    direciton > 0
      ? records.value.filter(item => item.sortOrder > row.sortOrder)
      : records.value.filter(item => item.sortOrder < row.sortOrder);

  if (beyonds.length == 0) {
    return ElMessage.info('当前已是最大排序');
  }

  const nearest = findNearestValue(beyonds, row.sortOrder, x => x.sortOrder)!;
  const t = nearest.sortOrder;
  nearest.sortOrder = row.sortOrder;
  row.sortOrder = t;

  const resp = await repoInstance.updateTradeClassificationMember(classId!, row);
  const resp2 = await repoInstance.updateTradeClassificationMember(classId!, nearest);
  const { errorCode, errorMsg } = resp;
  const { errorCode: errorCode2, errorMsg: errorMsg2 } = resp2;

  if (errorCode == 0 && errorCode2 == 0) {
    ElMessage.success('移动成功');
  } else {
    ElMessage.error(`移动失败：${errorCode || errorCode2}/${errorMsg || errorMsg2}`);
  }
}

// 删除分类成员
async function deleteRow(row: TradeClassificationMember) {
  const result = await deleteConfirm('删除确认', `是否删除分类成员： ${row.memberName}？`);
  if (result !== true) {
    return;
  }

  const resp = await repoInstance.deleteTradeClassificationMember(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已删除');
    remove(records.value, x => x.id == row.id);
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  if (!classId || classType != TradeClassificationType.Asset) {
    records.value = [];
    return;
  }

  const list = (await repoInstance.getTradeClassificationMembers(classId)).data || [];
  records.value = list;
}
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'sortOrder', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="200"
    select
    fixed
    show-index
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>挂载</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 分类成员编辑对话框 -->
  <AddMembersDialog
    v-model="dialogVisible"
    :class-id="classId"
    :class-name="className"
    :assigned-order="assignedOrder"
    @success="handleDialogSuccess"
  />
</template>

<style scoped></style>
