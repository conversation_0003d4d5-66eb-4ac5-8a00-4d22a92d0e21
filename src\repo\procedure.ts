import { BaseRepo } from '../modules/base-repo';

export class ProcedureRepo extends BaseRepo {
    
    /**
     * 查询下单指令
     * @param status - 审核状态，1未审核 2已审核
     */
    async QueryOrderActions(status: number) {
        throw new Error('not implemented');
    }

    /**
     * 处理下单指令
     * @param status - 审核意见，1通过 0驳回
     */
    async HandleOrderAction(status: number) {
        throw new Error('not implemented');
    }

    /**
     * 订阅下单指令
     */
    async SubscribeOrderActionChange() {
        throw new Error('not implemented');
    }
}