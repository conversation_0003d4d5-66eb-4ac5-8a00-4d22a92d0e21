const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;
const electron = require('electron');
const BrowserWindow = electron.BrowserWindow;
/**
 * 订阅持仓的数据视图（一个窗口可对应多个视图）组合
 */
class ViewItem {

	constructor(windowId, viewId) {

		this.windowId = windowId;
		this.viewId = viewId;
	}
}

class TradbaleListModule extends ServerEnvMainModule {

	constructor(module_name) {
		super(module_name);

		this.bigmap = {};
	}

	makeMapKey(winId, viewId) {
		return `${winId}.${viewId}`;
	}

	register(winId, viewId) {

		let multi_key = this.makeMapKey(winId, viewId);
		this.bigmap[multi_key] = new ViewItem(winId, viewId);
	}

	unregister(winId, viewId) {
		let multi_key = this.makeMapKey(winId, viewId);
		delete this.bigmap[multi_key];
	}

	subscribe(winId, viewId) {

		// 记录阅者
		this.register(winId, viewId);
		var message = { fc: this.serverFunction.subscribeTradableList, reqId: 0, dataType: 1, body: ['inday_pool_change'] };
		this.loggerTrading.debug(`to subscribe tradable list: ${JSON.stringify(message)}`);
		this.quoteServer.send(message);
	}

	broadcastSubscribeOwner(channel, ...params) {
		Object.entries(this.bigmap).forEach(([key, value]) => {
			let subscriberWin = BrowserWindow.fromId(value.windowId);
			if (subscriberWin && !subscriberWin.isDestroyed()) {
				subscriberWin.webContents.send(this.systemIndayEvent[channel], ...params);
			}
		});
	}

	listen2Commands() {

		this.mainProcess.on(this.systemEvent.sysLoadingCompleted, (event) => {

			if(!this.userInfo.quoteServerRequired) {
				return;
			}
			
			// 向服务器发送指令
			this.mainProcess.on(this.systemEvent.tellReceiveTradableListReady, (event, winId, viewId) => {
				this.subscribe(winId, viewId);
			});

			//告诉client，你需要从新刷一遍
			this.quoteServer.listen2Event(this.serverEvent.tradableStockChanged, (notify) => {

				let data = JSON.parse(notify.body);
				if (data.eventName == 'inday_pool_change') {
					this.loggerTrading.debug(`force to refresh tradable list`);
					this.broadcastSubscribeOwner('refreshTradableList');
				} 
				else {
					let { eventData: changeData } = data;
					try {
						changeData = JSON.parse(changeData);
					}
					catch (e) {
						changeData = null;
					}
					// this.loggerTrading.debug(`to record client should reload tradable list: ${JSON.stringify(changeData)}`);
					this.broadcastSubscribeOwner('tradableListChangeReceived', changeData);
				}
			});
		});
	}

	run() {
		this.listen2Commands();
	}
}

module.exports = { TradbaleListModule };
