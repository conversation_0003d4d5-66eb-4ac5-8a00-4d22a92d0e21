﻿const { ServerEnvMainModule } = require('./main-module');
const systemEvent = require('../config/system-event').systemEvent;

class TradeServerModule extends ServerEnvMainModule {

    constructor(module_name) {
        super(module_name);
    }

    listen2AccountEquityPush() {

        this.tradingServer.listen2Event(this.serverEvent.accountEquityPush, (message_package) => {

            let equities = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.accountEquityPush.toString(), equities);
        });
    }

    listen2AccountPositionPush() {

        this.tradingServer.listen2Event(this.serverEvent.accountPositionPush, (message_package) => {

            let positions = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.accountPositionPush.toString(), positions, message_package.reqId);
        });
    }

    listen2MotherOrderPush() {

        this.tradingServer.listen2Event(this.serverEvent.motherOrderResult, (message_package) => {

            let mother_orders = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.motherOrderResult.toString(), mother_orders);
        });

        this.tradingServer.listen2Event(this.serverEvent.motherOrderPush, (message_package) => {

            let mother_orders = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.motherOrderPush.toString(), mother_orders);
        });
    }

    listen2ClosePositionReply() {

        this.tradingServer.listen2Event(this.serverEvent.closePositionReply, (message_package) => {

            let reply = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.closePositionReply.toString(), reply);
        });
    }

    listen2FileScanAlgoOrderReply() {

        this.tradingServer.listen2Event(this.serverEvent.algoOrderReceived, (message_package) => {

            let reply = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.algoOrderReceived.toString(), reply);
        });
    }

    listen2RiskAlert() {

        this.tradingServer.listen2Event(this.serverEvent.riskAlertReceived, (message_package) => {

            let message = JSON.parse(message_package.body);
            let targetWin = this.centralWindow;
            targetWin && targetWin.webContents.send(this.serverEvent.riskAlertReceived.toString(), message);
        });
    }

    run() {

        this.mainProcess.on(this.systemEvent.sysLoadingCompleted, (event) => {
			
			this.listen2AccountEquityPush();
            this.listen2AccountPositionPush();
            this.listen2MotherOrderPush();
            this.listen2ClosePositionReply();
            this.listen2FileScanAlgoOrderReply();
            this.listen2RiskAlert();
		});
    }
}

module.exports = { TradeServerModule };
