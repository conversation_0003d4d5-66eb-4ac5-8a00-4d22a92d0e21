<template>
	<div id="root-bg-pic" class="s-full-size"></div>
    <div id="root-bg" class="s-full-size"></div>
    <div id="body-main" class="s-full-size">
        <div class="win-drag s-dragable"></div>
        <div class="bulletin s-color-white s-center">
            <i class="el-icon-loading s-fs-20"></i>
            <template>
                <span class="desc-text s-fs-16">{{stepDescription}}</span>
            </template>
        </div>
        <div class="bottom-bar">
            <div class="step-wrapper">
                <el-steps v-bind:active="stepIndex" process-status="wait" finish-status="success" simple>
                    <el-step v-for="(step, step_idx) in steps" v-bind:key="step_idx" v-bind:title="step.title"></el-step>
                </el-steps>
            </div>
        </div>
    </div>
</template>