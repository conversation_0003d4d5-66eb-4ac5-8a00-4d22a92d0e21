
/**
 * local settings
 */

const path = require('path');
const fs = require('fs');
const isDev = process.resourcesPath.indexOf('node_modules') > 0;
//如果是mac平台下的release版本的话，就不要放到程序的相对目录了，万能目录
const isDarwin = process.platform.toLowerCase() == 'darwin';
const darwinCwdPath = "/private/var/tmp/gaoyu-pb";
const appDir = isDarwin && !isDev ? darwinCwdPath : process.cwd();
const localAppDataPath = path.join(appDir, 'app-data');
const localUserProfilePath = path.join(appDir, 'user-data');

/**
 * 同步方式，根据路径创建完整文件夹
 * @param {*} dir_path 完整文件夹路径
 */
function MakeDirSync(dir_path) {

    if (fs.existsSync(dir_path)) {
        return;
    }

    var levels = [];

    while (!fs.existsSync(dir_path)) {

        levels.push(dir_path);
        dir_path = path.dirname(dir_path);
    }

    while (levels.length > 0) {
        fs.mkdirSync(levels.pop());
    }
}

const LocalSetting = {

    /** 软件正常运行，依赖的数据文件目录 */
    get appDataPath() {

        if (!fs.existsSync(localAppDataPath)) {
            MakeDirSync(localAppDataPath);
        }
        
        return localAppDataPath;
    },

    /** 软件正常运行，用户本地数据文件目录 */
    get userDataPath() {
        
        if (!fs.existsSync(localUserProfilePath)) {
            MakeDirSync(localUserProfilePath);
        }

        return localUserProfilePath;
    },
    
    getMarketStockPath() {
        return path.join(localAppDataPath, 'market-stock-list.json');
    },

    getMarketBondPath() {
        return path.join(localAppDataPath, 'market-bond-list.json');
    },

    getMarketFuturePath() {
        return path.join(localAppDataPath, 'market-future-list.json');
    },

    getMarketOptionPath() {
        return path.join(localAppDataPath, 'market-option-list.json');
    },
};

module.exports = { LocalSetting };