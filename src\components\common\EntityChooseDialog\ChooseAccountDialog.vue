<script setup lang="ts">
import ChooseAccountView from './ChooseAccountView.vue';
import { reactive, useTemplateRef, watch } from 'vue';
import type { AccountInfo } from '@/types';

const props = defineProps<{
  modelValue: boolean;
  title?: string;
  // 至少选择多少个
  least?: number;
  selectedIds: string[];
}>();

const dialogState = reactive({
  isVisible: false,
  targets: [] as string[],
});

watch(
  () => props.modelValue,
  newVal => {
    dialogState.isVisible = newVal;
    if (newVal) {
      dialogState.targets = [...props.selectedIds];
    }
  },
);

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [selectedItems: AccountInfo[]];
}>();

const hideDialog = () => {
  emit('update:modelValue', false);
  dialogState.targets = [];
};

const $tableRef = useTemplateRef('$tableRef');

const confirmChoose = async () => {
  const selections = $tableRef.value?.getSelectedRows() || [];
  emit('confirm', selections);
  hideDialog();
};
</script>

<template>
  <el-dialog
    :model-value="dialogState.isVisible"
    :title="title || '请选择账号'"
    width="900px"
    @close="hideDialog"
    draggable
  >
    <div h-500 of-y-hidden>
      <ChooseAccountView
        ref="$tableRef"
        :least="least"
        :ison="dialogState.isVisible"
        :selected-ids="dialogState.targets"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hideDialog">取消</el-button>
        <el-button type="primary" @click="confirmChoose">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}
</style>
