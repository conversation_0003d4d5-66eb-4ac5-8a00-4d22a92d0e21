<script setup lang="ts">
import { ref, computed, useTemplateRef } from 'vue';
import { type MomUser, ActionLogTypeEnum } from '../../../../xtrade-sdk/dist';
import UserBasicInfo from './UserBasicInfo.vue';
import UserProductPermission from './UserProductPermission.vue';
import UserTradePermission from './UserTradePermission.vue';
import UserMachineBinding from './UserMachineBinding.vue';
import UserActionLog from './UserActionLog.vue';
import { AdminService } from '@/api';
import { ElMessage } from 'element-plus';
import { hasPermission } from '@/script';
import { MenuPermitUserManagement } from '@/enum';

const { user } = defineProps<{
  user?: MomUser;
}>();

const visible = defineModel<boolean>({ default: false });

const emit = defineEmits<{
  success: [];
}>();

// 当前激活的标签页
const activeTab = ref('basic');

const basicInfoRef = useTemplateRef('basicInfoRef');
const machineBindingRef = useTemplateRef('machineBindingRef');

// 是否编辑模式
const isEdit = computed(() => !!user);

// 对话框标题
const dialogTitle = computed(() => (isEdit.value ? '编辑用户' : '新建用户'));

// 标签页配置
const basicTabs = [
  { name: 'basic', label: '基本信息', icon: 'court' },
  hasPermission(MenuPermitUserManagement.机器绑定) && {
    name: 'machine',
    label: '机器绑定',
    icon: 'unlink',
  },
].filter(Boolean) as { name: string; label: string; icon: string }[];

const logTabs = [
  hasPermission(MenuPermitUserManagement.产品权限) && {
    name: 'product',
    label: '产品权限',
    icon: 'password',
  },
  hasPermission(MenuPermitUserManagement.交易权限) && {
    name: 'trade',
    label: '交易权限',
    icon: 'puzzle',
  },
  hasPermission(MenuPermitUserManagement.操作日志) && {
    name: 'operation',
    label: '操作日志',
    icon: 'text',
  },
  hasPermission(MenuPermitUserManagement.登录日志) && {
    name: 'system',
    label: '登录日志',
    icon: 'tag',
  },
].filter(Boolean) as { name: string; label: string; icon: string }[];

// 根据是否编辑模式显示不同的标签页
const tabs = computed(() => (isEdit.value ? [...basicTabs, ...logTabs] : basicTabs));

const hasFooter = computed(() => activeTab.value === 'basic' || activeTab.value === 'machine');

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  activeTab.value = 'basic';
};

const handleSave = async () => {
  const valid = await basicInfoRef.value?.validate();

  if (valid) {
    // 验证机器绑定
    const machineValid = await machineBindingRef.value?.validate();
    if (!machineValid) {
      ElMessage.warning('请检查机器绑定地址是否正确');
      return;
    }

    if (isEdit.value && user) {
      // 编辑用户
      const {
        // password,
        ...updateData
      } = {
        ...user,
        ...basicInfoRef.value?.form,
        ...machineBindingRef.value?.getForm(),
      };
      const { errorCode, errorMsg } = await AdminService.updateUser(updateData);
      if (errorCode === 0) {
        emit('success');
        ElMessage.success('修改成功');
        handleClose();
      } else {
        ElMessage.error(errorMsg || '操作失败');
      }
    } else {
      // 创建用户
      const { errorCode, errorMsg } = await AdminService.createUser({
        ...basicInfoRef.value?.form,
        ...machineBindingRef.value?.getForm(),
        password: '123456',
        // password: aesEncrypt(form.value.password!),
      });
      if (errorCode === 0) {
        emit('success');
        ElMessage.success('添加成功');
        handleClose();
      } else {
        ElMessage.error(errorMsg || '操作失败');
      }
    }
  } else {
    if (activeTab.value !== 'basic') {
      ElMessage.warning('请先完成基本信息的填写');
    }
  }
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="920px"
    @close="handleClose"
    draggable
    destroy-on-close
  >
    <el-tabs class="typical-tabs" v-model="activeTab">
      <el-tab-pane v-for="tab in tabs" :key="tab.name" :name="tab.name" :label="tab.label">
        <template #label>
          <div flex="~ items-center gap-2">
            <i class="iconfont" :class="`icon-${tab.icon}`"></i>
            <span>{{ tab.label }}</span>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>

    <UserBasicInfo ref="basicInfoRef" v-show="activeTab === 'basic'" :user="user" />

    <UserProductPermission v-show="activeTab === 'product'" :user="user" />

    <UserTradePermission v-show="activeTab === 'trade'" :user="user" />

    <UserMachineBinding ref="machineBindingRef" v-show="activeTab === 'machine'" :user="user" />

    <UserActionLog
      v-show="activeTab === 'operation'"
      :user="user"
      :visible="visible"
      :log-type="ActionLogTypeEnum.操作日志"
    />

    <UserActionLog
      v-show="activeTab === 'system'"
      :user="user"
      :visible="visible"
      :log-type="ActionLogTypeEnum.登录日志"
    />

    <template v-if="hasFooter" #footer>
      <div class="dialog-footer">
        <el-button w-200 @click="handleClose">取消</el-button>
        <el-button w-200 type="primary" @click="handleSave">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped></style>
