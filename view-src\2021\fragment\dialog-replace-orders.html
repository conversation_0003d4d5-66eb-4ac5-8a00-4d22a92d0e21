<div class="dialog-replace-orders">

	<el-dialog width="960px"
			   :title="states.title"
			   :visible="states.visible"
			   :close-on-click-modal="false"
			   :show-close="false">

		<div class="themed-box s-pdt-5 s-pdb-5">

			<label class="themed-color s-pdr-5">跟盘价</label>

			<el-select placeholder="请选择跟盘价"
						v-model="states.stage"
						@change="handleStageChange"
						class="s-w-120 s-pdr-5">

				<el-option v-for="(item, item_idx) in stages"
							:key="item_idx"
							:label="item.mean"
							:value="item.code"></el-option>
			</el-select>

			<label class="themed-color s-pdr-5">偏移量</label>

			<el-input-number placeholder="偏移"
							 class="s-w-150"
							 v-model="states.offset"
							 :min="-*********" 
							 :max="*********"
							 :step="0.01"
							 @change="handleOffsetChange"></el-input-number>

			<div class="s-pull-right">
				<el-input placeholder="输入关键字搜索"
						  class="s-w-120"
						  prefix-icon="el-icon-search"
						  v-model="states.keywords"
						  @change="filterRecords" clearable></el-input>
			</div>

		</div>

		<div class="data-list">

			<table>
				<tr>
					<th label="账号名称" 
						min-width="202.0" 
						prop="accountName" 
						formatter="formatAccountName" filterable overflowt sortable searchable></th>

					<th label="产品"
						min-width="155"
						prop="fundName" overflowt filterable sortable searchable></th>

					<th label="代码" 
						fixed-width="100" 
						prop="instrument" filterable overflowt sortable searchable></th>

					<th label="名称" 
						fixed-width="80" 
						prop="instrumentName" filterable overflowt sortable searchable></th>

					<th label="方向" 
						fixed-width="70" 
						prop="direction"
						formatter="formatDirection" 
						export-formatter="formatDirectionText" 
						filter-data-provider="rebindDirection" sortable></th>

					<th label="委托量" 
						fixed-width="80" 
						prop="leftVolume" 
						align="right" filterable sortable thousands-int></th>

					<th label="原委托价" 
						fixed-width="60" 
						prop="orderPrice" 
						align="right" 
						formatter="formatPrice"></th>

					<th label="跟盘价"
						fixed-width="80"
						prop="stageName"></th>

					<!-- <th label="委托金额"
						fixed-width="80"
						prop="entrustAmount"
						align="right" filterable sortable thousands></th> -->

				</tr>
			</table>

		</div>

		<div class="user-footer themed-box">
			<el-pagination class="s-pull-right"
							:page-sizes="paging.pageSizes"
							:page-size.sync="paging.pageSize"
							:total="paging.total"
							:current-page.sync="paging.page"
							:layout="paging.layout"
							@size-change="handlePageSizeChange"
							@current-change="handlePageChange"></el-pagination>
		</div>

		<span slot="footer" class="dialog-footer">
			<el-button type="primary" @click="confirm">确定</el-button>
			<el-button type="default" @click="cancel">取消</el-button>
		</span>

	</el-dialog>

</div>