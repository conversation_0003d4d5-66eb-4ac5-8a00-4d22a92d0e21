const httpRequest = require('../libs/http').http;

class TerminalRepository {

    getAll() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/terminal').then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    createTerminal(terminal) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/terminal', terminal).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    updateTerminal(terminal) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/terminal', terminal).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    deleteTerminal(terminal_id) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/terminal', {params:{terminal_id: terminal_id}}).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }
}

module.exports = { repoTerminal: new TerminalRepository() };