﻿/*
    the deepest layer to Interaction with remote server
*/

const net = require("net");
const MessageEncoder = require('./message-encoder').MessageEncoder;
const ServerEventEmitter = require('../component/server-event-emitter').ServerEventEmitter;
const systemEvent = require('../../config/system-event').systemEvent;
const serverEvent = require('../../config/server-event').serverEvent;
const serverFunction = require('../../config/server-function-code').serverFunction;
const helper = require('../../libs/helper').helper;
const logging = require('../../libs/logging');
const loggerConsole = logging.getConsoleLogger();
const loggerSys = logging.getSystemLogger();

class Server {

    /**
     * 
     * @param {String} server_role 
     * @param {Object} server_config 
     * @param {ServerEventEmitter} server_event_emitter 
     * @param {Function} connection_change_callback 
     */
    constructor(server_role, server_config, server_event_emitter, connection_change_callback) {

        this.loggerConsole = loggerConsole;
        this.loggerSys = loggerSys;

        if (!server_config || !server_config.ip || !server_config.port) {

            let msg = '服务器IP/Port或未设置，不能连接。';
            this.loggerConsole.fatal(msg);
            this.loggerSys.fatal(msg);
            throw new Error(msg);
        }
        else if(!(server_event_emitter instanceof ServerEventEmitter)) {

            let msg = '服务器事件处理单元未指定，不能连接。';
            this.loggerConsole.fatal(msg);
            this.loggerSys.fatal(msg);
            throw new Error(msg);
        }
        else if(typeof connection_change_callback != 'function') {
            throw new Error('network connection change callback must be a function');
        }

        this.systemEvent = systemEvent;
        this.serverEvent = serverEvent;
        this.serverFunction = serverFunction;
        this.helper = helper;

        this.connChgCbk = connection_change_callback;
        this.serverConfig = { ip: server_config.ip, port: server_config.port };
        this.serverRole = server_role;
        this._setServerName();
        this.eventEmitter = server_event_emitter;

        // the most important flag indicating if the current connection is ok.
        this.isConnected = false;
        // this flag indicating if user tries to logout manually
        this.isLogedOut = false;
        // the real network connection instance that everything relies on
        this.connectionObj = null;
        // data package encoder/decoder
        this._messageResolver = new MessageEncoder();
        // server triggered event (code > local event name) map
        this._serverEventCode2NameMap = this._createServerEventMap();
        // tasks to be executed when connection closed
        this.closedCallbacks = [];
    }
    
    _createServerEventMap() {
        
        var map = {};
        for(let event_name in this.serverEvent) {
            let ev_code = this.serverEvent[event_name];
            map[ev_code] = event_name;
        }
        return map;
    }

    disconnect(motivation) {

        try {

            if(this.isConnected) {

                // flag, indicating if the network disconnection is caused by app initiatively
                this.isByAppInitiativeDisconnect = motivation === this.systemEvent.appInitiativeDisconnect;
                
                var msg1 = `server-agent > to disconnect from ${this.displayName} > begin, motivation: ${motivation}`;
                this.loggerConsole.info(msg1);
                this.loggerSys.info(msg1);

                // close the connection asynchronously
                this.connectionObj.end();

                var msg2 = `server-agent > to disconnect from ${this.displayName} > end`;
                this.loggerConsole.info(msg2);
                this.loggerSys.info(msg2);
            }
        }
        catch(ex) {
            
            var msg = `server-agent > disconnect from server ${this.displayName} failed with exception`;
            this.loggerConsole.error(msg);
            this.loggerSys.error(msg);
        }
    }

    _setServerName() {
        this.displayName = `${this.serverRole}/${this.serverConfig.ip}:${this.serverConfig.port}`;
    }

    _updateServerConfig(another_server) {
        
        this.loggerSys.debug(`server-agent > server changed from ${JSON.stringify(this.serverConfig)} to ${JSON.stringify(another_server)}`);

        this.serverConfig.ip = another_server.ip;
        this.serverConfig.port = another_server.port;
        this._setServerName();
    }

    _registerInternalDisconnectCallback(task) {
        this.closedCallbacks.push(task);
    }

    _disconnectCurrentAndConnect2Another(another_server) {

        var reconnect_2_new = ()=> {

            this._updateServerConfig(another_server);
            this.connect();
        };

        if(this.isConnected) {
            
            this.loggerSys.info('server-agent > current server still connected, need to disconnect and connect to new');
            this._registerInternalDisconnectCallback(reconnect_2_new);
            this.disconnect('change-server');
        }
        else {

            this.loggerSys.info('server-agent > current server is disconnected, update to new and connect');
            reconnect_2_new();
        }
    }

    /**
     * 重置数据包暂存队列
     */
    _resetMessageSlice() {
        this._messageResolver.messageSlice = null;
    }

    /*
        _initialize socket connection and bind event handlers
    */

    connect(another_server) {

        // reset, no matter what happened before
        this.isLogedOut = false;

        if(another_server) {
            
            this.loggerSys.info('server-agent > will connect to another server');
            this._disconnectCurrentAndConnect2Another(another_server);
            return;
        }

        if (this.isConnected) {
            this.loggerSys.info(`server-agent > server ${this.displayName} is now already connected, do nothing but return`);
            return;
        }

        this.loggerSys.info(`server-agent > create connection to server > begin > ${this.displayName}`);
        this.connectionObj = net.createConnection({ host: this.serverConfig.ip, port: this.serverConfig.port }, () => {
            
            this.connectionObj.setKeepAlive(true);
            this.connectionObj.setNoDelay(true);

            let msg = `server-agent > successfully connected to server ${this.displayName}`;
            this.loggerConsole.debug(msg);
            this.loggerSys.debug(msg);
            this.isConnected = true;
            this.connChgCbk(true);
            this.eventEmitter.emit(this.systemEvent.connEstablished);
        });

        this.loggerSys.info(`server-agent > create connection to server > end > ${this.displayName}`);

        /*
            the following event listeners listen very basic original socket events
        */

        this.connectionObj.on('data', (data) => { this._digestMessage(data); });
        this.connectionObj.on('timeout', () => {

            this.isConnected = false;
            this.connChgCbk(false);
            this._resetMessageSlice();
            this._stopBeatHeart();

            let msg = `server-agent > server timed out: ${this.displayName}`;
            this.loggerConsole.error(msg);
            this.loggerSys.error(msg);

            // disconnected by app initiatively, prevent from triggering event
            if(this.isByAppInitiativeDisconnect) {
                return;
            }
            
            this.eventEmitter.emit(this.systemEvent.connTimedOut);
        });

        this.connectionObj.on('error', (err) => {

            this.isConnected = false;
            this.connChgCbk(false);
            this._resetMessageSlice();
            this._stopBeatHeart();

            let msg = `server-agent > server error: ${this.displayName} > ${JSON.stringify(err)}`;
            this.loggerConsole.error(msg);
            this.loggerSys.error(msg);

            // disconnected by app initiatively, prevent from triggering event
            if(this.isByAppInitiativeDisconnect) {
                return;
            }

            this.eventEmitter.emit(this.systemEvent.connError);
        });

        this.connectionObj.on('close', () => {

            this.isConnected = false;
            this.connChgCbk(false);
            this._resetMessageSlice();
            this._stopBeatHeart();
            
            let msg = `server-agent > disconnected from server ${this.displayName}`;
            this.loggerConsole.debug(msg);
            this.loggerSys.debug(msg);

            // disconnected by app initiatively, prevent from triggering event
            if(this.isByAppInitiativeDisconnect) {
                // restore the flag
                this.isByAppInitiativeDisconnect = false;
                return;
            }
            
            this.eventEmitter.emit(this.systemEvent.connClosed);
            
            // set a delay to startup tasks, to maximumly make sure all listeners are procasted already
            setTimeout(()=>{

                while(this.closedCallbacks.length > 0) {
                    try {
                        this.closedCallbacks.pop()();
                    }
                    catch(ex) {
                        this.loggerSys.fatal(`server-agent > fatal error happens while executing a closed-callback: ${ex.message}`);
                    }
                }
            }, 200);
        });
    }

    logout() {

        if(!this.isConnected) {

            let msg = `server-agent > try 2 disconnect from a <disconnected> server ${this.displayName}, do nothing`;
            this.loggerConsole.info(msg);
            this.loggerSys.info(msg);
            return;
        }

        this.isLogedOut = true;
        var msg = `server-agent > send a disconnect request 2 server ${this.displayName}`;
        this.loggerConsole.info(msg);
        this.loggerSys.info(msg);
        this.send({ fc: this.serverFunction.userLogout, reqId: 0, dataType: 1 });

        var msg2 = 'server-agent > to close connection from client, to make double confirm';
        // to double confirm disconnection happens
        this.loggerConsole.info(msg2);
        this.loggerSys.info(msg2);
        this.disconnect('logout-disconnect');
    }

    /*
        send a message to server via the connection
    */

    send(message) {

        if (message.body === null || message.body === undefined) {
            message.body = '';
        }

        if (message.body instanceof Array || this.helper.isJson(message.body)) {
            message.body = JSON.stringify(message.body);
        }

        this.connectionObj.write(this._messageResolver.encode(message));
    }

    /**
     * start an everlasting heart beating action
     */
    startHeartBeating() {

        if (this._heartBeatJob === undefined) {
            this._heartBeatJob = setInterval(() => { this.send({ fc: this.serverFunction.heartBeat, reqId: 0, dataType: 0, body: '' }); }, 5000);
        }
    }

    /**
     * stop to beat heart
     */
    _stopBeatHeart() {

        if (this._heartBeatJob) {
            
            this.loggerSys.info(`server-agent > to stop heart beat to server ${this.displayName}`);
            clearInterval(this._heartBeatJob);
            delete this._heartBeatJob;
        }
    }

    /**
     * start an everlasting ping action
     * @param {Function} message_handler pong message handler
     */
    start2Ping(message_handler) {

        this.unlisten2Event(this.serverEvent.pong);
        this.listen2Event(this.serverEvent.pong, event => { 
            
            message_handler({ start: this._lastPingTs, end: Date.now() });
            this._lastPingTs = undefined;
        });

        this.stop2Ping();

        this._pingJob = setInterval(() => {

            /**
             * 未连接时，不可发送
             */
            if (!this.isConnected) {
                return;
            }

            /**
             * 上一个Ping请求，已发出，但尚未获得回应，放弃请求，继续等待
             */
            if (!!this._lastPingTs) {
                return;
            }

            this._lastPingTs = Date.now();
            this.send({ fc: this.serverFunction.ping, reqId: 0, dataType: 0, body: '' });

        }, 1000 * 30);
    }

    /**
     * stop to send send ping command
     */
    stop2Ping() {

        this.loggerSys.info(`server-agent > to stop to send ping command to server ${this.displayName}`);
        clearInterval(this._pingJob);
        this._pingJob = undefined;
    }

    // 注册服务器时间处理程序
    listen2Event(event_name, handler) {
        this.eventEmitter.register(event_name, handler);
    }

    // 取消注册过的服务器事件处理程序
    unlisten2Event(event_name, handler) {
        this.eventEmitter.unregister(event_name, handler);
    }

    /*
        handle a message
    */

    _digestMessage(data_package) {
        
        let data_length = data_package.length || 0;
        let messages = this._messageResolver.decode(data_package);

        for (var idx = 0; idx < messages.length; idx++) {

            let message = messages[idx];
            if (message.fc === undefined) {
                message.fc = 0;
            }

            let event_code = message.fc.toString();
            let event_name = this._serverEventCode2NameMap[event_code];
            let event_msg = `server data arrives ${event_code}/${event_name}/${data_length}`;
            this.loggerConsole.debug(event_msg);
            this.loggerSys.debug(event_msg);
            
            // all data messages emited via this interface
            this.eventEmitter.emit(event_code, message);
        }
    }
};

module.exports = { Server };