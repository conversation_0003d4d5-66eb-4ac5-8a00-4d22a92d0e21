import { SocketCommunication } from './socket';
import { SocketDataPackage } from '../types/data-package';
import { GetLogger } from '../global-state';
import { WsMessageResolver } from './web-message-resolver';
import { ServerFunction } from '../config/server-function';

const defaultLogger = GetLogger();

/**
 * WebSocket 通信类
 */
export class WebSocketCommunication extends SocketCommunication {

    private ws: WebSocket | null = null;
    private resolver: WsMessageResolver;

    constructor(server_type: string, host: string, port: number) {
        
        super(server_type, host, port);
        this.resolver = new WsMessageResolver();
    }

    get server() {
        return `ws://${this.host}:${this.port}`;
    }

    private get isConnected() {
        return this.ws && this.ws.readyState == WebSocket.OPEN;
    }

    private handleConnectedWs(evt: Event) {

        defaultLogger.info('Web Socket server connected', this.serverType, this.server);
        super.handleConnected();
    }

    private handleClosedWs(evt: CloseEvent) {

        const { code, reason } = evt;

        if (code == 1000 || code == 0) {
            defaultLogger.info('Web Socket server closed', code, reason, this.serverType, this.server);
        }
        else {
            defaultLogger.error('Web Socket server closed with error', code, reason, this.serverType, this.server);
        }

        super.handleClosed();
    }

    private handleErroredWs(evt: Event) {

        defaultLogger.error('Web Socket server error', this.serverType, this.server);
        super.handleErrored({} as any);
    }

    private async handleMessageReceivedWs(evt: MessageEvent<Blob>) {
        
        const data = await evt.data.arrayBuffer();
        const messages = this.resolver.decode(data);
        messages.forEach(msg => { 
            defaultLogger.debug('Web socket message received', msg);
            super.handleMessageReceived(msg); 
        });
    }

    connect(): void {

        if (this.isConnected) {
            return;
        }
 
        const ws = new WebSocket(this.server);
        ws.addEventListener('open', this.handleConnectedWs.bind(this));
        ws.addEventListener('close', this.handleClosedWs.bind(this));
        ws.addEventListener('error', this.handleErroredWs.bind(this));
        ws.addEventListener('message', this.handleMessageReceivedWs.bind(this));
        this.ws = ws;
    }

    disconnect(): void {

        if (this.isConnected) {

            this.ws?.close();
            this.dispose();
        }
    }

    send(data: SocketDataPackage) {

        if (this.isConnected) {

            this.precheck(data);
            const encoded = this.resolver.encode(data);
            this.ws?.send(encoded);
            
            if (data.fc === ServerFunction.HeartBeat) {
                defaultLogger.trace('Sending heart beating signal', this.serverType, this.server);
            }
            else {
                defaultLogger.debug('Sending data out', data, this.serverType, this.server);
            }
        }
        else {
            defaultLogger.error('Unable to send message due to disconnection', data, this.serverType, this.server);
        }
    }

    dispose(): void {

        super.dispose();
        this.ws = null;
    }
}