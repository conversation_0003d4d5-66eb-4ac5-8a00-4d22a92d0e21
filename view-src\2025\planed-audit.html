<div class="planed-audit s-full-height">
    <div class="mother-orders-title">母单列表 ({{ parentOrders.length }}条)</div>
    <div class="mother-orders-section">
        <el-table 
            ref="$mother" 
            :data="parentOrders" 
            @row-click="handleSelectMotherOrder" 
            :default-sort = "{ prop: 'aopId', order: 'descending' }" 
            height="100%" 
            highlight-current-row
            >
            <el-table-column prop="aopId" label="母单Id" min-width="100" sortable></el-table-column>
            <el-table-column prop="fileName" label="文件名" min-width="150" sortable></el-table-column>
            <el-table-column prop="prodCode" label="产品代码" min-width="100" sortable></el-table-column>
            <el-table-column prop="prodName" label="产品名称" min-width="150" sortable></el-table-column>
            <el-table-column prop="tradeDate" label="交易日期" min-width="100" sortable></el-table-column>
            <el-table-column prop="auditStatus" label="审核状态" min-width="100" :sort-method="sortAuditStatus" sortable>
                <template slot-scope="scope">
                    <span v-if="scope.row.auditStatus === 'pass'">通过</span>
                    <span v-else-if="scope.row.auditStatus === 'fail'">失败</span>
                    <span v-else>{{scope.row.auditStatus}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="auditMsg" label="审核信息" min-width="200" :sort-method="sortAuditMsg" sortable show-overflow-tooltip></el-table-column>
            <el-table-column prop="orderStatus" label="母单状态" min-width="100" :sort-method="sortOrderStatus" sortable>
                <template slot-scope="scope">
                    <span v-if="scope.row.orderStatus == 0" style="color: red;">下单失败</span>
                    <span v-else-if="scope.row.orderStatus == 1" style="color: red;">下单成功</span>
                    <span v-else-if="scope.row.orderStatus == 2">下单中</span>
                    <span v-else-if="scope.row.orderStatus == 3" style="color: red;">废单</span>
                    <span v-else>{{scope.row.orderStatus}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="validateStatus" label="有效状态" min-width="100" :sort-method="sortValidateStatus" sortable>
                <template slot-scope="scope">
                    <span v-if="scope.row.validateStatus == 'Y' || scope.row.validateStatus == 'y'" style="color: green;">有效</span>
                    <span v-else-if="scope.row.validateStatus == 'N' || scope.row.validateStatus == 'n'" style="color: red;">无效</span>
                    <span v-else>{{scope.row.validateStatus}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="child-orders-title">
        <span>子单列表 ({{ childOrders.length }}条)</span>
        <span v-if="states.current" style="color: #999;">{{ formatCurrentMotherInfo() }}</span>
    </div>
    <div class="child-orders-section">
        <el-table :data="childOrders" height="100%">
            <!-- <el-table-column prop="aopId" label="母单Id" min-width="100" sortable></el-table-column> -->
            <el-table-column prop="secuCode" label="标的代码" min-width="100" sortable></el-table-column>
            <el-table-column prop="investType" label="投资种类" min-width="100" sortable></el-table-column>
            <el-table-column prop="side" label="委托方向" min-width="100" sortable></el-table-column>
            <el-table-column prop="orderNum" label="指令数量" min-width="100" align="right" :formatter="thousandsInt" sortable></el-table-column>
            <el-table-column prop="priceModel" label="价格模式" min-width="200" sortable show-overflow-tooltip></el-table-column>
            <el-table-column prop="marketType" label="交易市场" min-width="100" sortable></el-table-column>
            <el-table-column prop="investCode" label="投顾编号" min-width="100" sortable></el-table-column>
            <el-table-column prop="orderPrice" label="指令价格" min-width="100" align="right" :formatter="thousands" sortable></el-table-column>
            <el-table-column prop="orderRemark" label="指令备注" min-width="200" sortable show-overflow-tooltip></el-table-column>
        </el-table>
    </div>
</div>