<div class="app-view-root s-border-box">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">

            <div class="s-typical-toolbar">
                <el-button size="mini" type="primary" @click="showCreate">
                    <i class="el-icon-plus"></i> 创建应用
                </el-button>
                <el-button size="mini" class="s-pull-right" @click="refresh">
                    <span class="el-icon-refresh"></span> 刷新
                </el-button>
            </div>

            <data-tables 
                layout="pagination,table" 
                @row-click="setAsCurrent"
                class="indicator s-searchable-table"
                v-bind:data="apps"
                v-bind:table-props="tableProps" 
                v-bind:pagination-props="paginationDef">

                <el-table-column label="ID" width="60" prop="id"></el-table-column>
                <el-table-column label="应用名称" min-width="200" prop="appName" show-overflow-tooltip></el-table-column>
                <el-table-column label="报告名称" min-width="100" prop="title" show-overflow-tooltip></el-table-column>
                <el-table-column label="模板ID" min-width="100" prop="templateId" show-overflow-tooltip></el-table-column>
                <el-table-column label="控件名称" min-width="100" prop="componentName" show-overflow-tooltip></el-table-column>
                <el-table-column label="应用类别" min-width="100" prop="appType" :formatter="formatAppType" show-overflow-tooltip></el-table-column>

                <el-table-column label="已分享人员" min-width="200" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-row>
                            <el-col class="s-ellipsis" :span="24">
                                <el-tooltip content="分享模板" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button operation-block el-icon-edit"
                                        @click="hope2Share(scope.row)"></a>
                                </el-tooltip>
                                <span>
                                    {{ scope.row.users && scope.row.users.length > 0 ?
                                scope.row.users.map(user => user.fullName).join('、') : '暂无分享'}}
                                </span>
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="70" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-tooltip content="编辑" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-distance icon-button el-icon-edit" style="margin-right: 5px" @click="showEdit(scope.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-distance s-color-red el-icon-delete" @click="hope2Remove(scope.row)"></a>
                        </el-tooltip>
                    </template>
                </el-table-column>

            </data-tables>
        </div>

        <el-dialog 
            width="500px" 
            class="app-dialog" 
            :title="(formd.id ? '编辑' : '创建') + '应用'"
            :visible="form.visible" 
            :show-close="false"
            v-bind:close-on-click-modal="false" 
            v-bind:close-on-press-escape="false" 
            v-drag>

            <el-form class="app-form" :model="formd" ref="cform" :rules="rules" label-width="100px">
                
                <el-form-item label="应用类型" prop="appType">
                    <el-radio-group v-model="formd.appType">
                        <el-radio v-for="(item, item_idx) in appTypes" :key="item_idx" :label="item.code" @change="handleAppTypeChange">{{ item.mean }}</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="应用名称" prop="appName">
                    <el-input v-model="formd.appName" placeholder="请输入应用名称"></el-input>
                </el-form-item>

                <template v-if="isTemplateType()">

                    <el-form-item label="报告名称" prop="title">
                        <el-input v-model="formd.title" placeholder="如 “公募基金模板（持仓）”"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="模板ID" prop="templateId">
                        <el-input v-model="formd.templateId" placeholder="请输入模板ID"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="控件名称" prop="componentName">
                        <!-- <el-input v-model="formd.componentName" placeholder="如 “NewReportViewRoute”"></el-input> -->
                        <el-select placeholder="控件名称" class="s-full-width" v-model="formd.componentName" clearable>
                            <el-option v-for="(item, item_idx) in controls" :key="item_idx" :label="item" :value="item"></el-option>
					    </el-select>
                    </el-form-item>

                </template>

                <template v-else>

                    <el-form-item label="URL地址" prop="url">
                        <el-input v-model="formd.url" placeholder="请输入URL地址"></el-input>
                    </el-form-item>

                </template>
                
                <el-form-item label="应用简介" prop="describe">
                    <el-input v-model="formd.describe" resize="none" type="textarea" :rows="4"
                        placeholder="请输入应用简介"></el-input>
                </el-form-item>

            </el-form>

            <div slot="footer">
                <el-button type="primary" size="small" @click="beforeSave">确定</el-button>
                <el-button @click="closeCreate" size="small">取消</el-button>
            </div>

        </el-dialog>

        <el-dialog 
            class="app-dialog" 
            title="分享应用"
            :visible="shared.visible"
            :show-close="false"
            v-bind:close-on-click-modal="false"
            v-bind:close-on-press-escape="false" 
            @open="handleShareShown"
            v-drag>

            <el-form class="app-form" :model="shared.data" label-width="100px">
                <el-form-item class="app-form-select" label="选择用户">
                    <el-select style="width: 100%;" v-model="shared.users" placeholder="请选择需要分享的用户" filterable multiple>
                        <el-option v-for="(item, idx) in users" :key="idx" :label="item.fullName" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <div slot="footer">
                <el-button type="primary" size="small" @click="share">确定</el-button>
                <el-button size="small" @click="closeShare">取消</el-button>
            </div>

        </el-dialog>
    </template>
</div>