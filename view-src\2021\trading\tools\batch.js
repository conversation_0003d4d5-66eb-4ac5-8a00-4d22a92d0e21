const { IView } = require('../../../../component/iview');
const ChannelView = require('../../fragment/trading-channel');
const PricesView = require('../module/prices');
const LevelsView = require('../module/level1');
const CompeteTradeView = require('../batch/trade-compete');
const CreditTradeView = require('../batch/trade-credit');
const AccountGroupView = require('../../fragment/account-group');
const DataRecordsView = require('../batch/records');
const { TradeChannel, InstrumentInfo, LevelMessage, TickData } = require('../../model/message');
const { TickRepository } = require('../../../../repository/tick');

class View extends IView {

    /**
     * 适配当前交易渠道的交易视图
     */
    get focusedTradeView() {
        return this._focusedTradeView;
    }
    
    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '批量交易');

        var astype = this.systemEnum.assetsType;
        this.channels = {

            spot: new TradeChannel(1, '现货竞价', astype.stock.code, [astype.stock.code, astype.fund.code, astype.bond.code], { isSpot: true }),
            // credit: new TradeChannel(2, '融资融券', astype.stock.code, [astype.stock.code, astype.fund.code, astype.bond.code], { isCredit: true }),
            future: new TradeChannel(3, '期货', astype.future.code, [astype.future.code], { isFuture: true }),

            // option: new TradeChannel(4, '期权', astype.option.code, [astype.option.code], { isOption: true }),
            // fix: new TradeChannel(5, '盘后定价', astype.stock.code, null, { isAfterClose: true }),
            // bond: new TradeChannel(6, '债券', astype.stock.code, [astype.bond.code], { isBond: true }),
            // apply: new TradeChannel(7, '申购认购', astype.stock.code, [astype.stock.code, astype.fund.code, astype.bond.code], { isApply: true }),
            // pledge: new TradeChannel(8, '质押出入库', astype.bond.code, [astype.bond.code], { isPledge: true }),
        };

        this.defaultChannel = this.channels.spot;

        this.states = {

            /** 当前的交易频道 */
            channel: this.defaultChannel,
            /** 当前的交易合约 */
            instrument: null,
            /** 当前的交易合约名称 */
            instrumentName: null,
        };

        this.tickRepo = new TickRepository();
        this.handleTickChangeBinder = this.handleTickChange.bind(this);
    }

    createChannel() {

        var $root = this.$container.querySelector('.trading-channel');
        var view = new ChannelView('@2021/fragment/trading-channel');
        view.trigger('set-as-channels', this.helper.dict2Array(this.channels));
        view.registerEvent('channel-selected', this.handleChannelChange.bind(this));
        view.loadBuild($root);
        view.trigger('set-default-channel', this.defaultChannel);
        this.channelView = view;
    }

    /**
     * @param {TradeChannel} channel 
     */
    handleChannelChange(channel) {

        this.states.channel = channel;
        this.brocastChannel();
        this.showProperTradeView();
    }

    createPrices() {

        var $root = this.$container.querySelector('.block-prices');
        var view = new PricesView('@2021/trading/module/prices');
        view.registerEvent('price-selected', this.handlePriceSelect.bind(this));
        view.loadBuild($root);
        this.priceView = view;
    }

    /**
     * @param {Number} price 
     */
    handlePriceSelect(price) {
        this.focusedTradeView.trigger('set-as-price', price);
    }

    createLevel() {

        var $root = this.$container.querySelector('.block-levels');
        var view = new LevelsView('@2021/trading/module/level1');
        view.registerEvent('level-selected', this.handleLevelSelect.bind(this));
        view.loadBuild($root);
        this.levelView = view;
    }

    /**
     * @param {LevelMessage} message 
     */
    handleLevelSelect(message) {
        this.focusedTradeView.trigger('set-as-price-and-direction', message);
    }

    createTradeViews() {

        var $tradeRoot = this.$tradeRoot = this.$container.querySelector('.block-trade');
        this.$firstColumn = this.$container.querySelector('col.first-column');

        /**
         * 现货竞价、期货、期权类，公用视图
         */

        var competeView = new CompeteTradeView('@2021/trading/batch/trade-compete');
        var $root4Compete = document.createElement('div');
        $root4Compete.classList.add('s-full-height');
        $tradeRoot.appendChild($root4Compete);
        competeView.loadBuild($root4Compete);
        this.trade4Compete = competeView;
        this._focusedTradeView = competeView;

        /**
         * 两融交易视图
         */

        var creditView = new CreditTradeView('@2021/trading/batch/trade-credit');
        var $root4Credit = document.createElement('div');
        $root4Credit.classList.add('s-full-height');
        $tradeRoot.appendChild($root4Credit);
        creditView.loadBuild($root4Credit, null, () => { $root4Credit.style.display = 'none'; });
        this.trade4Credit = creditView;
        
        var selectedIns = 'selected-one-instrument';
        var allocate = 'allocate-volume-to-account';
        var modeChange = 'trade-mode-change';
        var setPreview = 'set-as-order-preview';
        var orderMade = 'batch-order-made';

        /**
         * 监听，从交易面板，抛出的各种典型事件
         */

        this.tradeViews = [competeView, creditView];
        this.tradeViews.forEach(thisView => {

            /** 监听合约选择事件 */
            thisView.registerEvent(selectedIns, this.handleInstrumentSelected.bind(this));
            /** 数量变化，引起的数量到账号的分摊 */
            thisView.registerEvent(allocate, (...args) => { this.accountGroupView.trigger(allocate, ...args); });
            /** 交易模式变化 */
            thisView.registerEvent(modeChange, (...args) => { this.accountGroupView.trigger(modeChange, ...args); });
            /** 价格、数量、方向等交易参数变化，引起的委托预览分配（通过外围模块传递） */
            thisView.registerEvent(setPreview, (...args) => { this.recordView.trigger(setPreview, ...args); });
            /** 下单动作发出，通知交易数据外围模块 */
            thisView.registerEvent(orderMade, _ => { this.recordView.trigger(orderMade); });
        });
    }

    /**
     * 根据交易渠道，展示适配的交易界面
     */
    showProperTradeView() {
        
        var channel = this.states.channel;
        var channels = this.channels;
        var expected;

        switch (channel) {

            case channels.spot:
            case channels.future:
            // case channels.option:

                expected = this.trade4Compete;
                this.$firstColumn.setAttribute('width', 576);
                this.$tradeRoot.style.width = '254px';
                break;

            // case channels.credit: 

            //     expected = this.trade4Credit; 
            //     this.$firstColumn.setAttribute('width', 596);
            //     this.$tradeRoot.style.width = '274px';
            //     break;
        }

        this.tradeViews.forEach(thisView => {

            if (thisView === expected) {
                if (thisView.$container) {
                    thisView.$container.parentElement.style.display = 'block';
                }
            }
            else {
                if (thisView.$container) {
                    thisView.$container.parentElement.style.display = 'none';
                }
            }
        });

        if (!expected) {
            this.trade4Compete.show();
        }

        this._focusedTradeView = expected || this.trade4Compete;
    }

    brocastChannel() {

        var channel = this.states.channel;
        var eventName = 'set-channel';
        this.accountGroupView.trigger(eventName, channel);
        this.priceView.trigger(eventName, channel);
        this.levelView.trigger(eventName, channel);
        this.tradeViews.forEach(thisView => { thisView.trigger(eventName, channel); });
        this.recordView.trigger(eventName, channel);
    }

    /**
     * @param {InstrumentInfo} insInfo 
     */
    handleInstrumentSelected(insInfo) {

        /**
         * 向各个子视图，广播合约变化，子视图根据合约（合约可能为null）设置相应的信息
         */

        var eventName = 'set-as-instrument';
        this.priceView.trigger(eventName, insInfo);
        this.levelView.trigger(eventName, insInfo);
        this.focusedTradeView.trigger(eventName, insInfo);
        this.accountGroupView.trigger(eventName, insInfo);
        this.recordView.trigger(eventName, insInfo);

        /**
         * 提取上一个合约 & 与当前合约，并写入当前合约
         */

        var lastIns = this.states.instrument;
        var currentIns = insInfo ? insInfo.instrument : null;
        var currentInsName = insInfo ? insInfo.instrumentName : null;

        this.states.instrument = currentIns;
        this.states.instrumentName = currentInsName;

        /**
         * 退订上一合约行情 & 订阅新合约行情
         */
        this.subscribeTick(lastIns, currentIns);
    }

    /**
     * 退订上一合约行情 & 订阅新合约行情
     * @param {String} lastIns 上一合约
     * @param {String} currentIns 当前合约
     */
    subscribeTick(lastIns, currentIns) {

        if (lastIns) {
            this.tickRepo.unsubscribe(lastIns, this.handleTickChangeBinder);
        }

        if (currentIns) {
            this.tickRepo.subscribe(currentIns, this.handleTickChangeBinder);
        }
    }

    /**
     * 处理TICK数据变化
     * @param {{ instrument, tickType, tick }} tickData TICK数据
     * @param {*} isReceipt 是否为订阅回执
     */
    handleTickChange(tickData, isReceipt) {

        const { instrument, tickType, tick } = tickData;
        
        if (instrument != this.states.instrument || tickType != this.systemTrdEnum.tickType.tick) {
            return;
        }

        var eventName = 'set-first-tick';
        var tickd = new TickData(tick);
        this.priceView.trigger(eventName, tickd);
        this.levelView.trigger(eventName, tickd);

        if (isReceipt) {
            this.focusedTradeView.trigger(eventName, tickd);
        }
    }

    createAccountGroup() {

        var $root = this.$container.querySelector('.block-accounts');
        var view = new AccountGroupView('@2021/fragment/account-group');
        view.loadBuild($root);
        view.registerEvent('selected-accounts-changed', (accounts) => { this.focusedTradeView.trigger('set-as-accounts', accounts); });
        view.registerEvent('account-selected', (...args) => { this.recordView.trigger('account-selected', ...args); });
        this.accountGroupView = view;
    }

    createDataRecords() {

        var $root = this.$container.querySelector('.data-records');
        var view = new DataRecordsView('@2021/trading/batch/records', false, this.defaultChannel);
        view.loadBuild($root);
        this.recordView = view;
    }

    refresh() {
        this.interaction.showMessage('该页面未提供刷新');
    }
    
    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    build($container) {

        super.build($container);
        this.createChannel();
        this.createPrices();
        this.createLevel();
        this.createTradeViews();
        this.createAccountGroup();
        this.createDataRecords();
        this.brocastChannel();
        this.showProperTradeView();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;
