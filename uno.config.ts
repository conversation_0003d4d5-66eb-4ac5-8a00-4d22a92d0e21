import { defineConfig, presetWind3, presetAttributify, presetIcons } from 'unocss';
import presetRemToPx from '@unocss/preset-rem-to-px';
export default defineConfig({
  presets: [presetIcons(), presetAttributify(), presetWind3(), presetRemToPx({ baseFontSize: 4 })],
  // ...UnoCSS options
  rules: [
    ['aic', { 'align-items': 'center' }],
    ['jcc', { 'justify-content': 'center' }],
    ['jcsb', { 'justify-content': 'space-between' }],
    ['jcs', { 'justify-content': 'start' }],
    ['jce', { 'justify-content': 'end' }],
    ['tag-active', { color: '#178BF0', 'border-color': '#178BF0' }],
    [/^fs-(\d+(?:\.\d+)?)$/, ([_, num]) => ({ 'font-size': `${num}px` })],
    ['red', { 'background-color': 'rgba(239, 51, 99, 1)' }],
    ['green', { 'background-color': '#368a66' }],
    [
      'toe',
      {
        'text-overflow': 'ellipsis',
        overflow: 'hidden',
        'white-space': 'nowrap',
        'word-wrap': 'normal',
      },
    ],
    ['form-icon', { filter: 'var(--g-form-icon-filter)' }],
  ],
  shortcuts: {
    thb1: 'text-[--g-text-color-1] hover:text-[--g-white]',
    thb2: 'text-[--g-text-color-2] hover:text-[--g-white]',
  },
});
