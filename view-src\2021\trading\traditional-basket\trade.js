const { IView } = require('../../../../component/iview');
const { CodeMeanItem, BasketOrderParam } = require('../../model/message');

class View extends IView {

    /**
     * 是否为ETF篮子
     */
    get isEtfBasket() {
        return this._isEtfBasket === true;
    }

    /**
     * 当前选择方向是否为：买入
     */
     get isBuy() {
        return this.uistates.direction == this.directions.buy.code;
    }

    /**
     * 当前选择方向是否为：卖出
     */
    get isSell() {
        return this.uistates.direction == this.directions.sell.code;
    }

    /**
     * 当前选择方向是否为：调仓
     */
    get isAdjust() {
        return this.uistates.direction == this.directions.adjust.code;
    }

    get theMethod() {
        return this.smethods.find(x => x.code == this.uistates.method);
    }

    constructor(view_name) {

        super(view_name, false, '篮子交易下单');

        var dirs = this.systemTrdEnum.tradingDirection;
        /**
         * 委托方向
         */
        this.directions = {

            buy: new CodeMeanItem(dirs.buy.code, dirs.buy.mean),
            sell: new CodeMeanItem(dirs.sell.code, dirs.sell.mean),
            adjust: new CodeMeanItem(0, '调仓'),
        };

        /*
        
        下单方式：

        VOLUME("数量篮子", 1),——按数量篮子模式下单，数量单位为 篮（executeVolume）——复制模式
        PROPORTION("权重篮子", 2),——按权重篮子模式下单，数量单位为 元——复制模式（executeVolume）
        PROPORTION_ACCOUNT_RATIO("权重篮子与账号比例", 3),——按权重篮子下单，账号比例放入TaskDetailBean中的multiple，executeVolume表示总金额——分派模式
        PROPORTION_ASSET_RATIO("权重篮子与资产比例", 4);,——按权重篮子下单，，executeVolume表示仓位，例如输入50，表示所有账号都操作资产比例的50%；——复制模式
        
        */

        var methods = this.systemTrdEnum.basketMethod;

        /**
         * 委托方式
         */
        this.methods = { 
            volume: methods.volume, 
            weight: methods.weight, 
        };

        this.smethods = Object.values(this.methods);
        this.stages = this.systemTrdEnum.bidingStages;

        /**
         * 下单面板通用核心状态
         */
        this.uistates = {

            direction: this.directions.buy.code,
            instrument: null,
            instrumentName: null,
            method: this.methods.volume.code,
            stage: this.stages[0].code,
            scale: 0,
            priceStep: 0.01,
            priceOffset: 0,
            isSomethingChanged: false,
        };

        this.exclude = {

            suspend: false,
            cash: false,
            ceiling: false,
            floor: false,
        };

        this.registerEvent('set-as-basket', this.handleBasketChange.bind(this));
        this.registerEvent('set-criteria-as-changed', this.flagSomeChanged.bind(this));
    }

    /**
     * @param {Number} basketId 
     * @param {String} basketName 
     * @param {Boolean} isEtf 
     */
    handleBasketChange(basketId, basketName, isEtf) {

        this._isEtfBasket = isEtf;
        this.uistates.instrument = basketId || null;
        this.uistates.instrumentName = basketName || null;
        this.smethods.clear();
        this.smethods.merge(isEtf ? [this.methods.volume] : this.helper.dict2Array(this.methods));
        this.uistates.method = this.smethods[0].code;
        this.flagAsLocalChanged();
    }

    flagAsLocalChanged() {
        this.flagSomeChanged();
    }

    flagSomeChanged() {

        /**
         * 是否篮子交易的某个条件发生了变化 ~ 具备预览重算的基础
         */
        this.uistates.isSomethingChanged = true;
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                directions: this.helper.dict2Array(this.directions),
                methods: this.smethods,
                stages: this.stages,
                uistates: this.uistates,
                exclude: this.exclude,
            },

            computed: {

                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                theMethod: () => { return this.theMethod; },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleDirectionChange,
                this.handleScaleChange,
                this.hope2Preview,
                this.hope2Entrust,
                this.handleMethodChange,
                this.handleStageChange,
                this.handleOffsetChange,
            ]),
        });
    }

    handleDirectionChange() {
        this.flagAsLocalChanged();
    }

    handleOffsetChange() {
        this.flagAsLocalChanged();
    }

    handleScaleChange() {
        this.flagAsLocalChanged();
    }

    handleMethodChange() {

        this.uistates.scale = 0;
        this.flagAsLocalChanged();
    }

    handleStageChange() {
        this.flagAsLocalChanged();
    }

    hope2Preview() {

        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        this.uistates.isSomethingChanged = false;
        this.trigger('preview-basket-orders', this.formParams());
    }

    hope2Entrust() {

        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        this.uistates.isSomethingChanged = false;
        this.trigger('place-basket-orders', this.formParams());
    }

    formParams() {

        var states = this.uistates;
        var theMethod = this.theMethod;

        return new BasketOrderParam({

            direction: states.direction,
            directionName: this.isBuy ? '买入' : this.isSell ? '卖出' : '调仓',
            
            basketId: states.instrument,
            basketName: states.instrumentName,

            method: states.method,
            methodName: theMethod.mean,
            methodLabel: theMethod.label,
            methodUnit: theMethod.unit,

            scale: states.scale,
            stage: states.stage,
            stageName: this.stages.find(x => x.code == states.stage).mean,
            offset: states.priceOffset,
            adjustType: 1,

        }, JSON.parse(JSON.stringify(this.exclude)));
    }

    areParamsOk() {

        var uistates = this.uistates;

        if (!uistates.instrument) {
            return '请选择篮子';
        }

        if (!(uistates.scale > 0)) {
            return '委托' + this.theMethod.label + '无效';
        }

        // if (this.theMethod.code == this.methods.weight2Asset.code && uistates.scale > 100) {
        //     return `${this.methods.weight2Asset.mean}，不能超过100%`;
        // }

        if (!Number.isInteger(this.helper.safeDevide(uistates.priceOffset, uistates.priceStep))) {
            return `偏移量${uistates.priceOffset}，非最小价差${uistates.priceStep}，的整数倍`;
        }

        return true;
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;