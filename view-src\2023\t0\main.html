<div class="main-view s-full-size">
    <!-- 顶部展示 -->
    <div class="top-box themed-box">
        <template>
            <!-- 新建按钮 -->
            <el-button type="info" class="create-button" @click="create()">
                <i class="el-icon-plus"></i>
                <span>新建日内回转</span>
            </el-button>
            <!-- 总览 -->
            <div class="summary-block">
                <div class="sum-unit">
                    <div class="item-value">{{ stock_count }}个</div>
                    <div class="item-label">回转股票数</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ strategy_count }}个</div>
                    <div class="item-label">今日策略数</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ running_count }}个</div>
                    <div class="item-label">运行中</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ stopped_count }}个</div>
                    <div class="item-label">非运行</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value" :class="classBenefit(total_profit)">{{ thousandsInt(total_profit) }}</div>
                    <div class="item-label">今日总盈亏</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value" :class="classBenefit(closed_profit)">{{ thousandsInt(closed_profit) }}</div>
                    <div class="item-label">实现盈亏</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value" :class="classBenefit(float_profit)">{{ thousandsInt(float_profit) }}</div>
                    <div class="item-label">敞口浮动盈亏</div>
                </div>
            </div>
        </template>
    </div>
    <!-- 筛选栏 -->
    <div class="control-box themed-box">
        <template>

            <div class="control-unit">
                <span>母单查询</span>
            </div>

            <div class="control-unit">
                <label class="ctr-label">账号</label>
                <el-select 
                    placeholder="请选择账号" 
                    class="s-w-150" 
                    v-model="recordId" 
                    @change="handleAccountChange" 
                    filterable 
                    clearable>
                    <el-option 
                        v-for="(item, item_idx) in accounts" 
                        :key="item_idx" 
                        :value="getProperAccountId(item)"
                        :label="formatSelectAccountName(item)"></el-option>
                </el-select>
            </div>

            <div class="control-unit">
                <label class="ctr-label">算法</label>
                <el-select 
                    placeholder="请选择算法" 
                    v-model="algoId" 
                    @change="handleAlgoChange" 
                    class="s-w-150" 
                    filterable 
                    clearable>
                    <el-option-group v-for="(group, group_idx) in algoGrps" :key="group_idx" :label="group.name">
                        <el-option v-for="(item, item_idx) in group.algoes" :key="item_idx" :label="item.algorithmName" :value="item.id"></el-option>
                    </el-option-group>
                </el-select>
            </div>
            
            <div class="control-unit">
                <label class="ctr-label">母单</label>
                <el-select 
                    placeholder="请选择母单" 
                    v-model="taskId"
                    @change="handleInstanceChange" 
                    class="s-w-200" 
                    filterable 
                    clearable>
                    <el-option v-for="(item, item_idx) in instances" :key="item_idx" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </div>

            <el-button type="primary" @click="filterTasks">查询</el-button>

        </template>
    </div>
    <!-- 算法母单平铺 -->
    <div class="morder-list">
        <template>
            <div v-for="(item, item_idx) in tasks" 
                :key="item_idx" 
                @click="handleTaskCheck(item)"
                class="each-morder"
                :class="isCurrentChoosed(item) ? 'selected' : ''"
                v-show="meetLocal(item)">

                <div class="morder-name">
                    <div class="morder-name-inner s-ellipsis">{{ item_idx + 1 }}/{{ tasks.length }} {{ item.task_name }}</div>
                </div>
                <div class="morder-aspects">
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">算法名称</span>
                            <span class="field-value">{{ item.strategy_name }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">总盈亏</span>
                            <span class="field-value" :class="classBenefit(item.total_profit)">{{ thousandsInt(item.total_profit) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">分配资金</span>
                            <span class="field-value">{{ thousandsInt(item.algop.money) }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">实现盈亏</span>
                            <span class="field-value" :class="classBenefit(item.closed_profit)">{{ thousandsInt(item.closed_profit) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">股票数量</span>
                            <span class="field-value">{{ item.stock_count }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">敞口浮动盈亏</span>
                            <span class="field-value" :class="classBenefit(item.float_profit)">{{ thousandsInt(item.float_profit) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">状态</span>
                            <span class="field-value" :class="isTaskRunning(item) ? 's-color-green' : 's-color-red'">
                                <span>{{ isTaskRunning(item) ? '运行中' : '已停止' }}</span>
                            </span>
                        </div>
                        <div class="lower-one">
                        </div>
                    </div>
                </div>
                <div class="morder-operation">
                    
                    <!-- <el-button type="info" class="morder-button" @click="editm(item)">
                        <i class="el-icon-edit"></i>
                        <span>编辑</span>
                    </el-button> -->

                    <el-button v-if="isTaskRunning(item)" type="danger" class="morder-button" @click="stopm(item)">
                        <i class="el-icon-video-pause"></i>
                        <span>停止</span>
                    </el-button>

                </div>
            </div>
        </template>
    </div>
    <div class="child-task-title">
        <span>{{ title }}</span>
    </div>
    <!-- 单个算法实例，内含子单列表 -->
    <div class="child-task-list">
        <table>
			<tr>
				<th label="代码/名称" min-width="140" prop="stock_code" formatter="formatCodeName" sortable overflowt></th>
				<!-- <th label="持仓数量" min-width="80" prop="totalPosition" align="right" sortable thousands-int></th>
				<th label="可用数量" min-width="80" prop="yesterdayPosition" align="right" sortable thousands-int></th> -->
				<th label="目标量" min-width="80" prop="target_volume" align="right" sortable thousands-int></th>
				<th label="买入量/卖出量" min-width="140" watch="buy_amount, sell_amount" align="right" formatter="formatBuySell" sortable></th>
				<th label="买入均价/卖出均价" min-width="140" watch="buy_avg_price,sell_avg_price" align="right" formatter="formatAvgPrice" sortable></th>
				<th label="实现盈亏" min-width="90" prop="closed_profit" class-maker="makeBenefitClass" align="right" sortable thousands></th>
				<th label="敞口浮动盈亏" min-width="140" prop="float_profit" class-maker="makeBenefitClass" align="right" sortable thousands></th>
				<th label="回转持仓敞口" min-width="140" prop="open_amount" class-maker="makeBenefitClass" align="right" sortable thousands></th>
				<th label="操作" min-width="60" prop="status" fixed="right" formatter="formatActions"></th>
			</tr>
		</table>
    </div>
</div>
<!-- 创建编辑弹窗 -->
<div class="creation-dialog-external"></div>