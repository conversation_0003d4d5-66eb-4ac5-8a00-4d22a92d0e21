import type {
  LegacyAccountInfo,
  MomFinanceAccountDetail,
} from '../../../xtrade-sdk/dist/types/table/account';

/**
 * Mom账号类型的别名（前端界面开发已多处使用，请勿修改）
 */
export type AccountInfo = LegacyAccountInfo;
export type FinanceAccountDetail = MomFinanceAccountDetail;

export interface FinanceAccountDetailField {
  key: {
    [K in keyof FinanceAccountDetail]: FinanceAccountDetail[K] extends number ? K : never;
  }[keyof FinanceAccountDetail];
  label: string;
  format: (value: number) => string;
  /** 是否需要特殊处理颜色 */
  hasColor?: boolean;
}
