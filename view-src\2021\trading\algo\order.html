<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<span>算法母单</span>

		<el-input placeholder="输入关键字搜索" 
				  prefiicon="el-icon-search" 
				  class="keywords s-mgl-10"
				  v-model="states.keywords"
				  @change="filterOrders" clearable></el-input>

		<div class="s-pull-right">
			<el-button type="primary" @click="hope2CancelCheckeds">撤勾选</el-button>
			<el-button type="primary" @click="hope2CancelAll">撤全部</el-button>
			<!-- <el-button type="primary" @click="hope2Replace">追单</el-button> -->
		</div>

	</div>

	<div class="data-list">

		<table>
			<tr>
				<th label="展开" fixed-width="65" prop="isOpened" formatter="formatOpener" fixed></th>
				<th type="check" fixed-width="65" fixed></th>
				<th label="所属产品" min-width="100" prop="fundName" overflowt searchable></th>
				<th label="账号名称" min-width="202.0" prop="accountName" formatter="formatAccountName" overflowt searchable></th>
				<th label="算法类型" min-width="70" prop="algorithmName" overflowt searchable></th>
				<th label="证券代码" min-width="100" prop="instrument" overflowt searchable></th>
				<th label="证券名称" min-width="80" prop="instrumentName" overflowt searchable></th>
				<th label="目标量" min-width="60" prop="volume" align="right" thousands-int></th>
				<th label="成交数量" min-width="60" prop="tradedVolume" align="right" thousands-int></th>
				
				<th label="方向" 
					min-width="50" 
					prop="direction" 
					formatter="formatDirection2" 
					export-formatter="formatDirectionText2" 
					disabled-filter-data-provider="rebindDirection"></th>

				<th label="开始时间" min-width="70" prop="effectiveTime" formatter="formatTime" searchable></th>
				<th label="结束时间" min-width="70" prop="expireTime" formatter="formatTime" searchable></th>
				<th label="参数" min-width="120" prop="algoParam" searchable overflowt></th>
				<th label="任务名称" min-width="100" prop="taskName" searchable overflowt></th>
				<th label="错误信息" min-width="120" prop="remark" searchable overflowt></th>
				<th label="批次ID" min-width="150" prop="taskId" overflowt searchable></th>
				<th label="排序" prop="sort_key" width="0" min-width="0"></th>
				
				<th label="状态"
					fixed="right"
					prop="algorithmStatus"
					fixed-width="60"
					formatter="formatStatus"
					export-formatter="formatStatus"></th>

				<th label="操作"
					fixed="right"
					prop="isCompleted"
					fixed-width="60"
					formatter="formatActions"
					exportable="false"></th>
					
			</tr>
		</table>

	</div>

</div>