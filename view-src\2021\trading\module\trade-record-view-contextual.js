const { TradeRecordView } = require('./trade-record-view');
const { AccountDetail } = require('../../../../model/account');

class ContextualTradeRecordView extends TradeRecordView {

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.registerEvent('set-context-account', this.handleAccountChange.bind(this));
    }

    /**
     * @param {AccountDetail} account 
     */
    handleAccountChange(account) {

        var newProperId = this.helperUi.getProperAccountId(account);
        var lastProperId = this.properId;
        var newAccountId = (account || {}).accountId;
        var lastAccountId = this.accountId;

        if (this.helper.isNotNone(lastProperId)) {
            this.handleBeforeContextChange(lastProperId);
        }

        if (this.tableObj) {
            this.tableObj.clear();
        }

        this.properId = newProperId;
        this.accountId = newAccountId;

        if (this.helper.isNotNone(newProperId)) {
            
            this.subChange();
            this.turn2Request();
        }
    }

    /**
     * 处置上下文（即将）切换事件，在该方法实现：对上一主体尚未保存的状态作清理
     * @param {*} lastId 上一主体ID
     */
    handleBeforeContextChange(lastId) {
        throw new Error('not implemented');
    }

    filterByChannel() {
        
        /**
         * 账号交易数据，随账号变化而变化，不随交易渠道的变化而变，故无需响应该事件
         */
    }

    build($container, tableName) {
        super.build($container, tableName);
    }
}

module.exports = { ContextualTradeRecordView };