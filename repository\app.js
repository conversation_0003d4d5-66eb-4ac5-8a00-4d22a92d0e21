const httpRequest = require("../libs/http").http;

class appRepository {
  createApp(app) {
    return new Promise((resolve, reject) => {
      httpRequest.put("/application", app).then(
        resp => {
          resolve(resp.data);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  updateApp(app) {
    return new Promise((resolve, reject) => {
      httpRequest.put("/application", app).then(
        resp => {
          resolve(resp.data);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  getAll() {
    return new Promise((resolve, reject) => {
      httpRequest.get("/application/all").then(
        resp => {
          resolve(resp.data);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  deleteApp(app_id) {
    return new Promise((resolve, reject) => {
      httpRequest
        .delete("/application", {
          params: {
            app_id
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          error => {
            reject(error);
          }
        );
    });
  }

  share2Users(app_id, share_type, AppMappings) {
    return new Promise((resolve, reject) => {
      httpRequest
        .put("/application/mapping", AppMappings, {
          params: {
            app_id,
            share_type
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }

  getMyApplications() {
    return new Promise((resolve, reject) => {
      httpRequest.get("/application").then(
        resp => {
          resolve(resp.data);
        },
        err => {
          reject(err);
        }
      );
    });
  }

  login2Fof() {

    return new Promise((resolve, reject) => {
      httpRequest.post(`https://yitou.gaoyusoft.com/yitou/v1/core/xtrade/exchange/token`).then(
        resp => { resolve(resp.data); },
        err => { reject(err); }
      );
    });
  }
}

module.exports = { repoApp: new appRepository() };
