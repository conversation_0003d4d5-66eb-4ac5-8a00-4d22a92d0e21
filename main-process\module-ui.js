﻿/**
 * UI manager
 */

const electron = require('electron');
const remote = require('@electron/remote/main');
const BrowserWindow = electron.BrowserWindow;
const path = require('path');
const Routing = require('../model/routing').Routing;
const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;

class UIModule extends ServerEnvMainModule {

    /**
     * @param {String} module_name 
     * @param {Array<Routing>} routings 
     */
    constructor(module_name, routings) {

        super(module_name);
        this.routings = routings;
    }

    createSignInWindow() {

        this.mainProcess.emit(this.systemEvent.huntWinLandscapeFromMain, '@sign-in', { width: 360, height: 475, maximizable: false, resizable: false }, the_win => {
            // will never happen (just 4 code intelligence)
            if (!(the_win instanceof BrowserWindow)) {
                return;
            }

            this.signInWindow = the_win;
            this.signInWindow.on('closed', () => {
                this.exitApp();
            });
            this.setContextDataItem(this.dataKey.loginWindowId, this.signInWindow.id);
        });
    }

    directUser2SigninWindow() {

        try {
            this.closNotSignInWindows();
            this.signInWindow.show();
        } 
        catch (ex) {

            this.loggerConsole.fatal(ex.message + ' > ' + ex.stack);
            this.loggerSys.fatal(ex.message);
        }
    }

    sendNetworkStatusMessage(message) {
        
        var central_window = this.centralWindow;
        central_window && central_window.webContents.send(this.systemEvent.networkStatusChange, message);
    }

    listen2NetworkConnChange() {

        this.tradingServer.listen2Event(this.systemEvent.connEstablished, event => {
            this.sendNetworkStatusMessage({ ok: true, content: '交易服务器已连接' });
        });

        this.tradingServer.listen2Event(this.systemEvent.connClosed, event => {
            this.sendNetworkStatusMessage({ ok: false, content: '交易服务器连接已断开' });
            this.isDisconnectedByLogout ? this.directUser2SigninWindow() : null;
        });

        this.tradingServer.listen2Event(this.serverEvent.forcedKickOut, event => {
            this.doLogout();
            this.signInWindow.webContents.send(this.serverEvent.forcedKickOut.toString());
        });

        // var require_quote_server = !this.userInfo ? false : !this.userInfo.isSuperAdmin && !this.userInfo.isBrokerAdmin;
        // if (require_quote_server) {

        //     this.quoteServer.listen2Event(this.systemEvent.connEstablished, event => {
        //         this.sendNetworkStatusMessage({ ok: true, content: '行情服务器已连接' });
        //     });

        //     this.quoteServer.listen2Event(this.systemEvent.connClosed, event => {
        //         this.sendNetworkStatusMessage({ ok: false, content: '行情服务器连接已断开' });
        //     });
        // }
    }

    shouldExit() {
        // return !this.isDisconnectedByLogout && !this.isDisconnectedTradingServerAccidently && !this.isDisconnectedQuoteServerAccidently;
        return !this.isDisconnectedByLogout && !this.isDisconnectedTradingServerAccidently;
    }

    setCentralWindowId2Context(win_id) {
        this.setContextDataItem(this.dataKey.centralWindowId, win_id);
    }

    createMainWindow() {

        this.dashboardWindow = new BrowserWindow({
                
            width: 1024,
            minWidth: 1024,
            height: 680,
            minHeight: 680,
            show: true,
            frame: false,
            webPreferences: {
                webviewTag: true,
                nodeIntegration: true,
                contextIsolation: false,
            }
        });

        const { brokerCode } = this.app.otherSysSettings || {};
        // 设置当前窗口对Render Process可见
        remote.enable(this.dashboardWindow.webContents);
        this.dashboardWindow.loadURL(`file://${path.join(__dirname, '../component/win-dashboard.html')}`);
        this.equipMainWindow();
        const webAppSession = electron.session.fromPartition('persist:xtrade-external-app');
        webAppSession.webRequest.onBeforeSendHeaders((details, callback) => {
            details.requestHeaders['gaoyu-token'] = this.userInfo.token;
            details.requestHeaders['gaoyu-user-id'] = this.userInfo.userId + '';
            details.requestHeaders['gaoyu-broker-code'] = brokerCode || '';
            callback({ cancel: false, requestHeaders: details.requestHeaders });
        });
    }

    equipMainWindow() {

        this.dashboardWindow.on('closed', () => {

            this.dashboardWindow = null;
            this.setCentralWindowId2Context(null);
            if (this.shouldExit()) {
                this.exitApp();
            }
        });

        this.setCentralWindowId2Context(this.dashboardWindow.id);
        this.dashboardWindow.on(this.systemEvent.renderWindowPrepared, () => {
            this.dashboardWindow.emit(this.systemEvent.globalViewRouting, this.routings);
        });
    }

    handleSuccessfulLogin(event) {

        setTimeout(() => {
            this.loggerSys.info('login is successful, hide login window, and show welcome window');
            // hide sign-in window anyway
            this.signInWindow.hide();
        }, 200);

        // create welcome ( data preparing ) window
        this.mainProcess.emit(this.systemEvent.huntWinLandscapeFromMain, '@welcome', { width: 450, height: 270, maximizable: false, resizable: false }, the_win => {

            // will never happen (just 4 code intelligence)
            if (!(the_win instanceof BrowserWindow)) {
                return;
            }

            this.welcomeWindow = the_win;
            this.welcomeWindow.on('closed', () => {
                this.welcomeWindow = null;
                // 在系统准备完毕之前，欢迎窗口被人为关闭时，软件将退出
                if (!this.sysPrepaingCompleted) {
                    this.exitApp();
                }
            });
        });
    }

    closNotSignInWindows() {

        var all_windows = this.electron.BrowserWindow.getAllWindows();
        var signin_win_id = this.signInWindow.id;

        all_windows.forEach(the_win => {
            
            if (the_win.id == signin_win_id || !the_win.isVisible()) {
                return;
            }
            else {
                the_win.close();
            }
        });
    }

    doLogout() {

        this.setContextDataItem(this.dataKey.disconnectedByLogout, true);
        this.closNotSignInWindows();

        try {
            this.tradingServer.logout();
        }
        catch (ex) {
            
            this.loggerConsole.fatal(ex.message + ' > ' + ex.stack);
            this.loggerSys.fatal(ex.message);
        }

        // try {
        //     this.quoteServer.logout();
        // } 
        // catch (ex) {
            
        //     this.loggerConsole.fatal(ex.message + ' > ' + ex.stack);
        //     this.loggerSys.fatal(ex.message);
        // }

        // user asks to log out, the system goes back to sign-in window
        this.signInWindow.show();
    }

    sendPongMessage(message) {

        var central_window = this.centralWindow;
        central_window && central_window.webContents.send(this.systemEvent.pingPong, message);
    }

    listen2Events() {

        // 创建第一个窗口 --  登录窗口
        this.mainProcess.once('to-create-first-window', this.createSignInWindow.bind(this));

        // 登录成功（包括交易服务器、行情服务器，综合判断为登录成功）
        this.mainProcess.on(this.systemEvent.loginRequestCompleted, this.handleSuccessfulLogin.bind(this));

        // 欢迎窗口，完成必备数据加载
        this.mainProcess.on(this.systemEvent.sysLoadingCompleted, event => {

            this.sysPrepaingCompleted = true;
            this.tradingServer.start2Ping(this.sendPongMessage.bind(this));
            this.listen2NetworkConnChange();
            this.createMainWindow();
        });

        // 主窗口已准备完毕
        this.mainProcess.on(this.systemEvent.mainWindowReady, event => {

            if (this.welcomeWindow) {
                this.welcomeWindow.close();
            }
            this.dashboardWindow.show();
        });

        /**
         * 注销的两种情况：
         * 1. 用户主动注销
         * 2. 管理员强制用户下线
         */
        this.mainProcess.on(this.systemEvent.toLogout, event => {
            delete this.sysPrepaingCompleted;
            this.doLogout();
        });
    }

    checkSingleInstance() {

        var got_locked = this.app.requestSingleInstanceLock();

        this.app.on('second-instance', (cmd_line, working_dir) => {

            if (this.dashboardWindow) {

                this.dashboardWindow.isMinimized() ? this.dashboardWindow.restore() : null;
                this.dashboardWindow.focus();
            } 
            else if (this.welcomeWindow) {

                this.welcomeWindow.isMinimized() ? this.welcomeWindow.restore() : null;
                this.welcomeWindow.focus();
            } 
            else if (this.signInWindow) {

                this.signInWindow.isMinimized() ? this.signInWindow.restore() : null;
                this.signInWindow.focus();
            }
        });

        if (!got_locked) {
            this.exitApp();
        }
    }

    exitApp() {
        this.app.quit();
    }

    run() {
        
        // this.checkSingleInstance();
        this.listen2Events();
    }
}

module.exports = { UIModule };
