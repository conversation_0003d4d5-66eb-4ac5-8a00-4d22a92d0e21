<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { deepClone } from '@/script';
import type { AnyIndicatorRiskParamObject, CommonRiskAlertConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { RiskSummarizationMethod } from '../../RiskParamTypeAndSummary';
import { AlertTypes, ExpressionType, RiskBsFlags } from '@/enum/riskc';
import { DefaultRiskParamCreator } from '../../DefaultRiskParamCreator';

const mountExpressionTypes = [ExpressionType.GreaterThan, ExpressionType.GreaterEqual];

/**
 * 单笔交易额
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  bsFlag: number;
  paramAlert: CommonRiskAlertConfig;
  paramBlock: CommonRiskAlertConfig;
}

const { ruleSetting } = defineProps<{
  ruleSetting: RuleInnerSetting | null | undefined;
}>();

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const definedActions = computed(() => {
  const { paramAlert, paramBlock } = localRuleSetting.value as RuleInnerSetting;
  return [paramAlert, paramBlock];
});

const rules = {
  bsFlag: [{ required: true, message: '请选择交易方向', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = handleInputChange(newValue);
  },
  { immediate: true },
);

function handleInputChange(newValue: RuleInnerSetting | null | undefined) {
  return newValue && JSON.stringify(newValue) != '{}'
    ? deepClone(newValue)
    : createEmptyRiskParam();
}

function createEmptyRiskParam(): RuleInnerSetting {
  return DefaultRiskParamCreator[IdcComponentNameDef.SingleOrderMaxAmount]();
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string | string[]];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return RiskSummarizationMethod[IdcComponentNameDef.SingleOrderMaxAmount](localRuleSetting.value);
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="bsFlag">
          <el-select
            v-model="localRuleSetting.bsFlag"
            style="width: 100px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in RiskBsFlags"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <label class="placed-label" pl-10>单笔最大交易额</label>
        </el-form-item>
      </div>
      <template v-for="(item, idx) in definedActions" :key="idx">
        <div class="custom-row">
          <el-form-item label="" prop="expression">
            <div w-full flex aic gap-10>
              <el-select
                v-model="item.expression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in mountExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="0"
                :step="1"
                :min="0"
                v-model="item.value"
                @change="handleParamHotChange"
                style="width: 100px"
              ></el-input-number>
              <label class="placed-label">万元时</label>
              <el-select
                v-model="item.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in AlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
