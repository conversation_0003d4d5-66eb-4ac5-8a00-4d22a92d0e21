<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<el-radio-group v-model="states.type">
			<el-radio v-for="(item, item_idx) in types"
					  :key="item_idx"
					  :label="item.code"
					  @change="filterPositions">{{ item.mean }}</el-radio>
		</el-radio-group>

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords"
				  v-model="states.keywords"
				  @change="filterPositions" clearable></el-input>

		<div class="s-pull-right">
			<!-- <el-button type="primary" @click="hope2CloseCheckeds">平仓</el-button> -->
			<!-- <el-button type="primary" @click="createAsBasket">导成篮子</el-button> -->
		</div>

	</div>

	<div class="data-list">

		<table>
			<tr>
				<th type="check" fixed-width="40" fixed></th>

				<th label="代码" 
					fixed-width="100" 
					prop="instrument" overflowt filterable sortable searchable></th>

				<th label="名称" 
					fixed-width="80" 
					prop="instrumentName" overflowt filterable sortable searchable></th>

				<th label="账号名称" 
					min-width="202.0" 
					prop="accountName" 
					formatter="formatAccountName" overflowt filterable sortable searchable></th>

				<th label="产品" 
					min-width="150" 
					prop="fundName" overflowt filterable sortable searchable></th>
				
				<th label="方向" 
					fixed-width="70" 
					prop="direction" 
					watch="direction" 
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>
				
				<th label="总仓" 
					fixed-width="80" 
					prop="totalPosition" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="持仓账号占比"
					fixed-width="100" 
					watch="totalPosition,lastPrice"
					align="right" 
					formatter="formatPosPct" filterable sortable></th>

				<th label="持仓均价" 
					fixed-width="80" 
					prop="avgPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="最新价" 
					fixed-width="80" 
					prop="lastPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="浮动盈亏" 
					min-width="80" 
					prop="floatProfit" 
					align="right" 
					class-maker="makeBenefitClass" 
					footer-class-maker="makeBenefitClass" overflowt sortable summarizable thousands></th>
				
				<th label="持仓市值" 
					min-width="80" 
					prop="marketValue" 
					align="right" sortable summarizable thousands></th>
				
				<th label="今仓" 
					fixed-width="80" 
					prop="todayPosition" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="冻结仓数" 
					fixed-width="80" 
					prop="frozenVolume" 
					align="right" filterable thousands-int></th>

				<th label="昨仓" 
					fixed-width="80" 
					prop="yesterdayPosition" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="可平数" 
					fixed-width="80" 
					prop="closableVolume" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="盈亏" 
					min-width="80" 
					prop="profit" 
					align="right" 
					class-maker="makeBenefitClass" 
					footer-class-maker="makeBenefitClass" overflowt sortable summarizable thousands></th>
				
				<th label="平仓盈亏" 
					min-width="80" 
					prop="closeProfit" 
					align="right" 
					class-maker="makeBenefitClass" 
					footer-class-maker="makeBenefitClass" overflowt sortable summarizable thousands></th>

				<th label="手续费" 
					min-width="80" 
					prop="usedCommission" 
					align="right" overflowt sortable summarizable thousands></th>

				<th label="保证金" 
					min-width="80" 
					prop="usedMargin" 
					align="right" overflowt sortable summarizable thousands></th>

				<th type="program"
					label="更新时间" 
					fixed-width="100" 
					prop="updateTime" 
					watch="updateTime" 
					formatter="formatTime" sortable></th>

				<th fixed="right" label="操作" 
					fixed-width="130" 
					formatter="formatActions" 
					exportable="false"></th>
			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>