﻿html,
body {
    margin: 0;
    padding: 0;
    padding-right: 0 !important;
    background-color: #0c1016;
    color: white;
    font-size: 12px;
    overflow: hidden;
}

/*
	font setting
*/

body,
div,
a,
span,
label,
input,
textarea,
button,
select,
code,
table,
tr,
td,
th,
ul,
li,
dl,
dt,
form,
nav,
p,
i {
    font-size: 12px;
    font-family: 'PingFangSC-Medium, sans-serif', 'Microsoft YaHei';
}

.iconfont {
    font-size: 12px;
}

/*
	original html element style setting
*/

a {
    text-decoration: none;
}
table {
    border-collapse: collapse;
    width: 100%;
}
td {
    padding: 0;
}

/*
	dragging
*/

.s-drag-handler {
    -webkit-app-region: drag;
}

.s-no-drag {
    -webkit-app-region: no-drag;
}

.s-unselectable {
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    user-select: none;
}

.s-native-select {
    border: none;
    border-radius: 3px;
    color: white;
    background: #0c1016;
}

/* 
	basic
*/

.s-block {
    display: block;
}

.s-inline-block {
    display: inline-block;
}

.s-hidden {
    display: none;
}

.s-border-box {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.s-pull-left {
    display: block;
    float: left;
}

.s-pull-right {
    display: block;
    float: right;
}

.s-height-unset {
    height: unset !important;
}
.s-text-center {
    text-align: center !important;
}
.s-text-right {
    text-align: right !important;
}
.s-bold {
    font-weight: bold;
}
.s-underline {
    text-decoration: underline;
}
.s-cp {
    cursor: pointer;
}
.s-flag {
    display: inline-block;
    padding: 0 4px;
    line-height: 18px;
    border-radius: 3px;
    text-align: center;
    vertical-align: middle;
    font-size: 90%;
}
.s-order-status-canceled {
    background-color: #777;
}

.s-order-status-preparing {
    background-color: #3399ff;
}

.s-order-status-waiting {
    background-color: #996600;
}

.s-order-status-progressing {
    background-color: #cc6633;
}

.s-order-status-ok {
    background-color: #66cc00;
}

.s-order-status-invalid {
    background-color: #ff0033;
}

/*
	margin & padding
*/

.s-no-margin {
    margin: 0 !important;
}
.s-no-padding {
    padding: 0 !important;
}

.s-pdl-5 {
    padding-left: 5px;
}
.s-pdl-10 {
    padding-left: 10px;
}
.s-pdl-15 {
    padding-left: 15px;
}
.s-pdl-20 {
    padding-left: 20px;
}
.s-pdl-30 {
    padding-left: 30px;
}
.s-pdr-5 {
    padding-right: 5px;
}
.s-pdr-10 {
    padding-right: 10px;
}
.s-pdr-15 {
    padding-right: 15px;
}
.s-pdr-20 {
    padding-right: 20px;
}
.s-pdt-5 {
    padding-top: 5px;
}
.s-pdt-10 {
    padding-top: 10px;
}
.s-pdt-15 {
    padding-top: 15px;
}
.s-pdt-20 {
    padding-top: 20px;
}
.s-pdb-5 {
    padding-bottom: 5px;
}
.s-pdb-10 {
    padding-bottom: 10px;
}
.s-pdb-15 {
    padding-bottom: 15px;
}
.s-pdb-20 {
    padding-bottom: 20px;
}
.s-pd-5 {
    padding: 5px;
}
.s-pd-10 {
    padding: 10px;
}
.s-pd-15 {
    padding: 15px;
}
.s-pd-20 {
    padding: 20px;
}
.s-mgl-5 {
    margin-left: 5px;
}
.s-mgl-10 {
    margin-left: 10px;
}
.s-mgl-15 {
    margin-left: 15px;
}
.s-mgl-20 {
    margin-left: 20px;
}
.s-mgr-5 {
    margin-right: 5px;
}
.s-mgr-10 {
    margin-right: 10px;
}
.s-mgr-15 {
    margin-right: 15px;
}
.s-mgr-20 {
    margin-right: 20px;
}
.s-mgt-5 {
    margin-top: 5px;
}
.s-mgt-10 {
    margin-top: 10px;
}
.s-mgt-15 {
    margin-top: 15px;
}
.s-mgt-20 {
    margin-top: 20px;
}
.s-mgb-5 {
    margin-bottom: 5px;
}
.s-mgb-10 {
    margin-bottom: 10px;
}
.s-mgb-15 {
    margin-bottom: 15px;
}
.s-mgb-20 {
    margin-bottom: 20px;
}
.s-mg-5 {
    margin: 5px;
}
.s-mg-10 {
    margin: 10px;
}
.s-mg-15 {
    margin: 15px;
}
.s-mg-20 {
    margin: 20px;
}

.s-ellipsis {
    overflow: hidden;
    word-wrap: normal;
    white-space: nowrap !important;
    text-overflow: ellipsis;
}

.s-no-select {
    user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

.s-full-width {
    width: 100%;
}

.s-full-height {
    height: 100%;
}

.s-full-size {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.s-w-100 {
    width: 100px;
}
.s-w-120 {
    width: 120px;
}
.s-w-150 {
    width: 150px;
}
.s-w-180 {
    width: 180px;
}
.s-w-200 {
    width: 200px;
}
.s-w-240 {
    width: 240px;
}
.s-w-280 {
    width: 280px;
}
.s-w-300 {
    width: 300px;
}
.s-w-320 {
    width: 320px;
}

.s-fs-8 {
    font-size: 8px;
}
.s-fs-10 {
    font-size: 10px;
}
.s-fs-12 {
    font-size: 12px;
}
.s-fs-14 {
    font-size: 14px;
}
.s-fs-16 {
    font-size: 16px;
}
.s-fs-18 {
    font-size: 18px;
}
.s-fs-20 {
    font-size: 20px;
}
.s-fs-24 {
    font-size: 24px;
}

.s-color-white {
    color: white !important;
}
.s-color-red {
    color: #ff4848 !important;
}
.s-color-green {
    color: #3ef16f !important;
}
.s-color-yellow {
    color: #ef9d22 !important;
}
.s-color-blue {
    color: #16a9e4 !important;
}
.s-color-grey {
    color: #999 !important;
}
.s-color-black {
    color: #000 !important;
}

.s-bg-white {
    background-color: white !important;
}
.s-bg-green {
    background-color: #00cc33 !important;
}
.s-bg-yellow {
    background-color: #ca5e15 !important;
}
.s-bg-orange {
    background-color: #ee6a50 !important;
}
.s-bg-red {
    background-color: #e54646 !important;
}

.s-opacity-9 {
    opacity: 0.9;
}
.s-opacity-8 {
    opacity: 0.8;
}
.s-opacity-7 {
    opacity: 0.7;
}
.s-opacity-6 {
    opacity: 0.6;
}
.s-opacity-5 {
    opacity: 0.5;
}
.s-opacity-4 {
    opacity: 0.4;
}
.s-opacity-3 {
    opacity: 0.3;
}
.s-opacity-hover:hover {
    opacity: 1;
}

.s-flex {
    display: flex;
    align-items: center;
}
.s-d-flex {
    display: flex;
}

/*
	scroll bar
*/

html::-webkit-scrollbar,
.el-table > .el-table__body-wrapper::-webkit-scrollbar,
.s-scroll-bar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: #262f3c;
}

html::-webkit-scrollbar-track,
.el-table > .el-table__body-wrapper::-webkit-scrollbar-track,
.s-scroll-bar::-webkit-scrollbar-track {
    border: 1px solid #2c3544;
    background-color: #181e28;
    border-radius: 8px;
    visibility: hidden;
}

.html:hover::-webkit-scrollbar-track,
.el-table > .el-table__body-wrapper:hover::-webkit-scrollbar-track,
.s-scroll-bar:hover::-webkit-scrollbar-track {
    visibility: visible;
}

html::-webkit-scrollbar-thumb,
.el-table > .el-table__body-wrapper::-webkit-scrollbar-thumb,
.s-scroll-bar::-webkit-scrollbar-thumb {
    height: 8px;
    box-shadow: 0 0 2px #8cb5ed inset;
    background-color: #264b7d;
    border-radius: 8px;
    visibility: hidden;
}

html::-webkit-scrollbar-corner,
.el-table > .el-table__body-wrapper::-webkit-scrollbar-corner,
.s-scroll-bar::-webkit-scrollbar-corner {
    background-color: #262f3c;
}

html:hover::-webkit-scrollbar-thumb,
.el-table > .el-table__body-wrapper:hover::-webkit-scrollbar-thumb,
.s-scroll-bar:hover::-webkit-scrollbar-thumb {
    visibility: visible;
}

/*
	element ui basic controls
*/

.el-button--small,
.el-button--small.is-round {
    padding: 6px 8px;
}

.el-button--mini,
.el-button--mini.is-round {
    padding: 3px 5px 4px 5px;
}

.el-table td .el-button--mini,
.el-table td .el-button--mini.is-round {
    padding: 3px 5px;
}

.el-input__inner,
.el-input--small .el-input__inner,
.el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    background: #0c1016;
    color: white;
    border-radius: 2px;
    border: none;
    font-size: 12px;
}

.el-input__icon,
.el-input--mini .el-input__icon {
    line-height: 18px;
}

.el-textarea__inner {
    background: #0c1016;
    color: white;
    border-radius: 2px;
    border: none;
    font-size: 12px;
}
.el-checkbox {
    color: white;
}
.el-checkbox.is-checked {
    opacity: 1;
}
.el-checkbox__label {
    padding-left: 5px;
    font-size: 12px;
    opacity: 0.6;
}
.el-checkbox__label:hover {
    opacity: 1;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
    color: white;
    opacity: 1;
}
.el-checkbox__inner {
    background-color: #475e7a;
    border: none;
    border-radius: 2px;
}
.el-checkbox__inner::after {
    height: 6px;
    width: 4px;
    left: 5px;
    top: 2px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #2c79f2;
}
.el-loading-mask {
    background-color: #666;
    opacity: 0.8;
}
.el-loading-mask > .el-loading-spinner > svg > circle {
    stroke: white;
}
.el-loading-mask > .el-loading-spinner > .el-loading-text {
    color: white;
}
.el-radio {
    margin-right: 10px;
}
.el-radio__label {
    padding-left: 5px;
}


/*
	element ui pagination
*/

.el-pagination {
    color: #8cb5ed;
}
.el-pagination * {
    font-size: 12px;
    font-weight: normal;
}
.el-pagination button {
    padding: 0 3px !important;
    min-width: unset;
    border-radius: 2px;
}
.el-pagination button:disabled {
    background-color: inherit;
    color: inherit;
}
.el-pagination .btn-next,
.el-pagination .btn-prev,
.el-pagination button:disabled {
    margin-top: 4px;
    background: #0c1016;
    color: #65a1ff;
}
.el-pagination__total {
    color: #65a1ff;
    font-weight: normal;
    margin-top: 4px;
}
.el-pager {
    margin: 0 5px;
    position: relative;
    bottom: -5px;
}
.el-pager li {
    min-width: 26px;
    background: unset;
}
.el-pagination button,
.el-pagination span:not([class*='suffix']) {
    height: 20px;
    line-height: 20px;
}
.el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {
    color: #8cb5ed;
}
.el-pagination .el-input__inner {
    height: 20px;
    line-height: 20px;
    margin-top: 4px;
}
.el-pagination__sizes {
    margin-right: 5px;
}

/*
	element table style
*/

.el-table,
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td,
.el-table__expand-icon {
    color: white;
}
.el-table .el-table__body {
    width: 100% !important;
}
.el-table thead {
    color: #8cb5ed;
}
.el-table td,
.el-table th {
    padding: 0;
}
.el-table .cell {
    line-height: 24px;
}
.el-table .cell,
.el-table th div,
.el-table--border td:first-child .cell,
.el-table--border th:first-child .cell {
    padding-left: 5px;
}
.el-table .cell,
.el-table th div {
    padding-right: 5px;
}
.el-table,
.el-table__expanded-cell {
    background-color: #1a212b;
}
.el-table th,
.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td,
.el-table__fixed-footer-wrapper tbody td,
.el-table__fixed-right-patch {
    background-color: #1a212b;
}
.el-table tr {
    background-color: #232b37;
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
    background: #1a212b;
}
.el-table--striped .el-table__body tr.el-table__row.expanded-row td {
    background: #0c1016;
    border-bottom: 1px solid #2c3544;
}
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
.el-table__body tr.current-row > td,
.el-table__body tr.hover-row.current-row > td,
.el-table__body tr.hover-row.el-table__row--striped.current-row > td,
.el-table__body tr.hover-row.el-table__row--striped > td,
.el-table__body tr.hover-row > td,
.el-table__body tr:hover > td {
    background-color: #2c79f2 !important;
}
.el-table--border::after,
.el-table--group::after,
.el-table::before,
.el-table__fixed-right::before,
.el-table__fixed::before {
    background-color: unset;
}
.el-table td,
.el-table th.is-leaf,
.el-table--border th,
.el-table__fixed-right-patch {
    border-bottom: none;
}
.el-table__footer-wrapper td,
.el-table__fixed-footer-wrapper tbody td {
    border-top: none;
}
.el-table .caret-wrapper {
    height: 24px;
}
.el-table .sort-caret.ascending {
    top: 1px;
}
.el-table .sort-caret.descending {
    bottom: 1px;
}
.el-table .el-table__body tr td .icon-button,
.el-table .el-table__body tr td [class*=" el-icon-"], 
.el-table .el-table__body tr td [class^=el-icon-] {

    display: inline-block;
    margin-right: 5px;
    font-size: 16px;
}

/*
	tab view
*/

.tab-panel {
    box-sizing: border-box;
    height: 28px;
    background-color: #313c4c;
    padding: 4px 80px 0 10px;
}
.tab-unit {
    display: inline-block;
    height: 100%;
    /* padding-left: 12px;
    padding-right: 12px; */
    line-height: 22px;
    background-color: #2c3544;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
}
.tab-unit.selected {
    background-color: #1f2631;
}
.tab-panel > .tab-unit:not(:first-child) {
    margin-left: 5px;
}
.win-top-tabs .tab-unit {
    margin-left: 15px;
}
.win-top-tabs.tab-panel {
    height: 28px;
}
.tab-unit .tab-inner {
    display: inline-block;
    padding: 0 !important;
    height: 100%;
    position: relative;
}
.tab-unit .tab-close {
    display: none;
    width: 20px;
    height: 100%;
    line-height: 24px;
    padding-left: 8px;
    position: relative;
    left: 3px;
    opacity: 0.8;
}
.tab-unit:hover .tab-close {
    display: inline-block !important;
}
.tab-unit .tab-close:hover {
    opacity: 1;
}
.tab-list-navi {
    display: block;
    float: right;
    margin-right: -80px;
    margin-top: 2px;
    padding-right: 10px;
}
.table-control {
    width: 100%;
}

/*
	toolbar
*/

.table-query-toolbar {
    height: 32px;
    line-height: 33px;
    box-sizing: border-box;
    background-color: #1f2631;
    padding-left: 15px;
}
.table-query-toolbar > *:not(:first-child) {
    margin-left: 10px;
}
.table-query-toolbar .el-input.searching-keywords {
    width: 170px;
}
.table-query-toolbar .option-box {
    padding-left: 10px;
}
.table-query-toolbar .option-box .el-checkbox {
    margin-right: 5px;
}
.table-query-toolbar .operation-box {
    padding-left: 10px;
}
.table-query-toolbar .operation-box > a {
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
    margin-right: 5px;
    color: #8cb5ed;
    opacity: 0.8;
}
.table-query-toolbar .operation-box > a:hover {
    color: white;
    opacity: 1;
}
.table-query-toolbar .el-pagination {
    float: right;
}
.table-query-toolbar .tab-unit-top-right-button-box {

    position: absolute;
    right: 0;
    margin-top: -25px;
    margin-right: 110px;
    line-height: 18px;
}

.table-query-toolbar .tab-unit-top-right-button-box button {
    padding: 2px 5px;
}

/* dialog的统一修饰 */
.el-dialog__wrapper .el-dialog__header {
    padding: 0 5px;
    background-color: #313c4c;
    height: 28px;
    line-height: 28px;
}

.el-dialog__wrapper .el-dialog__title {
    color: #ffffff;
    font-size: 14px;
}

.el-dialog__wrapper .el-dialog__headerbtn {
    right: 5px;
    top: 2px;
}

.el-dialog__wrapper .el-dialog__body {
    padding: 0;
}

.el-dialog__wrapper .el-dialog__footer {
    padding: 0 20px;
    background-color: #0c1016;
    height: 40px;
    line-height: 40px;
}

.el-dialog__wrapper .el-dialog__footer .el-button {
    height: 24px;
    line-height: 24px;
    width: 80px;
    padding: 0;
    border: 1px solid #8393ab;
    /* background-color: #0c1016; */
    color: white;
}

.el-dialog__wrapper .el-dialog__footer .el-button.el-button--primary {
    background: #2c79f2;
}

.el-tabs--border-card {
    border: 1px solid #313c4c;
}

.el-tabs--border-card .el-tabs__content {
    background-color: #0c1016;
}

.el-tabs--border-card .el-tabs__nav-scroll {
    background-color: #313c4c;
    color: white;
}

.el-tabs--border-card .el-tabs__nav-scroll .el-tabs__item.is-active {
    background-color: #1f2631;
    border: none;
}

.clear-fix {
    clear: both;
}

.btn-cancel-fix {
    float: right;
    margin-left: 10px;
}

.sc-table {
    position: relative;
}
/* splitter */
.s-splitter {
    width: 100%;
    cursor: s-resize;
    height: 4px;
    background: #33455f;
}
.s-vertical-splitter {
    height: 100%;
    cursor: e-resize;
    width: 4px;
    background: #33455f;
}

.view-template {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
}

/*
    splitter
*/

.splitter-box .splitter-bar {
    background-color: #666;
    text-align: center;
    vertical-align: middle;
}

.splitter-box .splitter-bar::before {
    content: '...';
    position: relative;
    top: -12px;
    font-size: 16px;
    line-height: 16px;
    color: #ccc;
}

.splitter-box .splitter-bar:hover {
    background-color: #c2c2c2;
}

.splitter-box .splitter-bar:hover .splitter-bar::before {
    display: none;
}

.splitter-box.horizontal {
    width: 100%;
}

.splitter-box.horizontal .splitter-bar {
    cursor: col-resize;
}

.splitter-box.vertical {
    height: 100%;
}

.splitter-box.vertical .splitter-bar {
    cursor: row-resize;
}

.cell-edit-anchor {
    cursor: pointer;
}

.cell-edit-anchor:hover {
    text-decoration: underline;
}

.cell-edit-anchor::after {

    font-family: element-icons!important;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    vertical-align: baseline;
    display: inline-block;
    -webkit-font-smoothing: antialiased;

    content: '\e78c';
    padding-left: 5px;
    visibility: hidden;
}

.cell-edit-anchor:hover::after {
    visibility: visible;
}

.input-searching {
    width: 200px;
}