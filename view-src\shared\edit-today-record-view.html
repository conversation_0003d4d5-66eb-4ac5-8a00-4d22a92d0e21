<div class="edit-today-record-view">
    <el-dialog :title="title" :visible="visible" :close-on-click-modal="false" :show-close="false" width="600px">
        <div style="padding: 10px;">
            <el-form size="mini" :model="form" :rules="rules" ref="form" :label-width="labelWidth" @submit.native.prevent>
                <el-row v-for="(row, rowIndex) in rows" :key="rowIndex">
                    <el-col v-for="(col, colIndex) in row.cols" :key="colIndex" :span="col.span || 12">
                        <el-form-item size="mini" :label="col.label" :prop="col.prop" :label-width="col.labelWidth">
                            <el-input v-if="!col.type" :disabled="col.disabled" v-model.trim="form[col.prop]"
                                :placeholder="col.placeholder">
                                <template v-if="col.append" slot="append">{{col.append}}</template>
                            </el-input>
                            <el-radio-group v-if="col.type == 'radio'" :disabled="col.disabled" v-model="form[col.prop]"
                                :placeholder="col.placeholder" @[haschange(col)]="col.handleChange">
                                <el-radio v-for="option in col.options" :key="option.value" :label="option.value">
                                    {{option.label}}</el-radio>
                            </el-radio-group>
                            <el-select v-if="col.type == 'select'" class="s-full-width" :disabled="col.disabled"
                                v-model="form[col.prop]" :placeholder="col.placeholder" :multiple="col.multiple"
                                :value-key="col.valueKey" :clearable="col.clearable" :collapse-tags="col.collapseTags"
                                :filterable="col.filterable" @[haschange(col)]="col.handleChange">
                                <el-option v-for="option in col.options" :key="option.value" :label="option.label"
                                    :value="option.value" :disabled="option.disabled"></el-option>
                            </el-select>
                            <el-autocomplete v-if="col.type == 'autocomplete'" class="s-full-width"
                                :disabled="col.disabled" v-model.trim="form[col.prop]" :placeholder="col.placeholder"
                                :fetch-suggestions="col.fetch" :trigger-on-focus="col.triggerOnFocus || false"
                                @[hasselect(col)]="col.handleSelect">
                                <template v-if="col.stock" slot-scope="{ item }">
                                    <div class="matched-item">
                                        <span class="item-code">[{{ item.value }}] </span>
                                        <span class="item-name">{{ item.instrumentName }}</span>
                                    </div>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div slot="footer">
            <el-button type="primary" size="small" @click="save">确定</el-button>
            <el-button size="small" @click="cancel">取消</el-button>
        </div>
    </el-dialog>
</div>