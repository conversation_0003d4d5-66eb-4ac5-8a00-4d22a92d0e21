const { IView } = require('../../component/iview');
const { GaoyuAlgo, GaoyuAlgoGroup, GaoyuAlgoParam, GaoyuAlgoHelper } = require('../../model/algo-vendor');
const { repoAlgo } = require('../../repository/algorithm');

class AlgoParamPartView extends IView {

    constructor(view_name, title) {

        super(view_name, false, title || '算法与参数');
        this.startTimeNames = ['effectiveTime'].map(x => x.toLowerCase());
        this.stopTimeNames = ['expireTime'].map(x => x.toLowerCase());
        this.createFields();
    }

    createFields() {

        this.algoGrps = [new GaoyuAlgoGroup()].splice(1);
        this.algoes = [new GaoyuAlgo({})].splice(1);
        this.algoParams = [new GaoyuAlgoParam({})].splice(1);
        this.uoptions = [{ label: '', value: '' }].splice(1);
        this.validation = { rules: {} };
    }

    /**
     * @param {Array<GaoyuAlgoParam>} data
     */
    typedsArray(data) {
        return data;
    }

    /**
     * 根据当前选择的算法，更新对应的参数
     */
    alignAlgoParams(algoId) {

        const matchedAlgo = this.algoes.find(x => x.id == algoId);
        const dynamicParams = this.typedsArray(matchedAlgo ? this.helper.deepClone(matchedAlgo.params) : []);
        
        /**
         * 将开始时间与结束时间，提前
         */
        
        let startParam = dynamicParams.find(x => this.startTimeNames.some(name => typeof x.prop == 'string' && x.prop.toLowerCase() == name));
        let stopParam = dynamicParams.find(x => this.stopTimeNames.some(name => typeof x.prop == 'string' && x.prop.toLowerCase() == name));

        if (stopParam) {
            
            dynamicParams.remove(x => x === stopParam);
            dynamicParams.unshift(stopParam);
        }

        if (startParam) {

            dynamicParams.remove(x => x === startParam);
            dynamicParams.unshift(startParam);
        }

        this.algoParams.clear();
        this.algoParams.refill(dynamicParams);

        /**
         * 生成表单填写验证规则
         */

        var rules = {};
        dynamicParams.forEach(item => {
            rules[item.prop] = [{ required: true, message: `请输入${item.label}` }];
        });

        this.validation.rules = rules;
    }

    /**
     * 是否整数参数 
     */
    isIntegerParam(dataType) {
        return GaoyuAlgoHelper.isInteger(dataType);
    }

    /**
     * 是否带小数参数 
     */
    isDecimalParam(dataType) {
        return GaoyuAlgoHelper.isDecimal(dataType);
    }

    /**
     * 是否时间参数 
     */
    isTimeParam(dataType) {
        return GaoyuAlgoHelper.isTime(dataType);
    }

    /**
     * 是否时间范围参数 
     */
    isTimeRangeParam(dataType) {
        return GaoyuAlgoHelper.isTimeRange(dataType);
    }

    /**
     * 是否文本参数 
     */
    isTextParam(dataType) {
        return GaoyuAlgoHelper.isText(dataType);
    }

    /**
     * 是否下拉选项参数 
     */
    isUserOptionParam(dataType) {
        return GaoyuAlgoHelper.isUserOption(dataType);
    }

    /**
     * 是否算法参数填值符合预期
     */
    areAlgoParamsApplicable() {

        let requires = this.algoParams.filter(x => x.required && x.display);
        let oks = requires.filter(item => {

            if (this.isIntegerParam(item.type)
                    || this.isDecimalParam(item.type)
                    || this.isTextParam(item.type)
                    || this.isUserOptionParam(item.type)) {

                return this.helper.isNotNone(item.defaultValue);
            }
            else if (this.isTimeParam(item.type)) {
                return item.defaultValue instanceof Date;
            }
            else if (this.isTimeRangeParam(item.type)) {
            
                let value = item.defaultValue;
                return value instanceof Array && value.length == 2 && value[0] instanceof Date && value[1] instanceof Date;
            }
            else {
                return true;
            }
        });

        return oks.length == requires.length;
    }

    convert2TimeStamp(value) {
        
        const fmt_time = 'hh:mm:ss';
        const today = new Date().format('yyyy-MM-dd');

        if (value instanceof Date) {
            return new Date(`${today} ${value.format(fmt_time)}`).getTime();
        }
        else if (value instanceof Array && value.length >= 1 && value[0] instanceof Date) {
            return new Date(`${today} ${value[0].format(fmt_time)}`).getTime();
        }
        else if (typeof value == 'number' && value.toString().length == 6 || typeof value == 'string' && value.length == 6) {

            let hms = value.toString();
            return new Date(`${today} ${hms.substring(0, 2)}:${hms.substring(2, 4)}:${hms.substring(4, 6)}`).getTime();
        }

        return null;
    }

    collectAlgoParamsInput() {

        let params = {};
        this.algoParams.forEach(item => {

            const value = item.defaultValue;

            if (this.isIntegerParam(item.type) || 
                this.isDecimalParam(item.type) || 
                this.isTextParam(item.type) || 
                this.isUserOptionParam(item.type)) {

                params[item.prop] = value
            }
            else if (this.isTimeParam(item.type)) {
                params[item.prop] = value instanceof Date ? value.getTime() : null;
            }
            else if (this.isTimeRangeParam(item.type)) {

                params[item.prop] = value instanceof Array && 
                                    value.length == 2 &&
                                    value[0] instanceof Date && 
                                    value[1] instanceof Date ?
                                    { start: value[0].getTime(), end: value[1].getTime() } : null;
            }
            else {
                params[item.prop] = value;
            }
        });

        /**
         * 从动态参数里，提取【开始时间】与【结束时间】
         */

        const param_start = this.algoParams.find(x => this.startTimeNames.some(name => typeof x.prop == 'string' && x.prop.toLowerCase() == name));
        const param_end = this.algoParams.find(x => this.stopTimeNames.some(name => typeof x.prop == 'string' && x.prop.toLowerCase() == name));
        const effectiveTime = { value: null };
        const expireTime = { value: null };

        if (param_start) {

            effectiveTime.value = this.convert2TimeStamp(param_start.defaultValue);
            // 删除动态参数里对应的【开始时间】
            delete params[param_start.prop];
        }
        
        if (param_end) {

            expireTime.value = this.convert2TimeStamp(param_end.defaultValue);
            // 删除动态参数里对应的【结束时间】
            delete params[param_end.prop];
        }

        return { params, effectiveTime, expireTime };
    }

    /**
     * 根据算法类别及账号，查询可以适配的算法
     */
    async queryQualifiedAlgoes(algoClass, accountId) {

        var resp = await repoAlgo.queryAlgoesV2403(algoClass, accountId);
        var { errorCode, errorMsg, data } = resp;
        if (errorCode != 0) {
            return this.interaction.showError(`算法加载失败：${errorCode}/${errorMsg}`);
        }

        var algoes = Array.isArray(data) ? data.map(x => new GaoyuAlgo(x)) : [];
        var map = algoes.groupBy(x => x.brokerId);
        var flattends = [];
        this.algoGrps.clear();
        
        for (let key in map) {

            let gmembers = map[key];
            this.algoGrps.push(new GaoyuAlgoGroup(key, gmembers));
            flattends.merge(gmembers);
        }

        this.algoes.refill(flattends);
    }
}

module.exports = { AlgoParamPartView };