const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');
const { SmartTable } = require('../../libs/table/smart-table');

class View extends BaseAdminView {
    get repoRole() {
        return require('../../repository/role').repoRole;
    }

    constructor(view_name) {
        super(view_name, '角色管理');
        this.$container = null;
        this.roleList = [];
        this.roleListHash = {};
        this.roleTypeList = [];
        this.roleStatusList = [
            { code: 1, mean: '启用', status: true },
            { code: 0, mean: '禁用', status: false },
        ];
        this.searching = {
            prop: ['roleName'],
            value: '',
        };
        this.originalRoleForm = {
            roleName: null,
            activeFlag: true,
            id: null,
            description: null,
        };
        this.rightTabs = [];

        this.cached = {
            fullPermissionList: false,
        };

        this.fullPermissionList = null;
        this.menus = [];
        this.lastRefreshTime = new Date();
    }

    async requestRoleList() {

        var loading = this.interaction.showLoading({ text: '请求角色列表...' });

        try {

            var resp = await this.repoRole.getAll();
            var role_list = resp.data;
            role_list.forEach(role => { this.roleListHash[role.id] = role; });
            role_list.orderByDesc(cdt => cdt.id);
            this.roleList.refill(role_list);
        } 
        catch (ex) {
            this.interaction.showHttpError(`角色数据服务异常：${ex.httpCode}/${ex.message}`);
        } 
        finally {
            loading.close();
        }
    }

    createApp() {
        var controller = this;
        this.tableProps = { maxHeight: 500, ...this.systemSetting.tableProps };
        this.vueApp = new Vue({

            el: this.$container.querySelector('.role-view-root'),
            mixins: [],
            directives: {
                drag,
            },
            data: {
                searching: this.searching,
                filters: [this.searching],
                roleList: this.roleList,
                roleTypeList: this.roleTypeList,
                roleStatusList: this.roleStatusList,
                rightTabs: this.rightTabs,
                tableProps: this.tableProps,
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },
                searchDef: {
                    inputProps: {
                        placeholder: '请输入搜索关键字',
                        prefixIcon: 'el-icon-search',
                    },
                    props: ['roleName'],
                },
                dialog: {
                    role: {
                        isCreation: true,
                        visible: false,
                        rules: {
                            roleName: [{ type: 'string', required: true, message: '请输入角色名称' }, this.systemSetting.specialCharacterFilterRule, this.systemSetting.limitInputLengthRule],
                        },
                        form: this.helper.deepClone(this.originalRoleForm),
                    },
                    roleRights: {
                        title: null,
                        visible: false,
                        currentRow: null,
                    },
                },
                menuData: [],
                selected: [],
            },
            components: {
                DataTables: DataTables.DataTables,
            },
            mounted() {},
            methods: {
                handleSelect(val) {
                    this.selected = val;
                },
                refresh() {
                    controller.refresh();
                },
                showCreationDialog: function() {
                    this.dialog.role.isCreation = true;
                    this.dialog.role.form = controller.helper.deepClone(controller.originalRoleForm);
                    this.dialog.role.visible = true;
                    this.$nextTick(() => {
                        this.$refs['form-editing-role'].clearValidate();
                    });
                },
                isOrgRole: function(role_data) {
                    return role_data.orgId === undefined || role_data.orgId;
                },
                async configRights(role_data) {
                    var result = await Promise.all([controller.getRoleRightsFullList(), controller.getRoleRights(role_data)]);
                    if (result.every(resp => resp.flag)) {
                        role_data.permissions = result[1].data;
                    }
                    this.dialog.roleRights.currentRow = role_data;
                    this.dialog.roleRights.title = `配置角色 ${role_data.roleName} 权限`;
                    this.dialog.roleRights.visible = true;
                    this.$nextTick(() => {
                        this.toggleSelection();
                    });
                },
                toggleSelection() {
                    let permissions = this.dialog.roleRights.currentRow.permissions;
                    this.$refs.roleTable.clearSelection();
                    console.log(permissions, this.menuData);
                    permissions.forEach(per => {
                        let matched = this.menuData.find(menu => menu.id == per.menuId);

                        if (matched) {
                            this.$refs.roleTable.toggleRowSelection(matched);
                        } else {
                            this.menuData.forEach(menu => {
                                if (menu.children) {
                                    let matched = menu.children.find(sub => sub.id == per.menuId);
                                    if (matched) {
                                        this.$refs.roleTable.toggleRowSelection(matched);
                                    }
                                }
                            });
                        }
                    });
                },
                toggleEnableStatus: async function(role_data) {
                    let result = await controller.saveRole(Object.assign({}, role_data, { activeFlag: role_data.activeFlag }));
                    if (result.flag) {
                        this.updateTable(result.data);
                    }
                },
                showEditDialog: function(role_data) {
                    this.renderForm(role_data);
                    this.dialog.role.isCreation = false;
                    this.dialog.role.visible = true;
                },
                renderForm(row) {
                    let form = this.dialog.role.form;
                    for (const key in form) {
                        if (row.hasOwnProperty(key)) {
                            this.dialog.role.form[key] = row[key];
                        }
                    }
                },
                confirmSaveRole: function() {
                    this.$refs['form-editing-role'].validate(async valid => {
                        if (valid) {
                            let result = await controller.saveRole(this.dialog.role.form);
                            if (result.flag) {
                                this.dialog.role.visible = false;
                                this.updateTable(result.data);
                            }
                        }
                    });
                },
                updateTable: result => {
                    if (this.roleListHash[result.id] === undefined) {
                        this.roleList.unshift(result);
                    } else {
                        var targetRole = controller.roleList.find(cdt => cdt.id == result.id);
                        this.helper.extend(targetRole, result);
                    }
                    this.roleListHash[result.id] = result;
                },
                async confirmSaveRoleRights() {
                    let rights = this.selected.map(tab => ({
                        menuId: tab.id,
                        menuName: tab.menuName,
                        permissions: [],
                        parentMenuId: tab.parentMenuId,
                    }));
                    console.log(rights);
                    let flag = await controller.saveRoleRights(this.dialog.roleRights.currentRow.id, rights);
                    if (flag) {
                        this.dialog.roleRights.visible = false;
                        let roleData = Object.assign({}, this.dialog.roleRights.currentRow, {
                            permissions: rights,
                        });
                        this.updateTable(roleData);
                    }
                },
                handleTabChange(val, id) {
                    let matchedTab = controller.rightTabs.find(tab => tab.id == id);
                    if (!val) {
                        matchedTab.selectedRights = [];
                    } else {
                        for (const permission of matchedTab.rights) {
                            if (permission.defaultPermission) {
                                matchedTab.selectedRights.push(permission.id);
                            }
                        }
                    }
                },
                handleRightChange(id) {
                    let matchedTab = controller.rightTabs.find(tab => tab.id == id);
                    if (matchedTab.selectedRights.length > 0) {
                        matchedTab.selected = true;
                        for (const permission of matchedTab.rights) {
                            if (permission.defaultPermission && !matchedTab.selectedRights.includes(permission.id)) {
                                matchedTab.selectedRights.push(permission.id);
                            }
                        }
                    }
                },
                removeRole(row) {
                    controller.interaction.showConfirm({
                        title: '警告',
                        message: '确定要删除当前角色吗？',
                        confirmed: () => {
                            let roleId = row.id;
                            controller.removeFromRoleList(roleId).then(flag => {
                                if (flag) {
                                    controller.roleList.remove(cdt => cdt.id == roleId);
                                    delete controller.roleListHash[roleId];
                                }
                            });
                        },
                    });
                },
            },
        });

        this.vueApp.$nextTick(() => {
            this.resizeWindow();
        });
    }

    async saveRole(info) {
        let output = {
            flag: false,
            data: null,
        };
        try {
            const resp = info.id ? await this.repoRole.updateRole(info) : await this.repoRole.createRole(info);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('保存角色成功');
                output.flag = true;
                if (typeof resp.data !== 'undefined') {
                    output.data = resp.data;
                }
            } else {
                console.log(resp);
                this.interaction.showHttpError(`保存角色失败，详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (error) {
            console.error(error);
            this.interaction.showError('保存角色失败');
        }
        return Promise.resolve(output);
    }

    async saveRoleRights(role_id, rights) {
        /**
         * 由于后台返回的数据带有比较神奇的$ref 这样的数据，会导致 在编辑权限的时候，产生一个{id: undefined},
         * 服务器会报 500，因此，将这个undefined过滤掉
         */
        rights.forEach(right => {
            right.permissions = right.permissions.filter(cdt => cdt.id !== undefined);
        });
        let flag = false;
        try {
            const resp = await this.repoRole.saveRoleRights(role_id, rights);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('保存角色权限成功');
                flag = true;
            } else {
                console.log(resp);
                this.interaction.showHttpError('保存角色权限失败');
            }
        } catch (error) {
            console.error(error);
            this.interaction.showError('保存角色权限失败');
        }

        return flag;
    }

    async getRoleRights(roleData) {
        let output = {
            flag: false,
            data: null,
        };
        if (typeof roleData.permissions !== 'undefined') {
            output.data = roleData.permissions;
            output.flag = true;
            return Promise.resolve(output);
        }
        try {
            const resp = await this.repoRole.getRoleRights(roleData.id);
            if (resp.errorCode == 0) {
                this.renderSelectedTabs(resp.data);
                output.flag = true;
                output.data = resp.data;
            } else {
                console.log(resp);
                output.flag = false;
                this.interaction.showHttpError(`获取角色权限失败,详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (error) {
            console.error(error);
            output.flag = false;
            this.interaction.showError('获取角色权限失败');
        }
        return Promise.resolve(output);
    }

    async removeFromRoleList(roleId) {
        let flag = false;

        let loading = this.interaction.showLoading({
            text: '正在操作中...',
        });
        try {
            const resp = await this.repoRole.removeRole(roleId);
            if (resp.errorCode === 0) {
                flag = true;
                this.interaction.showSuccess('删除角色成功!');
            } else {
                this.interaction.showHttpError('删除角色失败，详细信息:"' + resp.errorMsg + '"');
            }
        } catch (exp) {
            this.interaction.showHttpError('删除角色失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(flag);
    }

    /**
     * 获取角色列表 单次执行
     */
    async getRoleRightsFullList() {
        let output = {
            flag: false,
        };
        if (this.cached.fullPermissionList) {
            output.flag = true;
            return Promise.resolve(output);
        }
        try {
            const resp = await this.repoRole.getRoleRightsFullList();
            if (resp.errorCode == 0) {
                this.cached.fullPermissionList = true;
                this.fullPermissionList = resp.data;
                this.renderRightTabs(resp.data);
                output.flag = true;
            } else {
                console.log(resp);
                output.flag = false;
                this.interaction.showHttpError('获取全量配置失败');
            }
        } catch (error) {
            console.error(error);
            output.flag = false;
            this.interaction.showError('获取全量配置失败');
        }
        return Promise.resolve(output);
    }

    renderRightTabs(data) {
        let tabs = this.rightTabs;

        for (const permission of data) {
            let matched = tabs.find(tab => tab.id == permission.menuId);
            if (matched) {
                matched.rights.push(permission);
            } else {
                tabs.push({
                    name: permission.menuName,
                    selected: false,
                    selectedRights: [],
                    rights: [permission],
                    id: permission.menuId,
                });
            }
        }

        tabs.forEach(tab => {
            let queryIndex = -1;
            tab.rights.forEach((right, index) => {
                if (right.permissionName === '查询') {
                    queryIndex = index;
                }
            });

            if (queryIndex !== 0 && queryIndex !== -1) {
                let temp = tab.rights[0];
                tab.rights[0] = tab.rights[queryIndex];
                tab.rights[queryIndex] = temp;
            }
        });
    }

    renderSelectedTabs(data) {
        console.log(data);
        let tabs = this.rightTabs;
        for (const tab of tabs) {
            tab.selected = false;
            tab.selectedRights = [];
        }
        for (const row of data) {
            let matchedTab = tabs.find(tab => tab.id == row.menuId) || {};
            matchedTab.selected = true;
            matchedTab.selectedRights = row.permissions ? row.permissions.map(per => per.id) : [];
        }
    }

    async getAllMenus() {
        let resp = await this.repoRole.getAllMenus();
        if (resp.errorCode == 0) {
            this.menus = resp.data;
            this.renderTableData();
        }
    }

    renderTableData() {
        let raw = this.helper.deepClone(this.menus);
        let menus = [],
            raw_menus = [];
        raw_menus = raw.sort((a, b) => b.id - a.id);

        for (let i = raw_menus.length - 1; i >= 0; i--) {
            const raw_menu = raw_menus[i];
            if (!raw_menu.parentMenuId) {
                menus.push(raw_menu);
                raw_menus.splice(i, 1);
            }
        }
        if (raw_menus.length > 0) {
            raw_menus.forEach(raw_menu => {
                let matched = menus.find(menu => menu.id == raw_menu.parentMenuId);
                if (matched) {
                    if (!matched.children) {
                        matched.children = [];
                    }
                    matched.children.push(raw_menu);
                }
            });
        }
        
        this.vueApp.menuData = menus;
    }

    resizeWindow() {

        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.maxHeight = winHeight - 150;
    }

    createTable4Export() {

        const $table = this.$container.querySelector('.table-4-role-list-export');
        this.table4Export = new SmartTable($table, (rd) => rd.id, this, { tableName: 'smt-xf231', displayName: this.title });
    }

    formatActivation(row) {
        return row.activeFlag ? '启用' : '已禁用';
    }

    refresh() {

        let now = new Date();
        if (now.getTime() - this.lastRefreshTime.getTime() >= 2000) {
            this.requestRoleList();
            this.lastRefreshTime = now;
        } 
        else {
            this.interaction.showWarning('您的操作太快，2S后重试！');
        }
    }

    exportSome() {

        this.table4Export.refill(this.roleList);
        this.table4Export.exportAllRecords(`系统角色-${new Date().format('yyyyMMdd')}`);
    }

    build($container) {

        this.$container = $container;
        this.createApp();
        this.createTable4Export();
        this.requestRoleList();
        this.getAllMenus();
        window.onresize = () => {
            this.throttle(this.resizeWindow.bind(this));
        };
    }
}

module.exports = View;
