.algo-design {

    .input-default-value {
        .el-form-item__content {
            > * {
                width: 100%;
            }
        }
    }

    .option-item {

        display: flex;
        gap: 10px;
        align-items: center;

        .option-item-label {
            flex: 1 1 50px;
        }

        .option-item-delete {
            flex: 0 0 20px;
        }
    }

    .select-user-options {

        display: flex;
        gap: 10px;
        align-items: center;

        .el-select {
            flex: 1 1 50px;
        }

        .add-button {
            flex: 0 0 20px;
        }
    }

    .el-radio-button__inner {
        padding: 0 15px;
        line-height: 20px;
    }
}