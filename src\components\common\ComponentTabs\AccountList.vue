<script setup lang="tsx">
import VirtualizedTable from '../../common/VirtualizedTable.vue';
import AccountBasicInfoForm from '../../AccountView/AccountBasicInfoForm.vue';
import AccountEquityForm from '../../AccountView/AccountEquityForm.vue';
import AccountFeeForm from '../../AccountView/AccountFeeForm.vue';
import AccountIoMoneyForm from '../../AccountView/AccountIoMoneyForm.vue';
import AccountTransferForm from '../../AccountView/AccountTransferForm.vue';
import RuleOverview from '../../RiskTemplateView/EntityEntrance/RuleOverview.vue';
import { onMounted, reactive, ref, useTemplateRef, nextTick, inject, computed } from 'vue';
import { ElMessage, ElMessageBox, TableV2SortOrder } from 'element-plus';
import { getEmptyTableColumnConfig, type ColumnDefinition, type RowAction } from '@/types';
import { remove, renderLabel, hasPermission } from '@/script';
import { TABLE_COLUMN_SELECT_KEY } from '@/keys';
import { MenuPermitAccountManagement, MenuPermitTrade } from '@/enum';

import {
  Repos,
  type AccountEquityInfo,
  type LegacyAccountInfo,
  type MomWorkflow,
  AccountStatusEnum,
  IdentityType,
} from '../../../../../xtrade-sdk/dist';
import { deleteConfirm } from '@/script/interaction';
import {
  accountNameCol,
  financeAccountCol,
  fundNameCol,
  assetTypeCol,
  balanceCol,
  availableCol,
  marginCol,
  marketValueCol,
  risePercentCol,
  navCol,
  positionProfitCol,
  closeProfitCol,
  preBalanceCol,
  loanBuyBalanceCol,
  loanSellBalanceCol,
  commissionCol,
  brokerNameCol,
  orgNameCol,
  frozenMarginCol,
} from './shared/columnDefinitions';

interface CellRenderParam {
  rowData: LegacyAccountInfo;
  cellData: any;
}

interface ContextFundInfo {
  fundId: string | null;
  fundName: string | null;
  isBounded?: boolean;
  isAvailable?: boolean;
  boundedAccountIds?: string[];
}

const repoInstance = new Repos.GovernanceRepo();
const recordsRepo = new Repos.RecordsRepo();
const adminRepo = new Repos.AdminRepo();

const { contextual, trade } = defineProps<{
  /** 上下文基金信息 */
  contextual?: ContextFundInfo;
  /** 是否交易界面 */
  trade?: boolean;
}>();

const emitter = defineEmits<{
  bind: [data: LegacyAccountInfo[]];
  unbind: [data: LegacyAccountInfo[]];
  'row-click': [row: LegacyAccountInfo];
  refresh: [];
  'account-ids-change': [ids: string];
}>();

const isRootMode = computed(() => !contextual);
const is4Bounded = computed(() => contextual && contextual.isBounded === true);
const is4Available = computed(() => contextual && contextual.isAvailable === true);
const boundedAccountIds = computed(() => contextual?.boundedAccountIds || []);

// 流程列表
const workflows = ref<MomWorkflow[]>([]);

// 基础列定义
const columns = trade
  ? ([
      {
        ...accountNameCol,
        cellRenderer: renderNameCol,
        fixed: true,
      },
      financeAccountCol,
      fundNameCol,
      assetTypeCol,
      balanceCol,
      availableCol,
      marginCol,
      marketValueCol,
      risePercentCol,
      navCol,
      {
        key: 'takeRisk',
        title: '风险度（无字段）',
        width: 100,
        sortable: true,
        align: 'right',
      },
      positionProfitCol,
      closeProfitCol,
      preBalanceCol,
      loanBuyBalanceCol,
      loanSellBalanceCol,
      {
        key: 'totalDebt',
        title: '负债（无字段）',
        width: 100,
        sortable: true,
        align: 'right',
      },
      {
        key: 'withdrawQuota',
        title: '可取资金（无字段）',
        width: 100,
        sortable: true,
        align: 'right',
      },
      commissionCol,
      brokerNameCol,
      {
        key: 'connectionStatus',
        title: '连接状态',
        width: 100,
        sortable: true,
        cellRenderer: renderConnectionSwitch,
      },
      { key: 'accountId', title: 'ID', width: 100, sortable: true },
    ] as ColumnDefinition<LegacyAccountInfo>)
  : ([
      {
        ...accountNameCol,
        cellRenderer: renderNameCol,
        fixed: true,
      },
      assetTypeCol,
      orgNameCol,
      brokerNameCol,
      {
        key: 'connectionStatus',
        title: '连接状态',
        width: 100,
        sortable: true,
        cellRenderer: renderConnectionSwitch,
      },
      {
        key: 'status',
        title: '状态',
        width: 80,
        sortable: true,
        cellRenderer: renderStatusSwitch,
      },
      {
        key: 'preRiskControl',
        title: '盘前风控',
        width: 100,
        sortable: true,
        cellRenderer: renderPreRiskControlSwitch,
      },
      {
        key: 'autoApprove',
        title: '快速审核',
        width: 100,
        sortable: true,
        cellRenderer: renderAutoApproveSwitch,
      },
      {
        key: 'availableCheck',
        title: '资金检查',
        width: 100,
        sortable: true,
        cellRenderer: renderAvailableCheckSwitch,
      },
      {
        key: 'positionCheck',
        title: '持仓检查',
        width: 100,
        sortable: true,
        cellRenderer: renderPositionCheckSwitch,
      },
      {
        key: 'workFlowName',
        title: '绑定流程',
        width: 150,
        sortable: true,
        dynamicWidth: false,
        cellRenderer: renderWorkflowSelect,
        textRenderer: (cellData: any) =>
          renderLabel(
            cellData,
            workflows.value.map(x => ({
              label: x.workFlowName,
              value: x.id,
            })),
          ),
      },
      balanceCol,
      marketValueCol,
      availableCol,
      {
        key: 'profitRatio',
        title: '收益率',
        width: 100,
        sortable: true,
        align: 'right',
        cellRenderer: renderPercentage,
      },
      positionProfitCol,
      navCol,
      preBalanceCol,
      frozenMarginCol,
      closeProfitCol,
      loanBuyBalanceCol,
      loanSellBalanceCol,
      commissionCol,
      marginCol,
      { key: 'accountId', title: 'ID', width: 100, sortable: true },
    ] as ColumnDefinition<LegacyAccountInfo>);

// 权限判断
const canCreate = computed(() => hasPermission(MenuPermitAccountManagement.创建账号));
const canEdit = computed(() =>
  hasPermission(trade ? MenuPermitTrade.修改账号 : MenuPermitAccountManagement.修改账号),
);
const canDelete = computed(() => hasPermission(MenuPermitAccountManagement.删除账号));
const canDownload = computed(() => hasPermission(MenuPermitAccountManagement.下载账号列表));
const canColumnConfig = computed(() => hasPermission(MenuPermitAccountManagement.列配置));
const canAvailableCheck = computed(() => hasPermission(MenuPermitAccountManagement.资金检查));
const canPositionCheck = computed(() => hasPermission(MenuPermitAccountManagement.持仓检查));
const canConnectionSwitch = computed(() => hasPermission(MenuPermitAccountManagement.切换连接状态));
const canStatusSwitch = computed(() => hasPermission(MenuPermitAccountManagement.切换状态));
const canPreRiskControl = computed(() => hasPermission(MenuPermitAccountManagement.盘前风控));
const canQuickReview = computed(() => hasPermission(MenuPermitAccountManagement.快速审核));
const canBindWorkflow = computed(() => hasPermission(MenuPermitAccountManagement.绑定流程));

const canFeeManagement = computed(() => hasPermission(MenuPermitAccountManagement.费用设置));
const canEquityMaintenance = computed(() => hasPermission(MenuPermitAccountManagement.权益维护));
const canRiskControl = computed(() => hasPermission(MenuPermitAccountManagement.风控设置));
const canInOutManagement = computed(() => hasPermission(MenuPermitAccountManagement.出入金维护));
const canFundTransfer = computed(() => hasPermission(MenuPermitAccountManagement.资金划转));
const canSyncFuturesContract = computed(() =>
  hasPermission(MenuPermitAccountManagement.同步期货合约),
);
const canSyncFuturesFee = computed(() => hasPermission(MenuPermitAccountManagement.同步期货费率));
const canCompareAll = computed(() => hasPermission(MenuPermitAccountManagement.全部比对));
const canOverrideAll = computed(() => hasPermission(MenuPermitAccountManagement.全部覆盖));
const canFundCompare = computed(() => hasPermission(MenuPermitAccountManagement.资金比对));
const canFundOverride = computed(() => hasPermission(MenuPermitAccountManagement.资金覆盖));

const nestedActions: RowAction<LegacyAccountInfo>[] = [
  {
    label: '权益维护',
    icon: 'setting',
    show: () => canEquityMaintenance.value,
    onClick: row => {
      const {
        id,
        preBalance,
        available,
        frozenMargin,
        loanBuyBalance,
        loanSellBalance,
        loanSellQuota,
      } = row;
      const equity_info: AccountEquityInfo = {
        account_id: id,
        pre_balance: preBalance,
        available: available,
        frozen: frozenMargin,
        buy_balance: loanBuyBalance,
        sell_balance: loanSellBalance,
        sell_quota: loanSellQuota,
      };

      dialogEquity.visible = true;
      nextTick(() => {
        $equityForm.value!.reset(equity_info);
      });
    },
  },
  {
    label: '费用设置',
    icon: 'setting',
    show: () => canFeeManagement.value,
    onClick: row => {
      const { accountId, accountName } = row;
      dialogFee.visible = true;
      nextTick(() => {
        $feeForm.value!.reset(accountId, accountName);
      });
    },
  },
  {
    label: '风控设置',
    icon: 'setting',
    show: () => canRiskControl.value,
    onClick: row => {
      openRiskSetting(row);
    },
  },
  {
    label: '出入金维护',
    icon: 'setting',
    show: () => canInOutManagement.value,
    onClick: row => {
      openIoMoneyDialog(row);
    },
  },
  {
    label: '资金划转',
    icon: 'setting',
    show: () => canFundTransfer.value,
    onClick: row => {
      openTransferDialog(row);
    },
  },
  {
    label: '同步期货合约',
    icon: 'setting',
    show: () => canSyncFuturesContract.value,
    onClick: row => {
      syncFutureInstruments(row);
    },
  },
  {
    label: '同步期货费率',
    icon: 'setting',
    show: () => canSyncFuturesFee.value,
    onClick: row => {
      syncFutureFees(row);
    },
  },
  {
    label: '全部比对',
    icon: 'setting',
    show: () => canCompareAll.value,
    onClick: row => {
      takeAction(row, '比对', repoInstance.CompareAccount);
    },
  },
  {
    label: '全部覆盖',
    icon: 'setting',
    show: () => canOverrideAll.value,
    onClick: row => {
      takeAction(row, '覆盖', repoInstance.OverwriteAccount);
    },
  },
  {
    label: '资金比对',
    icon: 'setting',
    show: () => canFundCompare.value,
    onClick: row => {
      compareAccountFinance(row);
    },
  },
  {
    label: '资金覆盖',
    icon: 'setting',
    show: () => canFundOverride.value,
    onClick: row => {
      takeAction(row, '资金覆盖', repoInstance.OverwriteAccountFinance);
    },
  },
  {
    label: '删除',
    icon: 'setting',
    type: 'danger',
    show: () => canDelete.value,
    onClick: row => {
      deleteRow(row);
    },
  },
];

const editAction: RowAction<LegacyAccountInfo> = {
  label: '编辑',
  icon: 'edit',
  show: () => canEdit.value,
  onClick: row => {
    editAccount(row);
  },
};

const collapsedAction: RowAction<LegacyAccountInfo> = {
  label: '操作',
  icon: 'monitor',
  onClick: row => {
    console.log('to operate account', row);
  },
  nesteds: nestedActions,
};

// 行操作
const rowActions = computed<RowAction<LegacyAccountInfo>[]>(() => {
  if (is4Bounded.value) {
    return [];
  } else {
    return [editAction, collapsedAction];
  }
});

async function takeAction(
  row: LegacyAccountInfo,
  action: string,
  method: (account_id: string) => Promise<any>,
) {
  const choice = await ElMessageBox.confirm(
    `对账号 "${row.accountName}" 进行 "${action}" 吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  );
  if (choice === 'confirm') {
    const { errorCode, errorMsg } = await method.call(repoInstance, row.id);
    if (errorCode === 0) {
      ElMessage.success('操作成功');
    } else {
      ElMessage.error(errorMsg || '操作失败');
    }
  }
}

// 处理账号状态变更
async function handleStatusChange(row: LegacyAccountInfo, enabled: boolean) {
  const newStatus = enabled ? AccountStatusEnum.Enabled : AccountStatusEnum.Disabled;
  const { errorCode, errorMsg } = await repoInstance.UpdateAccount({
    ...row,
    status: newStatus,
  });

  if (errorCode === 0) {
    row.status = newStatus;
    ElMessage.success('状态更新成功');
  } else {
    ElMessage.error(errorMsg || '状态更新失败');
  }
}

// 处理盘前风控变更
async function handlePreRiskControlChange(row: LegacyAccountInfo, enabled: boolean) {
  if (!row.workFlowId) {
    ElMessage.error('请先绑定流程');
    return;
  }

  const { errorCode, errorMsg } = await repoInstance.setAccountPreRiskControl(row.id, enabled);

  if (errorCode === 0) {
    row.preRiskControl = enabled;
    ElMessage.success('盘前风控设置成功');
  } else {
    ElMessage.error(errorMsg || '盘前风控设置失败');
  }
}

// 处理快速审核变更
async function handleAutoApproveChange(row: LegacyAccountInfo, enabled: boolean) {
  if (!row.workFlowId) {
    ElMessage.error('请先绑定流程');
    return;
  }

  const { errorCode, errorMsg } = await repoInstance.setAccountAutoApprove(row.id, enabled);

  if (errorCode === 0) {
    row.autoApprove = enabled;
    ElMessage.success('快速审核设置成功');
  } else {
    ElMessage.error(errorMsg || '快速审核设置失败');
  }
}

// 处理资金检查变更
async function handleAvailableCheckChange(row: LegacyAccountInfo, enabled: boolean) {
  const { errorCode, errorMsg } = await repoInstance.setAccountAvailableCheck(row.id, enabled);

  if (errorCode === 0) {
    row.availableCheck = enabled;
    ElMessage.success('资金检查设置成功');
  } else {
    ElMessage.error(errorMsg || '资金检查设置失败');
  }
}

// 处理持仓检查变更
async function handlePositionCheckChange(row: LegacyAccountInfo, enabled: boolean) {
  const { errorCode, errorMsg } = await repoInstance.setAccountPositionCheck(row.id, enabled);

  if (errorCode === 0) {
    row.positionCheck = enabled;
    ElMessage.success('持仓检查设置成功');
  } else {
    ElMessage.error(errorMsg || '持仓检查设置失败');
  }
}

// 处理连接状态变更
async function handleConnectionStatusChange(row: LegacyAccountInfo, enabled: boolean) {
  const action = enabled ? '连接' : '断开连接';
  const choice = await ElMessageBox.confirm(`是否${action}${row.accountName}账号？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  });

  if (choice === 'confirm') {
    const method = enabled ? repoInstance.ConnectAccount : repoInstance.DisconnectAccount;
    const { errorCode, errorMsg } = await method.call(repoInstance, row.id);

    if (errorCode === 0) {
      row.connectionStatus = enabled;
      ElMessage.success(`${action}成功`);
      request();
    } else {
      ElMessage.error(errorMsg || `${action}失败`);
    }
  }
}

// 处理流程绑定变更
async function handleWorkflowChange(row: LegacyAccountInfo, workflowId?: number | null) {
  const { errorCode, errorMsg } = await adminRepo.BindProcedure(
    parseInt(row.id),
    workflowId || undefined,
  );

  if (errorCode === 0) {
    row.workFlowId = workflowId || undefined;
    const workflow = workflows.value.find(w => w.id === workflowId);
    row.workFlowName = workflow?.workFlowName || '';
    ElMessage.success(`流程${workflowId ? '绑定' : '取消绑定'}成功`);
  } else {
    ElMessage.error(errorMsg || `流程${workflowId ? '绑定' : '取消绑定'}失败`);
  }
}

const tableCfgParams = inject(TABLE_COLUMN_SELECT_KEY, reactive(getEmptyTableColumnConfig()));

function renderNameCol(params: CellRenderParam) {
  const { accountName, connectionStatus } = params.rowData;
  return (
    <div class="name-cell">
      <div class={[connectionStatus ? 'bg-green' : 'bg-red', 'status', 'toe']}></div>
      <div class="name toe">{accountName}</div>
    </div>
  );
}

function renderPercentage(params: CellRenderParam) {
  return <span>{((params.cellData as number) * 100).toFixed(2)}%</span>;
}

function renderStatusSwitch(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-switch
      model-value={rowData.status === AccountStatusEnum.Enabled}
      disabled={!canStatusSwitch.value}
      onChange={(value: boolean) => handleStatusChange(rowData, value)}
    />
  );
}

function renderPreRiskControlSwitch(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-switch
      model-value={rowData.preRiskControl}
      disabled={!canPreRiskControl.value}
      onChange={(value: boolean) => handlePreRiskControlChange(rowData, value)}
    />
  );
}

function renderAutoApproveSwitch(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-switch
      model-value={rowData.autoApprove}
      disabled={!canQuickReview.value}
      onChange={(value: boolean) => handleAutoApproveChange(rowData, value)}
    />
  );
}

function renderAvailableCheckSwitch(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-switch
      model-value={rowData.availableCheck}
      disabled={!canAvailableCheck.value}
      onChange={(value: boolean) => handleAvailableCheckChange(rowData, value)}
    />
  );
}

function renderPositionCheckSwitch(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-switch
      model-value={rowData.positionCheck}
      disabled={!canPositionCheck.value}
      onChange={(value: boolean) => handlePositionCheckChange(rowData, value)}
    />
  );
}

function renderConnectionSwitch(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-switch
      model-value={rowData.connectionStatus}
      disabled={!canConnectionSwitch.value}
      onChange={(value: boolean) => handleConnectionStatusChange(rowData, value)}
    />
  );
}

function renderWorkflowSelect(params: CellRenderParam) {
  const { rowData } = params;
  return (
    <el-select
      model-value={rowData.workFlowId}
      placeholder="选择流程"
      clearable
      disabled={!canBindWorkflow.value}
      onChange={(value: number | null) => handleWorkflowChange(rowData, value)}
      style="width: 140px"
    >
      {workflows.value.map(workflow => (
        <el-option key={workflow.id} label={workflow.workFlowName} value={workflow.id} />
      ))}
    </el-select>
  );
}

const tableRef = useTemplateRef('tableRef');
const $basicForm = useTemplateRef('basicForm');
const $ioMoneyForm = useTemplateRef('ioMoneyForm');
const $transferForm = useTemplateRef('transferForm');
const records = ref<LegacyAccountInfo[]>([]);
const properRecords = computed(() => {
  const all = records.value;
  if (is4Bounded.value) {
    return all.filter(x => boundedAccountIds.value.includes(x.id));
  } else if (is4Available.value) {
    return all.filter(x => !boundedAccountIds.value.includes(x.id));
  }
  return all;
});

const dialogEdit = reactive({
  visible: false,
  title: '',
});

function configColumn() {
  Object.assign(tableCfgParams, {
    name: '账号管理',
    columns: columns.map(x => ({ title: x!.title || '', datakey: x!.key })),
    selected: [],
    callback: (selected: string[]) => {
      console.log('selected columns', selected);
    },
  });
}

function createAccount() {
  editAccount(null);
}

function editAccount(target: LegacyAccountInfo | null) {
  dialogEdit.visible = true;
  dialogEdit.title = target ? '编辑账号' : '创建账号';
  nextTick(() => {
    $basicForm.value!.reset(target);
  });
}

function cancelEditBasic() {
  dialogEdit.visible = false;
}

function unbindAccounts() {
  const rows = tableRef.value?.selectedRows || [];
  if (rows.length === 0) {
    ElMessage.error('请选择要解绑的账号');
    return;
  }

  emitter('unbind', rows);
}

function bindAccounts() {
  const rows = tableRef.value?.selectedRows || [];
  if (rows.length === 0) {
    ElMessage.error('请选择要绑定的账号');
    return;
  }

  emitter('bind', rows);
}

const $equityForm = useTemplateRef('equityForm');
const dialogEquity = reactive({
  visible: false,
  title: '',
});

function handleEquitySaved(item: AccountEquityInfo) {
  dialogEquity.visible = false;
  console.log('handleEquitySaved', item);
}

function handleEquityCanceled(item: AccountEquityInfo) {
  dialogEquity.visible = false;
  console.log('handleEquityCanceled', item);
}

const $feeForm = useTemplateRef('feeForm');
const dialogFee = reactive({
  visible: false,
  title: '',
});

// 出入金维护对话框
const dialogIoMoney = reactive({
  visible: false,
  accountName: '',
});

// 资金划转对话框
const dialogTransfer = reactive({
  visible: false,
  accountName: '',
});

function handleFeeSaved() {
  dialogFee.visible = false;
}

function handleFeeCanceled() {
  dialogFee.visible = false;
}

async function basicSaved(row: LegacyAccountInfo) {
  dialogEdit.visible = false;
  request();
}

async function deleteRow(row: LegacyAccountInfo) {
  const result = await deleteConfirm('删除账号', `确定要删除账号 "${row.accountName}" 吗？`);
  if (result) {
    const { errorCode, errorMsg } = await repoInstance.DeleteAccount(row.id);
    if (errorCode === 0) {
      ElMessage.success('删除成功');
      remove(records.value, x => x.id == row.id);
    } else {
      ElMessage.error(errorMsg || '删除失败');
    }
  }
}

// 账号风控设置对话框
const dialogRisk = reactive({
  visible: false,
  accountId: '',
  accountName: '',
  identityType: IdentityType.Account.value,
});

function openRiskSetting(account: LegacyAccountInfo) {
  const dlg = dialogRisk;
  dlg.accountId = account.accountId;
  dlg.accountName = account.accountName;
  dlg.visible = true;
}

function handleRiskSettingClosed() {
  const dlg = dialogRisk;
  dlg.accountId = '';
  dlg.accountName = '';
  dlg.visible = false;
}

async function request() {
  if (trade) {
    const { errorCode, data } = await repoInstance.QueryAccountBatch();
    if (errorCode === 0) {
      records.value = data || [];
      // 通知交易界面最新的账号id，用于我的委托/持仓/成交列表查询数据
      emitter('account-ids-change', records.value.map(x => x.id).join(';'));
    }
  } else {
    records.value = (await repoInstance.QueryAccounts()).data || [];
  }
}

function handleRowClick(row: LegacyAccountInfo) {
  emitter('row-click', row);
}

// 出入金维护
function openIoMoneyDialog(row: LegacyAccountInfo) {
  dialogIoMoney.accountName = row.accountName;
  dialogIoMoney.visible = true;
  nextTick(() => {
    $ioMoneyForm.value?.reset(row.id, row.accountName);
  });
}

function handleIoMoneySaved() {
  dialogIoMoney.visible = false;
  emitter('refresh');
}

function handleIoMoneyCanceled() {
  dialogIoMoney.visible = false;
}

// 资金划转
function openTransferDialog(row: LegacyAccountInfo) {
  dialogTransfer.accountName = row.accountName;
  dialogTransfer.visible = true;
  nextTick(() => {
    $transferForm.value?.reset(row.id, row.accountName);
  });
}

function handleTransferSaved() {
  dialogTransfer.visible = false;
}

function handleTransferCanceled() {
  dialogTransfer.visible = false;
}

// 同步期货合约
async function syncFutureInstruments(row: LegacyAccountInfo) {
  const choice = await ElMessageBox.confirm(
    `确定要同步账号 "${row.accountName}" 的期货合约吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  );

  if (choice === 'confirm') {
    const { errorCode, errorMsg } = await repoInstance.SyncFutureInstruments(row.id);
    if (errorCode === 0) {
      ElMessage.success('期货合约同步成功');
    } else {
      ElMessage.error(errorMsg || '期货合约同步失败');
    }
  }
}

// 同步期货费率
async function syncFutureFees(row: LegacyAccountInfo) {
  const choice = await ElMessageBox.confirm(
    `确定要同步账号 "${row.accountName}" 的期货费率吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  );

  if (choice === 'confirm') {
    const { errorCode, errorMsg } = await repoInstance.SyncFutureFees(row.id);
    if (errorCode === 0) {
      ElMessage.success('期货费率同步成功');
    } else {
      ElMessage.error(errorMsg || '期货费率同步失败');
    }
  }
}

// 资金比对
async function compareAccountFinance(row: LegacyAccountInfo) {
  const choice = await ElMessageBox.confirm(
    `确定要对账号 "${row.accountName}" 进行资金比对吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  );

  if (choice === 'confirm') {
    const { errorCode, errorMsg } = await recordsRepo.compareAccountFinance(row.id, false);
    if (errorCode === 0) {
      ElMessage.success('资金比对完成');
    } else {
      ElMessage.error(errorMsg || '资金比对失败');
    }
  }
}

// 加载流程列表
async function loadWorkflows() {
  const { errorCode, data } = await adminRepo.QueryProcedure();
  if (errorCode === 0) {
    workflows.value = data || [];
  }
}

onMounted(async () => {
  await Promise.all([request(), loadWorkflows()]);
  if (records.value.length > 0) {
    tableRef.value?.clickRow(records.value[0]);
  }
});
</script>

<template>
  <div class="account-list-view">
    <VirtualizedTable
      ref="tableRef"
      search-placeholder="搜索账号"
      :sort="{ key: 'id', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="properRecords"
      :row-actions="rowActions"
      :row-action-width="170"
      :select="!trade"
      :show-toolbar="!trade"
      fixed
      show-index
      @row-click="handleRowClick"
    >
      <template #actions>
        <div v-if="isRootMode" class="actions" flex aic>
          <el-button
            v-if="canColumnConfig"
            link
            size="small"
            class="typical-text-button"
            @click="configColumn"
          >
            <i class="iconfont icon-setting"></i>
            <span>列配置</span>
          </el-button>
          <el-button v-if="canDownload" link size="small" class="typical-text-button">
            <i class="iconfont icon-download"></i>
            <span>下载</span>
          </el-button>
          <el-button v-if="canCreate" type="primary" @click="createAccount">
            <i class="iconfont icon-add-new" mr-5></i>
            <span>新建账号</span>
          </el-button>
        </div>
        <div v-else-if="is4Bounded" class="actions" flex aic>
          <el-button type="primary" @click="unbindAccounts">
            <i class="iconfont icon-unlink" mr-5></i>
            <span>解除绑定</span>
          </el-button>
        </div>
        <div v-else-if="is4Available" class="actions" flex aic>
          <el-button type="primary" @click="bindAccounts">
            <i class="iconfont icon-add" mr-5></i>
            <span>绑定到产品</span>
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
    <el-dialog
      v-model="dialogEdit.visible"
      class="typical-dialog"
      width="624px"
      :title="dialogEdit.title"
      draggable
      destroy-on-close
      top="10vh"
    >
      <AccountBasicInfoForm
        ref="basicForm"
        @save="basicSaved"
        @cancel="cancelEditBasic"
      ></AccountBasicInfoForm>
    </el-dialog>
    <el-dialog
      v-model="dialogEquity.visible"
      class="typical-dialog"
      width="624px"
      title="权益维护"
      draggable
    >
      <AccountEquityForm
        ref="equityForm"
        @cancel="handleEquityCanceled"
        @save="handleEquitySaved"
      ></AccountEquityForm>
    </el-dialog>
    <el-dialog
      v-model="dialogFee.visible"
      class="typical-dialog"
      width="624px"
      title="费用设置"
      draggable
    >
      <AccountFeeForm
        ref="feeForm"
        @cancel="handleFeeCanceled"
        @save="handleFeeSaved"
      ></AccountFeeForm>
    </el-dialog>

    <!-- 出入金维护对话框 -->
    <el-dialog
      v-model="dialogIoMoney.visible"
      class="typical-dialog"
      width="500px"
      title="出入金维护"
      draggable
    >
      <AccountIoMoneyForm
        ref="ioMoneyForm"
        :account-name="dialogIoMoney.accountName"
        @save="handleIoMoneySaved"
        @cancel="handleIoMoneyCanceled"
      />
    </el-dialog>

    <!-- 资金划转对话框 -->
    <el-dialog
      v-model="dialogTransfer.visible"
      class="typical-dialog"
      width="600px"
      title="资金划转"
      draggable
    >
      <AccountTransferForm
        ref="transferForm"
        :account-name="dialogTransfer.accountName"
        @save="handleTransferSaved"
        @cancel="handleTransferCanceled"
      />
    </el-dialog>

    <!-- 账号风控设置对话框 -->
    <el-dialog
      v-model="dialogRisk.visible"
      @closed="handleRiskSettingClosed"
      class="typical-dialog"
      width="1300px"
      title="账号风控设置"
      draggable
    >
      <RuleOverview
        :identity-id="dialogRisk.accountId"
        :identity-name="dialogRisk.accountName"
        :identity-type="dialogRisk.identityType"
      ></RuleOverview>
    </el-dialog>
  </div>
</template>

<style scoped>
.account-list-view {
  height: 100%;
  width: 100%;
  :deep() {
    .name-cell {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .status {
      position: relative;
      top: 2px;
      width: 8px;
      height: 8px;
      border-radius: 4px;
    }
    .name {
      flex: 1 1 100px;
    }
  }
}
</style>
