const IView = require('../../../component/iview').IView;
const { TradeChannel } = require('../model/message');

class View extends IView {

    constructor(view_name) {

        super(view_name, false);
        var tchannel = new TradeChannel();
        this.channels = [tchannel];
        this.channels.pop();
        this.states = { selected: tchannel };
        this.states.selected = null;

        this.registerEvent('set-as-channels', this.setChannels.bind(this));
        this.registerEvent('set-default-channel', this.setAsChannel.bind(this));
    }

    createApp() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                states: this.states,
                channels: this.channels,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.handleSelect]),
        });
        
    }

    /**
     * @param {TradeChannel} channel
     */
    handleSelect(channel) {
        
        if (this.states.selected === channel) {
            return;
        }

        this.states.selected = channel;
        this.trigger('channel-selected', channel);
    }

    /**
     * @param {TradeChannel} channel 
     */
    setAsChannel(channel) {
        this.states.selected = channel;
    }

    /**
     * @param {Array<TradeChannel>} channels 
     */
    setChannels(channels) {

        this.channels.clear();
        this.channels.merge(channels);
        
        if (channels.length > 0 && this.helper.isNone(this.states.selected)) {
            this.states.selected = channels[0];
        }
        else {
            this.states.selected = null;
        }
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;
