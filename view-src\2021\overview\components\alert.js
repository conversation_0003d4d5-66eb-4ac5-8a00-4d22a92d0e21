const { IView } = require('../../../../component/iview');
const { SmartTable } = require('../../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { repoRisk } = require('../../../../repository/risk');
const { RiskMessage } = require('../../../../model/risk-message');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '风控消息');
    }

    /**
     * @param {RiskMessage} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    createTable($table) {
        
        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable($table, this.identifyRecord, this, { 
            tableName: 'smt-h92a', 
            displayName: '风控消息',
            pageSize: 9999,
        });
    }

    resizeTable() {
        this.tableObj.fitColumnWidth();
    }

    /**
     * @param {RiskMessage} message 
     */
    formatIdentityType(message) {

        let types = Object.values(this.systemEnum.identityType);
        let matched = types.find(x => x.code == message.identityType);
        return matched ? matched.mean : message.identityType;
    }

    /**
     * @param {RiskMessage} message 
     */
    formatWarningType(message) {
        
        let types = Object.values(this.systemEnum.warningType);
        let matched = types.find(x => x.code == message.warningType);
        return matched ? matched.mean : message.warningType;
    }

    isContextNotPresent() {
        return this.helper.isNone(this.identityId);
    }

    async qmessages() {

        if (this.isContextNotPresent()) {

            this.tableObj.clear();
            return;
        }

        let resp = await repoRisk.getRiskMessage(this.identityId);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {
            console.log('alert message response', resp);
            this.tableObj.refill(((data || {}).content || []).map(x => new RiskMessage(x)));
        }
        else {
            this.interaction.showError(`风控消息，获取失败：${errorCode}/${errorMsg}`);
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发送');
        this.qmessages();
    }

    exportSome() {
        this.tableObj.exportAllRecords(`风控记录-${new Date().format('yyyyMMdd')}`);
    }

    handleContextChange(identity_id) {

        this.resizeTable();

        if (this.identityId != identity_id) {
            
            this.identityId = identity_id;
            this.qmessages();
        }
    }

    build($container, options) {

        super.build($container);
        const $table = this.$container.querySelector('table');
        this.registerEvent('set-context-identity', this.handleContextChange.bind(this));
        this.createTable($table);
    }
}

module.exports = View;