<script setup lang="tsx">
import { computed, ref, shallowRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Repos, type MomUser, type LegacyFundInfo } from '../../../../xtrade-sdk/dist';

const { product } = defineProps<{
  product?: LegacyFundInfo;
}>();

const emit = defineEmits<{
  refresh: [];
}>();

// 仓库实例
const adminRepo = new Repos.AdminRepo();
const governanceRepo = new Repos.GovernanceRepo();

// 响应式数据
const allUsers = shallowRef<MomUser[]>([]);
const sharedUsers = computed(() => {
  if (!product) return [];
  const users: MomUser[] = [];
  product.users.forEach(user => {
    const fullUser = allUsers.value.find(u => u.id === user.userId);
    if (fullUser) {
      users.push(fullUser);
    }
  });
  return users;
});
const selectedUserId = ref<number | undefined>();

// 同机构的用户列表（用于下拉框显示）
const sameOrgUsers = computed(() => {
  if (!product || !allUsers.value.length) return [];

  return allUsers.value.filter(user => user.orgId === product.orgId && user.roleId !== 1);
});

// 检查用户是否已分享该产品
const isUserShared = (userId: number) => {
  return sharedUsers.value.some(u => u.id === userId);
};

// 按角色分组的已分享用户
const usersByRole = computed(() => {
  const groups: Record<string, MomUser[]> = {};

  sharedUsers.value.forEach(user => {
    const roleName = user.roleName || '未知角色';
    if (!groups[roleName]) {
      groups[roleName] = [];
    }
    groups[roleName].push(user);
  });

  return groups;
});

// 加载所有用户
const loadAllUsers = async () => {
  try {
    const { errorCode, errorMsg, data } = await adminRepo.QueryUsers();
    if (errorCode === 0) {
      allUsers.value = data || [];
    } else {
      ElMessage.error(errorMsg || '加载用户列表失败');
    }
  } catch (error) {
    console.error('加载用户列表失败:', error);
    ElMessage.error('加载用户列表失败');
  }
};

// 分享产品给用户或取消分享
const handleUserSelect = async (userId: number | undefined) => {
  if (!userId || !product) return;

  const user = allUsers.value.find(u => u.id === userId);
  if (!user) return;

  const isShared = sharedUsers.value.some(u => u.id === userId);

  let newUserIds: number[];
  if (isShared) {
    // 取消分享：从当前分享列表中移除该用户
    newUserIds = sharedUsers.value.filter(u => u.id !== userId).map(u => u.id);
  } else {
    // 分享：添加该用户到分享列表
    newUserIds = [...sharedUsers.value.map(u => u.id), userId];
  }

  const { errorCode, errorMsg } = await governanceRepo.ShareProduct2Users(product.id, newUserIds);

  if (errorCode === 0) {
    ElMessage.success(isShared ? '取消分享成功' : '分享成功');
    emit('refresh');
    // 清空选择
    selectedUserId.value = undefined;
  } else {
    ElMessage.error(errorMsg || (isShared ? '取消分享失败' : '分享失败'));
  }
};

// 取消分享用户
const handleRemoveUser = async (user: MomUser) => {
  if (!product) return;

  const newUserIds = sharedUsers.value.filter(u => u.id !== user.id).map(u => u.id);
  const { errorCode, errorMsg } = await governanceRepo.ShareProduct2Users(product.id, newUserIds);

  if (errorCode === 0) {
    ElMessage.success('取消分享成功');
    emit('refresh');
  } else {
    ElMessage.error(errorMsg || '取消分享失败');
  }
};

// 监听产品变化
watch(
  () => product,
  async newProduct => {
    if (newProduct) {
      await loadAllUsers();
    }
  },
  { immediate: true },
);
</script>

<template>
  <div>
    <!-- 用户选择下拉框 -->
    <div mt-20>
      <el-select
        class="typical-select"
        v-model="selectedUserId"
        placeholder="请选择要分享的用户"
        filterable
        clearable
        style="width: 300px"
        @change="handleUserSelect"
      >
        <el-option
          v-for="user in sameOrgUsers"
          :key="user.id"
          :label="`${user.roleName} - ${user.fullName} (${user.username})`"
          :value="user.id"
        >
          <div flex justify-between items-center>
            <div flex aic gap-10>
              <div>{{ user.fullName }}</div>
              <div color="[--g-text-color-3]">{{ user.roleName }}</div>
            </div>
            <div flex items-center gap-2>
              <i v-if="isUserShared(user.id)" class="iconfont icon-check"></i>
            </div>
          </div>
        </el-option>
      </el-select>
    </div>

    <!-- 已分享用户展示 -->
    <div max-h-500 of-y-auto>
      <div v-if="Object.keys(usersByRole).length === 0" w-full mt-50 flex aic jcc c-coolgray fs-16>
        暂无分享用户
      </div>
      <div mt-10 flex="~ col" gap-10 v-else>
        <div
          bg="[--g-block-bg-2]"
          flex
          aic
          gap-10
          px-20
          py-6
          v-for="(users, roleName) in usersByRole"
          :key="roleName"
        >
          <div min-w-100>{{ roleName }}</div>
          <div flex-1 min-w-1 flex flex-wrap gap-8>
            <div
              bg="[--g-block-bg-1]"
              hover="bg-[--g-block-bg-15]"
              class="group"
              flex
              aic
              gap-10
              h-32
              px-10
              border-rd-6
              cursor-pointer
              v-for="user in users"
              :key="user.id"
            >
              <div>{{ user.fullName }}</div>
              <i group-hover:hidden class="iconfont icon-check"></i>
              <i
                hidden
                group-hover:block
                @click="handleRemoveUser(user)"
                class="iconfont icon-close"
              ></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
