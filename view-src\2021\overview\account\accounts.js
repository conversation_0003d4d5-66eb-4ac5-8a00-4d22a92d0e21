const { TypicalDataView } = require('../../classcial/typical-data-view');
const { AccountDetail } = require('../../../../model/account');
const { repoAccount } = require('../../../../repository/account');

class AccountsView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '账号列表');
        this.registerEvent('refresh', this.refresh.bind(this));
    }

    createTable(options) {

        if (this.userInfo.isCounselor) {

            let $manage_cols = this.$table.querySelectorAll('th[condition="manage-only"]');
            $manage_cols.forEach($col => { $col.remove(); });
        }

        super.createTable(options);
    }

    /**
     * @param {AccountDetail} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    /**
     * @param {AccountDetail} record 
     */
    testRecords(record) {

        return this.tableObj.matchKeywords(record) 
            || this.testPy(record.accountName, this.states.keywords)
            || this.testPy(record.fundName, this.states.keywords)
            || this.testPy(record.strategyName, this.states.keywords);
    }

    async requestRecords() {

        var resp = await repoAccount.batchGetAccountCash();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`账号列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data || [];
        var accounts = records.map(item => new AccountDetail(item));
        this.tableObj.refill(accounts);
        
        if (accounts.length > 0) {
            this.tableObj.selectNextRow();
        }
        else {
            this.trigger('selected-one-account', null);
        }
    }

    /**
     * @param {AccountDetail} record
     */
    formatConnectionStatus(record) {
        return record.connectionStatus ? '<a class="s-flag s-bg-green">已连接</a>' : '<a class="s-flag s-bg-red">已断开</a>';
    }

    /**
     * @param {AccountDetail} record
     */
    formatConnectionStatusText(record) {
        return record.connectionStatus ? '已连接' : '已断开';
    }

    /**
     * @param {AccountDetail} record
     */
    formatClosePosition(record) {
        return '<button class="danger" event.onclick="confirmCloseAll">一键平仓</button>';
    }

    /**
     * @param {AccountDetail} record
     */
    confirmCloseAll(record) {

        this.interaction.showConfirm({

            title: '一键平仓确认',
            message: '一键平仓账号：' + record.accountName,
            confirmed: () => {
                console.log({ identityId: record.id }, record);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.closePosition, { identityId: record.id });
                this.interaction.showSuccess(`平仓请求已发出，目标：${record.accountName}`);
            },
        });
    }

    /**
     * @param {AccountDetail} record
     */
    handleRowSelected(record) {
        this.trigger('selected-one-account', record);
    }

    build($container, options) {

        super.build($container, options, { tableName: 'smt-oaa', heightOffset: 92 });
        this.requestRecords();
    }
}

module.exports = { AccountsView };