const fileExport = require('./file-export');

class ElTableToExcel {
	/**
	 * @param columns 必须是ElementTable的$refs引用的Table的$children
	 * @param data 必须是ElementTable的:data上绑定的数据
	 * @param successCallback 导出成功回调函数
	 * @param failedCallback 导出失败的回调函数
	 */
	constructor(options) {
		this.__initialize(options);
	}

	__initialize(options) {
		if (typeof options !== 'undefined') {
			this._singleSheet = true;
			//处理多张表的逻辑
			if (typeof options.sheets !== 'undefined') {
				this._singleSheet = false;
				this._sheets = options.sheets;
			} else if (Object.prototype.toString.call(options.sheets) === '[object Array]' && options.sheets.length === 1) {
				this._singleSheet = true;
				this._sheets = options.sheets;
			} else {
				this._sheets = [
					{
						columns: options.columns,
						data: options.data,
					},
				];
			}

			this._filters = options.filters;
			this._filename = options.filename;
			this._success = options.success;
			this._failed = options.failed;
		}

		return this;
	}

	//处理单张表的逻辑
	buildSingleSheet(sheet) {
		let { columns, data } = sheet;
		let filterColumn = this._filters;

		columns = columns
			.filter((cdt) => cdt.label !== undefined && filterColumn.every((filter) => cdt.label !== filter))
			.map((cdt) => {
				return {
					property: cdt.prop || cdt.property,
					// 对于el-table 是prop，对于data-tables，是property
					label: cdt.label,
					formatter: cdt.formatter,
				};
			});

		data = data.map((prd) => {
			//重新定义一个object，防止通过走了formatter之后，如果同一个字段被多个表格引用的话
			//经过一个forEach之后，这个定义的object会被map上所有的字段，但是是格式化后的
			let copy_obj = {};
			columns.forEach((column) => {
				let prop = column.property;
				//走格式化工具
				if (typeof column.formatter === 'function') {
					//说明当前的这个属性已经定义了
					// 为了防止同一个字段被多次引用的问题，那么，在这儿以formatter name + property来区分
					if (copy_obj[prop]) {
						let name = column.formatter.name;

						let variableKey = prop + name;

						prd[variableKey] = prd[prop];

						copy_obj[variableKey] = column.formatter(prd, {
							property: variableKey,
						});
					} else {
						copy_obj[prop] = column.formatter(prd, {
							property: prop,
						});
					}
				} else {
					copy_obj[prop] = prd[prop];
				}
			});
			return copy_obj;
		});

		return { data, columns };
	}

	buildMultiSheet() {
		this._sheets = this._sheets.map((sheet) => {
			return this.buildSingleSheet(sheet);
		});
	}

	/**
	 *
	 * @param options
	 * 1˚、单张表导出的时候，{ data, columns, filename, filters, success_callback, failed_callback }
	 *     或者直接使用多张表的定义形式只传入一个meta_data
	 * 2˚、多张表导出的时候, { sheets: [ { data, columns } ], ...（同上）  }
	 * @returns {ElTableToExcel}
	 */
	build(options) {
		this.__initialize(options);
		if (this._singleSheet) {
			this._sheets[0] = this.buildSingleSheet(this._sheets[0]);
		} else {
			this.buildMultiSheet();
		}
		return this;
	}

	/**
	 * 导出单张表
	 * @returns {ElTableToExcel}
	 */
	exportSingleSheet() {
		fileExport.exportSingleSheet(this._sheets[0], this._filename, this._success, this._failed);
	}

	exportMultiSheet() {
		fileExport.exportMultiSheet(this._sheets, this._filename, this._success, this._failed);
	}
}

module.exports = ElTableToExcel;
