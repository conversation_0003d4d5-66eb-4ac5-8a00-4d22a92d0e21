class SimpleStrategyInfo {

    constructor({ accountId, accountName }) {

        this.accountId = accountId;
        this.accountName = accountName;
    }
}

class StrategyInfo {
    
    constructor({

        strategyId, 
        strategyName,
        accounts = [new SimpleStrategyInfo({})].splice(1),
    }) {

        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.accounts = accounts;
    }
}

class V3StandardStrategy {

    constructor({

        available,
        balance,
        closeProfit,
        commission,
        connectCount,
        description,
        diffBalance,
        frozenCommission,
        frozenMargin,
        fundId,
        fundName,
        id,
        loanBuyBalance,
        loanSellBalance,
        loanSellQuota,
        margin,
        marketValue,
        positionProfit,
        preBalance,
        reportTemplates,
        risePercent,
        riskUsers,
        status,
        strategyAccounts,
        strategyName,
        traders,
        users,
        withdrawQuota
    }) {
        
        this.available = available;
        this.balance = balance;
        this.closeProfit = closeProfit;
        this.commission = commission;
        this.connectCount = connectCount;
        this.description = description;
        this.diffBalance = diffBalance;
        this.frozenCommission = frozenCommission;
        this.frozenMargin = frozenMargin;
        this.fundId = fundId;
        this.fundName = fundName;
        this.id = id;
        this.loanBuyBalance = loanBuyBalance;
        this.loanSellBalance = loanSellBalance;
        this.loanSellQuota = loanSellQuota;
        this.margin = margin;
        this.marketValue = marketValue;
        this.positionProfit = positionProfit;
        this.preBalance = preBalance;
        this.reportTemplates = Array.isArray(reportTemplates) ? reportTemplates : [];
        this.risePercent = risePercent;
        this.riskUsers = Array.isArray(riskUsers) ? riskUsers : [];
        this.status = status;
        this.strategyAccounts = Array.isArray(strategyAccounts) ? strategyAccounts.map(x => new V3StrategyAccount(x)) : [];
        this.strategyName = strategyName;
        this.traders = Array.isArray(traders) ? traders : [];
        this.users = Array.isArray(users) ? users : [];
        this.withdrawQuota = withdrawQuota;
    }

    setUserName(userMap) {
        
        this.users.forEach(item => {

            let matched = userMap[item.userId];
            if (matched) {
                item.fullName = matched.fullName;
            }
        });
    }

    setTraders(shareType) {
        
        let users = this.users.filter(x => x.shareType === shareType);
        this.traders.push(...users);
    }

    setRiskUsers(shareType) {
        
        let users = this.users.filter(x => x.shareType === shareType);
        this.riskUsers.push(...users);
    }
}

class V3StrategyAccount {

    constructor({

        accountAlias,
        accountId,
        accountName,
        assetType,
        available,
        balance,
        closeProfit,
        commission,
        connectionStatus,
        credit,
        detailId,
        diffBalance,
        financeAccountName,
        frozenCommission,
        frozenMargin,
        fundId,
        fundName,
        loanBuyBalance,
        loanSellBalance,
        loanSellQuota,
        margin,
        marketValue,
        maxLimitMoney,
        positionProfit,
        preBalance,
        risePercent,
        strategyId,
        strategyName
    }) {

        this.accountId = accountId;
        this.accountName = accountAlias || accountName;
        this.assetType = assetType;
        this.available = available;
        this.balance = balance;
        this.closeProfit = closeProfit;
        this.commission = commission;
        this.connectionStatus = connectionStatus;
        this.credit = credit;
        this.detailId = detailId;
        this.diffBalance = diffBalance;
        this.financeAccountName = financeAccountName;
        this.frozenCommission = frozenCommission;
        this.frozenMargin = frozenMargin;
        this.fundId = fundId;
        this.fundName = fundName;
        this.loanBuyBalance = loanBuyBalance;
        this.loanSellBalance = loanSellBalance;
        this.loanSellQuota = loanSellQuota;
        this.margin = margin;
        this.marketValue = marketValue;
        this.maxLimitMoney = maxLimitMoney;
        this.positionProfit = positionProfit;
        this.preBalance = preBalance;
        this.risePercent = risePercent;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
    }
}

class ChildAccountDetail {

    constructor({

        accountId,
        accountName,
        assetType,
        available,
        balance,
        beginAvailable,
        closeProfit,
        commission,
        dayProfit,
        deleteFlag,
        diffBalance,
        financeAccount,
        frozenCommission,
        frozenMargin,
        id,
        inMoney,
        increaseAmount,
        loanBuyBalance,
        loanSellBalance,
        loanSellQuota,
        margin,
        marketValue,
        maxLimitMoney,
        nav,
        optionMarketValue,
        outMoney,
        positionProfit,
        preBalance,
        preNav,
        profitRatio,
        sumProfit,
        tradingDay
    }) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.assetType = assetType;
        this.available = available;
        this.balance = balance;
        this.beginAvailable = beginAvailable;
        this.closeProfit = closeProfit;
        this.commission = commission;
        this.dayProfit = dayProfit;
        this.deleteFlag = deleteFlag;
        this.diffBalance = diffBalance;
        this.financeAccount = financeAccount;
        this.frozenCommission = frozenCommission;
        this.frozenMargin = frozenMargin;
        this.id = id;
        this.inMoney = inMoney;
        this.increaseAmount = increaseAmount;
        this.loanBuyBalance = loanBuyBalance;
        this.loanSellBalance = loanSellBalance;
        this.loanSellQuota = loanSellQuota;
        this.margin = margin;
        this.marketValue = marketValue;
        this.maxLimitMoney = maxLimitMoney;
        this.nav = nav;
        this.optionMarketValue = optionMarketValue;
        this.outMoney = outMoney;
        this.positionProfit = positionProfit;
        this.preBalance = preBalance;
        this.preNav = preNav;
        this.profitRatio = profitRatio;
        this.sumProfit = sumProfit;
        this.tradingDay = tradingDay;
    }
}

module.exports = {

    StrategyInfo,
    SimpleStrategyInfo,
    V3StandardStrategy,
    V3StrategyAccount,
    ChildAccountDetail,
};