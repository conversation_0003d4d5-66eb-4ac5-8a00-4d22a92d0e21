<script setup lang="tsx" generic="T extends 'account' | 'product' | 'user'">
import { computed, onBeforeUnmount, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage, TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { ColumnDefinition, RowAction } from '@/types';
import { isOrderCancelable, OrderStatusEnum, TradeDirectionEnum } from '@/enum';
import { RecordService, TradingService } from '@/api';
import { Repos } from '../../../../../xtrade-sdk/dist';
import type { OrderInfo } from '../../../../../xtrade-sdk/dist';
import {
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  orderStatusCol,
  directionCol,
  businessFlagCol,
  volumeOriginalCol,
  orderPriceCol,
  tradedVolumeCol,
  tradedPriceCol,
  createTimeCol,
  orderTimeCol,
  tradeTimeCol,
  exchangeOrderIdCol,
  frozenMarginCol,
  fundNameCol,
  assetTypeCol,
  foreignCol,
  forceCloseCol,
  remarkCol,
  accountNameCol,
} from './shared/columnDefinitions';

const recordsRepo = new Repos.RecordsRepo();

const { type, today, trade, identityId } = defineProps<{
  /** 类型 */
  type: T;
  /** 是否当日 */
  today?: boolean;
  /** 是否交易 */
  trade?: boolean;
  /** [账号|产品]（列表）id */
  identityId?: string;
}>();

// 基础列定义
const baseColumns = [
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  orderStatusCol,
  directionCol,
  businessFlagCol,
  volumeOriginalCol,
  orderPriceCol,
  tradedVolumeCol,
  tradedPriceCol,
  createTimeCol,
  orderTimeCol,
  tradeTimeCol,
  exchangeOrderIdCol,
  frozenMarginCol,
  fundNameCol,
  assetTypeCol,
  foreignCol,
  forceCloseCol,
  remarkCol,
] as ColumnDefinition<OrderInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.unshift(accountNameCol as any);
  }
  return cols;
});

const hasAction = computed(() => {
  return trade;
});

// 行操作
const rowActions: RowAction<OrderInfo>[] = [
  {
    label: '撤单',
    show: (row: OrderInfo) => isOrderCancelable(row.orderStatus),
    onClick: (row: OrderInfo) => {
      TradingService.cancelOrder(row.id);
      ElMessage.success('已发送撤单请求');
    },
    type: 'var(--g-red)',
  },
];

// 过滤委托状态选项
const filterOrderStatuses = [
  { label: '全部', value: 0 },
  { label: '未完成', value: 1 },
  { label: '废单', value: 2 },
  { label: '已完成', value: 3 },
  { label: '部分成交', value: 4 },
  { label: '已撤销', value: 5 },
];

// 过滤委托状态
const orderStatus = shallowRef(0);

// 委托数据
const orders = shallowRef<OrderInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

const customFilter = (item: OrderInfo) => {
  if (!orderStatus.value) {
    return true;
  }
  if (orderStatus.value === 1) {
    return isOrderCancelable(item.orderStatus);
  }
  if (orderStatus.value === 2) {
    return item.orderStatus === OrderStatusEnum.废单;
  }
  if (orderStatus.value === 3) {
    return [OrderStatusEnum.全成, OrderStatusEnum.已撤, OrderStatusEnum.部分成交撤单].includes(
      item.orderStatus,
    );
  }
  if (orderStatus.value === 4) {
    return [OrderStatusEnum.部分成交, OrderStatusEnum.部分成交撤单].includes(item.orderStatus);
  }
  if (orderStatus.value === 5) {
    return [OrderStatusEnum.已撤, OrderStatusEnum.部分成交撤单].includes(item.orderStatus);
  }
  return true;
};

// 监听账户/产品变化，重新获取委托数据
watch(
  () => identityId,
  newId => {
    if (newId) {
      tableRef.value?.refreshAsyncData();
    }
  },
);

// 实际应用中的数据获取函数
const asyncDataLoader = async (pageNo: number) => {
  if (today) {
    if (type == 'user') {
      const { errorCode, data } = await recordsRepo.QueryTodayOrdersNew({}, pageNo);
      if (errorCode === 0) {
        return { data: data?.contents || [], total: data?.totalSize || 0 };
      } else {
        return { data: [], total: 0 };
      }
    } else {
      // TODO 查询当日账号/产品订单
      return { data: [], total: 0 };
    }
  } else {
    // TODO:查询历史订单
    return { data: [], total: 0 };
  }
};

// 撤销选中的委托
const cancelSelectedOrders = () => {
  const selectedRows = tableRef.value?.selectedRows ?? [];

  if (selectedRows.length === 0) {
    ElMessage.warning('请选择委托');
    return;
  }

  const selectedCancelableRows = selectedRows.filter(row => isOrderCancelable(row.orderStatus));

  if (selectedCancelableRows.length === 0) {
    ElMessage.warning('无可撤销委托');
    return;
  }

  selectedCancelableRows.forEach(row => {
    TradingService.cancelOrder(row.id);
  });
  ElMessage.success('已发送撤单请求');
};

// 撤销全部委托
const cancelAllOrders = () => {
  const cancelableOrders = orders.value.filter(order => isOrderCancelable(order.orderStatus));
  if (cancelableOrders.length === 0) {
    ElMessage.warning('无可撤销委托');
    return;
  }
  cancelableOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤单请求');
};

// 撤销买单
const cancelBuyOrders = () => {
  const buyOrders = orders.value.filter(
    order => isOrderCancelable(order.orderStatus) && order.direction === TradeDirectionEnum.买入,
  );
  if (buyOrders.length === 0) {
    ElMessage.warning('无可撤销买单');
    return;
  }
  buyOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤买单请求');
};

// 撤销卖单
const cancelSellOrders = () => {
  const sellOrders = orders.value.filter(
    order => isOrderCancelable(order.orderStatus) && order.direction === TradeDirectionEnum.卖出,
  );
  if (sellOrders.length === 0) {
    ElMessage.warning('无可撤销卖单');
    return;
  }
  sellOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤卖单请求');
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns
    :async-data-loader
    :row-actions
    :customFilter
    select
    fixed
  >
    <template #left>
      <div flex aic>
        <span mr-8>订单状态:</span>
        <div w-120 mr-16>
          <el-select v-model="orderStatus" placeholder="选择状态" size="small">
            <el-option
              v-for="status in filterOrderStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
      </div>
    </template>
    <template #actions>
      <div v-if="hasAction" class="actions" flex aic>
        <el-button
          @click="cancelSelectedOrders"
          size="small"
          color="var(--g-primary)"
          :disabled="!tableRef?.selectedRows.length"
        >
          撤勾选
        </el-button>
        <el-button @click="cancelAllOrders" size="small" color="var(--g-primary)">全撤</el-button>
        <el-button @click="cancelBuyOrders" size="small" color="var(--g-primary)">撤买单</el-button>
        <el-button @click="cancelSellOrders" size="small" color="var(--g-primary)">
          撤卖单
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
