import type { ServerConfig, SysUserInfo } from '@/types';
import { getLocal, setLocal } from './utils';
import type { ShallowRef } from 'vue';
import { getConfigedServers } from '../../shared/servers';
import {
  GlobalState,
  RuntimeEnvironment,
  HttpAssist,
  ServerManager,
  MomUserType,
  type MomMenuTree,
  UserRole,
} from '../../../xtrade-sdk';

/**
 * 保存用户信息
 */
export const setUser = (user?: SysUserInfo) => {
  setLocal('user', user);
};

/**
 * 获取用户信息
 */
export const getUser = () => {
  return getLocal<SysUserInfo>('user');
};

/**
 * 是否登录
 */
export const isLoggedIn = () => {
  return !!getUser();
};

/**
 * 是否拥有全局数据权限
 */
export const hasGlobalDataPermission = () => {
  const usr = getUser();
  return (
    usr &&
    (usr.username == 'admin' || usr.orgId == 0 || usr.userType == MomUserType.globalScope.value)
  );
};

/**
 * 是否仅拥有机构内部数据权限
 */
export const hasOrgDataPermission = () => {
  const usr = getUser();
  return usr && usr.userType == MomUserType.orgScope.value;
};

/**
 * 是否仅拥有分享数据权限
 */
export const hasSharedDataPermission = () => {
  const usr = getUser();
  return usr && usr.userType == MomUserType.sharedScope.value;
};

/**
 * 登录用户是否超级管理员, 如果传入了roleId，则判断该角色是否为超级管理员
 */
export const isAdmin = (roleId?: number) => {
  if (roleId) {
    return roleId == UserRole.SuperAdmin.Value;
  }
  const usr = getUser();
  if (usr) {
    return usr.roleId == UserRole.SuperAdmin.Value;
  }
  return false;
};

/**
 * 获得当前用户可访问的数据权限范围（如果当前用户的数据权限未正确设置，则默认返回分享类的数据权限范围）
 */
export const getDataScopes = () => {
  const usr = getUser()!;
  if (!usr) {
    return [];
  }

  const userTypes = Object.values(MomUserType);
  const limitedTypes = userTypes.filter(type => type.value >= usr.userType);
  return limitedTypes.length > 0 ? limitedTypes : [MomUserType.sharedScope];
};

/**
 * 保存服务器信息
 */
export const setServer = (server_config: string) => {
  setLocal('server', server_config);
};

/**
 * 获取服务器信息
 */
export const getServer = () => {
  return getLocal<ServerConfig>('server');
};

/**
 * 是否为web端
 */
export const isWeb = () => {
  return !isElectron();
};

/**
 * 是否为客户端
 */
export const isElectron = () => {
  // Renderer process
  if (
    typeof window !== 'undefined' &&
    typeof window.process === 'object' &&
    window.process.type === 'renderer'
  ) {
    return true;
  }

  // Main process
  if (
    typeof process !== 'undefined' &&
    typeof process.versions === 'object' &&
    !!process.versions.electron
  ) {
    return true;
  }

  // Detect the user agent when the `nodeIntegration` option is set to true
  if (
    typeof navigator === 'object' &&
    typeof navigator.userAgent === 'string' &&
    navigator.userAgent.indexOf('Electron') >= 0
  ) {
    return true;
  }

  return false;
};

/**
 * 更新或添加数据到数组中
 * @param item 要更新或添加的数据项
 * @param data 目标数组的浅引用
 * @param identity 用于标识唯一性的字段名，默认为'id'
 * @description 如果数组中已存在相同identity的项，则更新该项；否则将新项添加到数组末尾
 */
export const putRow = <T extends Record<string, any>>(
  item: T,
  data: ShallowRef<T[]>,
  identity: keyof T = 'id',
) => {
  if (data.value.some(row => row[identity] === item[identity])) {
    data.value = data.value.map(row => (row[identity] === item[identity] ? item : row));
  } else {
    data.value = [...data.value, item];
  }
};

/** 尝试配置服务器 */
export const try2ConfigServer = async () => {
  const Servers = getConfigedServers();
  const lastServer = getServer() || Servers[0];
  const matched = Servers.find(item => item.name === lastServer?.name);

  if (matched == undefined) {
    return;
  }

  const { tradeServer, quoteServer, restServer } = matched.servers;
  const isMocked = getLocal<string>('mocked') === 'true';

  if (!isMocked) {
    // mock场景不设置具体的服务器地址
    const baseUrl = `http://${restServer.host}:${restServer.port}/quant/v3`;
    HttpAssist.SetHttpBaseUrl(baseUrl);
    console.log('HTTP base url set to', baseUrl);
  }

  GlobalState.SetEnv(RuntimeEnvironment.WebSocket);
  await ServerManager.SetTradeServer(tradeServer.host, tradeServer.port);
  await ServerManager.SetQuoteServer(quoteServer.host, quoteServer.port);
};

/** 在控制台打印权限sql语句 */
export const printPermissionSql = async () => {
  const { makeSqls, uniqueCheck } = await import('../enum/page-operation');
  if (!uniqueCheck()) {
    return;
  }
  makeSqls();
};

/**
 * 判断当前用户是否拥有指定权限
 * @param permissionKey 权限key，如 'create'、'edit'、'delete' 等
 * @param menuId 可选的菜单ID，如果不传则尝试从当前活动菜单获取
 * @returns 是否拥有该权限
 */
export const hasPermission = (permissionKey: string, menuId?: number): boolean => {
  const user = getUser();
  if (!user) {
    return false;
  }

  // 如果没有传入menuId，尝试从localStorage获取当前活动菜单
  let currentMenuId = menuId;
  if (!currentMenuId) {
    const activeMenu = getLocal<MomMenuTree>('activeMenu');
    if (activeMenu) {
      currentMenuId = activeMenu.id;
    }
  }

  if (!currentMenuId) {
    console.warn('无法确定当前菜单ID，权限判断失败');
    return false;
  }

  // 获取用户的角色菜单权限
  const roleMenus = getLocal<MomMenuTree[]>('userRoleMenus');
  if (!roleMenus || roleMenus.length === 0) {
    console.warn('未找到用户角色菜单权限数据');
    return false;
  }

  // 递归查找指定菜单及其权限
  const findMenuPermissions = (menus: MomMenuTree[], targetMenuId: number): MomMenuTree | null => {
    for (const menu of menus) {
      if (menu.id === targetMenuId) {
        return menu;
      }
      if (menu.children) {
        const found = findMenuPermissions(menu.children, targetMenuId);
        if (found) return found;
      }
    }
    return null;
  };

  const targetMenu = findMenuPermissions(roleMenus, currentMenuId);
  if (!targetMenu || !targetMenu.menListPermission) {
    return false;
  }

  // 检查是否拥有指定权限
  return targetMenu.menListPermission.some(
    permission => permission.permissionName === permissionKey,
  );
};
