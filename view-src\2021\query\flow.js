const IView = require('../../../component/iview').IView;

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, 'empty view');
    }

    createApp() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {
                
                title: this.title,
                name: this.viewName,
            }
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;