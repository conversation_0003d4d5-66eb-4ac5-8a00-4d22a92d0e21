function json2Class(data, class_name, sorting = false) {
    
    if (typeof data != 'object' || data == null) {
      return data;
    }
  
    let props = [];
    for (let key in data) {
      props.push(key);
    }
  
    let sorteds = sorting == true ? props.slice(0).sort() : props;
    console.log(`class ${class_name || ('ClassName' + new Date().getTime())} {
        constructor({ ${props.join(', ')} }) {
            ${sorteds.map(prop => `this.${prop} = ${prop};`).join('\n\t\t\t')}\n
        }
    }`);
}

function json2Interface(data, class_name, sorting = false) {
    
    if (typeof data != 'object' || data == null) {
        return data;
    }

    let kvs = [];
    for (let key in data) {

        let val = data[key];
        let tyn = typeof val;
        let prop = '', type = '';

        if (val == null) {

            prop = `${key}?`;
            type = 'number | string';
        }
        else if (tyn == 'string') {

            prop = key;
            type = 'string';
        }
        else if (tyn == 'number') {

            prop = key;
            type = 'number';
        }
        else if (tyn == 'boolean') {

            prop = key;
            type = 'boolean';
        }

        kvs.push({ prop, type });
    }

    let sorteds = sorting == true ? kvs.slice(0).sort(x => x.prop) : kvs;
    let lines = sorteds.map(x => `${x.prop}: ${x.type};`);
    console.log(lines.join('\n'));
    console.log(`interface ${class_name || ('InterfaceName' + new Date().getTime())} {\n
        ${sorteds.map(x => `${x.prop}: ${x.type};`).join('\t\n\t')}
    }`);
}

module.exports = {

    consoleHelper: {
        
        json2Class,
        json2Interface,
    }
};