<script setup lang="ts">
import { computed, ref } from 'vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab } from '@/types';

const accountIds = ref('');

const handleAccountIdsChange = (ids: string) => {
  // console.log('account-ids-change', ids);
  accountIds.value = ids;
};

// 定义tabs配置
const tabs = computed<ComponentTab[]>(() => [
  {
    label: '我的账号',
    component: 'MyAccount',
    events: {
      'account-ids-change': handleAccountIdsChange,
    },
  },
  {
    label: '我的指令',
    component: 'InstructionList',
    props: {
      type: 'user',
    },
  },
  {
    label: '我的委托',
    component: 'OrderList',
    props: {
      type: 'user',
      today: true,
      trade: true,
      identityId: accountIds.value,
    },
  },
  {
    label: '我的持仓',
    component: 'TodayPositions',
    props: {
      type: 'account',
    },
  },
  {
    label: '我的成交',
    component: 'TodayRecords',
    props: {
      type: 'account',
    },
  },
  {
    label: '我的产品',
    component: 'ProductList',
    props: {
      trade: true,
    },
  },
]);
</script>

<template>
  <div h-full flex="~ col">
    <ComponentTabs h-full :tabs="tabs" />
  </div>
</template>

<style scoped></style>
