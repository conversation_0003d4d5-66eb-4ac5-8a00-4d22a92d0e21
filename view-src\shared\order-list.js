const { TodayBaseList } = require('./baselist-today');
const { ContextObjectInfo } = require('../../model/context-object-info');
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const NumberMixin = require('../../mixin/number').NumberMixin;
const ModelConverter = require('../../model/model-converter').ModelConverter;
const Order = require('../../model/order').Order;
const BizHelper = require('../../libs/helper-biz').BizHelper;

class View extends TodayBaseList {

    get direction() {
        return this.systemTrdEnum.tradingDirection;
    }

    get orderStatus() {
        return this.systemEnum.orderStatus;
    }

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, TodayBaseList.ViewTitles.order);

        this.setDataFunCodeType(this.serverFunction.requestTodayOrder, 'order');
        this.orderTypes = this.systemEnum.orderStatus;
        this.cancelTypes = { all: 'all', buy: 'buy', sell: 'sell', selected: 'selected' };

        this.condition = {

            keywords: null,
            orderType: null,
        };

        this.setTitle([

            'id',
            'parentOrderId',
            'userId',
            'userName',
            'strategyId',
            'strategyName',
            'fundId',
            'fundName',
            'accountId',
            'financeAccount',
            'accountName',
            'instrument',
            'instrumentName',
            'frozenMargin',
            'frozenCommission',
            'frozenVolume',
            'tradedAmount',
            'tradedVolume',
            'tradedPrice',
            'cancelledVolume',
            'volumeOriginal',
            'commission',
            'errorCode',
            'errorMsg',
            'isForeign',
            'assetType',
            'orderStatus',
            'localOrderId',
            'exchangeOrderId',
            'orderPrice',
            'orderPriceType',
            'direction',
            'businessFlag',
            'positionEffect',
            'hedgeFlag',
            'optionType',
            'coverFlag',
            'isForceClose',
            'tradeTime',
            'orderTime',
            'tradingDay',
            'customId',
            'createTime',
            'updateTime',
            'adjustFlag',
            'remark',
            'ipMac',
            'receiveOrderTime',
            'sendTerminalTime',
            'receiveExchangeOrderIdTime',
            'identityid',
        ]);
    }

    /**
     * 退订，之前的，上下文对象的实时数据
     * @param {ContextObjectInfo} previous_context
     */
    unsubChange(previous_context) {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, previous_context.identityId, [this.serverEvent.orderChanged]);
    }

    /**
     * 订阅，当前的，上下文对象的实时数据
     */
    resubChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, this.identityId, [this.serverEvent.orderChanged]);
    }

    resetControls() {

        super.resetControls();
        this.condition.orderType = null;
        this.condition.keywords = null;
    }

    /**
     * @param {Array<Array>} locals
     * @param {Array<Array>} newers
     * @returns {Array<Array>}
     */
    mergeAll(locals, newers) {

        let id_idx = this.RecordIdIdx;
        let ut_idx = this.RecordUpdateTimeIdx;
        let map_loc = {};
        let map_new = {};

        locals.forEach((values) => { map_loc[values[id_idx]] = values; });
        newers.forEach((values) => { map_new[values[id_idx]] = values; });

        for (let ord_id in map_new) {

            let item_new = map_new[ord_id];
            let item_loc = map_loc[ord_id];

            if (item_loc === undefined) {
                locals.push(item_new);
            } 
            else {

                let utime_new = item_new[ut_idx];
                let utime_loc = item_loc[ut_idx];

                if (utime_new >= utime_loc) {
                    this.helper.extend(item_loc, item_new);
                }
            }
        }

        return locals;
    }

    /**
     * @param {Array<Array>} contents
     */
    consumeBatchPush(contents) {

        super.consumeBatchPush(contents);
        var records = ModelConverter.formalizeOrders(this.titles, contents);
        this.tableObj.refill(records);
    }

    /**
     * @param {*} struc
     */
    consumeRealtimePush(struc) {

        var order = new Order(struc);
        var is_adjust_4_fund = this.context.isAboutFund && order.adjustFlag;

        if (!is_adjust_4_fund) {
            this.tableObj.putRow(order);
        }
    }

    createToolbarApp() {

        this.toolbarApp = new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                orderTypes: this.orderTypes,
                cancelTypes: this.cancelTypes,
                allow2Trade: this.allow2Trade,
                condition: this.condition,
                sharedCondition: this.sharedCondition,
                paging: this.paging,
            },

            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.toggleRealtimePush,
                this.openAddRecordDialog, 
                this.filterRecords, 
                this.handlePageSizeChange, 
                this.handlePageChange, 
                this.batchCancel,
            ]),
        });
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.condition.keywords;

        /**
         * @param {Order} record
         */
        function filterByPinyin(record) {
            return thisObj.testKeywords(record.instrumentName, keywords);
        }

        /**
         * @param {Order} record
         */
        function testRecords(record) {

            let ord_type = thisObj.condition.orderType;
            let status_matched = ord_type === null || ord_type === '' || ord_type === record.orderStatus;
            return status_matched && (thisObj.tableObj.matchKeywords(record) || filterByPinyin(record));
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {

        if (!this.isDataPushReceived) {

            this.interaction.showMessage('数据未准备完整，请稍后翻页');
            return;
        }

        this.tableObj.setPageIndex(this.paging.page);
    }

    createTableComponent() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.table-control');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-ol',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        return tableObj;
    }
    
    handleTableFiltered(filtered_count) {
        this.paging.total = filtered_count;
    }

    /**
     * @description:
     * @param {Order} row_data
     * @return:
     */

    formatActions(row_data) {

        let actions = '';

        if (this.sharedCondition.canChangeRecord) {
            
            actions = `<button event.onclick="updateRecord"><i class="el-icon-edit"></i> 更改</button>
                    <button event.onclick="deleteRecord" class="danger"><i class="el-icon-delete"></i> 删除</button>`;
        }

        if (this.allow2Trade && !row_data.isCompleted) {
            actions += '<button event.onclick="cancelOrder">撤单</button>';
        }

        return actions;
    }

    /**
     * @returns {Array<Order>}
     */
    extractIncompletedOrders() {

        var all_orders = this.tableObj.extractAllRecords();
        return all_orders.filter((x) => x.isCompleted !== true);
    }

    batchCancel(cancel_type) {

        var cancel_types = this.cancelTypes;
        var incompleted_orders = this.extractIncompletedOrders();

        if (incompleted_orders.length == 0) {
            this.interaction.showMessage('未完成订单数量 = 0');
            return;
        }

        switch (cancel_type) {

            case cancel_types.all: {

                this.showCancelConfirm(incompleted_orders, '是否确认撤消<span class="s-bold">【所有】</span>订单');
                break;
            }

            case cancel_types.buy: {

                let buy_orders = incompleted_orders.filter((rd) => rd.direction === this.direction.buy.code);
                this.showCancelConfirm(buy_orders, '是否确认撤消<span class="s-color-red">【所有买入】</span>订单');
                break;
            }

            case cancel_types.sell: {

                let sell_orders = incompleted_orders.filter((rd) => rd.direction === this.direction.sell.code);
                this.showCancelConfirm(sell_orders, '是否确认撤消<span class="s-color-green">【所有卖出】</span>订单');
                break;
            }

            case cancel_types.selected: {

                if (!this.tableObj.hasAnyRowsChecked) {

                    this.interaction.showError('您未勾选订单');
                    return;
                }

                let keymap = this.tableObj.extractCheckedRecordsRowKeys();
                let checked_of_imcompleted = incompleted_orders.filter((x) => {
                    return keymap[this.identifyRecord(x)] === true;
                });

                if (checked_of_imcompleted.length == 0) {

                    this.interaction.showError('勾选的订单，全部已完成');
                    return;
                }

                this.showCancelConfirm(checked_of_imcompleted, '是否确认撤消<span class="s-color-red">【勾选】</span>订单');
                break;
            }
        }
    }

    /**
     * @param {Array<Order>} orders
     * @param {String} confirm_message
     */
    showCancelConfirm(orders, confirm_message) {

        if (orders.length == 0) {

            this.interaction.showMessage('没有符合条件的可撤订单');
            return;
        }

        this.interaction.showConfirm({

            title: '批量撤单确认',
            message: `${confirm_message}，影响订单数量：${orders.length}`,
            confirmed: () => {
                this.cancelOrder(orders);
            },
        });
    }

    /**
     * 同时响应批量撤单 | 单笔撤单
     @param {Order | Array<Order>} order 
     */
    cancelOrder(order) {

        var total = order instanceof Array ? order.length : 1;
        var ids = order instanceof Array ? order.map((x) => x.id) : [order.id];
        ids.forEach((ord_id) => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: ord_id });
        });
        this.interaction.showSuccess(`撤单请求已发出，数量 = ${total}`);
    }

    listen2Events() {

        this.standardListen(this.serverEvent.todayOrderPush, this.handleBatchPush.bind(this));
        this.standardListen(this.serverEvent.orderChanged, this.handleRealtimeChange.bind(this));
    }

    build($container) {

        super.build($container);
        this.listen2Events();
    }
}

module.exports = View;