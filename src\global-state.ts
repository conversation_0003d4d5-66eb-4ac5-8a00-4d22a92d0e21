import Utils from './modules/utils';
import { AssetType, UserRole } from './config/core';
import { RuntimeEnvironment } from './config/architecture';
import { InstrumentInfo } from './types/common';
import { BrocastLogger, ConsoleLogger, ILogger } from './middleware/logger';
import { UserLoginResponse, SysUserInfo } from './types/table/admin';

const defaultLogger = new ConsoleLogger();
const GlobalStates = {

    /** 运行环境 */
    Env: RuntimeEnvironment.WebSocket,
    /** 日志记录器（支持多种日志终端输出） */
    loggers: [defaultLogger] as ILogger[],
    /** 系统用户信息 */
    UserInfo: null as SysUserInfo | null,
    /** 市场各类合约 */
    Instruments: { 
        Stocks: [] as InstrumentInfo[], 
        Futures: [] as InstrumentInfo[], 
        Options: [] as InstrumentInfo[],
    },
};

/**
 * 设置运行环境类型
 */
export function SetEnv(env: RuntimeEnvironment) {
    GlobalStates.Env = env;
}

/**
 * 获取运行环境类型
 */
export function GetEnv() {
    return GlobalStates.Env;
}

/**
 * 设置日志记录终端（设置后，默认的 ConsoleLogger 终端将失效）
 */
export function SetLoggers(loggers: ILogger[]) {

    if (Array.isArray(loggers)) {
        GlobalStates.loggers = [...loggers];
    }
}

/**
 * 根据名称获取已注册的某个日志终端（如果未提供名称，则将返回默认的日志记录终端 -- 前提是默认终端没有被覆盖）
 */
export function GetLogger(name = 'Default') {
    return GlobalStates.loggers.find(x => x.name == name)!;
}

/**
 * 获取全部loggers
 */
export function GetLoggers() {
    return GlobalStates.loggers.slice(0);
}

/**
 * 获取全量已注册日志终端广播入口
 */
export function GetBrocastLogger() {
    return new BrocastLogger(GlobalStates.loggers);
}

/**
 * 设置用户信息
 */
export function SetUserInfo(plainUsr: UserLoginResponse | null, password?: string) {
   
    if (!plainUsr) {

        GlobalStates.UserInfo = null;
        return;
    }

    const { roleId } = plainUsr;
    const cloned = Utils.deepClone(plainUsr);
    const roles = Object.values(UserRole);
    const matchedRole = roles.find(x => x.Value == roleId)!;

    GlobalStates.UserInfo = Object.assign(cloned, {

        password: password || '',
        roleName: matchedRole.Label,

        isSuperAdmin: roleId == UserRole.SuperAdmin.Value,
        isBrokerAdmin: roleId == UserRole.BrokerAdmin.Value,
        isOrgAdmin: roleId == UserRole.OrgAdmin.Value,
        isProductManager: roleId == UserRole.ProductManager.Value,
        isRiskProtector: roleId == UserRole.RiskProtector.Value,
        isTradingMan: roleId == UserRole.TradingMan.Value,
        isCounselor: roleId == UserRole.Counselor.Value,
        isObserver: roleId == UserRole.Observer.Value,
        isQuoteServerRequired: roleId != UserRole.SuperAdmin.Value,
    });
}

/**
 * 更新用户密码（仅改变字段值）
 */
export function UpdateUserPassword(password: string) {
    GlobalStates.UserInfo && (GlobalStates.UserInfo.password = password);
}

/**
 * 更新用户凭证
 */
export function UpdateUserToken(token: string) {
    GlobalStates.UserInfo && (GlobalStates.UserInfo.token = token);
}

/**
 * 获取用户信息
 */
export function GetUserInfo() {
    return GlobalStates.UserInfo;
}

/**
 * 设置合约
 */
export function SetInstruments(assetType: number, data: InstrumentInfo[]) {
    
    const ref = GlobalStates.Instruments;

    switch (assetType) {

        case AssetType.Stock.Value: ref.Stocks = data; break;
        case AssetType.Future.Value: ref.Futures = data; break;
        case AssetType.Option.Value: ref.Options = data; break;
        default: throw new Error(`unsupported asset type/${assetType}`);
    }
}

/**
 * 获取合约
 */
export function GetInstruments(assetType: number) {
    
    const { Stocks, Futures, Options } = GlobalStates.Instruments;

    switch (assetType) {

        case AssetType.Stock.Value: return Stocks;
        case AssetType.Future.Value: return Futures;
        case AssetType.Option.Value: return Options;
        default: return [];
    }
}
