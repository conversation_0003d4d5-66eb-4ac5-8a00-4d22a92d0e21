<script setup lang="ts">
import { ref } from 'vue';
import TradeDirection from '@/components/NormalTradeView/TradeTabs/TradeDirection.vue';
import TradePanel from '@/components/NormalTradeView/TradeTabs//TradePanel.vue';
import type { AccountInfo, InstrumentInfo, StandardTick } from '@/types';
import { TradeDirectionEnum } from '@/enum';
import { TRADE_CHANNELS } from '@/enum/trade';

const { selectedAccount, lastTick } = defineProps<{
  selectedAccount?: AccountInfo;
  lastTick?: StandardTick;
}>();

// 交易方向
const direction = ref(TradeDirectionEnum.买入);

// 选中合约
const selectedInstrument = defineModel<InstrumentInfo>('instrument');
</script>

<template>
  <div flex="~ col">
    <TradeDirection v-model="direction" />
    <el-scrollbar>
      <div flex="~ 1" min-h-1>
        <!-- 左侧交易面板 -->
        <TradePanel
          w-270
          :last-tick="lastTick"
          :direction="direction"
          :active-account="selectedAccount"
          :active-channel="TRADE_CHANNELS[0]"
          v-model:instrument="selectedInstrument"
          ref="tradePanelRef"
        />
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped></style>
