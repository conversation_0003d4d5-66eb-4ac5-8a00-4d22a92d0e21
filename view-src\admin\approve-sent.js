
const ApproveController = require('./approve').ApproveController;

class View extends ApproveController {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '已执行订单');
        this.sendingStatus = {

            passed: { code: 0, mean: '已发送' },
            waiting: { code: 1, mean: '待审批' },
            rejected: { code: 2, mean: '驳回' },
        };
    }

    handleInstruction(event, instruction) {
        
        const rowd = this.reshapeOrder(instruction);
        if (rowd.instructionStatus == this.statuses.waiting.code) {

            let rkey = this.identifyRecord(rowd);
            if (this.tableObj.hasRow(rkey)) {
                this.tableObj.deleteRow(rkey);
            }
        }
        else {
            this.tableObj.putRow(rowd);
        }
    }

    formatApproveStatus(order, istatus) {

        let { executeStatus, errorCode, errorMsg } = order;
        let errorTitle = this.helper.isNotNone(errorCode) && this.helper.isNotNone(errorMsg) ? `title="错误信息：${errorCode}/${errorMsg}"` : '';
        let as = this.sendingStatus;
        let result;

        switch (istatus) {

            case as.passed.code:
                result = [as.passed.mean, 's-bg-green'];
                break;

            case as.waiting.code:
                result = [as.waiting.mean, 's-bg-yellow'];
                break;

            case as.rejected.code:
                result = [as.rejected.mean, 's-bg-red'];
                break;

            default:
                result = ['unknown', ''];
        }

        return `<span class="s-flag ${result[1]}" ${errorTitle}>${result[0]}</span>`;
    }

    formatApproveStatusText(order, istatus) {

        let as = this.sendingStatus;

        switch (istatus) {

            case as.passed.code:
                return as.passed.mean;

            case as.waiting.code:
                return as.waiting.mean;

            case as.rejected.code:
                return as.rejected.mean;

            default:
                return 'unknown';
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestInstructions(false, '已执行');
    }

    exportSome() {
        this.tableObj.exportAllRecords(`已执行订单-${new Date().format('yyyyMMdd')}`);
    }

    build($container) {

        super.build($container);
        this.setupTable('smt-aac');
        this.requestInstructions(false, '已执行');
        this.createToolbarApp([]);
        this.lisen2WinSizeChange(this.handleWinSizeChangeProxy);
        this.simulateWinSizeChange();
    }
}

module.exports = View;
