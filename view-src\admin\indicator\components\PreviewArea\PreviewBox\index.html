<div class="preview-box iconfont">
    <template v-if="single">
        <el-row class="single-table">
            <el-col :span="12">{{ item.name }}</el-col>
            <el-col :span="12">{{ getValue(item) }}</el-col>
        </el-row>
    </template>
    <template v-if="multi">
        <div class="multi-title">{{ item.name }}</div>
        <template v-if="item.widget == 'chart'">
            <v-chart v-if="!empty" ref="chart" theme="dark" :options="option" style="width: 100%;" :style="chartStyle">
            </v-chart>
            <div class="empty" v-if="empty">暂无数据</div>
        </template>
        <!-- <el-table max-height="400" v-if="item.widget == 'table'" :data="item.data">
            <el-table-column v-for="(column, index) in item.columns" :key="index" :label="column.label"
                :prop="column.prop" show-overflow-tooltip></el-table-column>
        </el-table> -->
        <data-tables v-if="item.widget == 'table'" :data="item.data" :page-size="100"
            :pagination-props="{ pageSizes: [50, 100, 200], layout: 'prev, pager, next, sizes, total' }"
            :table-props="{maxHeight: 400}">
            <el-table-column v-for="(column, index) in item.columns" :key="index" :label="column.label"
                :prop="column.prop" show-overflow-tooltip></el-table-column>
        </data-tables>
    </template>
    <div class="interval no-print" v-if="interval > 0 && !designMode">{{ itemInterval }}</div>
    <div class="excel no-print" v-if="!designMode">
        <el-tooltip content="导出数据" placement="bottom">
            <i class="iconfont icon-Excel" @click="handleExport"></i>
        </el-tooltip>
    </div>
</div>