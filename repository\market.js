
const http = require('../libs/http').http;
class MarketRepository {
	
	/**
	 * 获取概念、行业列表
	 * @param {Number} portfolio_type 1概念 / 2行业
	 */
	requestPortfolioes(portfolio_type) {

		return new Promise((resolve, reject) => {
			http.get(`${http.exAddr.quoteRestfulServer}/quote/v1/portfolio`, { params: { type: portfolio_type, }})
				.then((resp) => { resolve(resp.data); }, (err) => { reject(err); });
		});
	}

	/**
	 * 获取行业、概念的市场主要行情数据因子列表
	 * @param portfolio_code
	 */
	requestPortfolioStocks(portfolio_code) {
		return new Promise((resolve, reject) => {
			http.get(`${http.exAddr.quoteRestfulServer}/quote/v1/portfolio/content`, { params: { code: portfolio_code }})
				.then((resp) => { resolve(resp.data); }, (err) => { reject(err); });
		});
	}

	/**
	 * 获取行业、概念的市场主要行情数据因子列表
	 */
	getFactors() {
		return new Promise((resolve, reject) => {
			http.get(`${http.exAddr.quoteRestfulServer}/quote/v1/portfolio/attribute`)
				.then((resp) => { resolve(resp.data); }, (err) => { reject(err); });
		});
	}

	/**
	 * 获取行业、概念的市场主要行情数据因子，在当前时刻的最新值
	 */
	getFactorValues(instruments, indicators) {
		return new Promise((resolve, reject) => {
			http.post(`${http.exAddr.quoteRestfulServer}/quote/v1/portfolio/indicator`, {
				instruments,
				indicators
			}).then(
				(resp) => {
					resolve(resp.data);
				},
				(err) => {
					reject(err);
				},
			);
		});
	}

	getStockClassify(instruments) {
		let ins = Array.isArray(instruments) ? instruments : [instruments];
		return new Promise((resolve, reject) => {
			http.post(`${http.exAddr.quoteRestfulServer}/quote/v1/portfolio`, ins).then(
				(resp) => {
					resolve(resp.data);
				},
				(err) => {
					reject(err);
				},
			);
		});
	}
}

module.exports = { repoMarket: new MarketRepository() };
