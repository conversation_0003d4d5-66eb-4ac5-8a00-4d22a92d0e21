const { CreditBaseView } = require('./credit-base');
const { repoCredit } = require('../../../../repository/credit');

class View extends CreditBaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '两融合约');
        this.registerEvent('set-context-account', this.setAccount.bind(this));
    }

    createToolbar() {

        super.createToolbar();
        this.toolbarView.registerEvent('show-return-money-prompt', this.showReturnMoneyPrompt.bind(this));
    }

    async requestCreditData(criteria) {
        return await repoCredit.getContractInfo(criteria);
    }

    showReturnMoneyPrompt() {

        var records = this.tableObj.extractAllRecords();
        var openings = records.filter(item => item.tradeAmount != 0);

        if (records.length == 0) {
            return this.interaction.showMessage('无两融合约');
        }
        else if (openings.length == 0) {
            return this.interaction.showMessage('没有未了结的两融合约');
        }
        
        var thisObj = this;
        var totalAmount = openings.sum(item => item.tradeAmount);
        this.interaction.propmt(`待偿还总金额 = ${totalAmount}`,
                                {
                                    title: '信用业务 ~ 直接还款',
                                    inputValue: (totalAmount || 0).toFixed(2),
                                    inputValidator: function (value) {

                                        let amount = +value;
                                        if (isNaN(amount)) {
                                            return '请输入数值';
                                        }
                                        
                                        amount = +amount.toFixed(2);
                                        if (amount <= 0) {
                                            return '请输入有效的还款金额';
                                        }
                                        else if (amount > totalAmount) {
                                            return `还款金额${amount} > 未偿还总金额${totalAmount}`;
                                        }
                                    }
                                },
                                function (result) {

                                    if (result.action == 'confirm') {
                                        thisObj.returnMoney(+result.value);
                                    }
                                });
    }

    returnMoney(amount) {

        if (amount <= 0) {
            return;
        }
        else if (!this.isBySingleAccount) {
            return this.interaction.showError('仅在普通交易中，支持两融还款业务');
        }

        var dict = this.systemTrdEnum;
        var account = this.contextAccount;

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

            strategyId: account.fundId,
            accountId: account.accountId,
            userId: this.userInfo.userId,
            price: amount,
            volume: 0,
            instrument: null,
            priceType: dict.pricingType.fixedPrice.code,
            bsFlag: dict.tradingDirection.sell.code,
            businessFlag: dict.businessFlag.closeWithCapital.code,
            positionEffect: dict.positionEffect.close.code,
            customId: 'return-money-directly',
            orderTime: null,
            hedgeFlag: dict.hedgeFlag.Speculate.code,
        });

        this.interaction.showSuccess('直接还款，请求已发出');
    }

    build($container, options) {
        super.build($container, options, { isDebted: false }, 'table-credit-contract');
    }
}

module.exports = View;