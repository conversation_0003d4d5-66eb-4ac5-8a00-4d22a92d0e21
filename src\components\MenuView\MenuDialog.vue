<script setup lang="ts">
import { computed, reactive, watch, shallowRef } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { MomMenu, MomMenuTree, FormMenu } from '../../../../xtrade-sdk/dist';
import { AdminService } from '@/api';

interface Props {
  visible: boolean;
  menu?: MomMenu;
  parentMenuId?: number;
  menuTree: MomMenuTree[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'saved'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单引用
const formRef = shallowRef<FormInstance>();

// 表单数据
const formData = reactive<FormMenu & { sequence: number }>({
  menuName: '',
  menuIcon: '',
  parentMenuId: undefined,
  active: true,
  sequence: 0,
});

// 表单验证规则
const rules: FormRules = {
  menuName: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { min: 1, max: 50, message: '菜单名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  sequence: [
    { required: true, message: '请输入菜单权重', trigger: 'blur' },
    { type: 'number', min: 0, message: '菜单权重必须大于等于0', trigger: 'blur' },
  ],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
});

const isEdit = computed(() => !!props.menu);

const dialogTitle = computed(() => (isEdit.value ? '编辑菜单' : '创建菜单'));

// 扁平化菜单树用于父级菜单选择
const flatMenuOptions = computed(() => {
  const options: Array<{ label: string; value: number; disabled?: boolean }> = [];

  const flatten = (menus: MomMenuTree[], level = 0) => {
    menus.forEach(menu => {
      // 编辑时不能选择自己作为父级
      const disabled = isEdit.value && menu.id === props.menu?.id;

      options.push({
        label: '　'.repeat(level) + menu.menuName,
        value: menu.id,
        disabled,
      });

      if (menu.children && menu.children.length > 0) {
        flatten(menu.children, level + 1);
      }
    });
  };

  flatten(props.menuTree);
  return options;
});

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      resetForm();
    }
  },
);

// 重置表单
const resetForm = () => {
  if (props.menu) {
    // 编辑模式
    Object.assign(formData, {
      menuName: props.menu.menuName,
      menuIcon: props.menu.menuIcon,
      parentMenuId: props.menu.parentMenuId,
      active: props.menu.active,
      sequence: props.menu.sequence,
    });
  } else {
    // 创建模式
    Object.assign(formData, {
      menuName: '',
      menuIcon: '',
      parentMenuId: props.parentMenuId,
      active: true,
      sequence: 0,
    });
  }

  // 清除验证状态
  formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = () => {
  if (!formRef.value) return;

  formRef.value.validate(async valid => {
    if (valid) {
      if (isEdit.value && props.menu) {
        // 编辑菜单
        const updateData: MomMenu = {
          ...props.menu,
          ...formData,
        };
        await AdminService.updateMenu(updateData);
        ElMessage.success('菜单更新成功');
      } else {
        // 创建菜单
        await AdminService.createMenu(formData);
        ElMessage.success('菜单创建成功');
      }

      emit('saved');
    }
  });
};

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="400px"
    :close-on-click-modal="false"
    draggable
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="菜单名称" prop="menuName">
        <el-input
          v-model="formData.menuName"
          placeholder="请输入菜单名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="菜单图标" prop="menuIcon">
        <el-input
          v-model="formData.menuIcon"
          placeholder="请输入菜单图标"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="菜单权重" prop="sequence">
        <el-input-number
          class="sequence-input"
          v-model="formData.sequence"
          :min="0"
          :max="9999"
          placeholder="请输入菜单权重"
          w-full
          :controls="false"
        />
      </el-form-item>

      <el-form-item label="父级菜单">
        <el-select
          v-model="formData.parentMenuId"
          placeholder="请选择父级菜单（不选则为顶级菜单）"
          clearable
          filterable
          w-full
        >
          <el-option
            v-for="option in flatMenuOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否启用">
        <el-switch v-model="formData.active" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button flex-1 @click="handleCancel">取消</el-button>
      <el-button flex-1 type="primary" @click="handleSubmit">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.sequence-input {
  width: 100%;
}
</style>
