<template>
  <div class="form-container" overflow-y-auto overflow-x-hidden>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="110px"
      w-full
      mt-10
    >
      <div class="half-col">
        <el-form-item label="账号名称" prop="accountName">
          <el-input v-model.trim="formData.accountName" placeholder="账号名称" clearable>
            <template #prefix>
              <i class="iconfont icon-instruction"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="所属机构" prop="orgId">
          <OrgSelect
            v-model="formData.orgId"
            from="account"
            :filter-by-user-org="!hasGlobalDataScope"
            @change="handleOrgChange"
          />
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="所属产品" prop="funds">
          <ProductSelect
            w-full
            v-model="states.fundId"
            :real="true"
            from="account"
            :org-id="formData.orgId"
            @change="handleFundChange"
          />
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="资产类型" prop="assetType">
          <el-select
            v-model="formData.assetType"
            @change="handleAssetTypeChange"
            placeholder="请选择资产类型"
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in AssetTypes"
              :key="idx"
              :label="item.Label"
              :value="item.Value"
            />
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="账号类型" prop="credit">
          <el-select v-model="formData.credit" placeholder="请选择账号类型" filterable clearable>
            <el-option label="普通账号" :value="false" />
            <el-option label="信用账号" :value="true" />
            <template #prefix>
              <i class="iconfont icon-strategy"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="经纪商" prop="bkId">
          <BrokerSelect v-model="formData.bkId" from="account" @change="handleBrokerChange" />
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="营业部代码" prop="extInfo.yybId">
          <el-input v-model.trim="formData.extInfo.yybId" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="沪市股东代码" prop="extInfo.shSecId">
          <el-input v-model.trim="formData.extInfo.shSecId" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="深市股东代码" prop="extInfo.szSecId">
          <el-input v-model.trim="formData.extInfo.szSecId" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="资金账号" prop="financeAccount">
          <el-input v-model.trim="formData.financeAccount" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="交易账号" prop="extInfo.tradeAccount">
          <el-input v-model.trim="formData.extInfo.tradeAccount" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isFuture" class="half-col">
        <el-form-item label="APPID" prop="extInfo.tradeAccount">
          <el-input v-model.trim="formData.extInfo.tradeAccount" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="账号密码" prop="pwd">
          <el-input type="password" v-model.trim="formData.pwd" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-password"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item :label="isStock ? '通信密码' : 'AUTH CODE'" prop="extInfo.txPassword">
          <el-input type="password" v-model.trim="formData.extInfo.txPassword" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-password"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isFuture" class="half-col">
        <el-form-item label="AuthCode" prop="authCode">
          <el-input v-model.trim="formData.authCode" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-password"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="交易终端" prop="terminals">
          <TerminalSelect
            v-model="selectedTerminalId"
            from="account"
            @change="handleTerminalChange"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
  <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button v-if="isEdit && canTestConnection" type="success" @click="testConn" w-220>
      连通性测试
    </el-button>
    <el-button type="primary" @click="check" w-220>确定</el-button>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  reactive,
  ref,
  shallowRef,
  useTemplateRef,
  defineAsyncComponent,
} from 'vue';
import { type AccountInfo, type ProductInfo } from '@/types';
import { deepClone, getUser, hasGlobalDataPermission, isNone, hasPermission } from '@/script';
import { ElMessage } from 'element-plus';
import { MenuPermitAccountManagement } from '@/enum';

import {
  AssetType,
  CreateEmptyAccountRecord,
  Repos,
  type LegacyAccountInfo,
  type MomBroker,
  type MomOrganization,
  type MomTerminal,
} from '../../../../xtrade-sdk/dist';

const ProductSelect = defineAsyncComponent(() => import('../common/ProductSelect.vue'));
const OrgSelect = defineAsyncComponent(() => import('../common/OrgSelect.vue'));
const BrokerSelect = defineAsyncComponent(() => import('../common/BrokerSelect.vue'));
const TerminalSelect = defineAsyncComponent(() => import('../common/TerminalSelect.vue'));

const { contextProduct } = defineProps<{
  /** 关联的产品，用于账号创建时回填 */
  contextProduct?: ProductInfo | null;
}>();

const formRef = useTemplateRef('formRef');
const AssetTypes = [AssetType.Stock, AssetType.Future, AssetType.Option];
const repoInstance = new Repos.AdminRepo();
const repoGovInstance = new Repos.GovernanceRepo();
const hasGlobalDataScope = ref(hasGlobalDataPermission());
const formData = ref<AccountInfo>(CreateEmptyAccountRecord());

const currentUser = getUser()!;
const states = reactive({
  terminals: [] as number[],
  fundId: null as any,
});

// 终端选择相关
const selectedTerminalId = ref<number | undefined>();

const rules = {
  accountName: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
  orgId: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],
  funds: [{ required: true, message: '请选择所属产品', trigger: 'blur' }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'blur' }],
  credit: [{ required: true, message: '请选择账号类型', trigger: 'blur' }],
  bkId: [{ required: true, message: '请选择经纪商', trigger: 'blur' }],
  financeAccount: [{ required: true, message: '请输入资金账号', trigger: 'blur' }],
  // pwd: { required: true, message: '请输入资金账号密码' },
  'extInfo.tradeAccount': [{ required: true, message: '请输入交易账号', trigger: 'blur' }],
  'extInfo.yybId': [{ required: true, message: '请输入营业部代码', trigger: 'blur' }],
  'extInfo.shSecId': [{ required: true, message: '请输入沪市股东代码', trigger: 'blur' }],
  'extInfo.szSecId': [{ required: true, message: '请输入深市股东代码', trigger: 'blur' }],
};

const isStock = computed(() => {
  return formData.value.assetType == AssetType.Stock.Value;
});

const isFuture = computed(() => {
  return formData.value.assetType == AssetType.Future.Value;
});

const isEdit = computed(() => {
  return !!formData.value.id;
});

const canTestConnection = computed(() => {
  return hasPermission(MenuPermitAccountManagement.连通性测试);
});

const emitter = defineEmits<{
  cancel: [];
  save: [data: LegacyAccountInfo];
}>();

async function testConn() {
  const { errorCode, errorMsg } = await repoGovInstance.ConnectAccount(formData.value.id);
  if (errorCode === 0) {
    ElMessage.success('测试结果：连接成功');
  } else {
    ElMessage.error(errorMsg || '未能连接');
  }
}

const cancel = () => {
  emitter('cancel');
  formRef.value?.clearValidate();
};

const check = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      save();
    }
  });
};

function save() {
  const obj = formData.value;

  // 确保终端数据同步
  if (selectedTerminalId.value && !states.terminals.includes(selectedTerminalId.value)) {
    states.terminals = [selectedTerminalId.value];
  }

  const list = terminals.value.filter(x => states.terminals.includes(x.id));

  // 选择的终端列表（保存成功后，需单独再绑定终端）
  obj.terminals = list.map(x => {
    const { id, interfaceType, status, terminalName } = x;
    return {
      id,
      interfaceType,
      status,
      terminalName,
    };
  });

  // 所属产品已经在handleFundChange中设置好了
  submitData(deepClone(obj));
}

async function submitData(row: LegacyAccountInfo) {
  const isModify = !!row.id;
  const behavior = isModify ? '修改' : '创建';

  if (isNone(row.orgId)) {
    // 补充必要的字段
    row.orgId = currentUser.orgId;
    row.orgName = currentUser.orgName;
  }

  const { errorCode, errorMsg, data } = isModify
    ? await repoGovInstance.UpdateAccount(row)
    : await repoGovInstance.CreateAccount(row);

  if (errorCode === 0) {
    ElMessage.success(`${behavior}成功`);
    const account_id = data!.id;
    const terminal_ids = row.terminals.map(x => x.id);
    // 绑定终端
    if (terminal_ids.length > 0) {
      await repoGovInstance.BindAccountTerminals(account_id, terminal_ids);
    }
    // 绑定产品
    const { errorCode, data: product } = await repoGovInstance.QueryProductDetails([
      row.funds[0].fundId,
    ]);
    if (errorCode == 0 && Array.isArray(product)) {
      await repoGovInstance.BindProductAccounts(
        row.funds[0].fundId,
        product[0].fundAccounts.map(x => x.accountId).concat(account_id),
      );
    }
    emitter('save', data!);
  } else {
    ElMessage.error(errorMsg || `${behavior}失败`);
  }
}

function handleFundChange(_value?: string | undefined, product?: ProductInfo) {
  // 选择所属产品
  if (product) {
    formData.value.funds = deepClone([{ fundId: product.id, fundName: product.fundName }]);
  } else {
    formData.value.funds = [];
  }
}

function handleAssetTypeChange() {
  const obj = formData.value;
  const newVal = obj.assetType;

  if (newVal != AssetType.Stock.Value) {
    obj.credit = false;
  }

  obj.bkId = null as any;
  obj.brokerId = null as any;
  states.terminals = [];
}

const orgs = shallowRef<MomOrganization[]>([]);

const borkers = shallowRef<MomBroker[]>([]);
const terminals = shallowRef<MomTerminal[]>([]);

function handleBrokerChange(value: number | undefined, broker?: MomBroker) {
  if (broker) {
    formData.value.brokerId = broker.brokerId;
    formData.value.brokerName = broker.brokerName;
  } else {
    formData.value.brokerId = '';
    formData.value.brokerName = '';
  }
}

function handleOrgChange(value: number | undefined, org?: MomOrganization) {
  if (org) {
    formData.value.orgName = org.orgName;
  } else {
    formData.value.orgName = '';
  }
  // 清空选择的所属产品
  states.fundId = null;
  formData.value.funds = [];
}

// 处理终端选择变化
function handleTerminalChange(value: number | undefined, terminal?: MomTerminal) {
  if (value && terminal) {
    // 将选择的终端添加到states.terminals数组中
    if (!states.terminals.includes(value)) {
      states.terminals.push(value);
    }
  }
}

async function requestBrokers() {
  borkers.value = (await repoInstance.QueryBrokers()).data || [];
}

async function requestTerminals() {
  terminals.value = (await repoInstance.QueryTerminals()).data || [];
}

async function requestOrgs() {
  orgs.value = (await repoInstance.QueryOrgs()).data || [];
}

async function reset(context: AccountInfo | null) {
  const obj = (formData.value = context ? deepClone(context) : CreateEmptyAccountRecord());
  states.terminals = obj.terminals.map(x => x.id);
  states.fundId = obj.funds.length > 0 ? obj.funds[0].fundId : null;

  // 设置终端选择器的值（取第一个终端，因为新组件是单选）
  selectedTerminalId.value = states.terminals.length > 0 ? states.terminals[0] : undefined;

  requestBrokers();
  requestTerminals();
  requestOrgs();
  if (contextProduct) {
    formData.value.orgId = contextProduct.orgId;
    formData.value.orgName = contextProduct.orgName;
    states.fundId = contextProduct.id;
    handleFundChange(contextProduct.id, contextProduct);
  }
}

defineExpose({
  reset,
});

onMounted(() => {
  //
});
</script>

<style scoped>
.form-container {
  .half-col {
    float: left;
    width: 50%;
  }
  :deep() {
    .el-form-item__label {
      position: relative;
    }
    .el-form-item {
      width: 95%;
      margin-right: 10px !important;
    }
  }
}
</style>
