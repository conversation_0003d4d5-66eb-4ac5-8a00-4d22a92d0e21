<div>
	
	<el-select v-model="uistates.typeId"
			   placeholder="选择类型" 
			   class="s-w-100 s-mgr-10" clearable>

		<el-option v-for="(item, item_idx) in types" 
					:key="item_idx" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="istates.productId"
			   v-show="istates.showProduct"
			   placeholder="选择产品" 
			   @change="handleProductChange" 
			   class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="(item, item_idx) in products"
					:key="item_idx" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="istates.accountId" 
				v-show="istates.showAccount"
				placeholder="选择账号" 
				@change="handleAccountChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="(item, item_idx) in accounts" 
					:key="item_idx"
					:label="formatSelectAccountName(item)"
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="uistates.subsistId"
				placeholder="选择状态"
				class="s-w-100 s-mgr-10" clearable>
		
		<el-option v-for="(item, item_idx) in subsists" 
					:key="item_idx" 
					:label="item.label"
					:value="item.value"></el-option>
	</el-select>

	<el-input placeholder="关键字过滤" 
			  prefix-icon="el-icon-search" 
			  v-model="uistates.keywords" 
			  class="s-w-150 s-mgr-10" clearable></el-input>

	<el-button type="primary" icon="el-icon-search" @click="handleSearch"></el-button>
	<el-button v-if="uistates.showReturnMoneyButton" type="primary" class="s-pull-right" @click="showReturnMoneyPrompt">直接还款</el-button>
	
</div>