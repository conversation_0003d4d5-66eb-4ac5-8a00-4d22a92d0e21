const IView = require('../../component/iview').IView;
const SmartTable = require('../../libs/table/smart-table').SmartTable;

class View extends IView {

    get indicatorRepo() {
        return require('../../repository/indicator').repoIndicator;
    }

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '报告');
        this.reportDialog = { visible: false };
    }

    refresh() {
        this.requestReports();
    }

    createComponent() {

        var record_identifier = function (record) { return record.id; };
        
        this.tableObj = new SmartTable(this.$container.querySelector('table'), record_identifier, this, {

            tableName: 'smt-report',
            displayName: '报告',
            pageSize: 9999,
        });

        this.tableObj.setMaxHeight(500);
    }

    createApp() {

        this.vueApp = new Vue({

            el: this.$container.querySelector('.report-container'),
            data: {
                reportDialog: this.reportDialog,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.destroyReport]),
        });
    }

    destroyReport() {

        this.reportDialog.visible = false;
        if (this.newReport !== null && this.newReport !== undefined) {

            this.newReport.dispose();
            delete this.newReport;
        }
    }

    openReport(this_report) {

        this.reportDialog.visible = true;
        setTimeout(() => { this.loadBuildReport(this_report); }, 200);
    }

    loadBuildReport(this_report) {

        const NewReport = require('../admin/new-report');
        var param = { identity: this_report.id, identityName: this_report.reportName, templateId: this_report.templateId };
        this.newReport = new NewReport('@admin/new-report', true, { title: `报告 》 ${this_report.reportName}` });
        this.newReport.trigger('setContextData', param);
        this.newReport.loadBuild(this.vueApp.$el.querySelector('.container-content'));
    }

    async requestReports() {

        let $loading = this.interaction.showLoading({ text: '获取报告列表...' });
        try {

            let resp = await this.indicatorRepo.getReportDocumentList();
            if (resp.errorCode === 0) {

                let reports = resp.data;
                this.tableObj.refill(reports);
            } 
            else {
                this.interaction.showError(`获取报告列表失败，错误信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } 
        catch (e) {
            this.interaction.showError('获取报告列表失败!');
        } 
        finally {
            $loading.close();
        }
    }

    adjustTableHeight(win_width, win_height, is_maximized) {
        this.tableObj.setMaxHeight(win_height - (is_maximized ? 130 : 115));
    }

    build($container) {

        super.build($container);
        this.createComponent();
        this.requestReports();
        this.createApp();
        this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
    }
}

module.exports = View;
