<template>
  <div>
    <el-form :model="formData" label-width="100px">
      <el-form-item label="账号名称">
        <el-input :model-value="accountName" disabled />
      </el-form-item>
      <el-form-item label="交易日" required>
        <el-date-picker
          v-model="formData.trading_day"
          type="date"
          placeholder="选择交易日"
          format="YYYY-MM-DD"
          value-format="YYYYMMDD"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="入金金额">
        <el-input-number v-model="formData.in_money" :precision="2" :min="0" style="width: 100%" />
      </el-form-item>
      <el-form-item label="出金金额">
        <el-input-number v-model="formData.out_money" :precision="2" :min="0" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef } from 'vue';
import { ElMessage } from 'element-plus';
import { Repos } from '../../../../xtrade-sdk/dist';
import { formatDateTime } from '@/script';

interface IoMoneyFormData {
  trading_day: string;
  in_money: number;
  out_money: number;
}

const recordsRepo = new Repos.RecordsRepo();

const { accountName } = defineProps<{
  accountName: string;
}>();

const emitter = defineEmits<{
  save: [];
  cancel: [];
}>();

const accountId = shallowRef('');
const formData = ref<IoMoneyFormData>({
  trading_day: '',
  in_money: 0,
  out_money: 0,
});

const reset = (id: string, name: string) => {
  accountId.value = id;
  formData.value.trading_day = formatDateTime(new Date(), 'yyyyMMdd');
  formData.value.in_money = 0;
  formData.value.out_money = 0;
};

const cancel = () => {
  emitter('cancel');
};

const save = async () => {
  const { errorCode, errorMsg } = await recordsRepo.insertIoMoney(accountId.value, {
    trading_day: formData.value.trading_day,
    in_money: formData.value.in_money,
    out_money: formData.value.out_money,
  });

  if (errorCode === 0) {
    ElMessage.success('出入金记录保存成功');
    emitter('save');
  } else {
    ElMessage.error(errorMsg || '出入金记录保存失败');
  }
};

defineExpose({
  reset,
});
</script>
