/**
 * user related enumerations
 */

const systemUserEnum = {

    userRole: {

        superAdmin: { code: 1, mean: '超级管理员' },
        brokerAdmin: { code: 8, mean: '券商运维人员' },
        orgAdmin: { code: 2, mean: '机构管理员' },
        counselor: { code: 3, mean: '投资顾问' },
        product: { code: 4, mean: '产品经理' },
        riskProtector: { code: 5, mean: '风控员' },
        observing: { code: 6, mean: '查看员' },
        tradingMan: { code: 7, mean: '交易员' },
        investmentManager: { code: 10, mean: '投资经理' },
    },

    loginResult: {

        ok: { code: 0, mean: '登录成功' },
        userNameOrPasscodeError: { code: 1, mean: '用户名或密码错误' },
        userNameNonExist: { code: 10002, mean: '用户名不存在' },
        passcodeError: { code: 10003, mean: '密码错误' },
        userDisabled: { code: 10004, mean: '账号已被禁止登入' },
        alreadySignedIn: { code: 10005, mean: '账号已登录' },
    },

    flowAudit: {
        
        stepOver: { code: 0, mean: '跳过' },
        pause: { code: 1, mean: '待审批' },
        reject: { code: 2, mean: '驳回' },
    },
};

module.exports = { systemUserEnum };
