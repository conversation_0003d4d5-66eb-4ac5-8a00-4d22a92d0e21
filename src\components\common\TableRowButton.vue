<script lang="ts" setup>
import type { AnyObject, RowAction } from '@/types';
import { computed } from 'vue';

const props = defineProps<{
  /** 行操作 */
  action: RowAction<any>;
  /** 行操作 */
  rowData: AnyObject;
}>();

const hasNested = computed(() => {
  return props.action.nesteds && props.action.nesteds.length > 0;
});

function handleClick(event: MouseEvent) {
  event.stopPropagation();
  props.action.onClick(props.rowData);
}

function handleNestClick(event: MouseEvent, nested: RowAction<any>) {
  event.stopPropagation();
  nested.onClick(props.rowData);
}
</script>
<template>
  <template v-if="hasNested">
    <el-tooltip effect="light" placement="left" trigger="click">
      <template #content>
        <div v-for="(nested_item, nested_idx) in action.nesteds" :key="nested_idx">
          <div
            v-if="!nested_item.show || nested_item.show(rowData)"
            size="small"
            class="typical-text-button each-button"
            :class="nested_item.type || ''"
            @click="
              e => {
                handleNestClick(e, nested_item);
              }
            "
            :disabled="nested_item.disabled && nested_item.disabled(rowData)"
            link
          >
            <i v-if="nested_item.icon" :class="['iconfont', 'icon-' + nested_item.icon]" />
            <span>{{ nested_item.label || '' }}</span>
          </div>
        </div>
      </template>
      <el-button
        size="small"
        class="typical-text-button"
        @click="handleClick"
        :disabled="action.disabled && action.disabled(rowData)"
        link
      >
        <i v-if="action.icon" :class="['iconfont', 'icon-' + action.icon]" />
        {{ action.label || '' }}
      </el-button>
    </el-tooltip>
  </template>
  <template v-else>
    <el-button
      size="small"
      class="typical-text-button"
      @click="handleClick"
      :disabled="action.disabled && action.disabled(rowData)"
      link
    >
      <i v-if="action.icon" :class="['iconfont', 'icon-' + action.icon]" />
      {{ action.label || '' }}
    </el-button>
  </template>
</template>

<style scoped>
.each-button {
  height: 32px;
  line-height: 32px;
  padding: 0 20px;
  border-radius: 5px;
  cursor: default;
  justify-content: start;

  &:hover {
    background-color: var(--g-block-bg-6);
  }

  i {
    font-size: 20px;
  }

  span {
    padding-left: 10px;
    font-size: 14px;
    font-weight: 400;
    color: var(--g-text-color-6);
  }

  &.danger {
    opacity: 1 !important;
    i,
    span {
      color: var(--g-red) !important;
    }
  }
}
</style>
