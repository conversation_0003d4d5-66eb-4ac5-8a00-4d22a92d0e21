const BatchChildView = require('./batch-child');
const { TickData } = require('../../model/message');

class PriceItem {

    /**
     * @param {String} label 
     * @param {Number} value 
     */
    constructor(label, value, options = { isPercent: false, isAmount: false }) {

        this.label = label;
        this.value = value;
        this.isPercent = !!options.isPercent;
        this.isAmount = !!options.isAmount;
        this.colorClass = null;
    }

    /**
     * @param {Number} value 
     * @param {Number} base 
     */
    update(value, base) {

        this.value = value;
        this.updateClass(value, base);
    }

    /**
     * @param {Number} value 
     * @param {Number} base 
     */
    updateClass(value, base) {
        this.colorClass = value > base ? 's-color-red' : value < base ? 's-color-green' : '';
    }
}

class View extends BatchChildView {

    constructor(view_name) {

        super(view_name);
        
        this.prices = {

            change: new PriceItem('涨跌幅', 0, { isPercent: true }),
            latest: new PriceItem('最新价', 0),
            open: new PriceItem('今开', 0),
            highest: new PriceItem('最高', 0),
            lowest: new PriceItem('最低', 0),
            preclose: new PriceItem('昨收', 0),
            amount: new PriceItem('成交额', '0.00', { isAmount: true }),
        };

        var local = this.prices;
        this.pricesArr = [local.change, local.latest, local.open, local.highest, local.lowest, local.preclose, local.amount];
        this.uistates = {
            rowHeight: +(this.childViewHeight / this.pricesArr.length).toFixed(3),
        };

        this.registerEvent('set-first-tick', this.updatePrices.bind(this));
    }

    createApp() {

        this.viewIns = new Vue({

            el: this.$container.firstElementChild,
            data: {

                channel: this.channel,
                states: this.states,
                uistates: this.uistates,
                prices: this.prices,
            },
            mixins: [this.NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.formatViewTitle,
                this.handleSelect,
            ]),
        });
    }

    handleSelect(price) {
        this.trigger('price-selected', price);
    }

    handleInstrumentChange(lastIns, currentIns) {
        this.resetFields();
    }

    resetFields() {

        var local = this.prices;
        local.change.update(0, 0);
        local.latest.update(0, 0);
        local.change.updateClass(0, 0);
        local.open.update(0, 0);
        local.highest.update(0, 0);
        local.lowest.update(0, 0);
        local.preclose.update(0, 0);
        local.amount.update('0.00', 0);
    }

    /**
     * @param {TickData} tick 
     */
    updatePrices(tick) {

        var local = this.prices;
        var preclose = tick.preclose;

        local.change.value = 100 * tick.change;
        local.change.updateClass(tick.change, 0);
        local.latest.update(tick.latest, preclose);
        local.open.update(tick.open, preclose);
        local.highest.update(tick.high, preclose);
        local.lowest.update(tick.low, preclose);
        local.preclose.update(preclose, preclose);
        local.amount.update(tick.amountShortWords, 0);

        var rows = this.pricesArr.filter(item => item.value >= 0).length;
        var rowHeight = +(this.childViewHeight / rows).toFixed(3);
        this.states.rowHeight = rowHeight;
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;