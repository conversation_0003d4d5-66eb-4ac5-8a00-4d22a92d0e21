<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';
import type { MomUser, UserPasswordReset } from '../../../../xtrade-sdk/dist';

const { user } = defineProps<{
  user?: MomUser;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: (param?: any) => void) => {
        if (value !== form.value.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
};

const formRef = useTemplateRef('formRef');

const form = ref({
  newPassword: '',
  confirmPassword: '',
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  form.value = {
    newPassword: '',
    confirmPassword: '',
  };
  formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid && user) {
      try {
        const resetData: UserPasswordReset = {
          username: user.username,
          password: form.value.newPassword,
        };

        const { errorCode, errorMsg } = await AdminService.resetUserPassword(resetData);
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('密码重置成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '密码重置失败');
        }
      } catch (error) {
        console.error('密码重置失败:', error);
        ElMessage.error('密码重置失败');
      }
    }
  });
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};
</script>

<template>
  <el-dialog :model-value="visible" title="重置用户密码" width="400px" @close="handleClose">
    <div v-if="user" mb-20>
      <div class="user-info" p-15 bg="[--el-fill-color-light]" rounded>
        <div flex aic mb-10>
          <span class="label" w-80 text="[--el-text-color-regular]">用户名：</span>
          <span font-medium>{{ user.username }}</span>
        </div>
        <div flex aic>
          <span class="label" w-80 text="[--el-text-color-regular]">真实姓名：</span>
          <span font-medium>{{ user.fullName }}</span>
        </div>
      </div>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="form.newPassword"
          type="password"
          placeholder="请输入新密码"
          show-password
        />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.user-info {
  border: 1px solid var(--el-border-color-light);
}

.label {
  font-size: 14px;
}
</style>
