<script setup lang="ts">
import { ref } from 'vue';

const isMaximized = ref(false);
const handleMinimize = () => {
  window.ipcRenderer.send('window-minimize');
};

const handleMaximize = () => {
  window.ipcRenderer.send('window-maximize');
};

const handleClose = () => {
  window.ipcRenderer.send('window-close');
};

// 监听窗口状态变化
window.ipcRenderer.on('window-state-changed', (event, max: boolean) => {
  console.log(111);

  isMaximized.value = max;
});
</script>

<template>
  <div class="app-header" px-20 flex aic jcsb h-30 bg-dark>
    <div class="title" c-white fs-20>高御信息量化交易平台</div>
    <div class="controls" h-full z-1000 flex aic>
      <div i-mdi-window-minimize mr-10 c-white cursor-pointer @click="handleMinimize" />
      <div
        :class="isMaximized ? 'i-mdi-window-restore' : 'i-mdi-window-maximize'"
        mr-10
        c-white
        cursor-pointer
        @click="handleMaximize"
      />
      <div i-mdi-window-close c-white cursor-pointer @click="handleClose" />
    </div>
  </div>
</template>

<style scoped>
.app-header {
  -webkit-app-region: drag;
  .controls {
    -webkit-app-region: no-drag;
  }
}
</style>
