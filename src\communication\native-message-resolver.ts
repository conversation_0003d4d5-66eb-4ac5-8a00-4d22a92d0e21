import * as protobufjs from 'protobufjs';
import snappyjs from 'snappyjs';
import Utils from '../modules/utils';
import { StandardTick } from '../types/common';
import { TickType } from '../config/trading';
import { ServerEvent } from '../config/server-event';
import { SocketDataPackage } from '../types/data-package';
import { GetLogger } from '../global-state';
import { MessageResolver } from './message-resolver';

const defaultLogger = GetLogger();

const states = {

    tickRoot: null as (protobufjs.Root | null),
    tickTranslator: {

        tick: null as (protobufjs.Type | null),
        transaction: null as (protobufjs.Type | null),
        kline: null as (protobufjs.Type | null),
    }
};

/**
 * 初始化tick translator
 * @param protocolFile 行情数据解析协议文件（路径）
 */
export function Try2InitTickTranslator(protocolFile: string) {

    if (states.tickRoot) {
        return;
    }

    try {
        
        const root = protobufjs.loadSync(protocolFile);
        states.tickRoot = root;
        states.tickTranslator = {

            tick: root.lookupType('proto.TickBean'),
            transaction: root.lookupType('proto.Transaction'),
            kline: root.lookupType('proto.KLineBean'),
        };
    } 
    catch (error) {

        defaultLogger.fatal('Failed to initialize tick translator:', error);
        throw error;
    }
}

/**
 * 基于NetSocket的消息数据包编码解码类
 */
export class NsMessageResolver extends MessageResolver {

    /** 单次接收的数据不完整，暂存的已接收片段 */
    private messageSlice: Buffer | null;

    constructor() {

        super();
        this.messageSlice = null;
    }

    /**
     * 编码要发送到NetSocket服务器的消息
     */
    encode(message: SocketDataPackage) {

        let { fc, reqId, dataType, body } = message;
        let isBuffer = Buffer.isBuffer(body);

        // BODY部分预处理

        if (isBuffer) {
            // 
        }
        else if (Utils.isNone(body)) {
            body = '';
        }
        else if (Utils.isJson(body) || Array.isArray(body) || typeof body == 'object') {
            body = JSON.stringify(body);
        }
        else {
            body = body.toString();
        }

        const { HeaderSize, ContentEncoding, IS_ENCRYPTION_ENABLED } = MessageResolver;
        const bodyBytes = isBuffer ? body.byteLength : typeof body == 'string' ? Buffer.byteLength(body, ContentEncoding) : body.length;
        const data = Buffer.alloc(HeaderSize + bodyBytes, 0);

        data.writeInt32BE(fc, 0);
        // write the high order bits (shifted over)
        data.writeInt32BE(0, 4);
        // write the low order bits
        data.writeInt32BE(reqId! & 0xffff, 8);
        data.writeInt32BE(dataType!, 12);
        data.writeInt32BE(bodyBytes, 16);

        if (isBuffer) {

            for(let idx = HeaderSize; idx < data.length; idx++) {
                data[idx] = body[idx - HeaderSize];
            }
        }
        else {
            data.write(body, HeaderSize, ContentEncoding);
        }

        let body_data = data.slice(HeaderSize);
        if (IS_ENCRYPTION_ENABLED) {
            this.endecrypt(body_data);
        }

        return data;
    }

    /**
     * 解码从服务器收到的消息
     */
    decode(buffer: Buffer): SocketDataPackage[] {

        /**
         * 合并前一个数据包的暂存数据
         */

        let mslice = this.messageSlice;
        if (mslice != null && mslice.length > 0) {
            buffer = Buffer.concat([mslice, buffer], buffer.length + mslice.length);
        }

        const { HeaderSize, INT32_TAKEN_BYTES, IS_ENCRYPTION_ENABLED } = MessageResolver;
        let messages: SocketDataPackage[] = [];
        let startIndex = 0;
        let msg_not_integrated = 'waiting for the next shall-come data package';

        while (true) {

            let left_len = buffer.length - startIndex;

            if (left_len < HeaderSize) {

                if (left_len <= 0) {
                    this.messageSlice = null;
                }
                else {
                    this.messageSlice = buffer.slice(startIndex);
                }

                break;
            }

            let fc = buffer.readInt32BE(startIndex);
            startIndex += INT32_TAKEN_BYTES;

            let noUse = buffer.readInt32BE(startIndex);
            startIndex += INT32_TAKEN_BYTES;

            let reqId = buffer.readInt32BE(startIndex);
            startIndex += INT32_TAKEN_BYTES;

            let dataType = buffer.readInt32BE(startIndex);
            startIndex += INT32_TAKEN_BYTES;

            let bodyLen = buffer.readInt32BE(startIndex);
            startIndex += INT32_TAKEN_BYTES;

            if (buffer.length - startIndex < bodyLen) {

                this.messageSlice = buffer.slice(startIndex - HeaderSize);
                break;
            }
            else {

                let body = buffer.slice(startIndex, bodyLen + startIndex);
                startIndex += bodyLen;

                if (IS_ENCRYPTION_ENABLED) {
                    this.endecrypt(body);
                }

                let fcEvent = ServerEvent[fc];
                let msg: SocketDataPackage;

                if (fc === ServerEvent.HeartBeat) {
                    msg = { fc, fcEvent, reqId, dataType, body: null };
                }
                else if (fc === ServerEvent.TickPriceChanged) {
                    msg = { fc, fcEvent, reqId, dataType, body: this.translateTick(fc, dataType, body) };
                }
                else {
                    msg = { fc, fcEvent, reqId, dataType, body: this.castBuffer2Json(fc, body) };
                }

                messages.push(msg);
            }
        }

        return messages;
    }

    /**
     * 使用protobuf协议对Tick数据进行解析
     */
    private translateTick(se: ServerEvent, tick_type: number, body: Buffer) {

        switch (tick_type) {

            case TickType.simple: return this.castBuffer2Json(se, body);
            case TickType.tick: return states.tickTranslator.tick!.decode(body) as any as StandardTick;
            case TickType.transaction: return states.tickTranslator.transaction!.decode(body);
            case TickType.kline: return states.tickTranslator.kline!.decode(body);

            default: {

                defaultLogger.fatal('Unknown tick type', tick_type);
                return null;
            }
        }
    }

    /**
     * 将字节流数据转换为JSON对象
     */
    private castBuffer2Json(se: ServerEvent, body: Buffer) {

        if (se == ServerEvent.TodayOrderPush || se == ServerEvent.TodayPositionPush || se == ServerEvent.TodayTradeRecordPush) {
            body = snappyjs.uncompress(body);
        }

        const content = body.toString(MessageResolver.ContentEncoding);

        try {
            return Utils.isNotNone(content) ? JSON.parse(content) : null;
        }
        catch(ex) {

            defaultLogger.error('Unable to parse body to json', { body, content, ex });
            return body;
        }
    }

    /**
     * 加密解密字节流数据
     */
    private endecrypt(data: Buffer, offset?: number) {

        let index = offset || 0;

        while (index < data.length) {

            let next_index = index + 1;
            if (next_index < data.length) {

                let temp = (data[next_index] ^ 127);
                data[next_index] = (data[index] ^ 127);
                data[index] = temp;
            }

            index += 2;
        }
    }
}