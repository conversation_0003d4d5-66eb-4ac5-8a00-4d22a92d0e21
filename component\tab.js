const { IView } = require('./iview');
const Routing = require('../model/routing').Routing;
const helper = require('../libs/helper').helper;

class TabOption {

    /**
     * @param {String} title
     * @param {*} advanced_options
     */
    constructor(title, advanced_options) {

        this.title = title;
        this.isNormal = advanced_options === null || advanced_options === undefined;

        if (!this.isNormal) {

            this.defaultId = advanced_options.defaultId;
            this.placeholder = advanced_options.placeholder;
            this.datasource = [];
            this.datasource.merge(advanced_options.datasource);
        }
    }
}

class Tab {

    get hasDrawn() {
        return this._hasDrawn;
    }

    /**
     *
     * @param {Routing} routing <required>
     * @param {TabOption} options <optional>
     * @param {*} view_options <optional>
     */
    constructor(routing, options, view_options) {

        /**
         * tab id
         */
        this.id = 'tab-' + helper.makeToken();

        /**
         * view routing info
         */
        this.routing = routing;

        /**
         * options of the tab
         */
        this.options = options;

        /**
         * options of the tabbed view
         */
        this.viewOptions = view_options;

        /**
         * tab root element
         */
        var $tab_root = document.createElement('div');
        $tab_root.classList.add('tab-unit');
        $tab_root.classList.add('themed-hover-color');
        this.$tabRoot = $tab_root;

        /**
         * content root element
         */
        var $tab_content = document.createElement('div');
        $tab_content.classList.add('tabcontent-box');
        $tab_content.classList.add('s-border-box');
        $tab_content.style.display = 'none';
        this.$tabContent = $tab_content;
    }

    setContainer(container) {
        this.container = container;
    }

    /**
     * set script module instance for the tab view
     * @param {IView} module_ins
     */
    setLib(module_ins) {
        this.viewEngine = module_ins;
    }

    /**
     * show tab view content
     */
    show() {

        this.$tabContent.style.display = 'block';
        // this.container.$content.appendChild(this.$tabContent);
    }

    /**
     * hide tab view content
     */
    hide() {
        this.$tabContent.style.display = 'none';
        // this.$tabContent.remove();
    }

    /**
     * close the tab, and dispose allocated resources
     */
    close() {

        if (this.viewEngine) {
            this.viewEngine.dispose();
        }

        this.$tabContent.remove();
        // delete this.$tabContent;
        this.isDisposed = true;
    }

    /**
     * draw tab view
     */
    draw() {

        if (this._hasDrawn === true) {
            return;
        }

        let permits = this.viewOptions.permits;
        this._hasDrawn = true;
        this.viewEngine.setPermits(permits instanceof Array ? permits : []);
        this.viewEngine.setOptions(this.viewOptions);
        this.viewEngine.loadBuild(this.$tabContent, this.viewOptions, () => {
            try { this.container.handlers.created(this); } catch(ex) { console.error(ex); }
        });
    }
}

module.exports = { Tab, TabOption };
