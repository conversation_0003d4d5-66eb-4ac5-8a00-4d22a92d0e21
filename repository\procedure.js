const http = require('../libs/http').http;
class ProcedureRepository {
    constructor(){}

    approvalOrder(orderIds, status) {
        return new Promise((resolve, reject) => {
            http.put('/instruction', orderIds, {
                params: {
                    instruction_status: status
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getProcedure(pId) {
        let params = {};
        if (pId !== null && pId !== undefined) {
            params.workflow_id = pId;
        }
        return new Promise((resolve, reject) => {
            http.get('/workflow', {
                params
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    removeProcedure(pId){
        return new Promise((resolve, reject) => {
            http.delete('/workflow', {
                params: {
                    workflow_id: pId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getInstructions() {
        return new Promise((resolve, reject) => {
            // var resp = require('../mock-data/orders-20191017');
            // resolve(resp);
            http.get('/instruction').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    saveProcedure(model){
        return new Promise((resolve,reject) => {
            http.post('/workflow', model).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    updateProcedure(model) {
        return new Promise((resolve,reject) => {
            http.put('/workflow', model).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    bindProcedure(workflowId, accountId) {
        return new Promise((resolve,reject) => {
            http.put('/workflow/bind', {}, {
                params: {
                    workflow_id: workflowId,
                    account_id: accountId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }
}

module.exports = { repoProcedure: new ProcedureRepository() };