import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type { BasketOrderPreview } from '@/types/basket';

export const useCounterStore = defineStore('counter', () => {
  const count = ref(0);
  const doubleCount = computed(() => count.value * 2);
  function increment() {
    count.value++;
  }

  return { count, doubleCount, increment };
});

export const usePreviewDataStore = defineStore('previewData', () => {
  const previews = ref<BasketOrderPreview[]>([]);
  function updatePreviews(data: BasketOrderPreview[]) {
    previews.value = data;
  }

  return { previews, updatePreviews };
});
