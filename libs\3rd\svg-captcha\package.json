{"name": "svg-captcha", "version": "1.4.0", "description": "generate svg captcha in node.js or express.js", "main": "index.js", "scripts": {"test": "jest", "lint": "xo"}, "repository": {"type": "git", "url": "git+https://github.com/steambap/svg-captcha.git"}, "keywords": ["<PERSON><PERSON>a", "svg", "node captcha", "captcha generator", "captcha alternative"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=4.x"}, "license": "MIT", "bugs": {"url": "https://github.com/steambap/svg-captcha/issues"}, "homepage": "https://github.com/steambap/svg-captcha#readme", "dependencies": {"opentype.js": "^0.7.3"}, "devDependencies": {"jest": "^21.1.0", "jest-environment-node-debug": "^2.0.0", "xo": "^0.18.2"}, "xo": {"esnext": true, "envs": ["jest"], "rules": {"linebreak-style": [0]}}, "typings": "index.d.ts", "files": ["fonts/*", "lib/*", "index.d.ts", "*.md"]}