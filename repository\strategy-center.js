const http = require('../libs/http').http;

class StrategyRepository {

    constructor() {
        this.baseUrl = 'http://192.168.1.140:10446/api/strategy';
    }

    getStrategyList() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/all`).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    saveStrategy(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/add`, {
                data,
                method: 'post',
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    updateStrategy(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/update`, {
                data,
                method: 'put',
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    deleteStrategy(strategy_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/delete`, {
                params: {
                    strategy_id,
                },
                method: 'delete',
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getStrategyConfigTemplate(strategy_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params_template`, {
                params: {
                    strategy_id,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    saveStrategyConfigTemplate(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params_template`, {
                method: 'post',
                data,
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    updateStrategyConfigemplate(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params_template`, {
                method: 'put',
                data,
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    deleteStrategyConfigTemplate(strategy_id, config_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params_template`, {
                params: {
                    strategy_id,
                    config_id,
                },
                method: 'delete',
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    // config
    getConfigList() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params`).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    updateStrategyConfigParams(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params/update`, {
                data,
                method: 'put',
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    addStrategyConfigParams(data) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params/add`, {
                data,
                method: 'post',
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    startInstance(strategy_id, params_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/instances/start`, {
                method: 'post',
                params: {
                    strategy_id,
                    params_id,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    deleteStrategyConfigParams(strategy_id, params_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/params/delete`, {
                method: 'delete',
                params: {
                    strategy_id,
                    params_id,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    removeStartJob(strategy_id, params_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/instances/remove_start_job`, {
                method: 'delete',
                params: {
                    strategy_id,
                    params_id,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    addStartJob(strategy_id, params_id, hour, minute, second) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/instances/add_start_job`, {
                method: 'post',
                params: {
                    strategy_id,
                    params_id,
                    hour,
                    minute,
                    second,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getLogs(instance_id, params_id, strategy_id, begin_row, offset) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/detail/logs`, {
                params: {
                    instance_id,
                    params_id,
                    strategy_id,
                    begin_row,
                    offset,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getInstanceList() {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/instances`).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    stopInstance(strategy_id, params_id) {
        return new Promise((resolve, reject) => {
            http(`${this.baseUrl}/instances/stop`, {
                method: 'post',
                params: {
                    strategy_id,
                    params_id,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    downloadLog(instance_id) {
        try {
            return new Promise((resolve, reject) => {
                http(`${this.baseUrl}/instance/log`, {
                    params: {
                        instance_id,
                    },
                    responseType: 'blob',
                }).then(
                    resp => {
                        resolve(resp);
                    },
                    error => {
                        reject(error);
                    },
                );
            });
        } catch (error) {
            console.log(error);
            return {
                err: 1,
                errMsg: '',
            };
        }
    }
}

module.exports = { repoStrategy: new StrategyRepository() };
