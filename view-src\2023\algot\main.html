<div class="main-view s-full-size">

    <!-- 顶部展示 -->

    <div class="top-box themed-box">
        <template>
            <!-- 新建按钮 -->
            <el-button type="info" class="create-button" @click="create()">
                <i class="el-icon-plus"></i>
                <span>新建算法交易</span>
            </el-button>
            <!-- 总览 -->
            <div class="summary-block">
                <div class="sum-unit">
                    <div class="item-value">{{ strategyCount }}个</div>
                    <div class="item-label">今日策略数</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ runningStock }}个</div>
                    <div class="item-label">运行股票数</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ runningCount }}个</div>
                    <div class="item-label">运行中</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ stoppedCount }}个</div>
                    <div class="item-label">非运行</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ totalTarget }}</div>
                    <div class="item-label">今日目标量</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ totalBuyTarget }}</div>
                    <div class="item-label">买入目标量</div>
                </div>
                <div class="sum-unit">
                    <div class="item-value">{{ totalSellTarget }}</div>
                    <div class="item-label">卖出目标量</div>
                </div>
                <!-- <div class="sum-unit">
                    <div class="item-value">{{ percentageBy100(totalTarget > 0 ? totalTraded / totalTarget : 0) }}</div>
                    <div class="item-label">完成率</div>
                </div> -->
            </div>
        </template>
    </div>

    <!-- 筛选栏 -->
    
    <div class="control-box themed-box">
        <template>

            <div class="control-unit">
                <span>母单查询</span>
            </div>

            <div class="control-unit">
                <label class="ctr-label">账号</label>
                <el-select 
                    placeholder="请选择账号" 
                    class="s-w-150" 
                    v-model="recordId" 
                    @change="handleAccountChange" 
                    filterable 
                    clearable>
                    <el-option 
                        v-for="(item, item_idx) in accounts" 
                        :key="item_idx" 
                        :value="getProperAccountId(item)"
                        :label="formatSelectAccountName(item)"></el-option>
                </el-select>
            </div>

            <div class="control-unit">
                <label class="ctr-label">算法</label>
                <el-select 
                    placeholder="请选择算法" 
                    v-model="algoId" 
                    @change="handleAlgoChange" 
                    class="s-w-150" 
                    filterable 
                    clearable>
                    <el-option-group v-for="(group, group_idx) in algoGrps" :key="group_idx" :label="group.name">
                        <el-option v-for="(item, item_idx) in group.algoes" :key="item_idx" :label="item.name" :value="item.id"></el-option>
                    </el-option-group>
                </el-select>
            </div>
            
            <div class="control-unit">
                <label class="ctr-label">母单</label>
                <el-select 
                    placeholder="请选择母单" 
                    v-model="taskId"
                    @change="handleInstanceChange" 
                    class="s-w-200" 
                    filterable 
                    clearable>
                    <el-option v-for="(item, item_idx) in instances" :key="item_idx" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </div>

            <el-button type="primary" @click="filterTasks">查询</el-button>
            <el-button type="primary" @click="exportTasks">导出母单</el-button>
            <el-button type="primary" @click="exportEntrusts">导出委托</el-button>
            <el-button type="danger" @click="cancelAll">母单全撤</el-button>

        </template>
    </div>

    <!-- 数据导出（母单与子单） -->

    <div style="display: none;">
        <div class="table-all-task">
            <table>
                <tr>
                    <th label="母单名称" prop="taskName"></th>
                    <th label="母单ID" prop="taskId"></th>
                    <th label="算法名称" prop="algoName"></th>
                    <th label="产品名称" prop="fundName"></th>
                    <th label="账号名称" prop="accountName" formatter="formatAccountName"></th>
                    <th label="开始时间" prop="effectiveTime" formatter="formatDateTime"></th>
                    <th label="结束时间" prop="expireTime" formatter="formatDateTime"></th>
                    <th label="分配资金" prop="algoParam" formatter="formatTaskMoney"></th>
                    <!-- <th label="总买量" prop="totalBuyTarget" thousands-int></th>
                    <th label="总卖量" prop="totalSellTarget" thousands-int></th> -->
                    <th label="总目标量" prop="totalTarget" thousands-int></th>
                    <th label="总成交量" prop="totalTraded" thousands-int></th>
                    <th label="状态" prop="status" formatter="formatTaskStatus"></th>
                    <th label="母单进度" prop="progress" formatter="formatTaskProgress"></th>
                </tr>
            </table>
        </div>
        <div class="table-all-entrust">
            <table>
                <tr>
                    <th label="算法母单" min-width="140" prop="taskName"></th>
                    <th label="母单ID" min-width="140" prop="taskId"></th>
                    <th label="代码" min-width="140" prop="instrument"></th>
                    <th label="名称" min-width="140" prop="instrumentName"></th>
                    <th label="方向" min-width="70" prop="direction" formatter="formatAlgoDirection"></th>
                    <th label="已完成量" min-width="80" prop="tradedVolume" thousands-int></th>
                    <th label="已成交额" min-width="90" prop="tradeAmount" align="right" precision="2" thousands></th>
                    <th label="成交均价" min-width="90" prop="tradePrice" align="right" precision="2"></th>
                    <th label="未成交量" min-width="90" prop="pendingVolume" align="right" thousands-int></th>
                    <th label="错误信息" min-width="200" prop="remark"></th>
                </tr>
            </table>
        </div>
        <div class="table-unclosed-entrust">
            <table>
                <tr>
                    <th label="代码" min-width="140" prop="instrument"></th>
                    <th label="名称" min-width="140" prop="instrumentName"></th>
                    <th label="方向" min-width="70" prop="direction" formatter="formatAlgoDirection"></th>
                    <th label="未成交量" min-width="90" prop="pendingVolume" align="right" thousands-int></th>
                </tr>
            </table>
        </div>
    </div>

    <!-- 算法母单平铺 -->

    <div class="morder-list">
        <template>
            <div v-for="(item, item_idx) in tasks" 
                :key="item_idx" 
                @click="handleTaskCheck(item)"
                class="each-morder"
                :class="isCurrentChoosed(item) ? 'selected' : ''"
                v-show="meetLocal(item)">
                <div class="morder-name">
                    <div class="morder-name-inner s-ellipsis">{{ item_idx + 1 }}/{{ tasks.length }} {{ item.taskName }}</div>
                </div>
                <div class="morder-aspects">
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">算法名称</span>
                            <span class="field-value">{{ item.algoName }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">账号名称</span>
                            <span class="field-value">{{ formatSelectAccountName(item) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">运行状态</span>
                            <span class="field-value" :class="isTaskRunning(item) ? 's-color-green' : 's-color-red'">
                                <span>{{ formatTaskStatus(item) }}</span>
                            </span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">母单进度</span>
                            <span class="field-value">{{ percentageBy100(item.totalTarget > 0 ? item.totalTraded / item.totalTarget : 0) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">分配资金</span>
                            <span class="field-value">{{ thousandsInt(item.algoParam.money) }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">创建时间</span>
                            <span class="field-value">{{ formatTaskTime(item.createTime) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">开始时间</span>
                            <span class="field-value">{{ formatTaskTime(item.effectiveTime) }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">结束时间</span>
                            <span class="field-value">{{ formatTaskTime(item.expireTime) }}</span>
                        </div>
                    </div>
                    <div class="aspect-block">
                        <div class="upper-one">
                            <span class="field-name">涨跌停限制</span>
                            <span class="field-value">{{ formatActionLimit(item) }}</span>
                        </div>
                        <div class="lower-one">
                            <span class="field-name">算法类型</span>
                            <span class="field-value">{{ formatAlgoType(item) }}</span>
                        </div>
                    </div>
                </div>
                <div class="morder-operation">
                    <el-button v-if="isTaskRunning(item)" type="danger" class="morder-button" @click="stopm(item)">
                        <i class="el-icon-video-pause"></i>
                        <span>停止</span>
                    </el-button>
                    <el-button v-if="isTaskStopped(item) && hasNotCompleted(item)" type="primary" class="morder-button" @click="exportUnclosedEntrusts(item)">
                        <i class="el-icon-download"></i>
                        <span>导出剩余量</span>
                    </el-button>
                </div>
            </div>
        </template>
    </div>

    <!-- 子单标题 -->

    <div class="child-task-title themed-box">
        <span>{{ title }}</span>
        <el-input 
            placeholder="合约搜索"
            prefix-icon="el-icon-search" 
            class="s-w-200 s-mgl-10" 
            v-model.trim="keywords"
            @change="filterSubTasks" 
            clearable
            ></el-input>
    </div>

    <!-- 单个算法实例，内含子单列表 -->

    <div class="child-task-list">
        <table>
			<tr>
				<th label="代码/名称" min-width="140" prop="instrument" formatter="formatCodeName" searchable sortable overflowt></th>
				<th label="名称" prop="instrumentName" searchable sortable overflowt></th>
                <th label="方向" min-width="70" prop="direction" formatter="formatAlgoDirection" sortable></th>
				<th label="进度" min-width="80" watch="tradedVolume,volume" formatter="formatProgress" sortable></th>
				<th label="成交均价" min-width="90" prop="tradePrice" align="right" precision="2" sortable></th>
				<!-- <th label="市场均价" min-width="90" prop="marketPrice" align="right" precision="2" sortable></th> -->
				<th label="已成交额" min-width="90" prop="tradeAmount" align="right" precision="2" sortable thousands-int></th>
				<!-- <th label="撤单率" min-width="90" prop="cancelRate" align="right" percentage by100 sortable></th> -->
				<th label="未成交量" min-width="90" prop="pendingVolume" align="right" sortable thousands-int></th>
				<th label="错误信息" min-width="200" prop="remark" sortable overflowt></th>
				<th label="操作" min-width="60" prop="algorithmStatus" fixed="right" formatter="formatActions"></th>
			</tr>
		</table>
    </div>
</div>
<!-- 创建编辑弹窗 -->
<div class="creation-dialog-external"></div>