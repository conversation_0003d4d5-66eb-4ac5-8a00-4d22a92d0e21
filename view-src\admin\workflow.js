
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');
const { BaseAdminView } = require('./baseAdminView');

class View extends BaseAdminView {

    get systemEnum() {
        return require('../../config/system-enum').systemEnum;
    }

    get workflowRepo() {
        return require('../../repository/procedure').repoProcedure;
    }

    constructor(view_name) {

        super(view_name, '流程管理');
        this.enrolls = [];
        this.workflowList = [];
        this.roleListHash = {};
        this.roleList = [];

        this.statusEnums = Object.values(this.systemEnum.examineStatuses).filter(x => x.mean !== '驳回').map(x => ({ value: x.code, label: x.mean }));
        this.workflowDialog = {
            visible: false,
            data: {
                id: null,
                workFlowName: '',
            },
            rules: {
                workFlowName: [{ required: true, message: '工作流程名称不能为空' }],
            },
        };
    }

    createApp() {

        const self = this;
        this.vueApp = new Vue({

            el: this.$container.querySelector('.workflow-view-root'),
            components: {
                DataTables: DataTables.DataTables,
            },
            data: {
                tab: 'workflow',
                roleList: this.roleList,
                workflowList: this.workflowList,
                enrolls: this.enrolls,
                statusEnums: this.statusEnums,
                workflowDialog: this.workflowDialog,
            },
            directives: {
                drag,
            },
            methods: {
                tabChange() {},
                createWorkflow() {
                    if (self.enrolls.length > 0) {
                        this.tab = 'edit';
                        self.interaction.showConfirm({
                            title: '提示',
                            message: '当前流程的编辑尚未完成，确定要清除数据重新编辑吗?',
                            confirmed: () => {
                                self.enrolls.clear();
                                self.workflowDialog.data.workFlowName = '';
                            },
                        });
                    } else {
                        this.tab = 'edit';
                    }
                },
                editWorkflow(workflow) {
                    this.tab = 'edit';
                    self.buildWorkflow(workflow);
                },
                removeWorkflow(flow) {
                    self.interaction.showConfirm({
                        title: '提示',
                        message: '确定要移除工作流吗?',
                        confirmed: () => {
                            self.removeWorkflow(flow.id).then(resp => {
                                if (resp && resp.errorCode == 0) {
                                    self.workflowList.remove(x => x.id == flow.id);
                                }
                            });
                        },
                    });
                },
                want2saveWorkflow() {

                    const enrolls = self.enrolls;
                    const $interaction = self.interaction;

                    if (enrolls.length <= 0) {
                        $interaction.showError('流程尚未绑定角色，无法保存流程!');
                        return;
                    }

                    if (enrolls.find(x => x.role == null)) {
                        $interaction.showError('流程节点中尚未绑定角色，无法保存流程!');
                        return;
                    }

                    let diffs = enrolls.distinct(x => x.role);
                    if (diffs.length < enrolls.length) {
                        $interaction.showError('流程中存在重复角色，无法保存流程!');
                        return;
                    }

                    if (enrolls.some(x => x.status == null || x.status === '')) {
                        $interaction.showError('流程中有选择的角色尚未选择状态，无法保存流程!');
                        return;
                    }

                    self.workflowDialog.visible = true;
                },
                saveWorkflow() {

                    this.$refs.$dlgForm.validate(valid => {

                        if (!valid) {
                            return;
                        }

                        const entity = self.createWorkflowEntity({

                            id: self.workflowDialog.data.id || null,
                            orgId: self.userInfo.orgId,
                            workFlowName: self.workflowDialog.data.workFlowName,
                            content: self.helper.deepClone(self.enrolls),
                        });

                        self.saveWorkflow(entity).then(resp => {
                            
                            if (resp.errorCode == 0) {
                                
                                self.enrolls.clear();
                                this.cancelWorkflow();
                                this.tab = 'workflow';
                                self.getAllWorkflow();
                            }
                        });
                    });
                },
                cancelWorkflow() {
                    this.$refs.$dlgForm.clearValidate();
                    this.$refs.$dlgForm.resetFields();
                    self.workflowDialog.visible = false;
                },
                addRole() {
                    if (self.enrolls.length > 0) {
                        let last = self.enrolls[self.enrolls.length - 1];
                        if (!last.role) {
                            self.interaction.showError('请先选择角色再继续添加!');
                            return;
                        }
                        if (last.status == null || last.status === '') {
                            self.interaction.showError('请先选择角色状态再继续添加!');
                            return;
                        }
                    }
                    self.enrolls.push(self.generateAuditNode());
                },
                removeRole(role) {
                    self.enrolls.remove(x => x.id == role.id);
                },
                validRoleUnique(this_role) {
                    if (!this_role) {
                        return;
                    }
                    let roleEntities = self.enrolls.filter(x => x.role == this_role.role);
                    if (roleEntities.length > 1) {
                        self.interaction.showError('不能重复选择相同角色!');
                        this.$nextTick(() => {
                            this_role.role = null;
                        });
                    }
                },
            },
        });
    }

    generateAuditNode() {
        return {
            id: this.getNodeId(),
            role: null,
            status: null,
        };
    }

    resizeWindow() {
        var winHeight = this.thisWindow.getSize()[1];
        //Title的高度是35 Tab的高度是30 底部状态栏30
        var extraHeight = 35 + 30 + 30;
        var net_height = winHeight - extraHeight;
        var box = this.$container.querySelector('.workflow-view-root .s-scroll-bar');
        if (box) {
            box.style.height = net_height + 'px';
        }
    }

    getRoleList() {
        
        this.roleList = this.helper.dict2Array(this.systemUserEnum.userRole)
                                   .map(role => {
                                        let transRole = { value: role.code, label: role.mean };
                                        this.roleListHash[role.code] = transRole;
                                        return transRole;
                                    });
    }

    /**
     * 生成合理的工作流程的数据
     * @param data
     */
    createWorkflowEntity(workflow) {
        
        let workflowEntity = Object.assign(
            {
                id: null,
                orgId: 0,
                workFlowName: '系统自定义流程',
                content: [],
            },
            workflow,
        );

        if (!Array.isArray(workflow.content)) {
            return workflowEntity;
        }

        workflowEntity.content = workflow.content.map(x => {
            let roleEntity = this.roleListHash[x.role] || {};
            return {
                roleType: x.role,
                roleName: roleEntity.label,
                defaultOffLineSetting: x.status,
            };
        });

        return workflowEntity;
    }

    async saveWorkflow(workflowEntity) {

        let loading = this.interaction.showLoading({ text: '操作进行中...' });
        let resp = null;
        
        try {
            resp = workflowEntity.id ? await this.workflowRepo.updateProcedure(workflowEntity) : await this.workflowRepo.saveProcedure(workflowEntity);
            loading.close();
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('保存工作流程成功!');
            } else {
                this.interaction.showError('保存工作流程失败,详细信息:' + resp.errorMsg);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('保存工作流程失败！');
        }
        return resp;
    }

    async getAllWorkflow() {
        let loading = this.interaction.showLoading({ text: '正在获取工作流程列表...' });
        try {
            this.workflowList.clear();
            let resp = await this.workflowRepo.getProcedure();
            loading.close();
            if (resp.errorCode == 0) {
                let list = resp.data || [];
                list = list.orderByDesc(x => x.id);
                this.workflowList.merge(list);
            } else {
                this.interaction.showError('获取工作流程列表失败,详细信息:' + resp.errorMsg);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('获取工作流程列表失败！');
        }
    }

    async removeWorkflow(pId) {
        let loading = this.interaction.showLoading({ text: '操作进行中...' });
        let resp = null;
        try {
            resp = await this.workflowRepo.removeProcedure(pId);
            loading.close();
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('删除工作流程成功!');
            } else {
                this.interaction.showError('删除工作流程失败,详细信息:' + resp.errorMsg);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('删除工作流程失败！');
        }
        return resp;
    }

    buildWorkflow(workflow) {
        
        if (!workflow) {
            return;
        }

        this.enrolls.clear();
        this.workflowDialog.data.id = workflow.id;
        this.workflowDialog.data.workFlowName = workflow.workFlowName;

        workflow.content.forEach(x => {
            this.enrolls.push({
                id: this.getNodeId(),
                role: x.roleType,
                status: x.defaultOffLineSetting,
            });
        });
    }

    getNodeId() {
        return new Date().getTime() + Math.random().toFixed(10).replace(/\./, '');
    }

    refresh() {
        this.getAllWorkflow();
    }
    
    exportSome() {
        this.interaction.showMessage('该页面未提供导出');
    }

    config() {
        this.interaction.showMessage('该页面未提供设置');
    }

    build($container) {

        super.build($container);
        this.getRoleList();
        this.getAllWorkflow();
        this.createApp();
        this.resizeWindow();
    }
}

module.exports = View;
