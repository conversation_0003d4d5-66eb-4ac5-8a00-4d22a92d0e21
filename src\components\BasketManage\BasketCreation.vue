<script setup lang="tsx">
import { reactive, ref, useTemplateRef, watch } from 'vue';
import { ElSelect, ElOption, ElInputNumber, ElMessage, type UploadFile } from 'element-plus';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { ColumnDefinition, RowAction } from '@/types';
import { Repos, type BasketInstrumentItem, type BasketItem } from '../../../../xtrade-sdk/dist';
import { deepClone, formatDateTime, getUser, remove } from '@/script';
import { AssetTypeEnum, ASSET_TYPES, ABSKET_TRADE_DIRECTIONS } from '@/enum/trade';
import { readExcel, type UploadExcelFileData } from '@/script/excel';

const { assetType, basket, onlyUploading } = defineProps<{
  assetType: AssetTypeEnum;
  basket: BasketItem | null;
  onlyUploading: boolean;
}>();

const usr = getUser()!;
const isCreation = ref(false);
const innerBasket = ref<BasketItem>(createEmptyBasket());
const records = ref<BasketInstrumentItem[]>([]);

watch(
  () => basket,
  () => {
    if (basket) {
      innerBasket.value = deepClone(basket);
      isCreation.value = false;
    } else {
      innerBasket.value = createEmptyBasket();
      isCreation.value = true;
    }
    request(innerBasket.value.basketId);
  },
  {
    immediate: true,
  },
);

function createEmptyBasket(): BasketItem {
  return {
    basketId: null as any,
    basketName: '',
    totalWeight: 0,
    etfCode: null,
    createUser: usr.userId,
  };
}

// Editable cell components
const EditableInputNumber = (props: {
  modelValue: string | number;
  precision: number;
  step: number;
  onUpdateModelValue: (value: string | number) => void;
}) => {
  return (
    <ElInputNumber
      modelValue={props.modelValue}
      onChange={props.onUpdateModelValue as any}
      controls={false}
      precision={props.precision}
      step={props.step}
      size="small"
      style="width: 100%"
    />
  );
};

const EditableSelect = (props: {
  modelValue: number;
  onUpdateModelValue: (value: number) => void;
  options: { label: string; value: number }[];
  disabled?: boolean;
}) => {
  return (
    <ElSelect
      modelValue={props.modelValue}
      onChange={props.onUpdateModelValue}
      disabled={props.disabled}
      size="small"
      style="width: 100%"
    >
      {props.options.map(option => (
        <ElOption key={option.value} label={option.label} value={option.value} />
      ))}
    </ElSelect>
  );
};

const updateAssetType = (row: BasketInstrumentItem, value: number) => {
  row.assetType = value;
};

const updateDirection = (row: BasketInstrumentItem, value: number) => {
  row.direction = value;
};

const updateVolume = (row: BasketInstrumentItem, value: number) => {
  row.volume = value;
};

const updateWeight = (row: BasketInstrumentItem, value: number) => {
  row.weight = value;
};

const getVolumeStep = (assetType: number) => {
  switch (assetType) {
    case AssetTypeEnum.股票:
      return 100;
    default:
      return 1;
  }
};

// Column definitions with editable cells
const columns: ColumnDefinition<BasketInstrumentItem> = [
  {
    key: 'instrumentName',
    title: '合约名称',
    width: 150,
    cellRenderer: ({ rowData }) => <span>{rowData.instrumentName}</span>,
  },
  {
    key: 'instrument',
    title: '合约代码',
    width: 120,
    cellRenderer: ({ rowData }) => <span>{rowData.instrument}</span>,
  },
  {
    key: 'assetType',
    title: '资产类型',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <EditableSelect
        disabled={true}
        modelValue={rowData.assetType}
        onUpdateModelValue={value => updateAssetType(rowData, value)}
        options={ASSET_TYPES}
      />
    ),
  },
  {
    key: 'direction',
    title: '买卖方向',
    width: 100,
    cellRenderer: ({ rowData }) => (
      <EditableSelect
        modelValue={rowData.direction}
        onUpdateModelValue={value => updateDirection(rowData, value)}
        options={ABSKET_TRADE_DIRECTIONS}
      />
    ),
  },
  {
    key: 'volume',
    title: '委托数量',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <EditableInputNumber
        precision={0}
        step={getVolumeStep(rowData.assetType)}
        modelValue={rowData.volume}
        onUpdateModelValue={value => updateVolume(rowData, value as number)}
      />
    ),
  },
  {
    key: 'weight',
    title: '权重',
    width: 120,
    cellRenderer: ({ rowData }) => (
      <EditableInputNumber
        precision={2}
        step={0.01}
        modelValue={rowData.weight}
        onUpdateModelValue={value => updateWeight(rowData, value as number)}
      />
    ),
  },
];

// Row actions
const rowActions: RowAction<BasketInstrumentItem>[] = [
  {
    label: '删除',
    icon: 'remove',
    type: 'text',
    onClick: row => {
      remove(records.value, x => x.instrument == row.instrument);
    },
  },
];

const repoInstance = new Repos.BasketRepo();

const save = async () => {
  // 验证篮子名称
  if (!innerBasket.value.basketName) {
    ElMessage.error('请输入篮子名称');
    return;
  }

  // 篮子合约信息预处理
  const { basketId, basketName } = innerBasket.value;
  const members = deepClone(records.value);
  members.forEach(x => {
    x.basketId = basketId;
    x.basketName = basketName;
  });

  // 提交篮子合约信息
  const resp = isCreation.value
    ? await repoInstance.CreateBasket(members)
    : await repoInstance.UpdateBasket(members);
  const { errorCode, errorMsg } = resp;
  if (errorCode == 0) {
    ElMessage.success('已保存');
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
};

const emitter = defineEmits<{
  close: [];
}>();

const close = () => {
  emitter('close');
};

const dialog = reactive({
  visible: false,
});

const hope2Upload = () => {
  dialog.visible = true;
};

const extensions = { xlsx: 'xlsx', xls: 'xls', csv: 'csv' };
const acceptTypes = [
  ...Object.values(extensions),
  'text/csv',
  'application/csv',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
];

const uploadRef = useTemplateRef('uploadRef');
const fileList = ref([]);
const totalFiles = ref(0);
const states = {
  selectedFiles: [] as UploadFile[],
};

const downloadTemplate = (extension: string) => {
  const fileName = 'basket-upload-template';
  const link = document.createElement('a');
  const ts = formatDateTime(new Date(), 'yyyyMMdd-hhmmss');
  link.href = `/templates/${fileName}.${extension}`;
  link.download = `篮子导入模板-${ts}`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleFileChange = (file: UploadFile, files: UploadFile[]) => {
  totalFiles.value = files.length;
  states.selectedFiles = [...files];
};

const handleFileRemove = (file: UploadFile) => {
  totalFiles.value = Math.max(0, totalFiles.value - 1);
  remove(states.selectedFiles, x => x.uid == file.uid);
};

/**
 * 上传文件前处理（多个文件时，触发多次）
 * @param file
 */
const beforeUpload = (file: any) => {
  const isValidType = acceptTypes.includes(file.type);
  if (!isValidType) {
    const err = `请上传 Excel (${extensions.xls}, ${extensions.xlsx}) 或 CSV (${extensions.csv}) 文件！`;
    ElMessage.error(err);
  }

  return isValidType;
};

const cancelUpload = () => {
  dialog.visible = false;
  fileList.value = [];
};

const confirmUpload = async () => {
  const total = states.selectedFiles.length;

  if (total == 0) {
    ElMessage.error('请选择要上传的文件！');
    return;
  }

  const records: UploadExcelFileData[] = [];

  for (let i = 0; i < total; i++) {
    const file = states.selectedFiles[i];
    const sheets = await readExcel(file.raw as File);
    records.push({ fileName: file.name, sheets });
  }

  totalFiles.value = 0;
  fileList.value = [];
  uploadRef.value?.clearFiles();
  dialog.visible = false;
  handleUpload(records);
};

function handleUpload(files: UploadExcelFileData[]) {
  const titles = ['合约代码', '合约名称', '委托数量', '委托金额', '交易方向', '权重'];
  const total_original = files.length;

  // 过滤掉没有数据的文件

  files.forEach(file => {
    const { sheets } = file;
    file.sheets = sheets.filter(sheet => {
      const { matrix } = sheet;
      const headers = matrix[0];
      const has_data = matrix.length > 1;
      return (
        has_data &&
        Array.isArray(headers) &&
        headers.length > 0 &&
        headers.every(x => titles.includes(x))
      );
    });
  });

  // 合并文件

  const oks = files.filter(file => file.sheets.length > 0);
  const file_records = oks.map(file => {
    const { fileName, sheets } = file;
    return {
      fileName,
      records: sheets.map(x => x.matrix.slice(1)).flat(),
    };
  });

  // 转换成JSON
  const total_records = file_records.map(file => file.records).flat();
  const basketId = innerBasket.value.basketId;
  const basketName = innerBasket.value.basketName || (null as any);
  const jsons: BasketInstrumentItem[] = total_records.map(record => {
    const [instrument, instrumentName, volume, amount, direction, weight] = record;
    return {
      basketId,
      basketName,
      instrumentName,
      instrument,
      assetType,
      direction: Number(direction),
      volume: Number(volume),
      amount: Number(amount),
      weight: Number(weight),
      createUser: usr.username,
    };
  });

  try {
    jsons.forEach(item => {
      const matched = records.value.find(x => x.instrument == item.instrument);
      if (matched) {
        Object.assign(matched, item);
      } else {
        records.value.push(item);
      }
    });

    const summaries = [
      `上传文件总数 = ${total_original}`,
      `有效文件数 = ${file_records.length}`,
      `导入合约数 = ${total_records.length}`,
    ];
    const summary = summaries.join('<br />');
    ElMessage.success({ message: summary, dangerouslyUseHTMLString: true });
  } catch (error) {
    ElMessage.error('批量导入失败');
    console.error(error);
  }
}

async function request(basket_id: number) {
  if (!basket_id) {
    records.value = [];
    return;
  }

  records.value = ((await repoInstance.QueryBasketInstruments(basket_id)) || {}).data || [];
}
</script>

<template>
  <div class="basket-detail-list" h-400>
    <VirtualizedTable
      :data="records"
      :columns="columns"
      :row-actions="rowActions"
      :row-action-width="80"
      fixed
    >
      <template #actions>
        <div v-if="!onlyUploading" flex aic gap-10>
          <label>篮子名称</label>
          <el-input
            placeholder="请输入篮子名称"
            v-model.trim="innerBasket.basketName"
            clearable
            style="width: 220px"
          />
          <el-button type="primary" @click="save">
            <i class="iconfont icon-save"></i>
            <span>保存</span>
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
  <div class="basket-toolbar" flex aic jcc>
    <div class="toolbar" h-48 lh-48>
      <el-button @click="close" style="width: 240px">取消</el-button>
      <el-button @click="hope2Upload" type="primary" style="width: 240px">从文件导入</el-button>
    </div>
  </div>

  <el-dialog
    title="导入合约到篮子"
    width="500px"
    top="300px"
    v-model="dialog.visible"
    @close="dialog.visible = false"
    :close-on-click-modal="false"
    append-to-body
    draggable
  >
    <div class="download-toolbar" m-b-10 flex aic gap-20>
      <em @click="downloadTemplate(extensions.xlsx)">下载Excel模板({{ extensions.xlsx }})</em>
      <em @click="downloadTemplate(extensions.xls)">下载Excel模板({{ extensions.xls }})</em>
      <el-tooltip content="CSV文件格式请保存为UTF-8编码，其他编码会导致乱码!">
        <em @click="downloadTemplate(extensions.csv)">下载CSV模板({{ extensions.csv }})</em>
      </el-tooltip>
    </div>
    <el-upload
      ref="uploadRef"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :before-upload="beforeUpload"
      :accept="acceptTypes.join(',')"
      :file-list="fileList"
      :auto-upload="false"
      :limit="10"
      multiple
      drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        <span>拖放文件到虚线框内，或者</span>
        <em>点击该区域</em>
      </div>
      <template #tip>
        <div class="el-upload__tip"></div>
      </template>
    </el-upload>
    <div class="upload-footer" m-t-30 m-b-20 flex aic gap-20>
      <el-button @click="cancelUpload" style="width: 200px">
        <span>取消</span>
      </el-button>
      <el-button
        type="primary"
        @click="confirmUpload"
        :disabled="totalFiles == 0"
        style="width: 200px"
      >
        <span>开始上传</span>
        <span v-if="totalFiles > 0" p-l-5>(文件数 = {{ totalFiles }})</span>
      </el-button>
    </div>
  </el-dialog>
</template>

<style scoped>
.download-toolbar {
  em {
    color: var(--el-color-primary);
    font-style: normal;
    &:hover {
      cursor: default;
      text-decoration: underline;
    }
  }
}
</style>
