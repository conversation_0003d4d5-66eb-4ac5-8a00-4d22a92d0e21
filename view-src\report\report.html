<div class="report-view-root">
    <div class="s-scroll-bar" style="overflow: auto;">
        <table>
            <tr>
                <th label="ID" prop="id" min-width="120"></th>
                <th label="报告名称" min-width="260" prop="reportName" overflowt sortable searchable></th>
                <th type="template" fixed="right" label="操作" fixed-width="100">
                    <a class="s-cp s-underline s-color-blue" event.onclick="openReport">查看报告</a>
                </th>
            </tr>
        </table>
    </div>
    <div class="report-container">
        <el-dialog class="report-dialog" title="产品报告" ref="reportDialog" fullscreen :visible.sync="reportDialog.visible"
            :before-close="destroyReport">
            <div class="container-content"></div>
        </el-dialog>
    </div>
</div>