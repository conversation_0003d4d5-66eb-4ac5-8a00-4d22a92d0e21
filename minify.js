const fs = require('fs');
const path = require('path');
const uglify = require('uglify-es');

var minify = function(dir) {
	
    var paths = fs.readdirSync(dir);
    paths.forEach(ele => {
		if (ele === 'node_modules' || ele === 'libs') {
			return;
		}
		let _dir = `${dir}/${ele}`;
		let stat = fs.statSync(_dir);
		if (stat.isDirectory()) {
			minify(_dir);
		}
		else {
			if (path.extname(ele) === '.js') {
				let result = uglify.minify(fs.readFileSync(_dir, {
					encoding: 'utf8'
				}));
				if(!result.error) {
					fs.writeFileSync(_dir, result.code);
				}
			}
		}
    });
};

console.log('minifying files...');
minify('./TEMP');
console.log('minifying files!');