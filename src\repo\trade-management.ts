import { BaseRepo } from '../modules/base-repo';
import { TradingAlgorithm } from '../types/table/algo';

const BaseUrl = '/algorithm/desc';

export class TradeManageRepo extends BaseRepo {
  constructor() {
    super();
  }

  /**
   * 查询所有算法
   */
  async QueryAlgos() {
    return await this.assist.Get<TradingAlgorithm[]>(`${BaseUrl}`);
  }

  /**
   * 创建算法
   */
  async CreateRule(algo: TradingAlgorithm) {
    return await this.assist.Post<[]>(`${BaseUrl}`, {}, algo);
  }

  /**
   * 删除算法
   */
  async DeleteRule(id: number) {
    return await this.assist.Delete<[]>(`${BaseUrl}`, { id });
  }
}
