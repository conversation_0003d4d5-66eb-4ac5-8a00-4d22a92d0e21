
const helper = require('../libs/helper').helper;
const Highcharts = require('highcharts/highcharts');
const Highstock = require('highcharts/highstock');
const MinkSeriesName = { price: 'series-price', avprice: 'series-av-price', quater: 'series-quater', volume: 'series-volume' };
const KlineSeriesName = { kline: 'series-kline', volume: 'series-volume', ma5: 'series-ma5', ma10: 'series-ma10', ma20: 'series-ma20' };

function getComparedClass(a, b) {
    return a > b ? 's-color-red' : a < b ? 's-color-green' : '';
}

function formatHands(hands) {
    return hands >= 10000  ? `${+(hands / 10000).toFixed(1)}万手` : `${parseInt(hands)}手`;
}

function formatAmount(amount) {
    return amount >= 100000000  ? `${+(amount / 100000000).toFixed(2)}亿` : `${+(amount / 10000).toFixed(1)}万`;
}

function formatMinkChartTooltip(pre_close) {

    var point_price = this.points.find(x => x.series.name == MinkSeriesName.price);
    var point_av_price = this.points.find(x => x.series.name == MinkSeriesName.avprice);
    var cur_price = point_price.y;
    var chg = cur_price - pre_close;
    var cht_percent = (cur_price - pre_close) * 100 / pre_close;
    var cls = getComparedClass(cht_percent, 0);
    var kvs = [
        ['时间', new Date(this.x).format('hh:mm')],
        ['价格', cur_price.toFixed(2), cls],
        ['涨跌', chg.toFixed(2), cls],
        ['涨跌幅', cht_percent.toFixed(2) + '%', cls],
        // ['均价', point_av_price.y.toFixed(2), cls],
        // ['现手', formatHands(point_av_price.point.cur_volume / 100)],
        // ['金额', `<span style="color:#50ffff;">${(point_av_price.point.cur_amount / 10000).thousands(true)}万</span>`],
        // ['总手', formatHands(point_av_price.point.total_volume / 100)],
    ];
    
    var content = kvs.map(item => { return `<span class="tooltip-ele-label">${item[0]}</span>
                                <span class="tooltip-ele-value s-pdr-10 ${item[2]}">${item[1]}</span>`; }).join('');

    return '<span style="color: white;">' + content + '</span>';
}

function formatKlineChartTooltip(is_dayk, pre_close) {
    
    var point1_pirce = this.points[0].point;
	var point_volume = this.points[1];
    var chg = point1_pirce.close - pre_close;
    var chg_percent = chg * 100 / pre_close;
	var kvs = [
		['开盘', point1_pirce.open, getComparedClass(point1_pirce.open, pre_close)],
		// ['最高', point1_pirce.high, getComparedClass(point1_pirce.high, pre_close)],
		// ['最低', point1_pirce.low, getComparedClass(point1_pirce.low, pre_close)],
		['收盘', point1_pirce.close, getComparedClass(point1_pirce.close, pre_close)],
		['涨跌', chg.toFixed(2), getComparedClass(chg, 0)],
		['涨跌幅', chg_percent.toFixed(2) + '%', getComparedClass(chg, 0)],
        // ['成交量', formatHands(point_volume.y / 100)],
        // ['金额', `<span style="color:#50ffff;">${formatAmount(point_volume.point.amount)}</span>`],
        // ['振幅', ((point1_pirce.high - point1_pirce.low) * 100 / point1_pirce.low).toFixed(2) + '%'],
    ];
    
    if(is_dayk) {
        kvs.splice(1, 0, ['日期', new Date(this.x).format('MM-dd')]);
    }
    else {
        kvs.splice(1, 0, ['时间', new Date(this.x).format('MM-dd hh:mm')]);
    }
	
	var content = kvs.map(item => { return `<span class="tooltip-ele-label">${item[0]}</span>
								<span class="tooltip-ele-value s-pdr-10 ${item[2]}">${item[1]}</span>`; }).join('');

	return '<span style="color: white;">' + content + '</span>';
}

class BaseKline {

    constructor({ $container, chart_title, instrument, instrument_name }) {

        if(!($container instanceof HTMLElement)) {
            throw new Error('param [$container] is not an html element');
        }

        this.$container = $container;
        this.chart = null;
        this.chartTitle = chart_title;
        this.instrument = instrument;
        this.instrumentName = instrument_name;

        this._basicChartOpt = { width: 460, height: 350, backgroundColor: '#070707' };
        this._upperChartHeight = '70%';
        this._axisLineColor = '#3C3C3C';
        this._gridlineColor = '#3C3C3C';
    }

    static createChartContainer() {

        var $container = document.createElement('div');
        $container.classList.add('kline-chart');
        return $container;
    }

    customizeBasicConfig({ tooltip_formatter, positioner, chart_title }) {

        return {
            chart: {
                width: this._basicChartOpt.width,
                height: this._basicChartOpt.height,
                backgroundColor: this._basicChartOpt.backgroundColor,
                animation: false,
            },
            legend: { enabled: false },
            credits: { enabled: false },
            title: {
                text: chart_title,
                style: { color: 'white', fontSize: '14px' },
            },
            tooltip: {

                shared: true,
                shadow: false,
                borderWidth: 0,
                borderColor: 'white',
                borderRadius: 0,
                padding: 0,
                useHTML: true,
                positioner: positioner,
                formatter: tooltip_formatter,
                backgroundColor: 'transparent',
            },
        };
    }

    getCrosshair() {
        return { width: 1, color: '#7A7A7A', snap: false };
    }

    setTitle(chart_title) {

        this.chartTitle = chart_title;
        this.chart.setTitle({ text: chart_title });
    }

    setInstrument(instrument, instrument_name) {

        this.instrument = instrument;
        this.instrumentName = instrument_name;
    }

    setData(ticks) {
        console.log('not implemented');
    }

    addPoint(tick) {
        console.log('not implemented');
    }

    beautifyAppearance() {
        console.log('not implemented');
    }

    show() {
        this.$container.style.display = 'inline-block';
    }

    hide() {
        this.$container.style.display = 'none';
    }

    initialize() {
        console.log('not implemented');
    }
}

class MinKline extends BaseKline {

	get dataPointMap() {
		return this._minutePosDataMap;
	}

    constructor({ $container, chart_title, instrument, instrument_name }) {

        super({ $container, chart_title, instrument, instrument_name });
        
        // 前收盘价（前一交易日的收盘价）
        this.preclose = 1;

        this._quaters = ['10:00', '10:30', '11:00', '11:30', '13:30', '14:00', '14:30', '15:00'];
        this._columnColor = { positive: '#FF5C5C', negative: '#39E365' };
        this._sharedLineWidth = 1.2;
        this._turboThreshold = 242;
        this._xaxisMinutes = this.enumerateMinutes();
        this._minutePosMap = this.locateMinPos();
        //用于存时间点上面的数据
        this._minutePosDataMap = {};
    }

	dropData() {

		helper.clearHash(this._minutePosDataMap);
		this.initialize();
	}

    setPreclose(preclose) {
        this.preclose = preclose;
    }

    setData(ticks) {

        if(!(ticks instanceof Array)) {
            console.warn('kline tick data must be an array');
            return;
        }

        /*
        kline tick data structure
        {
            time: utc date time,
			open: 123,
			high: 123,
			low: 123,
			close: 123,
			volume: 123,
            amount: 123,
            preclose: 123,
            instrument: 'SHSE.600036'
        }
        */

        var aligned_prices = helper.createArray(this._xaxisMinutes.length, null);
        var aligned_av_prices = helper.createArray(this._xaxisMinutes.length, null);
        var aligned_volumes = helper.createArray(this._xaxisMinutes.length, null);
        var total_amount = 0;
        var total_volume = 0;
		helper.clearHash(this._minutePosDataMap);
        ticks.forEach(point => {

            var seq = this._minutePosMap[point.time];
            if(seq == undefined) {
                return;
            }
			this._minutePosDataMap[point.time] = point;
            aligned_prices[seq] = { y: point.close };
            aligned_volumes[seq] = { y: point.volume, color: point.close >= point.open ? this._columnColor.positive : this._columnColor.negative };

            total_amount += point.amount;
            total_volume += point.volume;
            aligned_av_prices[seq] = {
                y: +(total_amount / total_volume).toFixed(2),
                cur_amount: point.amount,
                cur_volume: point.volume,
                total_amount: total_amount,
                total_volume: total_volume,
				instrument: point.instrument,
            };
        });

        var instant_redraw = false;
        this.seriesPrice.setData(aligned_prices, instant_redraw);
        this.seriesAvPrice.setData(aligned_av_prices, instant_redraw);
        this.seriesVolume.setData(aligned_volumes, instant_redraw);
        if(!instant_redraw) {
            this.chart.redraw();
        }

        this.beautifyAppearance();
    }

    beautifyAppearance() {

        if(this.hasBeautified || this.seriesPrice.points.length == 0) {
            return;
        }

        this.hasBeautified = true;

        var yaxis_price = this.chart.yAxis[0];
        var ychild_count = yaxis_price.gridGroup.element.childElementCount;

        if (ychild_count > 6) {
            var grid_line_1 = yaxis_price.gridGroup.element.firstElementChild;
            grid_line_1.style.strokeWidth = '2px';
            var grid_line_middle = yaxis_price.gridGroup.element.children[6];
            grid_line_middle.style.strokeWidth = '2px';
        }
        
        var xaxis_quater = this.chart.xAxis[1];
        var xchild_count = xaxis_quater.gridGroup.element.childElementCount;

        if (xchild_count > 3) {
            var grid_line_3 = xaxis_quater.gridGroup.element.children[3];
            grid_line_3.style.strokeWidth = '2px';
        }
    }

    addPoint(point) {

		if (point.instrument && this.instrument != point.instrument) {
			console.warn(`tick change data is not match, require instrument: ${this.instrument}, tick instrument: ${ point.instrument }`);
			return;
		}

        var seq = this._minutePosMap[point.time];
        if(seq === undefined) {
            console.error('invalidated tick change', point);
            return;
        }

        // 更新价格
        this.seriesPrice.points[seq].update({ y: point.close });
		this._minutePosDataMap[point.time] = point;
        // 计算平均成交价相关变量
        var new_amount = point.amount;
        var new_volume = point.volume;
        var cur_av_point = this.seriesAvPrice.points[seq];
		//说明之前计算的累计信息不可信，直接清除掉，并且重新计算
        cur_av_point.instrument != this.instrument && (cur_av_point.cur_amount = undefined);
        if(cur_av_point.cur_amount == undefined) {
            var recent_seq = seq - 1;
            var recent_point = this.seriesAvPrice.points[recent_seq];
            while(recent_seq >= 0 && typeof recent_point.cur_amount != 'number') {
                recent_seq -= 1;
                recent_point = this.seriesAvPrice.points[recent_seq];
            }

            if(typeof recent_point.cur_amount == 'number') {
                cur_av_point.total_amount = this.seriesAvPrice.points[seq - 1].total_amount;
                cur_av_point.total_volume = this.seriesAvPrice.points[seq - 1].total_volume;
            }
            else {
                cur_av_point.total_amount = 0;
                cur_av_point.total_volume = 0
            }

            cur_av_point.cur_amount = 0;
            cur_av_point.cur_volume = 0;
        }
        var chg_amount = new_amount - cur_av_point.cur_amount;
        var chg_volume = new_volume - cur_av_point.cur_volume;
        var total_amount = cur_av_point.total_amount + chg_amount;
        var total_volume = cur_av_point.total_volume + chg_volume;
        // 更新分时均线
        cur_av_point.update({
            y: +(total_amount / total_volume).toFixed(2),
            cur_amount: new_amount,
            cur_volume: new_volume,
            total_amount: total_amount,
            total_volume: total_volume,
			instrument: point.instrument
        });

        // 更新成交量
        this.seriesVolume.points[seq].update({ y: point.volume, color: point.close >= point.open ? this._columnColor.positive : this._columnColor.negative });       

    }

    castUtc2Minute(utc_time) {
        return new Date(utc_time).format('hh:mm');
    }

    enumerateMinutes() {
        
        var morning_begin = new Date(new Date().format('yyyy-MM-dd 09:30:00')).getTime();
        var afternoon_begin = new Date(new Date().format('yyyy-MM-dd 13:00:00')).getTime();
        var two_hours_in_s = 60 * 2;
        var minutes = [];

        for(let sec = 0; sec <= two_hours_in_s; sec++) {
            minutes.push(morning_begin + 1000 * 60 * sec);
        }
        for(let sec = 0; sec <= two_hours_in_s; sec++) {
            minutes.push(afternoon_begin + 1000 * 60 * sec);
        }
 
        return minutes;
    }

    locateMinPos() {

        var map = {};
        this._xaxisMinutes.forEach((each_min, idx) => { map[each_min] = idx; });
        return map;
    }

    initialize() {

        const SELF = this;
        const BASIC_OPTIONS = this.customizeBasicConfig({
            
            chart_title: '分时走势',
            positioner: function() { return { x: 55, y: 25 }; },
            tooltip_formatter: function () { return formatMinkChartTooltip.call(this, SELF.preclose); }
        });
        const CHART = Highcharts.chart(this.$container, helper.extend(BASIC_OPTIONS, {
            
            xAxis: [

                // 时间轴
                {
                    categories: this._xaxisMinutes,
                    crosshair: this.getCrosshair(),
                    labels: {
                        enabled: false,
                    },
                    lineWidth: 1,
                    lineColor: '#3C3C3C',
                },

                // 整点、半点轴
                {
                    categories: this._quaters,
                    lineWidth: 2,
                    lineColor: this._axisLineColor,
                    offset: -80,
                    gridLineColor: this._gridlineColor,
                    gridLineWidth: 1,
                },

                // 横向顶部对称整点、半点轴
                {
                    linkedTo: 1,
                    opposite: true,
                    labels: {
                        enabled: false,
                    },
                    tickWidth: 0,
                    lineWidth: 1,
                    lineColor: this._axisLineColor,
                    gridLineWidth: 0,
                },
            ],

            yAxis: [

                // 左侧价格轴
                {
                    height: this._upperChartHeight,
                    title: {
                        enabled: false,
                        text: '价格'
                    },
                    labels: {
                        zIndex: 11,
                        useHTML: true,
                        formatter: function() {
                            var label_class = getComparedClass(this.value, SELF.preclose);
                            return `<span class="s-fs-10 ${label_class}">${this.value.toFixed(2)}</span>`;
                        }
                    },
                    tickAmount: 13,
                    gridLineColor: this._gridlineColor,
                    gridLineWidth: 1,
                    crosshair: this.getCrosshair(),
                },

                // 右侧涨跌幅轴
                {
                    height: this._upperChartHeight,
                    opposite: true,
                    linkedTo: 0,
                    title: {
                        enabled: false,
                        text: '涨跌幅',
                    },
                    labels: {
                        zIndex: 12,
                        useHTML: true,
                        formatter: function() {
                            var price = this.value;
                            var label_class = getComparedClass(price, SELF.preclose);
                            var percentage = (price - SELF.preclose) * 100 / SELF.preclose;
                            return `<span class="s-fs-10 ${label_class}">${percentage.toFixed(2)}%</span>`;
                        }
                    },
                    gridLineWidth: 0,
                },

                // 下方成交量轴
                {
                    top: this._upperChartHeight,
                    height: (100 - parseInt(this._upperChartHeight)) + '%',
                    offset: -5,
                    title: {
                        enabled: false,
                        text: '成交量'
                    },
                    labels: {
                        zIndex: 13,
                    },
                    opposite: true,
                    showFirstLabel: false,
                    showLastLabel: false,
                    gridLineWidth: 0,
                },

                // 整点、半点轴
                {
                    height: this._upperChartHeight,
                    opposite: true,
                    linkedTo: 0,
                    title: {
                        enabled: false,
                        text: '时段',
                    },
                    labels: {
                        zIndex: 14,
                    },
                    gridLineWidth: 0,
                },
            ],

            plotOptions: {
                series: {
                    marker: {
                        enabled: false,
                        states: {
                            hover: {
                                fillColor: 'red',
                                radius: 3,
                                lineWidth: 0,
                                radiusPlus: 5,
                                lineWidthPlus: 0,
                            },
                        },
                    },
                    animation: false,
                },
                line: {
                    states: {
                        hover: {
                            lineWidth: this._sharedLineWidth,
                        }
                    }
                }
            },

            series: [
                {
                    type: 'line',
                    name: MinkSeriesName.price,
                    color: 'white',
                    lineWidth: this._sharedLineWidth,
                    data: [],
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'line',
                    name: MinkSeriesName.avprice,
                    color: 'yellow',
                    lineWidth: this._sharedLineWidth * 0.7,
                    data: [],
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'column',
                    name: MinkSeriesName.volume,
                    yAxis: 2,
                    data: [],
                    pointWidth: 1,
                    borderWidth: 0,
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'line',
                    name: MinkSeriesName.quater,
                    xAxis: 1,
                    yAxis: 3,
                    lineWidth: 0,
                    data: this._quaters.map(x => { return 0; }),
                    turboThreshold: this._turboThreshold,
                },
            ]
        }));

        this.chart = CHART;
        this.seriesPrice = this.chart.series[0];
        this.seriesAvPrice = this.chart.series[1];
        this.seriesVolume = this.chart.series[2];
    }
}

class Kline extends BaseKline {

    constructor({ $container, chart_title, instrument, instrument_name, scale, is_dayk }) {

        super({ $container, chart_title, instrument, instrument_name });
        this.scale = scale;
        this.isDayk = is_dayk;

        this._columnColor = { positive: '#FF5C5C', negative: '#00FFFF' };
        this._maxPointWidth = 10;
        this._turboThreshold = 300;
        this._lastTickTime = null;
    }

    setScale(scale) {
        this.scale = scale;
    }

    setIsDayk(is_dayk) {
        this.isDayk = is_dayk;
    }

    getEmptySet() {
        return [];
    }

    calculateMA(day_count, kline_points) {

        var result = [];
        for (let idx = 0, len = kline_points.length; idx < len; idx++) {
            let dt = kline_points[idx].time;
            if (idx < day_count) {
                result.push({ x: dt, y: null });
                continue;
            }
            var sum = 0;
            for (let idx2 = 0; idx2 < day_count; idx2++) {
                sum += kline_points[idx - idx2].close;
            }
            result.push({ x: dt, y: +(sum / day_count).toFixed(2) });
        }
        return result;
    }

    setData(ticks) {

        if(!(ticks instanceof Array)) {
            console.warn('kline tick data must be an array');
            return;
        }

        /*
        kline tick data structure
        {
            time: utc date time,
			open: 123,
			high: 123,
			low: 123,
			close: 123,
			volume: 123,
            amount: 123,
            preclose: 123,
        }
        */

        /*

        var aligned_prices = helper.createArray(this.xaxisMinutes.length, null);
        var aligned_volumes = helper.createArray(this.xaxisMinutes.length, null); 
        
        */

        var price_series = [];
        var volume_series = [];

        ticks.forEach((point, point_idx) => {
            
            price_series.push(helper.extend({ x: point.time, pointIdx: point_idx }, point));
            volume_series.push({
                x: point.time,
                y: point.volume,
                amount: point.amount,
                color: point.close >= point.open ? this._columnColor.positive : this._columnColor.negative
            });
        });

        if(ticks.length > 0) {
            this._lastTickTime = ticks.last(1).time;
        }

        var instant_redraw = false;
        this.seriesKline.setData(price_series, instant_redraw);
        this.seriesVolume.setData(volume_series, instant_redraw);

        var ma5_points = this.calculateMA(5, ticks);
        var ma10_points = this.calculateMA(10, ticks);
        var ma20_points = this.calculateMA(20, ticks);

        this.seriesMA5.setData(ma5_points, instant_redraw);
        this.seriesMA10.setData(ma10_points, instant_redraw);
        this.seriesMA20.setData(ma20_points, instant_redraw);

        if(!instant_redraw) {
            this.chart.redraw();
        }

        this.beautifyAppearance();
    }

    beautifyAppearance() {

        if(this.hasBeautified || this.seriesKline.points.length == 0) {
            return;
        }

        this.hasBeautified = true;
        var yaxis_price = this.chart.yAxis[0];
        var bottom_line = yaxis_price.gridGroup.element.children[0];
        bottom_line.style.strokeWidth = '2px';
    }

    addPoint(tick) {
        
        // this.seriesKline.addPoint(tick.close);
        // this.seriesVolume.setData({ y: tick.volume, color: tick.close >= tick.open ? this.columnColor.positive : this.columnColor.negative });
    }

    initialize() {

        const SELF = this;
        const BASIC_OPTIONS = this.customizeBasicConfig({
            chart_title: this.chartTitle,
            positioner: function() { return { x: 20, y: 0 }; },
            tooltip_formatter: function () {

                var point_price = this.points[0].point;
                var cur_point_idx = point_price.pointIdx;
                var previous = SELF.seriesKline.points[cur_point_idx - 1];
                var pre_close = previous ? previous.close : point_price.close;
                return formatKlineChartTooltip.call(this, SELF.isDayk, pre_close); 
            }
        });

        const CHART = Highstock.stockChart(this.$container, helper.extend(BASIC_OPTIONS, {
            
            chart: {
                width: this._basicChartOpt.width,
                height: this._basicChartOpt.height,
                backgroundColor: this._basicChartOpt.backgroundColor,
                marginLeft: 20,
                marginRight: 62,
                animation: false,
            },
            rangeSelector: { enabled: false },
            navigator: { enabled: false },
            scrollbar: { enabled: false },
            xAxis: {
                type: 'datetime',
                tickWidth: 0,
                crosshair: this.getCrosshair(),
                lineWidth: 0,
                labels: {
                    enabled: false,
                },
            },
            yAxis: [

                // 右侧价格轴
                {
                    height: this._upperChartHeight,
                    title: {
                        enabled: false,
                        text: '价格'
                    },
                    labels: {
                        zIndex: 12,
                        x: 40,                        
                        useHTML: false,
                        style: { color: '#999' },
                        formatter: function() { return this.value.toFixed(2); },
                    },
                    crosshair: this.getCrosshair(),
                    tickAmount: 10,
                    gridLineWidth: 1,
                    gridLineDashStyle: 'shortdash',
                    gridLineColor: this._gridlineColor,
                },

                // 下方成交量轴
                {
                    top: this._upperChartHeight,
                    height: (100 - parseInt(this._upperChartHeight)) + '%',
                    offset: -5,
                    opposite: true,
                    title: {
                        enabled: false,
                        text: '成交量'
                    },
                    labels: {
                        zIndex: 11,
                        x: 40,
                        style: { color: '#999' },
                    },
                    tickAmount: 4,
                    showFirstLabel: false,
                    showLastLabel: false,
                    gridLineWidth: 1,
                    gridLineDashStyle: 'shortdash',
                    gridLineColor: this._gridlineColor,
                },
            ],

            plotOptions: {
                series: {
                    dataGrouping: {
                        enabled: false,
                    },
                },
            },

            series: [
                {
                    type: 'candlestick',
                    name: KlineSeriesName.kline,
                    color: '#00FFFF',
                    upColor: '#070707',
                    lineColor: '#00FFFF',
                    upLineColor: '#FF5C5C',
                    maxPointWidth: this._maxPointWidth,
                    data: this.getEmptySet(),
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'column',
                    name: KlineSeriesName.volume,
                    data: this.getEmptySet(),
                    yAxis: 1,
                    maxPointWidth: this._maxPointWidth,
                    borderWidth: 0,
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'spline',
                    name: KlineSeriesName.ma5,
                    data: this.getEmptySet(),
                    color: '#FAFAFA',
                    lineWidth: 1,
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'spline',
                    name: KlineSeriesName.ma10,
                    data: this.getEmptySet(),
                    color: '#FFFF00',
                    lineWidth: 1,
                    turboThreshold: this._turboThreshold,
                },
                {
                    type: 'spline',
                    name: KlineSeriesName.ma20,
                    data: this.getEmptySet(),
                    color: '#FF00FF',
                    lineWidth: 1,
                    turboThreshold: this._turboThreshold,
                },
            ]
        }));

        this.chart = CHART;
        this.seriesKline = this.chart.series.find(x => x.name == KlineSeriesName.kline);
        this.seriesVolume = this.chart.series.find(x => x.name == KlineSeriesName.volume);
        this.seriesMA5 = this.chart.series.find(x => x.name == KlineSeriesName.ma5);
        this.seriesMA10 = this.chart.series.find(x => x.name == KlineSeriesName.ma10);
        this.seriesMA20 = this.chart.series.find(x => x.name == KlineSeriesName.ma20);
    }
}

module.exports = { MinKline, Kline };
