
const { http } = require('../libs/http');
const { helper } = require('../libs/helper');
const { systemEnum } = require('../config/system-enum');

class InstrumentRepository {

    getAll(asset_type, is_new_format) {

		return new Promise((resolve, reject) => {
			http.get('/instrument', { params: { type: asset_type, status: !!is_new_format }})
				.then(
					resp => {
						let biz_data = resp.data || {};
						let { errorCode, errorMsg } = biz_data;
						let matrix = biz_data.data || [];
						let titles = matrix.shift();
						let instruments = matrix.length >= 1 ? helper.convertMatrix2Json(titles, matrix) : [];
						if (asset_type == systemEnum.assetsType.future.code || asset_type == systemEnum.assetsType.option.code) {
							instruments.forEach(item => {
								item.instrument = item.instrument.replace(item.exchangeId + '.', '');
							});
						}
						resolve({ data: instruments, errorCode, errorMsg });
					}, 
					err => { reject(err); }
				);
		});
	}
}

module.exports = { repoInstrument: new InstrumentRepository() };