<div class="account-detail s-full-height">

	<div class="account-detail-internal themed-box s-full-height">
		
		<div class="account-detail-frame xtcontainer s-border-box s-full-height">

			<template>

				<div class="xtheader themed-header">
					<span>账号信息</span>
				</div>

				<div class="property-list s-full-height">

					<div class="account-property">
						<span class="themed-color prop-name s-ellipsis">账号名称</span>
						<span class="prop-value s-ellipsis" :title="states.accountName">{{ states.accountName }}</span>
					</div>
	
					<template v-for="(item, item_idx) in properties">
						<div v-if="isApplicable(item)" class="account-property" :key="item_idx">
							<span v-if="item.showTitle" :title="item.name" class="themed-color prop-name s-ellipsis">{{ item.name }}</span>
							<span v-else class="themed-color prop-name s-ellipsis">{{ item.name }}</span>
							<span v-if="item.isPercent" class="prop-value">{{ item.value }}%</span>
							<span v-else class="prop-value s-ellipsis">{{ thousandsDecimal(item.value) }}</span>
						</div>
					</template>

				</div>

			</template>

		</div>

	</div>

</div>