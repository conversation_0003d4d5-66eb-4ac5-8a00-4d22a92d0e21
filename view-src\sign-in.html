<template>
    <div class="template-root s-full-size">
        <div id="top-drag-handler" class="s-dragable">
            <a id="btn-close" class="s-no-drag" @click="closeWindow">
                <i class="el-icon-close"></i>
            </a>
        </div>
        <div id="brand-name" class="s-center s-fs-20">
            <img src="../asset/image/logo-name.png" width="266px" height="32px" />
            <br/>
            <span id="sub-introduction" class="s-opacity-6 s-fs-14 s-unselectable">云信KS主动资产管理交易系统</span>
        </div>
        <div class="sign-in-input-box">
            <div class="input-item">
                <el-input placeholder="请输入用户名" 
                            v-model.trim="userName" 
                            v-bind:disabled="isSigningIn" 
                            @blur="checkUserNameInput"
                            @keydown.native="move2Next($event)"></el-input>
                <div class="input-error s-color-red" :style="{ visibility: !!userNameError ? 'visible': 'hidden' }">{{userNameError}}</div>
            </div>
            <div class="input-item">
                <el-input id="input-user-passcode"
                            placeholder="请输入密码" 
                            type="password" 
                            v-model.trim="passcode"
                            v-bind:disabled="isSigningIn" 
                            @blur="checkPasscodeInput"
                            @keydown.native="finishInput($event)"></el-input>
                <div class="input-error s-color-red" :style="{ visibility: !!passcodeError ? 'visible': 'hidden' }">{{passcodeError}}</div>
            </div>
            <div class="input-item input-item-captcha">
                <el-input
                        id="input-captcha"
                        placeholder="请输入验证码"
                        v-model.trim="captcha"
                        v-bind:disabled="isSigningIn"
                        @blur="checkCaptchaInput"
                        @keydown.native="finishInput($event)"></el-input>
                <a id="captcha-img" v-show="!isSigningIn" @click="refreshCaptcha"></a>
                <div class="input-error s-color-red" :style="{ visibility: !!captchaError ? 'visible': 'hidden' }">
                    <template>
                        {{ captchaError }}
                    </template>
                </div>
            </div>
            <div class="input-item last-input-item">
                <el-checkbox style="margin-right: 0px" v-model="rememberUserName" v-bind:disabled="isSigningIn">记住用户名</el-checkbox>
                <el-select class="select-server-list s-pull-right" v-model="selectedServer" v-bind:disabled="isSigningIn" filterable>
                    <el-option v-for="(svr, svr_idx) in servers" v-bind:key="svr_idx" v-bind:value="svr.id" v-bind:label="svr.name"></el-option>
                </el-select>
            </div>
            <div class="input-item">
                <el-button class="s-full-width"
                            id="btn-to-sign-in"
                            v-bind:disabled="isSigningIn" 
                            @click="toSignIn">
                    <span v-if="isSigningIn">
                        正在登录
                        <i class="el-icon-loading" v-show="isSigningIn"></i>
                    </span>
                    <span v-else>
                        立即登录
                    </span>
                </el-button>
            </div>
        </div>
        <div v-if="changePasswordRequired" id="change-password-view">
            <div class="password-change-prompt">
                <p class="first-login-notice">您是首次登录，请修改密码！</p>
                <el-form :model="chgPwdForm" :rules="chgPwdRules" ref="$chgPwdForm" label-width="120px">
                    <!-- 原始密码 -->
                    <el-form-item label="原始密码" prop="old_password">
                        <el-input type="password" v-model.trim="chgPwdForm.old_password" autocomplete="off"></el-input>
                    </el-form-item>                            
                    <!-- 新密码 -->
                    <el-form-item label="新密码" prop="new_password">
                        <el-input type="password" v-model.trim="chgPwdForm.new_password" :maxlength="12" autocomplete="off"></el-input>
                    </el-form-item>                            
                    <!-- 确认新密码 -->
                    <el-form-item label="确认新密码" prop="confirm_password">
                        <el-input type="password" v-model.trim="chgPwdForm.confirm_password" :maxlength="12" autocomplete="off"></el-input>
                    </el-form-item>
                    <!-- 提交按钮 -->
                    <el-form-item>
                        <el-button type="primary" @click="changePassword">确认修改</el-button>
                        <el-button @click="decline2Exit">取消并退出</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>
