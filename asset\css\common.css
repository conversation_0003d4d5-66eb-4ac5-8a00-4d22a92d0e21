﻿
/*
------------
basic html element
------------
*/

html {

    margin: 0;
    padding: 0;
    height: 100%;
}

body {

    margin: 0;
    padding: 0;
    height: 100%;
    font-size: 12px;
    font-family: 'Microsoft YaHei';
    overflow: hidden;
}

table {

    border-collapse: collapse;
    width: 100%;
}

.iconfont {
    font-size: 12px;
}

/*
------------
select & drag
------------
*/

.s-dragable {
    -webkit-app-region: drag;
}

.s-no-drag {
    -webkit-app-region: no-drag;
}

.s-unselectable {

    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -khtml-user-select: none;
}

/*
------------
alignment
------------
*/

.s-pull-left {

    display: block;
    float: left;
}

.s-pull-right {

    display: block;
    float: right;
}

.s-center {
    text-align: center !important;
}

.s-right {
    text-align: right !important;
}

/*
------------
static appearance
------------
*/

.s-block {
    display: block;
}

.s-hidden {
    display: none;
}

.s-border-box {

    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.s-bold {
    font-weight: bold;
}

.s-underline,
.s-hover-underline:hover {
    text-decoration: underline;
}

.s-cp {
    cursor: pointer;
}

.s-cd {
    cursor: default;
}

.s-not-allowed {
    cursor: not-allowed;
}

.s-flag {

    display: inline-block;
    padding: 0 4px;
    line-height: 18px;
    border-radius: 3px;
    text-align: center;
    vertical-align: middle;
    font-size: 90%;
}

.s-ellipsis {

    overflow: hidden;
    word-wrap: normal;
    white-space: nowrap !important;
    text-overflow: ellipsis;
}

/*
------------
opacity
------------
*/

.s-opacity-9 {
    opacity: 0.9;
}

.s-opacity-8 {
    opacity: 0.8;
}

.s-opacity-7 {
    opacity: 0.7;
}

.s-opacity-6 {
    opacity: 0.6;
}

.s-opacity-5 {
    opacity: 0.5;
}

.s-opacity-4 {
    opacity: 0.4;
}

.s-opacity-3 {
    opacity: 0.3;
}

.s-opacity-hover:hover {
    opacity: 1;
}

/*
------------
font size
------------
*/

.s-fs-8 {
    font-size: 8px;
}

.s-fs-10 {
    font-size: 10px;
}

.s-fs-12 {
    font-size: 12px;
}

.s-fs-14 {
    font-size: 14px;
}

.s-fs-16 {
    font-size: 16px;
}

.s-fs-18 {
    font-size: 18px;
}

.s-fs-20 {
    font-size: 20px;
}

.s-fs-24 {
    font-size: 24px;
}

/*
------------
font color
------------
*/

.s-color-white {
    color: white !important;
}

.s-color-red {
    color: #ff4848 !important;
}

.s-color-green {
    color: #3ef16f !important;
}

.s-color-yellow {
    color: #ef9d22 !important;
}

.s-color-blue {
    color: #16a9e4 !important;
}

.s-color-grey {
    color: #999 !important;
}

.s-color-black {
    color: #000 !important;
}

/*
------------
background color
------------
*/

.s-bg-white {
    background-color: white !important;
}

.s-bg-grey {
    background-color: #999 !important;
}

.s-bg-green {
    background-color: #1BBE48 !important;
}

.s-bg-yellow {
    background-color: #ca5e15 !important;
}

.s-bg-orange {
    background-color: #ee6a50 !important;
}

.s-bg-red {
    background-color: #EF3939 !important;
}

.s-bg-blue {
    background-color: #2C79F2 !important;
}
/*
------------
size
------------
*/

.s-full-size {

    width: 100%;
    height: 100%;
}

.s-full-width {
    width: 100%;
}

.s-full-height {
    height: 100%;
}

.s-w-50 {
    width: 50px;
}

.s-w-100 {
    width: 100px;
}

.s-w-120 {
    width: 120px;
}

.s-w-150 {
    width: 150px;
}

.s-w-180 {
    width: 180px;
}

.s-w-200 {
    width: 200px;
}

.s-w-240 {
    width: 240px;
}

.s-w-280 {
    width: 280px;
}

.s-w-300 {
    width: 300px;
}

.s-w-320 {
    width: 320px;
}

/*
------------
paddings
------------
*/

.s-pd-5 {
    padding: 5px;
}

.s-pd-10 {
    padding: 10px;
}

.s-pd-15 {
    padding: 15px;
}

.s-pd-20 {
    padding: 20px;
}

.s-pdl-5 {
    padding-left: 5px;
}

.s-pdl-10 {
    padding-left: 10px;
}

.s-pdl-15 {
    padding-left: 15px;
}

.s-pdl-20 {
    padding-left: 20px;
}

.s-pdl-30 {
    padding-left: 30px;
}

.s-pdr-5 {
    padding-right: 5px;
}

.s-pdr-10 {
    padding-right: 10px;
}

.s-pdr-15 {
    padding-right: 15px;
}

.s-pdr-20 {
    padding-right: 20px;
}

.s-pdt-5 {
    padding-top: 5px;
}

.s-pdt-10 {
    padding-top: 10px;
}

.s-pdt-15 {
    padding-top: 15px;
}

.s-pdt-20 {
    padding-top: 20px;
}

.s-pdb-5 {
    padding-bottom: 5px;
}

.s-pdb-10 {
    padding-bottom: 10px;
}

.s-pdb-15 {
    padding-bottom: 15px;
}

.s-pdb-20 {
    padding-bottom: 20px;
}

/*
------------
margins
------------
*/

.s-mg-5 {
    margin: 5px;
}

.s-mg-10 {
    margin: 10px;
}

.s-mg-15 {
    margin: 15px;
}

.s-mg-20 {
    margin: 20px;
}

.s-mgl-5 {
    margin-left: 5px;
}

.s-mgl-10 {
    margin-left: 10px;
}

.s-mgl-15 {
    margin-left: 15px;
}

.s-mgl-20 {
    margin-left: 20px;
}

.s-mgr-5 {
    margin-right: 5px;
}

.s-mgr-10 {
    margin-right: 10px;
}

.s-mgr-15 {
    margin-right: 15px;
}

.s-mgr-20 {
    margin-right: 20px;
}

.s-mgr-30 {
    margin-right: 30px;
}

.s-mgt-5 {
    margin-top: 5px;
}

.s-mgt-10 {
    margin-top: 10px;
}

.s-mgt-15 {
    margin-top: 15px;
}

.s-mgt-20 {
    margin-top: 20px;
}

.s-mgb-5 {
    margin-bottom: 5px;
}

.s-mgb-10 {
    margin-bottom: 10px;
}

.s-mgb-15 {
    margin-bottom: 15px;
}

.s-mgb-20 {
    margin-bottom: 20px;
}

.s-flex {
    display: flex;
}

.s-jc-sb {
    justify-content: space-between;
}

.s-jc-c {
    justify-content: center;
}

.s-aic {
    align-items: center;
}

.s-fsbc {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.s-fcc {
    display: flex;
    justify-content: center;
    align-items: center;
}
  
.s-fdc {
    display: flex;
    flex-direction: column;
}

/*
------------
scroll bar
------------
*/

/*
=======================
scroll bar
=======================
*/

::-webkit-scrollbar {

    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {

    border-width: 1px;
    border-style: solid;
    border-radius: 8px;
    visibility: hidden;
}

:hover::-webkit-scrollbar-track {
    visibility: visible;
}

::-webkit-scrollbar-thumb {

    height: 4px;
    border-radius: 8px;
    visibility: hidden;
}

:hover::-webkit-scrollbar-thumb {
    visibility: visible;
}

/*
------------
main components of a page
------------
*/

.win-body {
	
	/* 顶部预留高度，分配给标题栏 */
	padding-top: 32px;
	/* 底部预留高度，分配给底边栏 */
	padding-bottom: 28px;
}

.win-header	{

	position: fixed;
	z-index: 999;
	top: 0;
	left: 0;
	height: 32px;
	width: 100%;
	padding: 0 16px;
	font-size: 14px;
}

.win-title {

	margin-right: 120px;
    padding-left: 10px;
	line-height: 32px;
}

.win-buttons {

    position: absolute;
    right: 10px;
	padding-left: 10px;
	line-height: 32px;
}

.win-buttons a {
    padding: 0 5px;
}

.view-template {

    box-sizing: border-box;
    width: 100%;
    height: 100%;
}

.xtcontainer {
    padding-top: 28px;
}

.xtcontainer .xtheader {

    margin-top: -28px;
    height: 28px;
    line-height: 28px;
    padding: 0 8px;
    overflow: hidden;
}

.xtform .xtinput {

    position: relative;
    box-sizing: border-box;
    height: 24px;
    margin: 8px 0;
}

.xtform .xtinput .xtlabel {

    box-sizing: border-box;
    display: inline-block;
    width: 20%;
    height: 100%;
    line-height: 24px;
    padding-right: 10px;
    text-align: right;
    overflow: hidden;
    user-select: none;
}

.xtform .xtinput > .el-input,
.xtform .xtinput > .el-autocomplete,
.xtform .xtinput > .el-input-number,
.xtform .xtinput > .el-select {

    width: 80%;
    top: -8px;
}

.xtform .xtinput .el-input .el-input__inner {
    width: 100%;
}

.xtpop-body {
    padding: 10px;
}

.xt-table-row-input {

    box-sizing: content-box;
    width: 80px;
    height: 18px;
    line-height: 18px;
    padding: 0 5px;
    border: none;
    border-radius: 1px;
}

.tabcontent-panel {
	
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	/* padding: 2px; */
}

.tabcontent-panel > .tabcontent-box {
	
    box-sizing: border-box;
    padding-top: 2px;
	width: 100%;
	height: 100%;
}

.frag-channels {

    padding: 2px 4px;
    line-height: 20px;
}

.frag-channels .channel-item {

    margin-right: 10px;
    display: inline-block;
    border-radius: 2px;
    padding: 0 5px;
}