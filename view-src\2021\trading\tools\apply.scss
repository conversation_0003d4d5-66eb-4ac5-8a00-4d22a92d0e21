@import 'asset/css/themed-dark';
@import 'asset/css/common';

.apply-trade {
    width: 100%;
    height: 100%;
}

$height: 350px;
.apply-user-input {
    display: flex;
    height: $height;

    .apply-input-container {
        min-width: 300px;

        .apply-trade-container {
            @extend .s-border-box;
            @extend .themed-box;
            height: 100%;
        }
    }

    .apply-account-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}

.apply-trade-container {
    .apply-trade-header {
        padding: 5px;
        @extend .themed-header;
    }

    .apply-input {
        display: flex;
        height: $height - 27px;
        flex-direction: column;

        .apply-input-item {
            display: flex;
            justify-content: space-around;
            flex: 1;
            align-items: center;

            .apply-input-item-label {
                width: 30%;
                text-align: center;
                @extend .themed-color;
            }

            .apply-input-item-content {
                width: 60%;
                text-align: center;
            }
        }
    }
}

.apply-tab {
    width: 100%;
    height: calc(100% - 352px);
    background-color: #1a212b;
    overflow: hidden;
}

.el-tabs--card {
    background-color: #1a212b;
}

.apply-account-header {
    @extend .themed-header;
    display: flex;
    flex-direction: row;
    border-left: 1px solid black;
    height: 27px;
    align-items: center;
}

.el-tabs__nav-scroll {
    background-color: #23354d;
}

.vxe-table--filter-wrapper {
    & * {
        color: black !important;
    }
}

.el-tabs__header {
    margin-bottom: 0px;
}

.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: none;
}

.el-tabs__item {
    @extend .themed-color;
    @extend .s-fs-12;
    border-bottom: none !important;
    border-left: none !important;

    line-height: 20px;
    padding: 0px 20px 3px 20px !important;
    margin-top: 3px;
    height: auto;
    margin-left: 10px;
}

.el-tabs__item.is-active {
    @extend .themed-selected;
    @extend .themed-box;
    border-radius: 2px 2px 0 0;
}

.el-tabs__item:hover {
    @extend .themed-selected;
}

.apply-tab {
    .el-tabs--card {
        height: 100%;

        .el-tabs__content {
            height: calc(100% - 26px) !important;

            .el-tab-pane {
                height: 100%;
            }
        }
    }
}
