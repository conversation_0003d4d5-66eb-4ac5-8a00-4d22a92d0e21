const httpRequest = require('../libs/http').http;

class KlineRepository {

    getAll(instrrments, begin_time, end_time, bar_type) {
        return new Promise((resolve, reject) => {
            httpRequest.get('http://211.152.51.189:9999/quote/v3/history/get_bars', { params: { symbols: instrrments.join(';'), begin_time: begin_time, end_time: end_time, bar_type: bar_type }})
                            .then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }
}

module.exports = { repoKline: new KlineRepository() };