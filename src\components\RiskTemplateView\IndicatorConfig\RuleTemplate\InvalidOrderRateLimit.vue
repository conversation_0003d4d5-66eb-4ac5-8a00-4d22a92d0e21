<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { deepClone, isNone } from '@/script';
import type { AnyIndicatorRiskParamObject, CommonRiskAlertConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { RiskSummarizationMethod } from '../../RiskParamTypeAndSummary';
import { AlertTypes, ExpressionTypes } from '@/enum/riskc';
import { DefaultRiskParamCreator } from '../../DefaultRiskParamCreator';

/**
 * 废单比率限制
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  expression: number;
  // 委托次数
  orderCount: number;
  // 单票还是汇总
  riskStatisticsType: number;
  paramAlert: CommonRiskAlertConfig;
  paramForbidOrder: CommonRiskAlertConfig;
}

const { ruleSetting } = defineProps<{
  ruleSetting: RuleInnerSetting | null | undefined;
}>();

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const definedActions = computed(() => {
  const { paramAlert, paramForbidOrder } = localRuleSetting.value as RuleInnerSetting;
  return [paramAlert, paramForbidOrder];
});

const rules = {
  actions: [
    { required: true, message: '请选择比较符', trigger: 'blur', validator: validateActions },
  ],
};

function validateActions(rule: any, value: any, callback: any) {
  const failed = definedActions.value.some(item => {
    const { expression, value, alertType } = item;
    if (isNone(expression) || isNone(value) || isNone(alertType)) {
      return true;
    } else {
      return false;
    }
  });
  if (failed) {
    callback(new Error('请设置完善的风控条件'));
  } else {
    callback();
  }
}

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = handleInputChange(newValue);
  },
  { immediate: true },
);

function handleInputChange(newValue: RuleInnerSetting | null | undefined) {
  return newValue && JSON.stringify(newValue) != '{}'
    ? deepClone(newValue)
    : createEmptyRiskParam();
}

function createEmptyRiskParam(): RuleInnerSetting {
  return DefaultRiskParamCreator[IdcComponentNameDef.InvalidOrderRate]();
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string | string[]];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return RiskSummarizationMethod[IdcComponentNameDef.InvalidOrderRate](localRuleSetting.value);
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <!-- <el-alert type="error" effect="dark" :closable="false" title="盘中不支持修改该指标" /> -->
      </div>
      <div class="custom-row">
        <el-form-item label="指标设置" prop="expression">
          <div w-full flex aic gap-10>
            <div w-auto>
              <label class="placed-label">委托次数</label>
            </div>
            <el-select
              v-model="localRuleSetting.expression"
              style="width: 100px"
              @change="handleParamHotChange"
            >
              <el-option
                v-for="(item, idx) in ExpressionTypes"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-input-number
              :controls="false"
              :precision="0"
              :step="1"
              :min="0"
              v-model="localRuleSetting.orderCount"
              @change="handleParamHotChange"
              style="width: 80px"
            ></el-input-number>
            <label class="placed-label">次时，废单比率按汇总限制</label>
          </div>
        </el-form-item>
      </div>
      <template v-for="(item, idx) in definedActions" :key="idx">
        <div class="custom-row">
          <el-form-item label="" prop="expression">
            <div w-full flex aic gap-10>
              <el-select
                v-model="item.expression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in ExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="0"
                :step="1"
                :min="0"
                :max="100"
                v-model="item.value"
                @change="handleParamHotChange"
                style="width: 80px"
              ></el-input-number>
              <label class="placed-label">%时，</label>
              <el-select
                v-model="item.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in AlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
