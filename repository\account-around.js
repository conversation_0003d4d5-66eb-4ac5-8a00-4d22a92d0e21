const { http } = require('../libs/http');
const { SanguItemInfo } = require('../model/sangu');
/**
 * 账号周边操作
 */
class AccountRoundRepository {

    /**
     * 查询全部账号检测结果
     */
    queryAll() {

        return new Promise((resolve, reject) => {
                    http.get('/account/overwrite').then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 查询账号检测结果详情
     * @param {Number|String} account_id 账号ID
     * @param {Number} check_type 检测类型代码
     */
    queryDetail(account_id, check_type) {

        return new Promise((resolve, reject) => {
                    http.get('/account/overwrite/result', { params: { account_id, check_type } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 覆盖
     */
    overlap(account_id, overlap) {

        return new Promise((resolve, reject) => {
                    http.post('/account/overlap', {}, { params: { account_id, overlap } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 成交检测
     */
    tradeCheck(accountId) {

        return new Promise((resolve, reject) => {
                    http.post('/account/check/trade', {}, { params: { accountId } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 委托检测
     */
    orderCheck(accountId) {

        return new Promise((resolve, reject) => {
                    http.post('/account/check/order', {}, { params: { accountId } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 权益检测
     */
    balanceCheck(accountId) {

        return new Promise((resolve, reject) => {
                    http.post('/account/check/balance', {}, { params: { accountId } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 实时持仓检测
     */
    realtimePosCheck() {

        return new Promise((resolve, reject) => {
                    http.post('/account/check/position').then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 散股查询
     * @param {*} account_id 某个具体账号，不指定为全部
     * @returns {{ errorCode: number, errorMsg: number, data: Array<Array> }}
     */
    sanguQuery(account_id = null) {

        return new Promise((resolve, reject) => {
            http.post('/account/sangu/handle', { params: { account_id, operate_way: 0 }}).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); }
            );
        });
    }

    /**
     * 卖出账号散股
     * @param {Array<SanguItemInfo>} targets
     */
    sanguSell(targets) {

        let originals = targets.map(item => {
            let { accountId, instrument, totalPosition, sanguPosition, operateWay, accountName } = item;
            return [accountId, instrument, totalPosition, sanguPosition, operateWay, accountName];
        });
        
        return new Promise((resolve, reject) => {
                    http.post('/account/sangu/order', originals).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }
}

module.exports = {

    /** 账号周边操作 */
    repoAccountAround: new AccountRoundRepository(),
};
