const IView = require('../../component/iview').IView;
const DragWidgetCom = require('./indicator/components/DragWidget/index');
const PreviewAreaCom = require('./indicator/components/PreviewArea/index');
const ConfigAreaCom = require('./indicator/components/ConfigArea/index');
const DesignHeaderCom = require('./indicator/components/DesignHeader/index');
const Echarts = require('vue-echarts');
Vue.component('v-chart', Echarts);

class View extends IView {
    get repoIndicator() {
        return require('../../repository/indicator').repoIndicator;
    }

    constructor(view_name, stand_alone, { title = '模板设计' }) {
        super(view_name, true, title);
        this.$container = null;
        this.vueApp = null;
        this.context = { template_id: null };
        this.registerEvent('setContextData', this.setContextData.bind(this));
    }

    setContextData(params) {
        this.context.template_id = params.template_id || null;
    }

    async createApp() {
        const self = this;
        let DragWidget = await new DragWidgetCom().build();
        let PreviewAreaIns = new PreviewAreaCom();
        await PreviewAreaIns.setOptions();
        let PreviewArea = await PreviewAreaIns.build();
        let ConfigArea = await new ConfigAreaCom().build();
        let DesignHeader = await new DesignHeaderCom().build();

        this.vueApp = new Vue({
            el: this.$container.querySelector('.template-view-root'),
            data: {
                currentItem: null,
                indicatorList: [],
                templateId: self.context.template_id,
                templateDetail: {},
                rawData: [
                    ['交易日', '序列1', '序列2'],
                    ['20180101', 1.03453512312, 1.05787123123],
                    ['20180201', 1.08354342342, 1.01558789789],
                    ['20180301', 0.99812456588, 1.02328348273],
                    ['20180401', 1.00486823423, 1.03278952334],
                    ['20180501', 1.12301290541, 0.99523403274],
                    ['20180601', 1.08512568678, 1.00556782311],
                    ['20180701', 1.15734778678, 1.12190890234],
                ],
            },
            mixins: [],
            components: {
                DragWidget,
                PreviewArea,
                ConfigArea,
                DesignHeader,
            },
            computed: {},
            watch: {
                currentItem(val) {
                    if (val == null) {
                        this.removeCurrentClass();
                    }
                },
            },
            mounted() {
                this.onClick();
                this.getIndicators();
                this.shouldGetTemplateDetail();
            },
            methods: {
                shouldGetTemplateDetail() {
                    if (this.templateId) {
                        this.getTemplateDetail();
                    }
                },
                async getTemplateDetail() {
                    let resp = await self.repoIndicator.getTemplateDetail(this.templateId);
                    if (resp.errorCode == 0) {
                        console.log(resp.data);
                        this.templateDetail = resp.data;
                    }
                },
                async handleSaveTemplate(controls, form) {
                    // console.log(controls);
                    const userInfo = self.userInfo;
                    let info = {
                        controls,
                        layout: {
                            interval: form.interval,
                        },
                        report_info: {
                            title_format: form.templateName,
                        },
                        create_user_id: userInfo.userId,
                        create_user_name: userInfo.userName,
                        org_id: userInfo.orgId,
                    };
                    let resp = this.templateId ? await self.repoIndicator.updateTemplate(this.templateId, info) : await self.repoIndicator.createTemplate(info);
                    if (resp.errorCode == 0) {
                        self.interaction.showSuccess('保存成功');
                        self.trigger('save', { template_name: form.templateName, ...resp.data });
                    }
                },
                async getIndicators() {
                    let resp = await self.repoIndicator.getIndicatorList();
                    if (resp.errorCode == 0) {
                        this.indicatorList = resp.data || [];
                    }
                },
                onClick() {
                    document.addEventListener('click', e => {
                        const item = e.target;
                        if (item.classList && (item.classList.contains('preview-box') || item.classList.contains('config-area') || item.classList.contains('preview-area'))) return;
                        const parents = this.getParents(item);
                        if (!parents.some(el => el.classList && (el.classList.contains('preview-box') || el.classList.contains('config-area')))) {
                            // this.currentItem = null;
                            this.$refs.previewArea.updateCurrent(-1);
                        }
                    });
                },
                handleUpdateCurrent(item, itemIndex) {
                    this.currentItem = item || null;
                    // const boxes = document.querySelectorAll('.preview-box');
                    // boxes.forEach((ele, index) => {
                    //     ele.classList.remove('current');
                    //     if (index == itemIndex) {
                    //         ele.classList.add('current');
                    //     }
                    // });
                },
                removeCurrentClass() {
                    // const $current = document.querySelector('.current');
                    // if ($current) {
                    //     $current.classList.remove('current');
                    // }
                },
                getParents(el, parentSelector /* optional */) {
                    // If no parentSelector defined will bubble up all the way to *document*
                    if (parentSelector === undefined) {
                        parentSelector = document;
                    }

                    let parents = [];
                    let p = el.parentNode;

                    while (p !== parentSelector) {
                        let o = p;
                        parents.push(o);
                        p = o.parentNode;
                    }
                    parents.push(parentSelector); // Push that parentSelector you wanted to stop at
                    return parents;
                },
            },
        });
    }

    dispose() {
        this.vueApp = null;
        while (this.$container.hasChildNodes()) {
            this.$container.removeChild(this.$container.firstChild);
        }
        this.trigger('close');
    }

    build($container) {
        this.$container = $container;
        // let params = new URLSearchParams(window.location.search).toString();
        // this.setContextData(params);
        this.createApp();
    }
}

module.exports = View;
