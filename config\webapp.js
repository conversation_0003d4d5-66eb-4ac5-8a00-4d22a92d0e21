class TemplateSaving2Url {

    constructor({ appType, templateId, componentName, title }) {

        this.appType = appType;
        this.templateId = templateId;
        this.componentName = componentName;
        this.title = title;
    }
}

class TemplateProvider {

    /**
     * @param {String} type 
     * @param {String} name 
     * @param {Function} url_formatter 
     */
    constructor(type, name, url_formatter) {

        this.type = type;
        this.name = name;
        this.urlFormatter = url_formatter;
    }
}

const Formatters = {

    /**
     * @param {TemplateSaving2Url} tmpl 
     */
    FormatFundReportUrl(tmpl) {

        let params = [
    
            { key: 'templateId', value: tmpl.templateId },
            { key: 'componentName', value: tmpl.componentName },
            { key: 'title', value: tmpl.title },
            { key: 'tabName', value: tmpl.title },
        ];
    
        let kvstr = params.map(x => `${x.key}=${encodeURIComponent(x.value)}`).join('&');
        return `${TemplateAppConfig.FundReportPageUrl}?${kvstr}`;
    }
};

const TemplateAppProvider = {
    fund: new TemplateProvider('fund-report', '基金分析报告', Formatters.FormatFundReportUrl),
};

const TemplateAppConfig = {
    
    TemplateUrlPrefix: 'http://template=',
    FundReportPageUrl: 'http://fund.gaoyusoft.com/#/independentPage',
};

const AppTypes = {

    normal: { code: 'normal', mean: '普通应用' },
    report: { code: TemplateAppProvider.fund.type, mean: '基金报告应用' },
};

module.exports = { TemplateAppConfig, TemplateAppProvider, AppTypes };