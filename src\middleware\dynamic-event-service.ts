import { GetLogger } from '../global-state';
import { PubSubCapability } from './pub-sub-capability';

const defaultLogger = GetLogger();

export type DynamicEventHandler = (...args: any) => void;

/**
 * 动态事件中间件
 */
export class DynamicEventService {

    /**
     * 动态事件订阅全量MAP
     */
    private eventMap: { [eventName: number | string]: PubSubCapability };

    constructor() {
        this.eventMap = {};
    }

    private ensureGet(event_name: number | string) {

        const matched = this.eventMap[event_name];
        return matched || (this.eventMap[event_name] = new PubSubCapability());
    }

    /**
     * 订阅
    */
    sub(event_name: number | string, handler: DynamicEventHandler) {
        this.ensureGet(event_name).sub(handler);
    }

    /**
     * 退订订阅
     * @param event_name 未提供则代表退订所有事件
     * @param handler 未提供则代表某事件，或所有事件下的回调函数
     */
    unsub(event_name?: number | string, handler?: DynamicEventHandler) {

        if (event_name) {

            const matched = this.ensureGet(event_name);
            if (typeof handler == 'function') {
                matched.unsub(handler);
            }
            else {
                matched.unsubAll();
            }
        }
        else {

            const events = Object.keys(this.eventMap);
            events.forEach(key => { delete this.eventMap[key]; });
        }
    }

    /**
     * 触发订阅回调
     */
    fire(event_name: number | string, ...args: any) {

        const matched = this.ensureGet(event_name);

        if (matched.totalHandlers > 0) {
            matched.fire(...args);
        }
        else {
            defaultLogger.warn('No event handlers registered', { event_name, args });
        }
    }
}