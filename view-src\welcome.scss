#root-bg-pic {
	position: absolute;
	z-index: 1;
	background-image: url(../asset/image/main-bg.jpg);
	background-size: cover;
	background-repeat: no-repeat;
}

#root-bg {
	position: absolute;
	z-index: 2;
	background-color: #001A34;
	opacity: 0.6;
}

#body-main {

	position: absolute;
	z-index: 99;

	.win-drag {

		position: absolute;
		width: 100%;
		height: 60px;
	}
}

.bottom-bar {
	position: absolute;
	bottom: 1px;
	width: 100%;
}

.step-wrapper {
	margin-left: 1px;
	margin-right: 1px;
}

.el-steps {
	display: block;
	padding: 0 10px;
	border-radius: 0;
}

.el-steps>.el-step {
    display: inline-block;
    width: 33.3%;
    height: 46px;
}

.el-steps>.el-step .el-step__line {
	display: none;
}

.el-steps>.el-step .el-step__head {
    top: 15px;
}

.el-steps>.el-step .el-step__main {
    padding-left: 20px;
    top: -3px;
}

.el-steps>.el-step .el-step__main .el-step__title {
    max-width: none;
}

.el-step__title,
.el-step__icon {
	color: black;
}

.bulletin {
    line-height: 230px;
    border: 1px solid #615757;
}

.bulletin .desc-text {
    position: relative;
    top: -1px;
    padding-left: 5px;
}