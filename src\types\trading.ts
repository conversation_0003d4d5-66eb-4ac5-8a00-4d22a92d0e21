/**
 * 常规订单
 */
export interface RegularOrder {
  /** 账号ID */
  accountId: number | string;
  /** 买卖标志 */
  bsFlag: number;
  /** 业务标志 */
  businessFlag: number;
  /** 自定义 ID */
  customId: number | string;
  /** 投机套保标识 */
  hedgeFlag: number;
  /** 合约代码 */
  instrument: string;
  /** 当前下单时间戳 */
  orderTime: number | null;
  /** 开平仓标志 */
  positionEffect: number;
  /** 价格 */
  price: number;
  /** 价格类型 */
  priceType: number;
  /** 用户ID */
  userId: number | string;
  /** 数量 */
  volume: number;
}

/**
 * 篮子数据结构
 */
export interface BasketInfoItem {
  /**
     * 合约名称
     */
  instrumentName: string;

  /**
   * 合约代码
   */
  instrument: string;

  /**
   * 资产类型
   */
  assetType: number;

  /**
   * 买卖方向
   */
  direction: number;

  /**
   * 委托数量
   */
  volume: number;

  /**
   * 权重
   */
  weight: number;
}

/**
 * 篮子订单
 */
export interface BasketOrder {
  /** 篮子ID */
  basketId: number | string;
  /** 价格类型 */
  priceFollowType: number;
  /** 偏移量 */
  priceDeviation: number;
  /** 方向：买入1，卖出-1，调仓0 */
  taskType: number;
  /** 调仓类型 0非调仓 1 普通调仓  2 两融调仓 */
  adjustType: number;
  /**业务类型：融资融券... */
  businessFlag: number;
  /** 预览模式，true返回预览结果，服务器不下单，false直接下单 */
  preview: boolean;
  /** 篮子限制规则 */
  orderRegulation: {
    excludeSuspension: boolean;
    excludeCashSubstitution: boolean;
    excludeUpperLimit: boolean;
    excludeLowerLimit: boolean;
  };
  /** 操作的篮子数量，或者金额，或者比例 */
  executeVolume: number;
  /** 下单方式 */
  executeType: number;
  /** 账号具体分配规则 */
  taskDetails: Array<{
    fundId: number | string;
    strategyId: number | string;
    accountId: number | string;
    multiple: number;
  }>;

  algorithmBean: {
    algoId: number | string;
    algoName?: string;
    algoParam: any;
    /** 开始时间戳 */
    effectiveTime: number;
    /** 结束时间戳 */
    expireTime: number;
  };
}

/**
 * 算法单
 */
export interface AlgoOrder {
  /** 账号ID */
  accountId: number | string;
  /** 过期后继续交易，0或1 */
  afterAction: number;
  /** 算法参数 */
  algoParam: any;
  /** 算法映射ID */
  algorithmMappingId: number;
  /** 交易方向 */
  direction: number;
  /** 开始时间戳 */
  effectiveTime: number;
  /** 结束时间戳 */
  expireTime: number;
  /** 主体ID */
  identityId: number | string;
  /** 合约代码 */
  instrument: string;
  /** 涨跌停继续交易，0或1 */
  limitAction: number;
  /** 算法任务名称 */
  taskName: string;
  /** 下单交易员ID */
  userId: number;
  /** 下单数量 */
  volume: number;
}

/**
 * 订单信息
 */
export interface OrderInfo {
  /** 账号ID */
  accountId: number;
  /** 账号名称 */
  accountName: string;
  /** 资产类型 */
  assetType: number;
  /** 业务标志 */
  businessFlag: number;
  /** 撤单数量 */
  cancelledVolume: number;
  /** 手续费 */
  commission: number;
  /** 自定义 ID */
  customId: number;
  /** 买卖方向 */
  direction: number;
  /** 交易所订单 ID */
  exchangeOrderId: string;
  /** 资金账号 */
  financeAccount: string;
  /** 是否强平 */
  forceClose: boolean;
  /** 是否境外 */
  foreign: boolean;
  /** 冻结手续费 */
  frozenCommission: number;
  /** 冻结保证金 */
  frozenMargin: number;
  /** 冻结数量 */
  frozenVolume: number;
  /** 基金 ID */
  fundId: number;
  /** 基金名称 */
  fundName: string;
  /** 投机套保标识 */
  hedgeFlag: number;
  /** 订单 ID */
  id: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 本地订单 ID */
  localOrderId: string;
  /** 委托价格 */
  orderPrice: number;
  /** 委托价格类型 */
  orderPriceType: number;
  /** 订单状态 */
  orderStatus: number;
  /** 委托时间 */
  orderTime: string;
  /** 父订单 ID */
  parentOrderId: number;
  /** 开平仓标志 */
  positionEffect: number;
  /** 成交金额 */
  tradedAmount: number;
  /** 成交数量 */
  tradedVolume: number;
  /** 成交价格 */
  tradedPrice: number;
  /** 交易日 */
  tradingDay: string;
  /** 用户 ID */
  userId: number;
  /** 用户姓名 */
  userName: string;
  /** 原委托数量 */
  volumeOriginal: number;
  /** 创建时间 */
  createTime: number;
  /** 成交时间 */
  tradeTime?: number;
  /** 更新时间 */
  updateTime?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 成交记录信息
 */
export interface TradeRecordInfo {
  /** 账号ID */
  accountId: number;
  /** 账号名称 */
  accountName: string;
  /** 资产类型 */
  assetType: number;
  /** 买卖方向 */
  direction: number;
  /** 交易所订单 ID */
  exchangeOrderId: string;
  /** 基金 ID */
  fundId: number;
  /** 基金名称 */
  fundName: string;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 订单 ID */
  orderId: number;
  /** 开平仓标志 */
  positionEffect: number;
  /** 成交 ID */
  tradeId: string;
  /** 成交价格 */
  tradedPrice: number;
  /** 成交时间 */
  tradeTime: string;
  /** 交易日 */
  tradingDay: string;
  /** 用户 ID */
  userId: number;
  /** 用户姓名 */
  userName: string;
  /** 成交数量 */
  volume: number;
  /** 手续费 */
  commission: number;
}

/**
 * 指令信息
 */
export interface InstructionInfo {
  /** 工作流ID */
  workFlowId: string;
  /** 工作流名称 */
  workFlowName: string;
  /** 账号名称 */
  accountName: string;
  /** 产品名称 */
  fundName: string;
  /** 来源用户名 */
  sourceUserName: string;
  /** 止损 */
  stopLoss: number;
  /** 指令类型 */
  instructionType: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 方向 */
  direction: number;
  /** 委托量 */
  volumeOriginal: number;
  /** 指令状态 */
  instructionStatus: number;
  /** 执行状态 */
  executeStatus: number;
  /** 开始时间 */
  startTime: string;
}

/**
 * 持仓信息
 */
export interface PositionInfo {
  /** ID */
  id: string;
  /** 账号ID */
  accountId: number;
  /** 账号名称 */
  accountName: string;
  /** 资产类型 */
  assetType: number;
  /** 均价 */
  avgPrice: number;
  /** 平仓盈亏 */
  closeProfit: number;
  /** 持仓方向 */
  direction: number;
  /** 冻结今仓数量 */
  frozenTodayVolume: number;
  /** 冻结数量 */
  frozenVolume: number;
  /** 资金账号 */
  financeAccount: string;
  /** 基金 ID */
  fundId: number;
  /** 基金名称 */
  fundName: string;
  /** 浮动盈亏 */
  floatProfit: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 上次结算价 */
  lastSettlePrice: number;
  /** 市值 */
  marketValue: number;
  /** 保证金率（按金额） */
  marginRateByMoney: number;
  /** 保证金率（按数量） */
  marginRateByVolume: number;
  /** 持仓成本 */
  positionCost: number;
  /** 结算价 */
  settlementPrice: number;
  /** 今持仓 */
  todayPosition: number;
  /** 交易日 */
  tradingDay: string;
  /** 已用保证金 */
  usedMargin: number;
  /** 已用手续费 */
  usedCommission: number;
  /** 昨持仓 */
  yesterdayPosition: number;
  /** 更新时间 */
  updateTime: number;
}

/**
 * 权益信息
 */
export interface EquityInfo {
  /** ID */
  id: number;
  /** 主体ID */
  identityId: number | string;
  /** 主体名称 */
  identityName: string;
  /** 可用资金 */
  available: number;
  /** 期初权益 */
  balance: number;
  /** 平仓盈亏 */
  closeProfit: number;
  /** 手续费 */
  commission: number;
  /** 当日盈亏 */
  dayProfit: number;
  /** 冻结手续费 */
  frozenCommission: number;
  /** 冻结保证金 */
  frozenMargin: number;
  /** 基金份额 */
  fundShare: number;
  /** 入金金额 */
  inMoney: number;
  /** 占用保证金 */
  margin: number;
  /** 市值 */
  marketValue: number;
  /** 净值 */
  nav: number;
  /** 出金金额 */
  outMoney: number;
  /** 持仓盈亏 */
  positionProfit: number;
  /** 昨日权益 */
  preBalance: number;
  /** 涨跌幅 */
  risePercent: number;
  /** 交易日 */
  tradingDay: string;
}

/**
 * 出入金信息
 */
export interface CashInfo {
  /** 创建时间 */
  createTime: number;
  /** ID */
  id: number;
  /** 主体ID */
  identityId: number | string;
  /** 主体名称 */
  identityName: string;
  /** 入金金额 */
  inMoney: number;
  /** 操作员ID */
  operatorUserId: number;
  /** 操作员名称 */
  operatorUserName: string;
  /** 出金金额 */
  outMoney: number;
  /** 交易日 */
  tradingDay: string;
  /** 类型 1: 分红入金 */
  type?: number;
}

/**
 * 算法信息
 */
export interface AlgorithmInfo {
  /** 账号ID */
  accountId: number;
  /** 后续动作 */
  afterAction: number;
  /** 算法映射 ID */
  algorithmMappingId: number;
  /** 算法名称 */
  algorithmName: string;
  /** 算法类型 */
  algorithmType: number;
  /** 算法参数 */
  algoParam: string;
  /** 买卖方向 */
  direction: number;
  /** 生效时间 */
  effectiveTime: string;
  /** 外部 ID */
  externalId: string;
  /** 过期时间 */
  expireTime: string;
  /** 标识 ID */
  identityId: number;
  /** 合约代码 */
  instrument: string;
  /** 限制动作 */
  limitAction: number;
  /** 任务名称 */
  taskName: string;
  /** 用户 ID */
  userId: number;
  /** 数量 */
  volume: number;
}

/**
 * 算法单详情信息
 */
export interface AlgorithmOrderInfo {
  /** 账号ID */
  accountId: number;
  /** 账号名称 */
  accountName: string;
  /** 后续动作 */
  afterAction: number;
  /** 算法映射 ID */
  algorithmMappingId: number;
  /** 算法名称 */
  algorithmName: string;
  /** 算法状态 */
  algorithmStatus: number;
  /** 算法类型 */
  algorithmType: number;
  /** 算法参数 */
  algoParam: string;
  /** 买卖方向 */
  direction: number;
  /** 生效时间 */
  effectiveTime: string;
  /** 外部 ID */
  externalId: string;
  /** 过期时间 */
  expireTime: string;
  /** 基金名称 */
  fundName: string;
  /** 详情 ID */
  id: number;
  /** 标识 ID */
  identityId: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 限制动作 */
  limitAction: number;
  /** 已委托数量 */
  orderedVolume: number;
  /** 订单列表 */
  orders: OrderInfo[];
  /** 供应商名称 */
  supplierName: string;
  /** 成交价格 */
  tradePrice: number;
  /** 已成交数量 */
  tradedVolume: number;
  /** 任务 ID */
  taskId: number;
  /** 任务名称 */
  taskName: string;
  /** 用户 ID */
  userId: number;
  /** 数量 */
  volume: number;
}

/**
 * 分页返回的数据结构
 */
export interface PagedResult<T> {
  contents: T[];
  identityId: number | string;
  pageNo: number;
  pageSize: number;
  totalPages: number;
  totalSize: number;
}
