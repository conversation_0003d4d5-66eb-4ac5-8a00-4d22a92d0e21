import { ICommunication } from './protocol';
import { RuntimeEnvironment, SocketServerType } from '../config/architecture';

const FactoryStates = {

    /** 交易服务器通信对象 */
    TradeCommClient: null as ICommunication | null,
    /** 行情服务器通信对象 */
    QuoteCommClient: null as ICommunication | null,
};

/**
 * 通信工厂类
 */
export class CommunicationFactory {

    /**
     * 创建交易服务器连接通信类
     */
    static async CreateTradeServer(env: RuntimeEnvironment, host: string, port: number): Promise<ICommunication> {
        return await this.CreateInstance(SocketServerType.TradeServer, env, host, port);
    }

    /**
     * 创建行情服务器连接通信类
     */
    static async CreateQuoteServer(env: RuntimeEnvironment, host: string, port: number): Promise<ICommunication> {
        return await this.CreateInstance(SocketServerType.QuoteServer, env, host, port);
    }

    /**
     * 基于运行环境创建适当的通信类实例
     */
    private static async CreateInstance(server_type: string, env: RuntimeEnvironment, host: string, port: number): Promise<ICommunication> {

        if (!host || !port || port <= 0) {
            throw new Error('host or port is invalid');
        }

        const istrade = server_type == SocketServerType.TradeServer;
        const isws = env === RuntimeEnvironment.WebSocket;
        const isns = env === RuntimeEnvironment.Native;
        const client = istrade ? FactoryStates.TradeCommClient : FactoryStates.QuoteCommClient;
        const existed = client && client.host === host && client.port === port;

        if (existed) {
            return client;
        }

        let instance: ICommunication;

        if (isws) {

            const { WebSocketCommunication } = await import('./web-socket');
            instance = new WebSocketCommunication(server_type, host, port);
        }
        // else if (isns) {

        //     const { NativeSocketCommunication } = await import('./native-socket');
        //     instance = new NativeSocketCommunication(server_type, host, port);
        // }
        else {
            throw new Error('Unsupported communication type' + env);
        }

        if (istrade) {
            FactoryStates.TradeCommClient = instance;
        }
        else {
            FactoryStates.QuoteCommClient = instance;
        }

        return instance;
    }
}
