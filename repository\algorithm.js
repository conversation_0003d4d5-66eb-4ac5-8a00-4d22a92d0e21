const http = require('../libs/http').http;
const { T0TaskPackage } = require('../model/t0');
const { GaoyuAlgo, GaoyuAlgoParamDefinition } = require('../model/algo-vendor');

/**
 * 算法单种类
 */
const AlgorithmClasses = {

    /** 普通算法单 */
    normal: 0,
    /** 涨停板预留 */
    boarding: 1,
    /** T0日内回转 */
    t0: 2,
};

class AlgorithmRepository {

    /**
     * 查询算法清单
     * @param {Number} algorithm_class
     */
    queryAlgoes(algorithm_class, account_id) {
        
        return new Promise((resolve, reject) => {
            http(`/algorithm/mapping`, { params: { algorithm_class, account_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 根据账号查询其（开户经纪商）所能支持的算法
     */
    queryAlgoesV2403(algorithm_class, account_id) {
        
        return new Promise((resolve, reject) => {
            http(`/algorithm/desc/mapping`, { params: { algorithm_class, account_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * @param {Array} data
     * @param {Number} algorithm_class 
     */
    order(data, algorithm_class = AlgorithmClasses.normal) {
        
        return new Promise((resolve, reject) => {
            http(`/algorithm/order`, { method: 'post', params: { algorithm_class }, data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    getOrderList() {

        return new Promise((resolve, reject) => {
            
            http(`/algorithm/query`).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 查询T0日内回转任务数据
     * @param {{ algorithm_class, fund_id, strategy_id, account_id }} params
     * @returns {{ errorCode, errorMsg, data: T0TaskPackage }}
     */
    qtasks(params) {

        return new Promise((resolve, reject) => {
            
            http(`/algorithm/task`, { params }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    cancelOrder(data) {

        return new Promise((resolve, reject) => {

            http(`/algorithm/cancel`, { method: 'post', data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 查询系统内部算法
     */
    queryInternalAlgoes() {
        
        return new Promise((resolve, reject) => {
            
            http(`/algorithm/desc`).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 新增或更新系统内部算法
     * @param {GaoyuAlgo} data 
     */
    createInternalAlgo(data) {
        
        return new Promise((resolve, reject) => {
            
            http(`/algorithm/desc`, { method : 'post', data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 删除系统内部算法
     */
    deleteInternalAlgo(id) {
        
        return new Promise((resolve, reject) => {
            
            http(`/algorithm/desc?id=${id}`, { method : 'delete' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 查询系统自定义算法参数
     */
    queryAlgoParams() {
        
        return new Promise((resolve, reject) => {
            
            http(`/algorithm/param`).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 新增或更新系统自定义算法参数
     * @param {GaoyuAlgoParamDefinition} data 
     */
    createAlgoParam(data) {
        
        return new Promise((resolve, reject) => {
            
            http(`/algorithm/param`, { method: 'post', data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * 删除系统自定义算法参数
     */
    deleteAlgoParam(id) {
        
        return new Promise((resolve, reject) => {
            
            http(`/algorithm/param?id=${id}`, { method: 'delete' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
}

module.exports = {

    repoAlgo: new AlgorithmRepository(),
    AlgorithmClasses,
};
