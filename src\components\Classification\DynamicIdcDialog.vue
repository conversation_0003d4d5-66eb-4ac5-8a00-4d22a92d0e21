<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { deepClone, getUser } from '@/script';
import { Repos, type TradeDynamicIndicator } from '../../../../xtrade-sdk/dist';
import { DynamicConditionTypes, ExpressionTypes } from '@/enum/riskc';

const { dynamicIndicator } = defineProps<{
  dynamicIndicator?: TradeDynamicIndicator;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules = {
  conditionType: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
  expressionType: [{ required: true, message: '请选择表达式类型', trigger: 'change' }],
  value: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
};

const usr = getUser()!;
const formRef = useTemplateRef('formRef');
const form = ref<TradeDynamicIndicator>(createEmpty());

function createEmpty() {
  const item: TradeDynamicIndicator = {
    id: null as any,
    conditionType: null as any,
    value: 0,
    creatorId: usr.userId,
    creatorUserName: usr.username,
    orgId: usr.orgId,
    orgName: usr.orgName,
    expressionType: null as any,
    updateTime: Date.now(),
    conditionColumn: null as any,
  };

  return item;
}

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (dynamicIndicator) {
      form.value = deepClone(dynamicIndicator);
    } else {
      resetForm();
    }
  }
});

const selectedCondition = computed(() => {
  const matched = DynamicConditionTypes.find(item => item.value === form.value.conditionType);
  return matched;
});

function handleCdtTypeChange() {
  form.value.conditionColumn = selectedCondition.value?.variable || '';
}

// 重置表单
const resetForm = () => {
  form.value = createEmpty();
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      handleSave();
    }
  });
};

const repoInstance = new Repos.DynamicIndicatorRepo();

async function handleSave() {
  const resp = dynamicIndicator
    ? await repoInstance.updateDynamicIndicator(form.value)
    : await repoInstance.createDynamicIndicator(form.value);

  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success(`已保存`);
    emit('success');
    handleClose();
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="dynamicIndicator ? '编辑动态指标' : '新建动态指标'"
    width="500px"
    @close="handleClose"
    draggable
  >
    <el-form
      ref="formRef"
      class="typical-form"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="80px"
    >
      <el-form-item label="目标指标" prop="conditionType">
        <el-select
          v-model="form.conditionType"
          placeholder="请选择指标"
          class="w-full"
          @change="handleCdtTypeChange"
        >
          <el-option
            v-for="item in DynamicConditionTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="运算符" prop="expressionType">
        <el-select v-model="form.expressionType" placeholder="请选择运算符" class="w-full">
          <el-option
            v-for="item in ExpressionTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="阈值" prop="value">
        <el-input-number
          v-model="form.value"
          placeholder="请输入阈值"
          :step="1"
          :precision="0"
          :min="selectedCondition?.min || 0"
          :max="selectedCondition?.max || 999999999999"
          :controls="false"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped></style>
