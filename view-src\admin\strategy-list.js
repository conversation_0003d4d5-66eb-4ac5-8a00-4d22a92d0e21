const drag = require('../../directives/drag');
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const strategyOnline = require('./strategy-online/strategy-online');
const { SmartTable } = require('../../libs/table/smart-table');
const { BaseAdminView } = require('./baseAdminView');
const { V3StandardStrategy, ChildAccountDetail } = require('../../model/strategy');
const { repoFund } = require('../../repository/fund');
const { repoUser } = require('../../repository/user');
const { repoAccount } = require('../../repository/account');
const { repoIndicator } = require('../../repository/indicator');
const { repoStrategy } = require('../../repository/strategy');
const { UiBuisinessMixin } = require('../../mixin/ui-business');
const { NumberMixin } = require('../../mixin/number');

const ShareTypes = {

    creator: { code: 0, mean: '创建' },
    trader: { code: 1, mean: '交易' },
    riskProtector: { code: 2, mean: '风控' },
    inspector: { code: 3, mean: '查看' },
}

class View extends BaseAdminView {

    get formatters() {
        return {
            formatYesNo: (row, column) => {
                let prop = column.property;
                return !!row[prop] ? '是' : '否';
            },
            formatValuation: (row, column) => {
                let val = row[column.property];
                let matched = this.valuationConfigs.find(valuation => valuation.val == val);
                return matched ? matched.label : '未知';
            },
            formatFundType: (row, column) => {
                let type = row[column.property];
                type *= 1;
                if (![1, 2, 3].includes(type)) {
                    return '未知';
                }
                return this.fundTypes.find(cdt => type === cdt.val).label;
            },
            thousands(row, column) {
                return NumberMixin.methods.thousands(row[column.property], false, 2);
            },
            formatMaxMoney: (row, column) => {
                let sum = 0;
                row.strategyAccounts.forEach(account => {
                    sum += account.maxLimitMoney;
                });
                return sum;
            },
            formatReduce: (row, column) => {
                let list = row.strategyAccounts || [];
                let key = column.property;
                if (list.length == 0) {
                    return 0;
                }
                return list.reduce(
                    (c, n) => {
                        c[key] += n[key] || 0;
                        return c;
                    },
                    { [key]: 0 },
                )[key];
            },
        };
    }

    constructor(view_name) {

        super(view_name, '策略管理');
        this.$container = null;
        this.vueApp = null;

        this.strategyList = [];
        this.strategyListHash = {};
        this.userList = [];
        this.riskUserList = [];
        this.traderList = [];
        this.userListHash = {};

        this.strategyOnline = strategyOnline;
        this.financeAccountList = [];
        this.financeAccountListHash = {};

        this.fundList = [];
        this.fundListHash = {};
        this.reportTemplateList = [];
        this.reportTemplateHash = {};
        this.$el = null;
        this.currentStrategy = this.typeds(null);
        this.searching = {
            prop: ['id', 'strategyName'],
            value: '',
        };

        this.form = this.createNew();
        this.dialogAccount = {
            visible: false, 
            data: [],
            selected: [],
        };
    }

    /**
     * @param {V3StandardStrategy} data 
     */
    typeds(data) {
        return data;
    }

    createNew() {
        return { id: null, strategyName: null, description: null, fundId: null };
    }

    /**
     * @param {V3StandardStrategy} strategy 
     */
    viewReportTemplate(strategy) {
        
        this.renderProcess.emit('open-top-tab', false, '@2021/overview/components/fund-analysis', `${strategy.strategyName}-分析`, {

            strategyId: strategy.id,
            strategyName: strategy.strategyName,
            isStrategy: true,
        });
    }

    getPrimaryLimitMoney(strategy) {

        let sum = 0;
        strategy.strategyAccounts.forEach(account => {
            sum += account.maxLimitMoney;
        });
        return sum;
    }

    getOnlineStatus(strategy) {
        return strategy.connectCount && strategy.connectCount > 0;
    }

    makeOnlineStatus(strategy) {
        return this.getOnlineStatus(strategy) ? '在线' : '不在线';
    }

    makeOnlineStatistics(strategy) {
        return strategy.connectCount || 0;
    }

    formatBoundAccounts(strategy) {

        let accounts = strategy.strategyAccounts;
        return accounts && accounts.length > 0 ? accounts.map(item => item.accountName).join('、') : '未绑定';
    }
    
    formatBoundTraders(strategy) {

        let traders = strategy.traders;
        return traders && traders.length > 0 ? traders.map(item => item.fullName || item.userName).join('、') : '未绑定';
    }
    
    formatBoundRiskUsers(strategy) {

        let riskUsers = strategy.riskUsers;
        return riskUsers && riskUsers.length > 0 ? riskUsers.map(item => item.fullName || item.userName).join('、') : '未绑定';
    }
    
    formatCloseProfit(strategy) {
        return this.summarizeField(strategy.strategyAccounts, 'closeProfit').thousands();
    }
    
    formatPositionProfit(strategy) {
        return this.summarizeField(strategy.strategyAccounts, 'positionProfit').thousands();
    }

    summarizeField(list, key) {

        if (list.length == 0) {
            return 0;
        }

        return list.reduce(
            (c, n) => {
                c[key] += n[key] || 0;
                return c;
            },
            { [key]: 0 }
        )[key];
    }
    
    createApp() {

        let controller = this;
        this.tableProps = {

            highlightCurrentRow: true,
            border: false,
            stripe: true,
            height: '260px',
        };

        this.vueApp = new Vue({
            el: this.$el,
            components: {
                DataTables: DataTables.DataTables,
            },
            filters: {},
            directives: {
                drag,
            },
            data: {
                searching: this.searching,
                filters: [this.searching],
                strategyOnline: this.strategyOnline.appData,
                tableProps: this.tableProps,
                paginationDef: this.systemSetting.tablePagination,
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'el-icon-search',
                    },
                },
                strategyList: this.strategyList,
                reportTemplateList: this.reportTemplateList,
                dialog: {
                    online: {
                        visible: false,
                        context: null,
                    },
                    strategy: {
                        visible: false,
                        title: null,
                        //获取form的结构
                        form: this.form,
                        // 是否禁止更换绑定的产品
                        disabled: false,
                        rules: {
                            strategyName: [
                                { type: 'string', required: true, message: '请输入策略名称' },
                                controller.systemSetting.specialCharacterFilterRule,
                                controller.systemSetting.limitInputLengthRule,
                            ],
                            fundId: [
                                { required: true, message: '请选择要绑定的产品' },
                                {
                                    validator(rule, value, callback) {

                                        let formd = controller.form;
                                        let otherStrategy = controller.strategyList.filter(x => x.strategyName == formd.strategyName && x.id != formd.id);
                                        if (otherStrategy.length <= 0) {
                                            callback();
                                        } 
                                        else {

                                            let flag = otherStrategy.some(x => x.fundId == value);
                                            if (flag) {
                                                callback(new Error('相同策略名无法绑定同一个产品，请修改'));
                                            }

                                            callback();
                                        }
                                    },
                                },
                            ],
                        },
                        accountList: [],
                    },
                    account: this.dialogAccount,
                    trader: {
                        visible: false,
                        data: controller.traderList,
                        model: [],
                    },
                    riskUser: {
                        visible: false,
                        data: controller.riskUserList,
                        model: [],
                    },
                    report: {
                        visible: false,
                        model: [],
                        data: controller.reportTemplateList,
                        defaultOptions: [],
                        defaultTemplateId: null,
                    },
                    permission: {
                        visible: false,
                        strategyPermissionList: [],
                        strategyPermissionListHash: {},
                    },
                },
                fundList: [],
                checkAll: false,
                checkTradersAll: false,
                checkRiskUsersAll: false,
                isIndeterminate: true,
                isIndeterminateTrader: true,
                isIndeterminateRisk: true,
                checkedPermissionStrategy: [],
            },
            computed: {
                isSuperAdmin: function() {
                    return controller.userInfo.isSuperAdmin;
                },
                isOrgAdmin: function() {
                    return controller.userInfo.isOrgAdmin;
                },
            },
            mixins: [NumberMixin, UiBuisinessMixin],
            watch: {
                'dialog.report.model'(newValue) {
                    if (Array.isArray(newValue)) {
                        this.dialog.report.defaultOptions = newValue.map(tid => {
                            let template = controller.reportTemplateHash[tid];
                            return {
                                label: controller.helper.readKey(template, 'report_info.title_format'),
                                id: tid,
                            };
                        });
                        if (newValue.every(x => x !== this.dialog.report.defaultTemplateId)) {
                            this.dialog.report.defaultTemplateId = null;
                        }
                    } else {
                        this.dialog.report.defaultTemplateId = null;
                    }
                },
            },
            methods: this.helper.extend(
                {
                    viewReportTemplate: (strategy) => { this.viewReportTemplate(strategy); },
                    handleBindReportTemplate: async strategy => {
                        this.currentStrategy = strategy;
                        await this.getReportTemplateList();
                        let bindTemplates = strategy.reportTemplates || [];
                        let defaultTpl = bindTemplates.find(x => x.default);
                        if (defaultTpl) {
                            this.vueApp.dialog.report.defaultTemplateId = defaultTpl.templateId;
                        }
                        bindTemplates = bindTemplates.map(x => x.templateId);
                        //默认模板的候选项
                        this.vueApp.dialog.report.defaultOptions = bindTemplates.map(tid => {
                            let template = controller.reportTemplateHash[tid];
                            return {
                                label: controller.helper.readKey(template, 'report_info.title_format'),
                                id: tid,
                            };
                        });
                        //绑定模板回填
                        this.vueApp.dialog.report.model = bindTemplates;
                        this.vueApp.dialog.report.visible = true;
                    },
                    async handleSaveReport() {
                        let defaultId = this.dialog.report.defaultTemplateId;
                        if (!defaultId) {
                            controller.interaction.showError('请选择一个默认报告模板!');
                            return;
                        }
                        let strategyId = controller.currentStrategy.id;
                        let json = {};
                        this.dialog.report.model.forEach(key => {
                            json[key] = 0;
                        });
                        json[defaultId] = 1;
                        let flag = await controller.bindTemplate(strategyId, json);
                        if (flag) {
                            controller.currentStrategy.reportTemplates = this.dialog.report.model.map(x => {
                                let templateDetail = controller.reportTemplateHash[x];
                                return {
                                    default: defaultId === x,
                                    templateId: x,
                                    templateName: controller.helper.readKey(templateDetail, 'report_info.title_format'),
                                };
                            });
                            this.handleCloseReport();
                            setTimeout(() => {
                                controller.interaction.showSuccess('保存模板信息成功!');
                            }, 300);
                        }
                    },
                    handleCloseReport() {
                        
                        this.dialog.report.visible = false;
                        this.dialog.report.defaultOptions = [];
                        this.dialog.report.defaultTemplateId = null;
                        this.dialog.report.model = [];
                        controller.currentStrategy = null;
                    },
                    riskConfig(selected_strategy) {

                        let strategyId = selected_strategy.id;
                        let strategyName = selected_strategy.strategyName;
                        controller.openWinRskSetting({
                            type: controller.systemEnum.identityType.strategy.code,
                            identity: strategyId,
                            name: strategyName,
                        });
                    },
                    behaviorConfig(selected_strategy) {
                        controller.openBehaviorSetting(selected_strategy);
                    },
                    getConvertProperty: (row, key) => {
                        return this.strategyOnline.getConnectProperty(row, key);
                    },
                    forceOffline: instance => {
                        this.interaction.showConfirm({
                            title: '警告',
                            message: '确定要将该实例强制下线？',
                            confirmed: () => {
                                this.strategyOnline.doForceOffline(instance, this.vueApp.dialog.online.context);
                            },
                            canceled: () => {
                                console.log('user cancel');
                            },
                        });
                    },
                    handleRowClick: row => {
                        this.currentStrategy = row;
                        this.setWindowTitle(`策略 - ${row.strategyName}`);
                        this.handleStrategySelection(this.currentStrategy);
                    },
                    async openStrategyCreationDialog() {
                        let result = await controller.getFundList();
                        if (result.flag) {
                            this.fundList = result.data;
                        }
                        controller.currentStrategy = null;
                        this.dialog.strategy.title = '创建策略';
                        controller.updateForm(controller.createNew());
                        this.$nextTick(() => {
                            this.$refs.strategyForm.clearValidate();
                        });
                        this.dialog.strategy.visible = true;
                    },
                    async handleEdit(row) {
                        controller.currentStrategy = row;
                        if (this.fundList.length <= 0) {
                            let results = await controller.getFundList();
                            this.fundList = results.data;
                        }
                        controller.updateForm(controller.helper.deepClone(row));
                        //当绑定了产品的时候不能再允许修改产品
                        if (Array.isArray(row.strategyAccounts) && row.strategyAccounts.length > 0) {
                            this.dialog.strategy.disabled = true;
                        }
                        this.dialog.strategy.title = `修改策略 ${row.strategyName}`;
                        if (this.$refs.strategyForm) {
                            this.$refs.strategyForm.clearValidate();
                        }
                        this.dialog.strategy.visible = true;
                    },
                    handleOpenAccountBindingDialog: (row) => {
                        this.currentStrategy = row;
                        this.openAccountBindingDialog(row);
                    },
                    handleSaveAccount: () => {
                        this.bindChildAccounts();
                    },
                    async handleSaveTrader() {

                        let result = await controller.updateStrategyUsers(
                            controller.currentStrategy.id,
                            this.dialog.trader.model,
                            controller.systemUserEnum.userRole.tradingMan.code,
                            ShareTypes.trader.code);

                        if (result.flag) {

                            this.dialog.trader.visible = false;
                            controller.currentStrategy.traders = result.data.map(user => {
                                let targetUser = controller.userListHash[user.userId] || {};
                                return {
                                    userId: user.userId,
                                    userName: user.userName,
                                    fullName: targetUser.fullName,
                                };
                            });
                            this.updateTable(controller.currentStrategy);
                        }
                    },
                    async handleSaveRiskUser() {

                        let result = await controller.updateStrategyUsers(
                            controller.currentStrategy.id,
                            this.dialog.riskUser.model,
                            controller.systemUserEnum.userRole.riskProtector.code,
                            ShareTypes.riskProtector.code,
                        );

                        if (result.flag) {

                            this.dialog.riskUser.visible = false;
                            controller.currentStrategy.riskUsers = result.data.map(user => {
                                let targetUser = controller.userListHash[user.userId] || {};
                                return {
                                    userId: user.userId,
                                    userName: user.userName,
                                    fullName: targetUser.fullName,
                                };
                            });
                            this.updateTable(controller.currentStrategy);
                        }
                    },
                    getPrimaryLimitMoney(strategy) {
                        return controller.getPrimaryLimitMoney(strategy);
                    },
                    async handleOpenTraderBindingDialog(row) {
                        controller.currentStrategy = row;
                        var flag = await controller.getUserList();
                        if (flag) {
                            this.dialog.trader.model = row.traders.map(user => user.userId);
                            this.dialog.trader.visible = true;
                        }
                    },
                    async handleOpenRiskBindingDialog(row) {
                        controller.currentStrategy = row;
                        var flag = await controller.getUserList();
                        if (flag) {
                            this.dialog.riskUser.model = row.riskUsers.map(user => user.userId);
                            this.dialog.riskUser.visible = true;
                        }
                    },
                    summarizeField(list, key) {
                        return controller.summarizeField(list, key);
                    },
                    getColor(num) {
                        return num > 0 ? 's-color-red' : num < 0 ? 's-color-green' : 's-color-grey';
                    },
                    getConnectClass(counter) {
                        return counter > 0 ? 's-color-green' : 's-color-red';
                    },
                    updateTable(revised) {

                        let records = controller.strategyList;
                        let map = controller.strategyListHash;
                        let id = revised.id;
                        let matched = map[id];

                        if (matched == undefined) {

                            records.unshift(revised);
                            map[id] = revised;

                            controller.setDefaultSelect(revised);
                            controller.handleStrategySelection(revised);
                        }
                        else {

                            let legacy = records.find(x => x.id == id);
                            controller.helper.extend(legacy, revised);
                        }
                    },
                    handlePermissionClone(row){
                        this.dialog.permission.visible = true;
                        this.tradersList = [];
                        this.riskUsersList = [];
                        this.reqTradersList = row.traders.map(trader => trader.userId);
                        this.reqRiskUsersList = row.riskUsers.map(riskUser => riskUser.userId);
                        this.strategyIdsList = [];
                        this.strategyNamesList = [];
                        controller.currentStrategy = row;
                        if(this.strategyList){
                            this.strategyList.forEach(strategy => {
                                // 排除当前策略
                                if(row.id != strategy.id) {
                                    this.dialog.permission.strategyPermissionList.push(strategy);
                                    this.dialog.permission.strategyPermissionListHash[strategy.id] = strategy;
                                    this.strategyIdsList.push(strategy.id);
                                    this.strategyNamesList.push(strategy.id + "_" + strategy.strategyName);
                                    if (strategy.traders && strategy.traders.length > 0) {
                                         strategy.traders.forEach(trader => {this.tradersList.push(trader.userId)});
                                    }
                                    if(strategy.riskUsers && strategy.riskUsers.length > 0){
                                        strategy.riskUsers.forEach(riskUser => {this.riskUsersList.push(riskUser.userId)});
                                    }
                                }
                            });
                        }
                    },
                    handleCheckAllChange(val){
                        this.checkedPermissionStrategy = val ? this.strategyIdsList.concat(this.strategyNamesList) : [];
                        this.isIndeterminate = false;
                        this.checkTradersAll = val;
                        this.isIndeterminateTrader = false;
                        this.checkRiskUsersAll = val;
                        this.isIndeterminateRisk = false;
                        this.reqTradersList = val ? this.tradersList : [];
                        this.reqRiskUsersList = val ? this.riskUsersList : [];
                    },
                    checkTradersAllChange(val){
                        if(this.checkAll){ 
                            this.checkedPermissionStrategy = this.strategyNamesList;
                            this.reqRiskUsersList = this.riskUsersList;
                            this.checkAll = false;
                        } else {
                            this.checkedPermissionStrategy = val ? this.strategyIdsList : [];
                            this.isIndeterminateTrader = false;
                            this.reqTradersList = val ? this.tradersList : [];
                            this.checkRiskUsersAll = false;
                        }
                    },
                    checkRiskUsersAllChange(val){
                        if(this.checkAll) { 
                            this.checkedPermissionStrategy = this.strategyIdsList;
                            this.reqTradersList = this.tradersList;
                            this.checkAll = false;
                        } else {
                            this.checkedPermissionStrategy = val ? this.strategyNamesList : [];
                            this.isIndeterminateRisk = false;
                            this.reqRiskUsersList = val ? this.riskUsersList : [];
                            this.checkTradersAll = false;
                        }
                    },
                    handleCheckedPermissionStrategyChange(records){
                        let traders = [];
                        let riskUsers = [];
                        // 区分是否
                        let tradeFlag = false;
                        let riskFlag = false;
                        records.forEach(record => {
                            let strategyIdOrName = record.split("_");
                            let strategy = this.dialog.permission.strategyPermissionListHash[strategyIdOrName[0]]
                            if(strategyIdOrName.length == 1) {
                                tradeFlag = true;
                                strategy.traders.forEach(trader => {traders.push(trader.userId)});
                            } else {
                                riskFlag = true;
                                strategy.riskUsers.forEach(riskUser => {riskUsers.push(riskUser.userId)});
                            }
                        });
                        this.reqTradersList = tradeFlag ? traders : controller.currentStrategy.traders.map(trader => trader.userId);
                        this.reqRiskUsersList = riskFlag ? riskUsers : controller.currentStrategy.riskUsers.map(riskUser => riskUser.userId);
                    },
                    async saveStrategyPermissionClone(){
                        let loading = controller.interaction.showLoading({
                            text: '正在处理中，请稍后...',
                        });
                        try {
                            let reqTradersList = Array.from(new Set(this.reqTradersList));
                            let reqRiskUsersList = Array.from(new Set(this.reqRiskUsersList));
                            
                            let result = await controller.saveStrategyPermissionCloneInfo(
                                controller.currentStrategy.id, reqTradersList, reqRiskUsersList);
                            if(result.errorCode == 0){ 
                                let traders = [];
                                let riskUsers = [];
                                result.data.forEach(result => {
                                    let targetUser = controller.userListHash[result.userId] || {};
                                    if(result.shareType == ShareTypes.trader.code){
                                        traders.push({userId: result.userId,userName: result.userName,fullName: targetUser.fullName});
                                    } else if(result.shareType == ShareTypes.riskProtector.code){
                                        riskUsers.push({userId: result.userId,userName: result.userName,fullName: targetUser.fullName});
                                    }
                                });
                                this.closeStrategyPermissionCloneDialog();
                                controller.currentStrategy.traders = traders;
                                controller.currentStrategy.riskUsers = riskUsers;
                                this.updateTable(controller.currentStrategy);
                                controller.interaction.showSuccess('权限克隆成功!');
                            } else {
                                controller.interaction.showError('权限克隆失败!' + result.errorMsg);
                            }
                            this.dialog.permission.visible = false;
                        } catch (error) {
                            controller.interaction.showError('权限克隆失败!' + error);
                        } finally {
                            loading.close();
                        }
                    },

                    closeStrategyPermissionCloneDialog(){
                        this.dialog.permission.visible = false;
                        this.checkedPermissionStrategy = [];
                        this.checkAll = false;
                        this.checkRiskUsersAll = false;
                        this.checkTradersAll = false;
                        this.dialog.permission.strategyPermissionList = [];
                        this.dialog.permission.strategyPermissionListHash = {};
                    },

                    getFundSummary(list) {
                        let sum = 0;
                        list.forEach(account => {
                            sum += account.maxLimitMoney;
                        });
                        return sum;
                    },
                    doClose() {
                        this.dialog.strategy.disabled = false;
                        this.dialog.strategy.visible = false;
                        controller.currentStrategy = null;
                        controller.updateForm(controller.createNew());
                    },
                    saveStrategy() {

                        this.$refs.strategyForm.validate(async isOk => {

                            if (!isOk) {
                                return;
                            }

                            let changes = controller.helper.deepClone(controller.form);
                            let result = await controller.saveStrategy(changes);
                            if (result.flag) {

                                controller.helper.extend(changes, result.data);
                                this.updateTable(changes);
                            }

                            this.dialog.strategy.visible = false;
                            this.dialog.strategy.disabled = false;
                            controller.currentStrategy = null;
                        });
                    },
                    makeOnlineStatus: strategy => {
                        return controller.makeOnlineStatus(strategy);
                    },
                    makeOnlineStatistics(strategy) {
                        return controller.makeOnlineStatistics(strategy);
                    },
                    viewOnlineInstance(strategy) {
                        let strategyId = strategy.id;
                        controller.strategyOnline.adaptOnlineList(strategyId, () => {
                            this.dialog.online.visible = true;
                            this.dialog.online.context = strategy;
                        });
                    },
                    handleDelete: row => {
                        controller.interaction.showConfirm({
                            title: '警告',
                            message: '确定要删除当前策略吗？',
                            confirmed: () => {
                                let strategyId = row.id;
                                this.removeStrategy(strategyId).then(flag => {
                                    if (flag) {
                                        this.strategyList.remove(cdt => cdt.id == strategyId);
                                        delete this.strategyListHash[strategyId];
                                        this.interaction.showSuccess('操作成功!');
                                    }
                                });
                            },
                        });
                    },
                },
                this.formatters,
            ),
        });
    }

    /**
     * @param {V3StandardStrategy} strategy 
     */
    async openAccountBindingDialog(strategy) {
        
        let resp = await repoAccount.getUnboundChildAccounts(strategy.id);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode != 0 || !Array.isArray(data)) {

            this.interaction.showError(`可绑策略账号查询错误：${errorCode}/${errorMsg}`);
            return;
        }

        let records = data.map(x => new ChildAccountDetail(x));
        let unbounds = records.map(x => ({ detailId: x.id, accountName: x.accountName }));

        /**
         * 将当前已经绑定的，合并到尚未被绑定过的账号
         */
        let exists = strategy.strategyAccounts.map(x => ({ detailId: x.detailId, accountName: x.accountName }));
        unbounds.push(...exists.filter(x => !unbounds.some(y => y.detailId == x.detailId)));

        const ref = this.dialogAccount;
        ref.selected = strategy.strategyAccounts.map(x => x.detailId);
        ref.data = unbounds;
        ref.visible = true;
    }

    async bindChildAccounts() {
        
        let ids = this.dialogAccount.selected;
        let resp = await repoAccount.bindChildAccounts(this.currentStrategy.id, ids);
        let { errorCode, errorMsg, data } = resp;
        if (errorCode == 0) {

            this.interaction.showSuccess('已绑定成功');
            this.dialogAccount.visible = false;
            this.refresh();
        }
        else {
            this.interaction.showError(`绑定失败：${errorCode}/${errorMsg}`);
        }
    }

    openBehaviorSetting(strategy) {

        if (!this.tbmgr) {

            const { TradeBehaviorManager } = require('./trade-behavior/trade-behavior-manager');
            this.tbmgr = new TradeBehaviorManager(this);
        }

        this.tbmgr.openSetting({

            identityType: this.systemEnum.identityType.strategy.code,
            identity: strategy.id,
            name: strategy.strategyName,
        });
    }

    updateForm(revised) {
        this.vueApp.dialog.strategy.form = this.form = this.helper.deepClone(revised);
    }

    setHeight(height) {
        this.tableProps.height = height - 50 + 'px';
    }

    /**
     * 更新策略用户
     */
    async updateStrategyUsers(strategy_id, user_ids, role_id, share_type) {
        let output = {
            flag: false,
            data: null,
        };
        try {
            const resp = await repoStrategy.updateStrategyUsers(strategy_id, user_ids, role_id, share_type);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('分配人员成功');
                output.flag = true;
                if (typeof resp.data !== 'undefined') {
                    output.data = resp.data;
                }
            } else {
                console.log(resp);
                this.interaction.showHttpError('分配人员失败');
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('分配人员失败');
        }
        return Promise.resolve(output);
    }

    async getFundList() {
        let output = {
            flag: false,
            data: null,
        };

        let loading = this.interaction.showLoading({
            text: '请求产品列表...',
        });
        try {
            const resp = await repoFund.getAll();
            if (resp.errorCode === 0) {
                let list = resp.data;
                list.forEach(fund => {
                    let fundId = fund.id;
                    this.fundListHash[fundId] = fund;
                });
                output.data = list;
                this.fundList = list;
                output.flag = true;
            } else {
                this.interaction.showHttpError('获取产品列表失败，详细信息："' + resp.errorMsg + '"');
            }
        } catch (exp) {
            this.interaction.showHttpError('获取产品列表失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    setDefaultSelect (first) {

        if (this.vueApp.$refs.table && this.vueApp.$refs.table.$children[1] && first) {
            
            let defaultRow = first;
            this.vueApp.$refs.table.$children[1].setCurrentRow(defaultRow);
            this.setWindowTitle(`策略 - ${defaultRow.strategyName}`);
        }
    }

    async removeStrategy (strategyId) {

        let output = false;

        let loading = this.interaction.showLoading({
            text: '操作进行中...',
        });

        try {
            const resp = await repoStrategy.delete(strategyId);
            if (resp.errorCode === 0 ) {
                output = true;
            } else {
                this.interaction.showHttpError('删除策略失败，详细信息'+ resp.errorMsg);
            }
        } catch (exp) {
            this.interaction.showHttpError('操作失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    async saveStrategy(revised) {

        var output = { flag: false, data: null };

        try {

            let resp = revised.id ? await repoStrategy.update(revised) : await repoStrategy.create(revised);
            if (resp.errorCode == 0) {

                this.interaction.showSuccess('保存策略成功');
                output.flag = true;
                if (resp.data !== undefined) {

                    let merged = this.helper.extend(revised);
                    this.helper.extend(merged, resp.data);
                    output.data = merged;
                }
            }
            else {
                this.interaction.showHttpError(`保存策略失败: ${resp.errorMsg}`);
            }
        } 
        catch (error) {
            this.interaction.showError(`保存策略失败`);
        }

        return Promise.resolve(output);
    }

    async saveStrategyPermissionCloneInfo(strategy_id, reqTradersList, reqRiskUsersList){
        return await repoStrategy.saveStrategyPermissionClone(strategy_id, reqTradersList, reqRiskUsersList);
    }

    /**
     * 获取账户列表
     */
    async getAccountList() {
        let output = {
            flag: false,
            data: null,
        };

        try {
            let resp = await repoAccount.getAll();
            if (resp.errorCode === 0 && typeof resp.data !== 'undefined') {
                this.financeAccountList = resp.data;
                resp.data.forEach(financeAccount => {
                    let accountId = financeAccount.id;
                    this.financeAccountListHash[accountId] = financeAccount;
                });
                output.data = resp.data;
                output.flag = true;
            }
        } catch (exp) {
            this.interaction.showHttpError('获取账户信息失败!');
            output.flag = false;
        }

        return Promise.resolve(output);
    }

    async handleStrategySelection(strategy) {

        if (!strategy) {
            return;
        }

        this.trigger(this.systemEvent.viewContextChange, {

            strategyId: strategy.id, 
            accounts: this.helper.deepClone(strategy.strategyAccounts),
        });
    }

    refresh() {

        this.searching.value = '';
        this.getStrategyList();
    }

    identifyRecord(strategy) {
        return strategy.id;
    }

    exportSome() {
        
        if (this.table4Export == undefined) {

            const $table = this.$container.querySelector('.table-all-strategies');
            this.table4Export = new SmartTable($table, this.identifyRecord, this);
        }
        
        this.table4Export.refill(this.helper.deepClone(this.strategyList));
        this.table4Export.exportAllRecords(`策略-${new Date().format('yyyy-MM-dd')}`);
    }

    async getReportTemplateList() {
        
        let loading = this.interaction.showLoading({ text: '请求报告模板列表...' });
        try {
            this.reportTemplateList.clear();
            const resp = await repoIndicator.getReportTemplateList({
                key_word: '',
                page_size: 999,
                page_no: 1,
            });
            if (resp.errorCode === 0) {
                loading.close();
                (resp.data || []).forEach(template => {
                    let info = {
                        id: template.id,
                        label: this.helper.readKey(template, 'report_info.title_format'),
                    };
                    this.reportTemplateHash[template.id] = template;
                    this.reportTemplateList.push(info);
                });
            } else {
                this.interaction.showHttpError(`请求报告模板列表出错,详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (error) {
            loading.close();
            this.interaction.showError('请求报告模板列表出错');
        }
    }

    async getStrategyList() {

        try {
            await this.getReportTemplateList();
            await this.getUserList();
        }
        catch (ex) {
            console.error(ex);
        }

        this.strategyList.clear();
        this.strategyListHash = {};

        /**
         * 获取策略基础列表
         */

        let loading = this.interaction.showLoading({ text: '请求策略列表...' });
        const resp = await repoStrategy.getAll();
        loading.close();
        let { errorCode, errorMsg } = resp;

        if (errorCode != 0) {

            this.interaction.showHttpError(`查询策略列表出错,详细信息：${errorCode}/${errorMsg}`);
            return;
        }

        let list = (resp.data || []).map(item => new V3StandardStrategy(item));
        /** 对策略按id进行倒排 */
        let strategies = list.sort((a, b) => this.helper.compare(a.id, b.id, false));

        /**
         * 1）建立策略索引表
         * 2）更新写入用户有关的字段
         */
        strategies.forEach(item => {

            this.strategyListHash[item.id] = item;
            item.setUserName(this.userListHash);
            item.setTraders(ShareTypes.trader.code);
            item.setRiskUsers(ShareTypes.riskProtector.code);
        });

        try {

            let resp2 = await repoStrategy.getDetail(strategies.map(x => x.id));
            let details = (resp2.data || {}).data || [];

            /**
             * 用详情来对基础列表进行扩充
             */

            strategies.forEach(item => {

                let matched = details.find(x => x.id == item.id);
                item.marketValue = matched.marketValue;
                item.balance = matched.balance;
                item.risePercent = matched.risePercent;
            });
        }
        catch (ex) {
            console.error(ex);
        }

        this.strategyList.refill(strategies);
        this.handleStrategySelection(strategies[0]);
    }

    /**
     * 获取交易员列表
     */
    async getUserList(callback) {
        let flag = false;
        let loading = this.interaction.showLoading({
            text: '请求分享人员列表...',
        });
        try {
            this.riskUserList.clear();
            this.traderList.clear();
            const resp = await repoUser.getAll();
            if (resp.errorCode === 0) {
                let full = resp.data || [];
                let traderList = full.filter(x => x.roleId === this.systemUserEnum.userRole.tradingMan.code);
                let riskUserList = full.filter(x => x.roleId === this.systemUserEnum.userRole.riskProtector.code);
                this.traderList.merge(traderList);
                this.riskUserList.merge(riskUserList);
                traderList.forEach(user => {
                    let userId = user.id;
                    this.userListHash[userId] = user;
                });
                //保存风控员
                riskUserList.forEach(user => {
                    let userId = user.id;
                    this.userListHash[userId] = user;
                });
                if (typeof callback === 'function') {
                    callback(full);
                }
                flag = true;
            } else {
                console.log(resp);
                this.interaction.showHttpError('查询人员列表出错');
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('查询人员列表出错');
        } finally {
            loading.close();
        }
        return Promise.resolve(flag);
    }

    build($container) {

        this.$container = $container;
        this.$el = this.$container.querySelector('.view-app-root');
        this.renderProcess.on('adjust-position-happened', () => { this.refresh(); });
        this.createApp();
        this.getStrategyList();
    }
}

module.exports = View;
