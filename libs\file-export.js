﻿
/**
 * export data to excel spread sheet
 */

const remote = require('@electron/remote');
const xlsx = require('node-xlsx');
const fs = require('fs');

function generateFile(buf, file_name, success_callback, error_callback) {
    
    var options = {
        title: '数据导出',
        defaultPath: file_name,
        filters: [{ name: 'Excels', extensions: ['xlsx'] }],
    };
    
    function confirm_saving(file_name) {

        if(!file_name) {
            return;
        }

        fs.writeFile(file_name, buf, err => {
            if (err) {
                console.error(err);
                typeof error_callback == 'function' ? error_callback(err) : null;
            }
            else {
                typeof success_callback == 'function' ? success_callback(file_name) : null;
            }
        });
    };

    const user_file_name = remote.dialog.showSaveDialogSync(remote.getCurrentWindow(), options);
    confirm_saving(user_file_name);
}

function extractFieldVal(column, row_data) {

    var data_formatter = column.formatter;

    if(typeof data_formatter == 'function') {
        try {
            let val = data_formatter(row_data[column.property], column.property, row_data);
            return val !== undefined && val !== null ? val : '';
        }
        catch(ex) {
            return '[ERR!]';
        }
    }
    else {
        let val = row_data[column.property];
        return val !== undefined && val !== null ? val : '';
    }
}

function exportMultiSheet (sheets, file_name, success_callback, error_callback) {

    if (!Array.isArray(sheets)) {
       return;
    }

    //移除不满足数据定义的
    sheets.remove(sheet => {
        let { columns, data } = sheet;
        return typeof columns === "undefined" ||  typeof  data === "undefined";
    });

    let fileMetaData = sheets.map((sheet, index) => {

        let { columns, data } = sheet;
        if (columns.length > 0) {
            columns.remove(function(col) {
                return typeof col.property != 'string' || col.property.trim().length == 0;
            });
        }

        let headers = columns.map(col => col.label || col.property);
        let matrix = [];
        matrix.push(headers);
        data.forEach(row => {
            matrix.push(columns.map(column => { return extractFieldVal(column, row); }));
        });

        return {
            name: 'Sheet' + (index + 1),
            data: matrix
        }
    });

    var buf = xlsx.build(fileMetaData);
    generateFile(buf, file_name, success_callback, error_callback);
}

function exportSingleSheet(sheet, file_name, success_callback, error_callback) {

    let { columns, data,} = sheet;
    if (columns.length > 0) {
        columns.remove(function(col) {
            return typeof col.property != 'string' || col.property.trim().length == 0;
        });
    }

    let headers = columns.map(col => { return col.label || col.property; });
    let matrix = [];

    // table header cells map to excel sheet header
    matrix.push(headers);
    data.forEach(row => {
        matrix.push(columns.map(column => { return extractFieldVal(column, row); }));
    });

    var buf = xlsx.build([{ name: 'Sheet1', data: matrix }]);
    generateFile(buf, file_name, success_callback, error_callback);
}

module.exports = { exportSingleSheet, exportMultiSheet };
