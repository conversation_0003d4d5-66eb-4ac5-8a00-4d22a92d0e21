import type { Column } from 'element-plus';
import type { JSX } from 'vue/jsx-runtime';

export interface CellRendererProps<T, K extends keyof T> {
  cellData: T[K];
  column: Column<T[K]>;
  rowData: T;
  rowIndex: number;
  columnIndex: number;
  columns: Column<T[K]>[];
}

export type CustomColumn<T> = {
  [K in keyof T]: Column<T[K]> & {
    key: K;
    dataKey?: K;
    /** 列动态宽度，默认为true，表格需要设置fixed */
    dynamicWidth?: boolean;
    /** 单元格渲染器 */
    cellRenderer?: (props: CellRendererProps<T, K>) => JSX.Element;
    /** 纯文本渲染，用于导出和计算动态宽度 */
    textRenderer?: (cellData: T[K], rowData: T) => string;
  };
}[keyof T];

/** 表格列定义 */
export type ColumnDefinition<T> = CustomColumn<T>[];

/** 行操作 */
export interface RowAction<T> {
  /** 按钮文字（文字与图标2选1） */
  label?: string;
  /** 按钮图标（文字与图标2选1） */
  icon?: string;
  /** 按钮类型（颜色） */
  type?: 'text' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'default' | any;
  /** 点击事件 */
  onClick: (rowData: T) => void;
  /** 是否显示 */
  show?: (rowData: T) => boolean;
  /** 是否禁用 */
  disabled?: (rowData: T) => boolean;
  /** 内嵌操作按钮 */
  nesteds?: RowAction<T>[];
}
