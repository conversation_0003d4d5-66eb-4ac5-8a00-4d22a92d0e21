import { BaseRepo } from '../modules/base-repo';
import { StandardTick } from '../types/common';

export class InstrumentRepo extends BaseRepo {
  /**
   * 请求最新合约行情
   * @param instruments 一个或多个合约代码
   */
  async RequestLastTick(instruments: string | string[]) {
    if (typeof instruments != 'string' && !Array.isArray(instruments)) {
      throw new Error('参数 instruments 类型错误');
    }

    if (typeof instruments == 'string') {
      instruments = [instruments];
    }

    const symbols = instruments.join(';');
    return this.assist.Get<StandardTick[]>(
      'http://history.gaoyusoft.com:9000/quote/v3/history/get_last_ticks',
      { symbols },
    );
  }
}
