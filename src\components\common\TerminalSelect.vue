<template>
  <div w-full>
    <el-select
      ref="selectRef"
      v-model="model"
      placeholder="请选择终端"
      filterable
      clearable
      :loading="loading"
      @change="handleChange"
    >
      <el-option
        v-for="terminal in filteredTerminals"
        :key="terminal.id"
        :label="terminal.terminalName"
        :value="terminal.id"
      />
      <template v-if="canCreate" #footer>
        <div p-2>
          <el-button type="primary" size="small" w-full @click="handleAddTerminal">
            <i class="iconfont icon-add" mr-1></i>
            添加终端
          </el-button>
        </div>
      </template>
    </el-select>
    <!-- 新建终端对话框 -->
    <TerminalDialog
      ref="createTerminalFormRef"
      v-if="showCreateDialog"
      v-model="showCreateDialog"
      @success="handleCreateTerminalSaved"
    />
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  ref,
  shallowRef,
  watch,
  defineAsyncComponent,
  useTemplateRef,
} from 'vue';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';
import { type MomTerminal } from '../../../../xtrade-sdk/dist';
import { hasPermission } from '@/script';
import { MenuPermitTerminalManagement, MenuPermitAccountManagement } from '@/enum';

const TerminalDialog = defineAsyncComponent(() => import('../TerminalView/TerminalDialog.vue'));

interface Props {
  /** 需要排除的终端ID列表 */
  excludeIds?: number[];
  /** 只显示启用状态的终端 */
  enabledOnly?: boolean;
  /** 来自哪个菜单路由，用于权限判断 */
  from?: string;
}

const { excludeIds = [], enabledOnly = true, from } = defineProps<Props>();

const emit = defineEmits<{
  change: [value: number | undefined, terminal?: MomTerminal];
  save: [terminal: MomTerminal];
  refresh: [];
}>();

const model = defineModel<number | undefined>();
const selectRef = useTemplateRef('selectRef');

// 响应式数据
const loading = ref(false);
const allTerminals = shallowRef<MomTerminal[]>([]);
const showCreateDialog = ref(false);

const canCreate = computed(() => {
  // 终端管理表单选择终端时，肯定有权限
  if (from == 'terminal') {
    return hasPermission(MenuPermitTerminalManagement.创建);
  } else if (from == 'account') {
    return hasPermission(MenuPermitAccountManagement.创建账号);
  } else {
    console.warn('TerminalSelect: 未设置创建权限判断依据');
    return false;
  }
});

// 计算属性：过滤后的终端列表
const filteredTerminals = computed(() => {
  let terminals = allTerminals.value;

  // 只显示启用状态的终端
  if (enabledOnly) {
    terminals = terminals.filter(t => t.status === 1);
  }

  // 排除指定的终端ID
  if (excludeIds.length > 0) {
    terminals = terminals.filter(t => !excludeIds.includes(t.id));
  }

  return terminals;
});

// 监听过滤条件变化，重新验证当前选择
watch(
  () => [enabledOnly, excludeIds],
  () => {
    // 如果当前选择的终端不在新的过滤结果中，清空选择
    if (model.value) {
      const isCurrentTerminalValid = filteredTerminals.value.some(t => t.id === model.value);
      if (!isCurrentTerminalValid) {
        model.value = undefined;
      }
    }
  },
  { deep: true },
);

onMounted(() => {
  loadTerminals();
});

// 处理选择变化
const handleChange = (value: number | undefined) => {
  emit(
    'change',
    value,
    allTerminals.value.find(t => t.id === value),
  );
  selectRef.value?.blur();
};

// 加载终端列表
const loadTerminals = async () => {
  loading.value = true;
  try {
    const terminals = await AdminService.getTerminals();
    allTerminals.value = terminals || [];
  } catch (error) {
    console.error('加载终端列表失败:', error);
    ElMessage.error('加载终端列表失败');
  }
  loading.value = false;
};

// 处理添加终端
const handleAddTerminal = () => {
  selectRef.value?.blur();
  showCreateDialog.value = true;
};

const handleCreateTerminalSaved = async (terminal: MomTerminal) => {
  await loadTerminals();
  model.value = terminal.id;
  emit('refresh');
  selectRef.value?.blur();
};
</script>

<style scoped></style>
