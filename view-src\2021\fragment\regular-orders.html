<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords"
				  v-model="states.keywords"
				  @change="handleSearch" clearable></el-input>
	</div>

	<div class="data-list">

		<table>
			<tr>
				<th label="账号名称" 
					min-width="202.0" 
					prop="accountName" 
					formatter="formatAccountName" filterable overflowt sortable searchable></th>

				<th label="发起人" 
					min-width="90" 
					prop="userName" filterable overflowt sortable searchable></th>

				<th label="代码" 
					fixed-width="100" 
					prop="instrument" filterable overflowt sortable searchable></th>

				<th label="名称" 
					fixed-width="80" 
					prop="instrumentName" filterable overflowt sortable searchable></th>

				<th label="订单状态" 
					fixed-width="100" 
					prop="orderStatus" 
					watch="orderStatus, errorMsg"
					formatter="formatOrderStatus" 
					export-formatter="formatOrderStatusText" 
					filter-data-provider="rebindOrderStatus" overflowt sortable></th>

				<th label="方向" 
					fixed-width="70" 
					prop="direction"
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>

				<th label="交易方式" 
					fixed-width="100" 
					prop="businessFlag"
					formatter="formatBusinessFlag" 
					export-formatter="formatBusinessFlag" 
					filter-data-provider="rebindBusinessFlag" sortable></th>

				<th label="委托量" 
					fixed-width="80" 
					prop="volumeOriginal" 
					align="right" filterable sortable summarizable thousands-int></th>

				<th label="委托价" 
					fixed-width="60" 
					prop="orderPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="成交量" 
					fixed-width="80" 
					prop="tradedVolume" 
					align="right" filterable sortable summarizable thousands-int></th>

				<th label="成交价" 
					fixed-width="60" 
					prop="tradedPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="创建时间" 
					fixed-width="100" 
					prop="createTime"
					formatter="formatTime" sortable></th>

				<th label="报单时间" 
					fixed-width="100" 
					prop="orderTime" 
					formatter="formatTime" sortable></th>

				<th label="成交时间" 
					fixed-width="100" 
					prop="tradeTime" 
					formatter="formatTime" filterable sortable></th>

				<th label="报单编号" 
					min-width="80" 
					prop="exchangeOrderId" overflowt sortable searchable></th>

				<th label="冻结资金" 
					fixed-width="80" 
					prop="frozenMargin" 
					align="right" thousands></th>

				<th label="产品" 
					min-width="150" 
					prop="fundName" filterable sortable overflowt searchable></th>

				<th label="资产类型" 
					fixed-width="100" 
					prop="assetType"
					formatter="formatAssetType" 
					filter-data-provider="rebindAssetType" sortable></th>

				<th label="外来单" 
					fixed-width="90" 
					prop="foreign"
					formatter="formatYesNo"
					export-formatter="formatYesNoText" 
					filter-data-provider="rebindYesNo" sortable></th>

				<th label="强平"
					fixed-width="80" 
					prop="forceClose"
					formatter="formatYesNo" 
					export-formatter="formatYesNoText" 
					filter-data-provider="rebindYesNo" sortable></th>

				<th label="备注" min-width="150" prop="remark" overflowt sortable></th>

			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>