const repoApply = require('../../../../repository/apply').repoApply;
const interaction = require('../../../../libs/interaction').interaction;
const axios = require('axios').default;

module.exports = class ApplyTrade {
    async getComponent() {
        let rsp = await axios.get(__filename.split('.')[0] + '.html');
        return {
            template: rsp.data,
            components: {},
            created() {
                this.initApplyInfo();
            },
            data() {
                return {
                    applyInfo: {
                        keywords: null,
                        accountId: '',
                        orderStep: 0,
                        orderMax: 0,
                        orderMin: 0,
                        purchaseCode: null,
                        purchaseName: '--',
                        purchaseAmount: '--',
                        purchasePrice: '--',
                        issueAmount: '--',
                        minPurchase: '--',
                        maxPurchase: '--',
                    },
                    filterIpoAccounts: [],

                    filterName: '',
                    accounts: [],
                    purchaseCodes: [],

                    accountNameFilters: [],
                    purchaseNameFilters: [],

                    accountIpoInfo: [],
                    dialogTableVisible: false,
                    selectApplyAccounts: [],
                    loading: true,
                };
            },
            methods: {
                async initApplyInfo() {
                    let resAccountIpoInfo = await repoApply.getAccountIpoInfo();
                    if (resAccountIpoInfo.errorCode != 0) return interaction.showHttpError('账号申购信息查询失败, 详细信息："' + resAccountIpoInfo.errorMsg + '"');
                    this.accountIpoInfo = resAccountIpoInfo.data || [];
                    let accountMap = new Map();
                    let instrumentMap = new Map();
                    this.accountNameFilters.length = 0;
                    this.purchaseNameFilters.length = 0;

                    this.accountIpoInfo.forEach((ipoInfo) => {
                        if (!accountMap.has(ipoInfo.accountId)) {
                            accountMap.set(ipoInfo.accountId, { label: ipoInfo.accountName, value: ipoInfo.accountId });
                            this.accountNameFilters.push({ label: ipoInfo.accountName, value: ipoInfo.accountName });
                        }

                        if (!instrumentMap.has(ipoInfo.purchaseCode)) {
                            instrumentMap.set(ipoInfo.purchaseCode, { label: ipoInfo.purchaseCode, value: ipoInfo.purchaseCode });
                            this.purchaseNameFilters.push({ label: ipoInfo.purchaseName, value: ipoInfo.purchaseName });
                        }
                    });
                    this.accounts = [...accountMap.values()];
                    this.purchaseCodes = [...instrumentMap.values()];
                    this.updateColumnFilters('accountName', this.accountNameFilters);
                    this.updateColumnFilters('purchaseName', this.purchaseNameFilters);
                    this.searchAccountIpo();
                    this.loading = false;
                },

                updateColumnFilters(columnName, options) {
                    const xTable = this.$refs.accountIpoTable;
                    const column = xTable.getColumnByField(columnName);
                    // 修改筛选列表，并默认设置为选中状态
                    xTable.setFilter(column, options);
                    // 修改条件之后，需要手动调用 updateData 处理表格数据
                    xTable.updateData();
                },

                filterAccountName({ value, row, column }) {
                    return XEUtils.toValueString(row.accountName).indexOf(value) > -1;
                },

                filterPurchaseName({ value, row, column }) {
                    return XEUtils.toValueString(row.purchaseName).indexOf(value) > -1;
                },

                searchAccountIpo() {
                    const filterName = XEUtils.toValueString(this.filterName).trim().toLowerCase();
                    if (filterName) {
                        const searchProps = ['financeAccount', 'accountName', 'purchaseCode', 'purchaseName'];
                        this.filterIpoAccounts = this.accountIpoInfo.filter((item) => searchProps.some((key) => XEUtils.toValueString(item[key]).toLowerCase().indexOf(filterName) > -1));
                    } else {
                        this.filterIpoAccounts = this.accountIpoInfo;
                    }
                },

                handleUserInput(item) {
                    let result = this.accountIpoInfo
                        .filter((ipoInfo) => ipoInfo.purchaseCode == this.applyInfo.purchaseCode && ipoInfo.accountId == this.applyInfo.accountId)
                        .map((newShare) => ({ ...newShare, purchaseAmount: newShare.purchaseAmount, orderStep: 0, orderMin: 0, orderMax: 0 }));

                    if (result.length > 0) {
                        this.applyInfo = result[0];
                        let stockInfo = this.applyInfo.purchaseCode.split('.');
                        this.applyInfo.orderStep = stockInfo[0] == 'SHSE' ? 1000 : 500;
                        this.applyInfo.orderMin = this.applyInfo.minPurchase;
                        this.applyInfo.orderMax = this.applyInfo.purchaseAmount;
                    } else {
                        this.applyInfo.purchaseName = '';
                        this.applyInfo.maxPurchase = '--';
                        this.applyInfo.minPurchase = '--';
                        this.applyInfo.issueAmount = '--';
                        this.applyInfo.purchaseAmount = '--';
                        this.applyInfo.purchasePrice = '--';
                        this.applyInfo.orderStep = 0;
                        this.applyInfo.orderMin = 0;
                        this.applyInfo.orderMax = 0;
                    }
                },

                selectAll({ checked, records }) {
                    this.selectApplyAccounts = records;
                },
                selectChange({ checked, records }) {
                    this.selectApplyAccounts = records;
                },

                singleAccountApply() {
                    // todo 参数检测
                    this.dialogTableVisible = true;
                    this.selectApplyAccounts = [this.applyInfo];
                },
                multiAccountApply() {
                    this.selectApplyAccounts = this.$refs.accountIpoTable.getCheckboxRecords();
                    // todo 参数检测
                    this.dialogTableVisible = true;
                },

                cancelApply() {
                    this.dialogTableVisible = false;
                },

                async applyRequest() {
                    this.dialogTableVisible = false;
                    let reqParam = this.selectApplyAccounts.map((applyAccount) => {
                        return { accountId: applyAccount.accountId, instrument: applyAccount.purchaseCode };
                    });
                    let resApply = await repoApply.apply(reqParam);
                    if (resApply.errorCode == 0) {
                        interaction.showSuccess('申购成功');
                    } else {
                        console.log(resApply);
                        interaction.showHttpError('申购失败, 详细信息："' + resApply.errorMsg + '"');
                    }
                },
            },
        };
    }
};
