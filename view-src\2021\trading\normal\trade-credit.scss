.nc2form {

    padding-right: 2px;

    .xtform .xtinput .xtlabel {
        width: 15%;
    }

    .xtform .xtinput > .el-input,
    .xtform .xtinput > .el-autocomplete,
    .xtform .xtinput > .el-input-number {
        width: 85%;
    }

    .effect-box {
        &.minified {
            .el-radio {
                margin-right: 5px;
            }
        }
    }

    .xtinput {

        &.subsidary {
            margin: -6px 0;
        }

        &.shorten {

            .el-input-number {
                width: 68%;
            }
        }

        &.button-row {
            margin-top: 14px;
        }

        .xtlabel {
            text-align: left;
        }

        button {
            width: 100%;
        }
    }
}

.nc2form-internal {

    overflow: hidden;

    .form-external {
        padding: 0 10px;
    }

    .direction-row {

        .el-radio-group {

            .el-radio-button {

                .el-radio-button__inner {
                    width: 100%;
                }
            }
        }
    }

    .limit-btn,
    .unit-txt {

        position: absolute;
        right: 0;
        z-index: 1;
        width: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
    }

    .prices-box {

        display: inline-block;
        line-height: 24px;
        width: 78%;
        position: relative;
        top: -7px;

        .price-item {

            display: inline-block;

            &:first-child {
                text-align: left;
            }

            &:last-child {

                text-align: right;
                float: right;
            }

            > a {
                padding-left: 4px;
            }
        }
    }

    .methods-box {

        display: inline-block;
        width: 78%;
        position: relative;
        top: -7px;

        .method-item {

            display: inline-block;
            text-align: center;
            line-height: 18px;
        }
    }

    .effect-box {
        
        display: inline-block;
        line-height: 24px;
        position: relative;
        top: -7px;
    }
}

.xtpop-body {

    .switch-item {

        line-height: 36px;
        text-align: center;

        span {
            padding-right: 15px;
        }
    }
}