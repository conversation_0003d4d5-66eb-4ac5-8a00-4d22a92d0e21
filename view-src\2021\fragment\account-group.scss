.account-group {

    .account-group-internal {
        overflow: hidden;
    }

    .xtheader {

        .el-select {

            margin-left: 8px;
            width: 150px;
        }
    
        .el-checkbox {
            margin-left: 12px;
        }
    }

    .table-external {
        padding-right: 5px;
    }

    &.single-account-mode {

        .ag-toolbar {
            
            .el-select,
            .el-input,
            .el-checkbox,
            .checked-info,
            .button-saveas {
                display: none;
            }
        }

        .smart-table-header {
            .column-check {
                display: none;
            }
        }
    }
}

.account-group-item {

    .group-name {
        padding-right: 12px;
    }

    .group-opers {

        .btn {
            
            padding: 5px 0 5px 5px;
            opacity: 0.6;
        }

        .btn:hover {
            opacity: 1;
        }
    }
}

.xt-table-row-input {
    width: 50px;
}