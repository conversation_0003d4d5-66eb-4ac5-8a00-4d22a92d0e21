@charset "UTF-8";
/*font*/
/*size*/
/*icon*/
/*color*/
/*input/radio/checkbox*/
/*popup*/
/*table*/
/*filter*/
/*menu*/
/*loading*/
/*validate*/
/*grid*/
/*toolbar*/
/*tooltip*/
/*pager*/
/*modal*/
/*checkbox*/
/*radio*/
/*button*/
/*input*/
/*textarea*/
/*form*/
/*select*/
/*switch*/
/*pulldown*/
.vxe-header--column {
    color: #8cb5ed;
}

/*滚动条整体部分*/
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
    background-color: #262f3c;
}

/*滚动条里面的小方块，能向上向下移动*/
::-webkit-scrollbar-thumb {
    background-color: #65A1FF;
    border-radius: 5px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb:hover {
    background-color: #65A1FF;
    box-shadow: inset 0 0 2px 0 #8CB5ED;
}

::-webkit-scrollbar-thumb:active {
    background-color: #65A1FF;
    box-shadow: inset 0 0 2px 0 #8CB5ED;
}

/*边角，即两个滚动条的交汇处*/
/**Variable**/
[class*=vxe-icon--] {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    direction: ltr;
    font-family: Verdana, Arial, Tahoma;
    font-weight: normal;
    user-select: none;
}
[class*=vxe-icon--].rotate45 {
    transform: rotate(45deg);
}
[class*=vxe-icon--].rotate90 {
    transform: rotate(90deg);
}
[class*=vxe-icon--].rotate180 {
    transform: rotate(180deg);
}

.vxe-icon--search, .vxe-icon--print, .vxe-icon--dot, .vxe-icon--calendar, .vxe-icon--eye,
.vxe-icon--eye-slash, .vxe-icon--upload,
.vxe-icon--download, .vxe-icon--error, .vxe-icon--remove, .vxe-icon--circle-plus, .vxe-icon--success, .vxe-icon--warning, .vxe-icon--info, .vxe-icon--question, .vxe-icon--refresh, .vxe-icon--minus, .vxe-icon--close, .vxe-icon--check, .vxe-icon--plus, .vxe-icon--more, .vxe-icon--edit-outline, .vxe-icon--funnel, .vxe-icon--d-arrow-left, .vxe-icon--d-arrow-right, .vxe-icon--arrow-top, .vxe-icon--arrow-right, .vxe-icon--arrow-left, .vxe-icon--arrow-bottom, .vxe-icon--caret-right, .vxe-icon--caret-left, .vxe-icon--caret-bottom, .vxe-icon--caret-top, .vxe-icon--menu, .vxe-icon--zoomout, .vxe-icon--zoomin, .vxe-icon--square {
    width: 1em;
    height: 1em;
    line-height: 1em;
}

.vxe-icon--search:after, .vxe-icon--search:before, .vxe-icon--print:after, .vxe-icon--print:before, .vxe-icon--dot:before, .vxe-icon--calendar:after, .vxe-icon--calendar:before, .vxe-icon--eye-slash:after, .vxe-icon--eye:before,
.vxe-icon--eye-slash:before, .vxe-icon--upload:after,
.vxe-icon--download:after, .vxe-icon--upload:before,
.vxe-icon--download:before, .vxe-icon--error:after, .vxe-icon--remove:after, .vxe-icon--circle-plus:after, .vxe-icon--success:after, .vxe-icon--warning:after, .vxe-icon--info:after, .vxe-icon--question:after, .vxe-icon--refresh:before, .vxe-icon--refresh:after, .vxe-icon--minus:before, .vxe-icon--close:before, .vxe-icon--check:before, .vxe-icon--plus:before, .vxe-icon--more:before, .vxe-icon--edit-outline:after, .vxe-icon--edit-outline:before, .vxe-icon--funnel:after, .vxe-icon--funnel:before, .vxe-icon--d-arrow-left:before, .vxe-icon--d-arrow-right:before, .vxe-icon--d-arrow-left:after, .vxe-icon--d-arrow-right:after, .vxe-icon--arrow-top:before, .vxe-icon--arrow-right:before, .vxe-icon--arrow-left:before, .vxe-icon--arrow-bottom:before, .vxe-icon--caret-right:before, .vxe-icon--caret-left:before, .vxe-icon--caret-bottom:before, .vxe-icon--caret-top:before, .vxe-icon--zoomout:after, .vxe-icon--zoomout:before, .vxe-icon--zoomin:before, .vxe-icon--zoomin:after, .vxe-icon--square:before {
    content: "";
    position: absolute;
}

.vxe-icon--square:before {
    left: 0.05em;
    top: 0.05em;
    width: 0.9em;
    height: 0.9em;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
}

.vxe-icon--zoomin {
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
    background-color: #fff;
}
.vxe-icon--zoomin:before, .vxe-icon--zoomin:after {
    background-color: inherit;
}
.vxe-icon--zoomin:before {
    left: -0.1em;
    top: 0.2em;
    width: 1.1em;
    height: 0.4em;
}
.vxe-icon--zoomin:after {
    top: -0.1em;
    left: 0.2em;
    width: 0.4em;
    height: 1.1em;
}

.vxe-icon--zoomout {
    position: relative;
}
.vxe-icon--zoomout:before {
    right: 0;
    top: 0;
    width: 0.7em;
    height: 0.7em;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--zoomout:after {
    left: 0.1em;
    bottom: 0.1em;
    width: 0.7em;
    height: 0.7em;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
    background-color: #fff;
}

.vxe-icon--menu:before {
    content: "";
    display: inline-block;
    width: 0.22em;
    height: 0.22em;
    box-shadow: 0 -0.36em 0, -0.36em -0.36em 0, 0.36em -0.36em 0, 0 0 0 1em inset, -0.36em 0 0, 0.36em 0 0, 0 0.36em 0, -0.36em 0.36em 0, 0.36em 0.36em 0;
    margin: 0.26em;
}

.vxe-icon--caret-right:before, .vxe-icon--caret-left:before, .vxe-icon--caret-bottom:before, .vxe-icon--caret-top:before {
    border-width: 0.4em;
    border-style: solid;
    border-color: transparent;
}

.vxe-icon--caret-top:before {
    left: 0.1em;
    bottom: 0.3em;
    border-bottom-color: inherit;
}

.vxe-icon--caret-bottom:before {
    left: 0.1em;
    top: 0.3em;
    border-top-color: inherit;
}

.vxe-icon--caret-left:before {
    right: 0.3em;
    bottom: 0.1em;
    border-right-color: inherit;
}

.vxe-icon--caret-right:before {
    left: 0.3em;
    bottom: 0.1em;
    border-left-color: inherit;
}

.vxe-icon--arrow-top:before, .vxe-icon--arrow-right:before, .vxe-icon--arrow-left:before, .vxe-icon--arrow-bottom:before {
    top: 0.4em;
    left: 0.14em;
    width: 0.7em;
    height: 0.7em;
    border-width: 0.15em;
    border-style: solid;
    border-top-color: inherit;
    border-right-color: inherit;
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-radius: 0.15em;
    transform: rotate(-45deg);
}

.vxe-icon--arrow-bottom:before {
    top: 0;
    left: 0.14em;
    transform: rotate(135deg);
}

.vxe-icon--arrow-left:before {
    top: 0.18em;
    left: 0.35em;
    transform: rotate(-135deg);
}

.vxe-icon--arrow-right:before {
    top: 0.18em;
    left: 0;
    transform: rotate(45deg);
}

.vxe-icon--d-arrow-left:before, .vxe-icon--d-arrow-right:before {
    left: 0.15em;
}
.vxe-icon--d-arrow-left:after, .vxe-icon--d-arrow-right:after {
    left: 0.58em;
}
.vxe-icon--d-arrow-left:before, .vxe-icon--d-arrow-right:before, .vxe-icon--d-arrow-left:after, .vxe-icon--d-arrow-right:after {
    top: 0.18em;
    width: 0.7em;
    height: 0.7em;
    border-width: 0.15em;
    border-style: solid;
    border-top-color: inherit;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: inherit;
    border-radius: 0.15em;
    transform: rotate(-45deg);
}

.vxe-icon--d-arrow-right:before, .vxe-icon--d-arrow-right:after {
    transform: rotate(135deg);
}
.vxe-icon--d-arrow-right:before {
    left: -0.25em;
}
.vxe-icon--d-arrow-right:after {
    left: 0.18em;
}

.vxe-icon--funnel:before {
    top: 0.05em;
    left: 0;
    border-width: 0.5em;
    border-style: solid;
    border-top-color: inherit;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
}
.vxe-icon--funnel:after {
    left: 0.41em;
    top: 0.4em;
    width: 0;
    height: 0.5em;
    border-width: 0 0.2em 0 0;
    border-style: solid;
    border-right-color: inherit;
}

.vxe-icon--edit-outline:before {
    height: 0.84em;
    width: 0.86em;
    top: 0.1em;
    left: 0.02em;
    border-radius: 0.2em;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--edit-outline:after {
    left: 0.6em;
    bottom: 0.2em;
    width: 0;
    height: 0.8em;
    border-radius: 0 0 80% 80%;
    border-width: 0 0 0 0.22em;
    border-style: solid;
    border-color: inherit;
    transform: rotate(45deg);
}

.vxe-icon--more:before {
    content: "...";
    top: 0;
    left: 0.1em;
    line-height: 0.5em;
    font-weight: 700;
}

.vxe-icon--plus:before {
    content: "+";
    left: -0.05em;
    bottom: 0;
    line-height: 0.9em;
    font-size: 1.4em;
}

.vxe-icon--check:before {
    left: 0.25em;
    bottom: 0.2em;
    width: 0.5em;
    height: 0.9em;
    border-width: 0.15em;
    border-style: solid;
    border-top-color: transparent;
    border-right-color: inherit;
    border-bottom-color: inherit;
    border-radius: 0.15em;
    border-left-color: transparent;
    transform: rotate(45deg);
}

.vxe-icon--close:before {
    content: "×";
    left: -0.05em;
    bottom: 0;
    line-height: 0.8em;
    font-size: 1.4em;
}

.vxe-icon--minus:before {
    content: "─";
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    line-height: 0.9em;
    font-size: 1.2em;
}

.vxe-icon--refresh {
    border-width: 0.1em;
    border-style: solid;
    border-radius: 50%;
    border-right-color: transparent !important;
    border-left-color: transparent !important;
}
.vxe-icon--refresh:before {
    left: 50%;
    top: 0;
    transform: translateX(50%) rotate(-45deg);
}
.vxe-icon--refresh:after {
    right: 50%;
    bottom: 0;
    transform: translateX(-50%) rotate(135deg);
}
.vxe-icon--refresh:before, .vxe-icon--refresh:after {
    width: 0;
    height: 0;
    border-width: 0.25em;
    border-style: solid;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
}
.vxe-icon--refresh.roll {
    animation: rollCircle 1s infinite linear;
}

@keyframes rollCircle {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.vxe-icon--error:before, .vxe-icon--remove:before, .vxe-icon--circle-plus:before, .vxe-icon--success:before, .vxe-icon--warning:before, .vxe-icon--info:before, .vxe-icon--question:before {
    content: "";
    border-radius: 50%;
    border-width: 0.5em;
    border-style: solid;
    border-color: inherit;
    position: absolute;
    top: 0;
    left: 0;
    transform: scale(0.95);
}

.vxe-icon--warning:after, .vxe-icon--info:after, .vxe-icon--question:after {
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    transform: rotate(-10deg) scale(0.75);
}

.vxe-icon--question:after {
    content: "?";
}

.vxe-icon--info:after {
    content: "¡";
}

.vxe-icon--warning:after {
    content: "!";
}

.vxe-icon--success:after {
    content: "✓";
    left: 0.25em;
    bottom: 0;
    color: #fff;
    font-size: 0.65em;
}

.vxe-icon--circle-plus:after {
    content: "+";
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    line-height: 1.4em;
    font-size: 0.8em;
}

.vxe-icon--remove:after {
    content: "─";
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    line-height: 1.5em;
    color: #fff;
    font-size: 0.7em;
}

.vxe-icon--error:after {
    content: "×";
    left: 0;
    bottom: 0;
    width: 100%;
    line-height: 1.4em;
    text-align: center;
    color: #fff;
    font-size: 0.8em;
}

.vxe-icon--upload,
.vxe-icon--download {
    overflow: hidden;
}
.vxe-icon--upload:before,
.vxe-icon--download:before {
    left: 0;
    width: 1em;
    border-width: 0;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--upload:after,
.vxe-icon--download:after {
    width: 100%;
    text-align: center;
    font-size: 2em;
}

.vxe-icon--upload:before {
    top: 0.1em;
    border-top-width: 0.1em;
}
.vxe-icon--upload:after {
    content: "↑";
    left: 0;
    top: 0.15em;
}

.vxe-icon--download:before {
    bottom: 0.05em;
    border-bottom-width: 0.1em;
}
.vxe-icon--download:after {
    content: "↑";
    left: 0;
    bottom: 0.15em;
    transform: rotate(180deg);
}

.vxe-icon--eye:before,
.vxe-icon--eye-slash:before {
    content: "●";
    top: 0.16em;
    left: 0;
    width: 1em;
    height: 0.68em;
    line-height: 0.25em;
    border-radius: 50%;
    border-width: 0.1em;
    border-style: solid;
    border-color: inherit;
    text-align: center;
}

.vxe-icon--eye-slash:after {
    top: -0.1em;
    left: 0.45em;
    width: 0;
    height: 1.2em;
    border-width: 0;
    border-style: solid;
    border-color: inherit;
    border-left-width: 0.1em;
    transform: rotate(45deg);
}

.vxe-icon--calendar:before {
    top: 0.15em;
    left: 0;
    width: 1em;
    height: 0.8em;
    border-width: 0.2em 0.1em 0.1em 0.1em;
    border-radius: 0.1em 0.1em 0 0;
    border-style: solid;
    border-color: inherit;
}
.vxe-icon--calendar:after {
    left: 0.2em;
    top: 0;
    width: 0.6em;
    height: 0.3em;
    border-width: 0 0.1em;
    border-style: solid;
    border-color: inherit;
}

.vxe-icon--dot:before {
    top: 0.25em;
    left: 0.25em;
    border-radius: 50%;
    border-width: 0.25em;
    border-style: solid;
    border-color: inherit;
}

.vxe-icon--print {
    box-shadow: inset 0 0 0 0.1em;
    border-width: 0.2em 0;
    border-style: solid;
    border-color: transparent !important;
    border-radius: 0.3em 0.3em 0 0;
}
.vxe-icon--print:before {
    width: 0.6em;
    height: 0.3em;
    top: -0.2em;
    left: 0.2em;
    box-shadow: inset 0 0 0 0.1em;
}
.vxe-icon--print:after {
    width: 0.6em;
    height: 0.6em;
    left: 0.2em;
    bottom: -0.2em;
    box-shadow: inset 0 0 0 0.1em;
    background-color: #fff;
}

.vxe-icon--search:before {
    top: 0;
    left: 0;
    width: 0.8em;
    height: 0.8em;
    border-width: 0.15em;
    border-style: solid;
    border-color: inherit;
    border-radius: 50%;
}
.vxe-icon--search:after {
    top: 0.75em;
    left: 0.6em;
    width: 0.35em;
    height: 0;
    border-width: 0.15em 0 0 0;
    border-style: solid;
    border-color: inherit;
    transform: rotate(45deg);
}

/**Variable**/
.vxe-custom--option, .vxe-export--panel-column-option, .vxe-table--filter-option, .vxe-table--render-default .vxe-cell--checkbox, .vxe-table--render-default .vxe-cell--radio {
    position: relative;
    user-select: none;
    cursor: pointer;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
    display: none;
    position: absolute;
    height: 1em;
    width: 1em;
}

.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
    font-size: 16px;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    border: 2px solid #dcdfe6;
    background-color: #fff;
    border-radius: 50%;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--unchecked-icon {
    display: inline-block;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.25em;
    width: 0.25em;
    top: 0.4em;
    left: 0.4em;
    border-radius: 50%;
    background-color: #fff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio {
    color: #409eff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--unchecked-icon {
    display: none;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon {
    display: inline-block;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon:before {
    border-color: #409eff;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio {
    cursor: not-allowed;
    color: #BFBFBF;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:after {
    background-color: #c0c4cc;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-custom--option .vxe-checkbox--icon:before, .vxe-export--panel-column-option .vxe-checkbox--icon:before, .vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    background-color: #fff;
    border-radius: 2px;
    border: 2px solid #dcdfe6;
}
.vxe-custom--option .vxe-checkbox--unchecked-icon, .vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--unchecked-icon {
    display: inline-block;
}
.vxe-custom--option .vxe-checkbox--checked-icon:after, .vxe-export--panel-column-option .vxe-checkbox--checked-icon:after, .vxe-table--filter-option .vxe-checkbox--checked-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.64em;
    width: 0.32em;
    top: 50%;
    left: 50%;
    border: 2px solid #fff;
    border-left: 0;
    border-top: 0;
    transform: translate(-50%, -50%) rotate(45deg);
}
.vxe-custom--option .vxe-checkbox--indeterminate-icon:after, .vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon:after, .vxe-table--filter-option .vxe-checkbox--indeterminate-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--indeterminate-icon:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    height: 2px;
    width: 0.6em;
    background-color: #fff;
    transform: translate(-50%, -50%);
}
.is--checked.vxe-custom--option, .is--checked.vxe-export--panel-column-option, .is--checked.vxe-table--filter-option, .vxe-table--render-default .is--checked.vxe-cell--checkbox, .is--indeterminate.vxe-custom--option, .is--indeterminate.vxe-export--panel-column-option, .is--indeterminate.vxe-table--filter-option, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox {
    color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--unchecked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-custom--option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--unchecked-icon {
    display: none;
}
.is--checked.vxe-custom--option .vxe-checkbox--icon:before, .is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--checked.vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--checked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--checked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--checked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--checked-icon {
    display: inline-block;
}
.is--indeterminate.vxe-custom--option .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--indeterminate-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--indeterminate-icon {
    display: inline-block;
}
.vxe-custom--option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon:before {
    border-color: #409eff;
}
.is--disabled.vxe-custom--option, .is--disabled.vxe-export--panel-column-option, .is--disabled.vxe-table--filter-option, .vxe-table--render-default .is--disabled.vxe-cell--checkbox {
    cursor: not-allowed;
    color: #BFBFBF;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:before, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:after, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:after, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:after, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:after {
    border-color: #c0c4cc;
}

[class*=vxe-]:after, [class*=vxe-]:before,
[class*=vxe-] *:after, [class*=vxe-] *:before, [class*=vxe-] {
    box-sizing: border-box;
}

.vxe-radio-button .vxe-radio--label, .vxe-radio .vxe-radio--label, .vxe-checkbox .vxe-checkbox--label, .vxe-table--render-default .vxe-header--column.col--ellipsis:not(.col--actived) > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis:not(.col--actived) > .vxe-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

[class*=vxe-] {
    font-variant: tabular-nums;
    font-feature-settings: "tnum";
}
.vxe-primary-color {
    color: #409eff;
}

.vxe-success-color {
    color: #67c23a;
}

.vxe-info-color {
    color: #909399;
}

.vxe-warning-color {
    color: #e6a23c;
}

.vxe-danger-color {
    color: #f56c6c;
}

.vxe-perfect-color {
    color: #1f242d;
}

.vxe-row:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.vxe-row > .vxe-col--1 {
    float: left;
    width: 4.16667%;
}
.vxe-row > .vxe-col--2 {
    float: left;
    width: 8.33333%;
}
.vxe-row > .vxe-col--3 {
    float: left;
    width: 12.5%;
}
.vxe-row > .vxe-col--4 {
    float: left;
    width: 16.66667%;
}
.vxe-row > .vxe-col--5 {
    float: left;
    width: 20.83333%;
}
.vxe-row > .vxe-col--6 {
    float: left;
    width: 25%;
}
.vxe-row > .vxe-col--7 {
    float: left;
    width: 29.16667%;
}
.vxe-row > .vxe-col--8 {
    float: left;
    width: 33.33333%;
}
.vxe-row > .vxe-col--9 {
    float: left;
    width: 37.5%;
}
.vxe-row > .vxe-col--10 {
    float: left;
    width: 41.66667%;
}
.vxe-row > .vxe-col--11 {
    float: left;
    width: 45.83333%;
}
.vxe-row > .vxe-col--12 {
    float: left;
    width: 50%;
}
.vxe-row > .vxe-col--13 {
    float: left;
    width: 54.16667%;
}
.vxe-row > .vxe-col--14 {
    float: left;
    width: 58.33333%;
}
.vxe-row > .vxe-col--15 {
    float: left;
    width: 62.5%;
}
.vxe-row > .vxe-col--16 {
    float: left;
    width: 66.66667%;
}
.vxe-row > .vxe-col--17 {
    float: left;
    width: 70.83333%;
}
.vxe-row > .vxe-col--18 {
    float: left;
    width: 75%;
}
.vxe-row > .vxe-col--19 {
    float: left;
    width: 79.16667%;
}
.vxe-row > .vxe-col--20 {
    float: left;
    width: 83.33333%;
}
.vxe-row > .vxe-col--21 {
    float: left;
    width: 87.5%;
}
.vxe-row > .vxe-col--22 {
    float: left;
    width: 91.66667%;
}
.vxe-row > .vxe-col--23 {
    float: left;
    width: 95.83333%;
}
.vxe-row > .vxe-col--24 {
    float: left;
    width: 100%;
}

/*animat*/
.is--animat .vxe-sort--asc-btn:before, .is--animat .vxe-sort--asc-btn:after,
.is--animat .vxe-sort--desc-btn:before,
.is--animat .vxe-sort--desc-btn:after,
.is--animat .vxe-filter--btn:before,
.is--animat .vxe-filter--btn:after {
    transition: border 0.1s ease-in-out;
}
.is--animat .vxe-input--wrapper .vxe-input {
    transition: border 0.1s ease-in-out;
}
.is--animat .vxe-table--expand-btn,
.is--animat .vxe-tree--node-btn {
    transition: transform 0.1s ease-in-out;
}
.is--animat .vxe-checkbox > input:checked + span,
.is--animat .vxe-radio > input:checked + span {
    transition: background-color 0.1s ease-in-out;
}

/*加载中*/
.vxe-loading {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
}
.vxe-loading.is--visible {
    display: block;
}
.vxe-loading .vxe-loading--spinner {
    width: 56px;
    height: 56px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.vxe-loading .vxe-loading--spinner:before, .vxe-loading .vxe-loading--spinner:after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #409eff;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: bounce 2s infinite ease-in-out;
}
.vxe-loading .vxe-loading--spinner:after {
    animation-delay: -1s;
}
@keyframes bounce {
    0%, 100% {
        transform: scale(0);
    }
    50% {
        transform: scale(1);
    }
}

.size--mini .vxe-loading .vxe-loading--spinner {
    width: 38px;
    height: 38px;
}

.size--small .vxe-loading .vxe-loading--spinner {
    width: 44px;
    height: 44px;
}

.size--medium .vxe-loading .vxe-loading--spinner {
    width: 50px;
    height: 50px;
}

.vxe-table--render-default .vxe-header--column.col--ellipsis,
.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column {
    height: 48px;
}

.vxe-table--render-default.size--medium .vxe-header--column.col--ellipsis,
.vxe-table--render-default.size--medium .vxe-body--column.col--ellipsis,
.vxe-table--render-default.size--medium .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--medium .vxe-body--column {
    height: 44px;
}

.vxe-table--render-default.size--small .vxe-header--column.col--ellipsis,
.vxe-table--render-default.size--small .vxe-body--column.col--ellipsis,
.vxe-table--render-default.size--small .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--small .vxe-body--column {
    height: 40px;
}

.vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis,
.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis,
.vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--mini .vxe-body--column {
    height: 36px;
}

.vxe-table-slots,
.vxe-table--file-form {
    display: none;
}

.vxe-table--print-frame {
    position: fixed;
    bottom: -100%;
    left: -100%;
    height: 0;
    width: 0;
    border: 0;
}

.vxe-table--body-wrapper {
    scroll-behavior: auto;
}

.vxe-table--body-wrapper,
.vxe-table--fixed-left-body-wrapper,
.vxe-table--fixed-right-body-wrapper {
    overflow-y: auto;
    overflow-x: auto;
}

/*默认的渲染*/
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-input,
.vxe-table--filter-wrapper .vxe-default-textarea {
    background-color: #1f242d;
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-cell .vxe-default-select,
.vxe-table--filter-wrapper .vxe-default-input,
.vxe-table--filter-wrapper .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-select {
    outline: 0;
    padding: 0 2px;
    width: 100%;
    color: white;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}
.vxe-cell .vxe-default-input:focus,
.vxe-cell .vxe-default-textarea:focus,
.vxe-cell .vxe-default-select:focus,
.vxe-table--filter-wrapper .vxe-default-input:focus,
.vxe-table--filter-wrapper .vxe-default-textarea:focus,
.vxe-table--filter-wrapper .vxe-default-select:focus {
    border: 1px solid #409eff;
}
.vxe-cell .vxe-default-input[disabled],
.vxe-cell .vxe-default-textarea[disabled],
.vxe-cell .vxe-default-select[disabled],
.vxe-table--filter-wrapper .vxe-default-input[disabled],
.vxe-table--filter-wrapper .vxe-default-textarea[disabled],
.vxe-table--filter-wrapper .vxe-default-select[disabled] {
    cursor: not-allowed;
    background-color: #f3f3f3;
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-cell .vxe-default-select,
.vxe-table--filter-wrapper .vxe-default-input,
.vxe-table--filter-wrapper .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-select {
    height: 34px;
}
.vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button,
.vxe-table--filter-wrapper .vxe-default-input[type=date]::-webkit-inner-spin-button {
    margin-top: 4px;
}
.vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button, .vxe-cell .vxe-default-input[type=number]::-webkit-inner-spin-button,
.vxe-table--filter-wrapper .vxe-default-input[type=date]::-webkit-inner-spin-button,
.vxe-table--filter-wrapper .vxe-default-input[type=number]::-webkit-inner-spin-button {
    height: 24px;
}
.vxe-cell .vxe-default-input::placeholder,
.vxe-table--filter-wrapper .vxe-default-input::placeholder {
    color: #C0C4CC;
}
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-default-textarea {
    resize: none;
    vertical-align: middle;
}
.vxe-cell .vxe-input,
.vxe-cell .vxe-textarea,
.vxe-cell .vxe-select,
.vxe-table--filter-wrapper .vxe-input,
.vxe-table--filter-wrapper .vxe-textarea,
.vxe-table--filter-wrapper .vxe-select {
    width: 100%;
    display: block;
}
.vxe-cell .vxe-input > .vxe-input--inner,
.vxe-cell .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-wrapper .vxe-input > .vxe-input--inner,
.vxe-table--filter-wrapper .vxe-textarea > .vxe-textarea--inner {
    padding: 0 2px;
}
.vxe-cell .vxe-textarea--inner,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-wrapper .vxe-textarea--inner,
.vxe-table--filter-wrapper .vxe-default-textarea {
    resize: none;
}

.vxe-table--checkbox-range,
.vxe-table--cell-main-area,
.vxe-table--cell-extend-area,
.vxe-table--cell-active-area,
.vxe-table--cell-copy-area {
    display: none;
    position: absolute;
    pointer-events: none;
    z-index: 1;
}

.vxe-table--fixed-left-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area,
.vxe-table--fixed-right-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area {
    z-index: 2;
}

.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area[half="1"],
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area[half="1"],
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area[half="1"] {
    border-right: 0;
}
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area[half="1"] {
    background-size: 3px 12px, 0 12px, 12px 3px, 12px 3px;
}

.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area[half="1"],
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area[half="1"],
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area[half="1"] {
    border-left: 0;
}
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area[half="1"] {
    background-size: 0 12px, 3px 12px, 12px 3px, 12px 3px;
}

/*复选框-范围选择*/
.vxe-table--checkbox-range {
    background-color: rgba(50, 128, 252, 0.2);
    border: 1px solid #006af1;
}

.vxe-table--cell-area {
    height: 0;
    font-size: 0;
    display: none;
}
.vxe-table--cell-area > .vxe-table--cell-main-area {
    background-color: rgba(64, 158, 255, 0.2);
    border: 1px solid #409eff;
}
.vxe-table--cell-area .vxe-table--cell-main-area-btn {
    display: none;
    position: absolute;
    right: -1px;
    bottom: -1px;
    width: 7px;
    height: 7px;
    border-style: solid;
    border-color: #fff;
    border-width: 1px 0 0 1px;
    background-color: #409eff;
    pointer-events: auto;
    cursor: crosshair;
}
.vxe-table--cell-area .vxe-table--cell-extend-area {
    border: 2px solid #409eff;
}

@keyframes moveCopyCellBorder {
    to {
        background-position: 0 -12px, 100% 12px, 12px 0, -12px 100%;
    }
}
.vxe-table--cell-copy-area {
    background: linear-gradient(0deg, transparent 6px, #409eff 6px) repeat-y, linear-gradient(0deg, transparent 50%, #409eff 0) repeat-y, linear-gradient(90deg, transparent 50%, #409eff 0) repeat-x, linear-gradient(90deg, transparent 50%, #409eff 0) repeat-x;
    background-size: 3px 12px, 3px 12px, 12px 3px, 12px 3px;
    background-position: 0 0, 100% 0, 0 0, 0 100%;
    animation: moveCopyCellBorder 0.5s infinite linear;
}

.vxe-table--cell-active-area {
    border: 2px solid #409eff;
}

.vxe-table--cell-multi-area > .vxe-table--cell-main-area {
    background-color: rgba(64, 158, 255, 0.2);
}

/*圆角*/
.vxe-table--render-default.is--round:not(.is--header):not(.is--footer) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round .vxe-table--border-line, .vxe-table--render-default.is--round .vxe-table--render-default.is--round {
    border-radius: 4px;
}
.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.body--wrapper {
    border-radius: 4px 4px 0 0;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-left--wrapper {
    border-radius: 4px 0 0 0;
}
.vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-right--wrapper {
    border-radius: 0 4px 0 0;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.body--wrapper {
    border-radius: 0 0 4px 4px;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-left--wrapper {
    border-radius: 0 0 0 4px;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-right--wrapper {
    border-radius: 0 0 4px 0;
}
/*table*/
.vxe-table--render-default {
    position: relative;
    font-size: 14px;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    /*边框*/
    /*列宽线*/
    /*边框线*/
    /*树形节点*/
    /*展开行*/
    /*设置列高度*/
    /*溢出列*/
    /*暂无数据*/
    /*校验不通过*/
    /*单元格编辑状态*/
    /*可编辑*/
}
.vxe-table--render-default .vxe-table--body-wrapper {
    background-color: #1f242d;
}
.vxe-table--render-default .vxe-table--footer-wrapper {
    background-color: #ffffff;
}
.vxe-table--render-default .vxe-table--header,
.vxe-table--render-default .vxe-table--body,
.vxe-table--render-default .vxe-table--footer {
    border: 0;
    border-spacing: 0;
    border-collapse: separate;
    table-layout: fixed;
}
.vxe-table--render-default .vxe-table--header-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper {
    overflow-x: hidden;
    overflow-y: hidden;
}
.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper {
    overflow-x: scroll;
}
.vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #262f3c;
}
.vxe-table--render-default .vxe-body--row.row--radio {
    background-color: #fff3e0;
}
.vxe-table--render-default .vxe-body--row.row--checked {
    background-color: #fff3e0;
}
.vxe-table--render-default .vxe-body--row.row--current {
    background-color: #2C79F2;
}
.vxe-table--render-default .vxe-body--row.row--hover {
    background-color: #374c6b;
}
.vxe-table--render-default .vxe-body--row.row--hover.row--stripe {
    background-color: #374c6b;
}
.vxe-table--render-default .vxe-body--row.row--hover.row--radio {
    background-color: #ffebbc;
}
.vxe-table--render-default .vxe-body--row.row--hover.row--checked {
    background-color: #ffebbc;
}
.vxe-table--render-default .vxe-body--row.row--hover.row--current {
    background-color: #374c6b;
}
.vxe-table--render-default.drag--resize .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--resize .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--resize .vxe-table--fixed-right-wrapper * {
    cursor: col-resize;
}
.vxe-table--render-default.drag--range .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-right-wrapper *, .vxe-table--render-default.drag--area .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-right-wrapper * {
    cursor: default;
}
.vxe-table--render-default.drag--extend-range .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-right-wrapper * {
    cursor: crosshair;
}
.vxe-table--render-default.column--highlight .vxe-header--column:not(.col--seq):hover {
    background-color: #d7effb;
}
.vxe-table--render-default.cell--area .vxe-table--main-wrapper {
    user-select: none;
}
.vxe-table--render-default .vxe-header--column,
.vxe-table--render-default .vxe-body--column,
.vxe-table--render-default .vxe-footer--column {
    position: relative;
    line-height: 24px;
    text-align: left;
}
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis) {
    padding: 12px 0;
}
.vxe-table--render-default .vxe-header--column.col--current,
.vxe-table--render-default .vxe-body--column.col--current,
.vxe-table--render-default .vxe-footer--column.col--current {
    background-color: #e6f7ff;
}
.vxe-table--render-default .vxe-header--column.col--center,
.vxe-table--render-default .vxe-body--column.col--center,
.vxe-table--render-default .vxe-footer--column.col--center {
    text-align: center;
}
.vxe-table--render-default .vxe-header--column.col--right,
.vxe-table--render-default .vxe-body--column.col--right,
.vxe-table--render-default .vxe-footer--column.col--right {
    text-align: right;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--center .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--center .vxe-cell {
    justify-content: center;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--right .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--right .vxe-cell {
    justify-content: flex-end;
}
.vxe-table--render-default .vxe-body--column.col--checkbox {
    user-select: none;
}
.vxe-table--render-default .vxe-table--footer-wrapper {
    border-top: 1px solid black;
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper, .vxe-table--render-default.border--full .vxe-table--header-wrapper, .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: #1f242d;
}
.vxe-table--render-default.border--default .vxe-header--column,
.vxe-table--render-default.border--default .vxe-body--column,
.vxe-table--render-default.border--default .vxe-footer--column, .vxe-table--render-default.border--inner .vxe-header--column,
.vxe-table--render-default.border--inner .vxe-body--column,
.vxe-table--render-default.border--inner .vxe-footer--column {
    background-image: linear-gradient(black, black);
    background-repeat: no-repeat;
    background-size: 100% 1px;
    background-position: right bottom;
}
.vxe-table--render-default.border--full .vxe-header--column,
.vxe-table--render-default.border--full .vxe-body--column,
.vxe-table--render-default.border--full .vxe-footer--column {
    background-image: linear-gradient(black, black), linear-gradient(black, black);
    background-repeat: no-repeat;
    background-size: 1px 100%, 100% 1px;
    background-position: right top, right bottom;
}
.vxe-table--render-default.border--full .vxe-table--fixed-left-wrapper .vxe-body--column {
    border-right-color: black;
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter, .vxe-table--render-default.border--full .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter, .vxe-table--render-default.border--outer .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter, .vxe-table--render-default.border--inner .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter {
    background-image: linear-gradient(black, black);
    background-repeat: no-repeat;
    background-size: 100% 1px;
    background-position: right bottom;
}
.vxe-table--render-default.border--inner .vxe-table--header-wrapper, .vxe-table--render-default.border--none .vxe-table--header-wrapper {
    background-color: #ffffff;
}
.vxe-table--render-default.border--inner .vxe-table--fixed-left-wrapper, .vxe-table--render-default.border--none .vxe-table--fixed-left-wrapper {
    border-right: 0;
}
.vxe-table--render-default.border--inner .vxe-table--border-line {
    border-width: 0 0 1px 0;
}
.vxe-table--render-default.border--none .vxe-table--border-line {
    display: none;
}
.vxe-table--render-default.border--none .vxe-table--header-border-line {
    display: none;
}
.vxe-table--render-default.border--none .vxe-table--footer-wrapper {
    border-top: 0;
}
.vxe-table--render-default.size--medium {
    font-size: 14px;
}
.vxe-table--render-default.size--medium .vxe-table--empty-placeholder,
.vxe-table--render-default.size--medium .vxe-table--empty-block {
    min-height: 44px;
}
.vxe-table--render-default.size--medium .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default.size--medium .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--medium .vxe-footer--column:not(.col--ellipsis) {
    padding: 10px 0;
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-select {
    height: 32px;
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
    margin-top: 3px;
}
.vxe-table--render-default.size--small {
    font-size: 13px;
}
.vxe-table--render-default.size--small .vxe-table--empty-placeholder,
.vxe-table--render-default.size--small .vxe-table--empty-block {
    min-height: 40px;
}
.vxe-table--render-default.size--small .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default.size--small .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--small .vxe-footer--column:not(.col--ellipsis) {
    padding: 8px 0;
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-select {
    height: 30px;
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
    margin-top: 2px;
}
.vxe-table--render-default.size--mini {
    font-size: 12px;
}
.vxe-table--render-default.size--mini .vxe-table--empty-placeholder,
.vxe-table--render-default.size--mini .vxe-table--empty-block {
    min-height: 36px;
}
.vxe-table--render-default.size--mini .vxe-header--column:not(.col--ellipsis),
.vxe-table--render-default.size--mini .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--mini .vxe-footer--column:not(.col--ellipsis) {
    padding: 6px 0;
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-select {
    height: 28px;
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
    margin-top: 1px;
}
.vxe-table--render-default .vxe-cell {
    white-space: pre-line;
    word-break: break-all;
    padding-left: 10px;
    padding-right: 10px;
}
.vxe-table--render-default .vxe-cell--placeholder {
    color: #C0C4CC;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon,
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    left: 0;
    top: 0.1em;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--label,
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--label {
    padding-left: 0.5em;
}
.vxe-table--render-default .vxe-cell--radio,
.vxe-table--render-default .vxe-cell--checkbox {
    padding-left: 1.2em;
}
.vxe-table--render-default .fixed--hidden {
    visibility: hidden;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
    width: 100%;
    position: absolute;
    top: 0;
    z-index: 5;
    overflow: hidden;
    background-color: inherit;
    transition: 0.3s box-shadow;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper {
    overflow-x: hidden;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper {
    width: calc(100% + 40px);
}
.vxe-table--render-default.is--header .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper:before,
.vxe-table--render-default.is--header .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper:before {
    display: none;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper {
    left: 0;
    width: 200px;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper.scrolling--middle {
    box-shadow: 4px 3px 4px 0px rgba(0, 0, 0, 0.12);
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
    right: 0;
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper.scrolling--middle {
    box-shadow: -4px 3px 4px 0px rgba(0, 0, 0, 0.12);
}
.vxe-table--render-default .vxe-table--header-wrapper,
.vxe-table--render-default .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper {
    position: relative;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper, .vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper {
    position: absolute;
    top: 0;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper {
    left: 0;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper {
    right: 0;
    overflow-y: auto;
}
.vxe-table--render-default .vxe-body--x-space {
    width: 100%;
    height: 1px;
    margin-bottom: -1px;
}
.vxe-table--render-default .vxe-body--y-space {
    width: 0;
    float: left;
}
.vxe-table--render-default .vxe-table--resizable-bar {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 100%;
    z-index: 9;
    cursor: col-resize;
}
.vxe-table--render-default .vxe-table--resizable-bar:before {
    content: "";
    display: block;
    height: 100%;
    background-color: #409eff;
}
.vxe-table--render-default .vxe-table--border-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    pointer-events: none;
    border: 1px solid black;
}
.vxe-table--render-default.is--tree-line .vxe-body--row:first-child .vxe-tree--line {
    border-width: 0 0 1px 0;
}
.vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image: none;
}
.vxe-table--render-default .vxe-tree--line-wrapper {
    position: relative;
    display: block;
    height: 0;
}
.vxe-table--render-default .vxe-tree--line {
    content: "";
    position: absolute;
    bottom: -0.9em;
    width: 0.8em;
    border-width: 0 0 1px 1px;
    border-style: dotted;
    border-color: #909399;
    pointer-events: none;
}
.vxe-table--render-default .vxe-cell--tree-node {
    position: relative;
}
.vxe-table--render-default .vxe-tree--btn-wrapper {
    position: absolute;
    top: 50%;
    width: 1em;
    height: 1em;
    text-align: center;
    transform: translateY(-50%);
    z-index: 1;
    user-select: none;
    cursor: pointer;
}
.vxe-table--render-default .vxe-tree--node-btn {
    display: block;
    color: white;
}
.vxe-table--render-default .vxe-tree--node-btn:hover {
    color: white;
}
.vxe-table--render-default .vxe-tree-cell {
    display: block;
    padding-left: 1.5em;
}
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default .vxe-table--expanded {
    cursor: pointer;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn {
    width: 1em;
    height: 1em;
    text-align: center;
    user-select: none;
    color: white;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn:hover {
    color: white;
}
.vxe-table--render-default .vxe-table--expanded + .vxe-table--expand-label {
    padding-left: 0.5em;
}
.vxe-table--render-default .vxe-body--expanded-column {
    border-bottom: 1px solid black;
}
.vxe-table--render-default .vxe-body--expanded-column.col--ellipsis > .vxe-body--expanded-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-table--render-default .vxe-body--expanded-cell {
    position: relative;
    padding: 20px;
    z-index: 1;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis > .vxe-cell {
    max-height: 48px;
}
.vxe-table--render-default.size--medium .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--medium .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--medium .vxe-footer--column.col--ellipsis > .vxe-cell {
    max-height: 44px;
}
.vxe-table--render-default.size--medium .vxe-cell--checkbox .vxe-checkbox--icon,
.vxe-table--render-default.size--medium .vxe-cell--radio .vxe-radio--icon {
    font-size: 15px;
}
.vxe-table--render-default.size--small .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--small .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--small .vxe-footer--column.col--ellipsis > .vxe-cell {
    max-height: 40px;
}
.vxe-table--render-default.size--small .vxe-cell--checkbox .vxe-checkbox--icon,
.vxe-table--render-default.size--small .vxe-cell--radio .vxe-radio--icon {
    font-size: 14px;
}
.vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis > .vxe-cell {
    max-height: 36px;
}
.vxe-table--render-default.size--mini .vxe-cell--checkbox .vxe-checkbox--icon,
.vxe-table--render-default.size--mini .vxe-cell--radio .vxe-radio--icon {
    font-size: 14px;
}
.vxe-table--render-default .vxe-table--empty-placeholder,
.vxe-table--render-default .vxe-table--empty-block {
    min-height: 48px;
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow: hidden;
    width: 100%;
    pointer-events: none;
}
.vxe-table--render-default .vxe-table--empty-block {
    display: none;
    visibility: hidden;
}
.vxe-table--render-default .vxe-table--empty-placeholder {
    display: none;
    position: absolute;
    top: 0;
    z-index: 3;
}
.vxe-table--render-default .vxe-table--empty-content {
    display: block;
    width: 50%;
    pointer-events: auto;
}
.vxe-table--render-default.is--empty .vxe-table--empty-block,
.vxe-table--render-default.is--empty .vxe-table--empty-placeholder {
    display: flex;
}
.vxe-table--render-default .vxe-body--column.col--selected {
    box-shadow: inset 0px 0px 0px 2px #409eff;
}
.vxe-table--render-default .vxe-body--column.col--actived, .vxe-table--render-default .vxe-body--column.col--selected {
    position: relative;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-cell--valid {
    width: 320px;
    position: absolute;
    bottom: calc(100% + 4px);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    pointer-events: none;
    z-index: 4;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-cell--valid .vxe-cell--valid-msg {
    display: inline-block;
    border-radius: 4px;
    padding: 8px 12px;
    color: #fff;
    background-color: #f56c6c;
    pointer-events: auto;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-input,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-textarea,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-select {
    border-color: #f56c6c;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-input > .vxe-input--inner {
    border-color: #f56c6c;
}
.vxe-table--render-default .vxe-body--row:first-child .vxe-cell--valid {
    bottom: auto;
    top: calc(100% + 4px);
}
.vxe-table--render-default .vxe-body--column:first-child .vxe-cell--valid {
    left: 10px;
    transform: translateX(0);
    text-align: left;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column {
    position: relative;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column:before {
    content: "";
    top: -5px;
    left: -5px;
    position: absolute;
    border-width: 5px;
    border-style: solid;
    border-color: transparent #19A15F transparent transparent;
    transform: rotate(45deg);
}
.vxe-table--render-default .vxe-body--column.col--dirty {
    position: relative;
}
.vxe-table--render-default .vxe-body--column.col--dirty:before {
    content: "";
    top: -5px;
    left: -5px;
    position: absolute;
    border-width: 5px;
    border-style: solid;
    border-color: transparent #f56c6c transparent transparent;
    transform: rotate(45deg);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived {
    box-shadow: inset 0px 0px 0px 2px #409eff;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived.col--valid-error {
    box-shadow: inset 0px 0px 0px 2px #f56c6c;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived .vxe-cell .vxe-default-input,
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived .vxe-cell .vxe-default-textarea {
    border: 0;
    padding: 0;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived .vxe-cell .vxe-default-input {
    height: 24px;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived .vxe-cell .vxe-input .vxe-input--inner {
    border: 0;
    padding-left: 0;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived .vxe-cell .vxe-textarea {
    height: 23px;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--actived .vxe-cell .vxe-textarea .vxe-textarea--inner {
    border: 0;
}
.vxe-table--render-default.vxe-editable .vxe-body--column {
    padding: 0;
}
.vxe-table--render-default.vxe-editable .vxe-body--column.col--actived {
    padding: 0;
}

/**Variable**/
/**Variable**/
/**Variable**/
.vxe-table {
    /*排序*/
}
.vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 0;
    border-bottom: 1px solid black;
}
.vxe-table .vxe-cell--sort {
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    text-align: center;
    display: inline-block;
    position: relative;
}
.vxe-table .vxe-sort--asc-btn,
.vxe-table .vxe-sort--desc-btn {
    position: absolute;
    left: 0.35em;
    color: #c0c4cc;
    width: 1em;
    text-align: center;
    cursor: pointer;
}
.vxe-table .vxe-sort--asc-btn:hover,
.vxe-table .vxe-sort--desc-btn:hover {
    color: white;
}
.vxe-table .vxe-sort--asc-btn.sort--active,
.vxe-table .vxe-sort--desc-btn.sort--active {
    color: #409eff;
}
.vxe-table .vxe-sort--asc-btn {
    top: -0.04em;
}
.vxe-table .vxe-sort--desc-btn {
    bottom: -0.04em;
}

.vxe-header--column {
    position: relative;
    font-weight: 700;
    user-select: none;
}
.vxe-header--column.col--ellipsis > .vxe-cell {
    display: flex;
    align-items: center;
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-header--column.col--ellipsis > .vxe-cell > i:not(.vxe-cell--title), .vxe-header--column.col--ellipsis > .vxe-cell > span:not(.vxe-cell--title) {
    flex-shrink: 0;
}
.vxe-header--column .vxe-cell--required-icon {
    display: inline-block;
    color: #f56c6c;
    width: 0.8em;
    height: 1em;
    line-height: 1em;
    font-family: Verdana, Arial, Tahoma;
    font-weight: normal;
    position: relative;
}
.vxe-header--column .vxe-cell--required-icon:before {
    content: "*";
    position: absolute;
    left: 0;
    top: 0.2em;
}
.vxe-header--column .vxe-cell--required-icon,
.vxe-header--column .vxe-cell--edit-icon,
.vxe-header--column .vxe-cell-help-icon,
.vxe-header--column .vxe-cell--title {
    vertical-align: middle;
}
.vxe-header--column .vxe-cell--required-icon {
    margin-right: 0.1em;
}
.vxe-header--column .vxe-cell--edit-icon,
.vxe-header--column .vxe-cell-help-icon {
    margin-right: 0.2em;
}
.vxe-header--column .vxe-cell-help-icon {
    cursor: help;
}
.vxe-header--column .vxe-cell--title {
    line-height: 1.5;
}
.vxe-header--column .vxe-resizable {
    position: absolute;
    right: -7px;
    bottom: 0;
    width: 14px;
    height: 100%;
    text-align: center;
    z-index: 1;
    cursor: col-resize;
}
.vxe-header--column .vxe-resizable.is--line:before, .vxe-header--column .vxe-resizable.is--line:after {
    content: "";
    display: inline-block;
    vertical-align: middle;
}
.vxe-header--column .vxe-resizable.is--line:before {
    width: 1px;
    height: 50%;
    background-color: #D9DDDF;
}
.vxe-header--column .vxe-resizable.is--line:after {
    width: 0;
    height: 100%;
}

.vxe-table--fixed-right-wrapper .vxe-header--column .vxe-resizable {
    right: auto;
    left: -7px;
}

/**Variable**/
.vxe-table--footer-wrapper {
    margin-top: -1px;
    background-color: #ffffff;
}
.vxe-table--footer-wrapper.body--wrapper {
    overflow-x: auto;
}

.vxe-footer--column.col--ellipsis > .vxe-cell {
    display: flex;
    align-items: center;
}
.vxe-footer--column.col--ellipsis > .vxe-cell .vxe-cell--item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/**Variable**/
.vxe-custom--option, .vxe-export--panel-column-option, .vxe-table--filter-option, .vxe-table--render-default .vxe-cell--radio, .vxe-table--render-default .vxe-cell--checkbox {
    position: relative;
    user-select: none;
    cursor: pointer;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--radio .vxe-radio--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    display: none;
    position: absolute;
    height: 1em;
    width: 1em;
}

.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
    font-size: 16px;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    border: 2px solid #dcdfe6;
    background-color: #fff;
    border-radius: 50%;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--unchecked-icon {
    display: inline-block;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.25em;
    width: 0.25em;
    top: 0.4em;
    left: 0.4em;
    border-radius: 50%;
    background-color: #fff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio {
    color: #409eff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--unchecked-icon {
    display: none;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon {
    display: inline-block;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon:before {
    border-color: #409eff;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio {
    cursor: not-allowed;
    color: #BFBFBF;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:after {
    background-color: #c0c4cc;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-custom--option .vxe-checkbox--icon:before, .vxe-export--panel-column-option .vxe-checkbox--icon:before, .vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    background-color: #fff;
    border-radius: 2px;
    border: 2px solid #dcdfe6;
}
.vxe-custom--option .vxe-checkbox--unchecked-icon, .vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--unchecked-icon {
    display: inline-block;
}
.vxe-custom--option .vxe-checkbox--checked-icon:after, .vxe-export--panel-column-option .vxe-checkbox--checked-icon:after, .vxe-table--filter-option .vxe-checkbox--checked-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.64em;
    width: 0.32em;
    top: 50%;
    left: 50%;
    border: 2px solid #fff;
    border-left: 0;
    border-top: 0;
    transform: translate(-50%, -50%) rotate(45deg);
}
.vxe-custom--option .vxe-checkbox--indeterminate-icon:after, .vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon:after, .vxe-table--filter-option .vxe-checkbox--indeterminate-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--indeterminate-icon:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    height: 2px;
    width: 0.6em;
    background-color: #fff;
    transform: translate(-50%, -50%);
}
.is--checked.vxe-custom--option, .is--checked.vxe-export--panel-column-option, .is--checked.vxe-table--filter-option, .vxe-table--render-default .is--checked.vxe-cell--checkbox, .is--indeterminate.vxe-custom--option, .is--indeterminate.vxe-export--panel-column-option, .is--indeterminate.vxe-table--filter-option, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox {
    color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--unchecked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-custom--option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--unchecked-icon {
    display: none;
}
.is--checked.vxe-custom--option .vxe-checkbox--icon:before, .is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--checked.vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--checked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--checked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--checked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--checked-icon {
    display: inline-block;
}
.is--indeterminate.vxe-custom--option .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--indeterminate-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--indeterminate-icon {
    display: inline-block;
}
.vxe-custom--option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon:before {
    border-color: #409eff;
}
.is--disabled.vxe-custom--option, .is--disabled.vxe-export--panel-column-option, .is--disabled.vxe-table--filter-option, .vxe-table--render-default .is--disabled.vxe-cell--checkbox {
    cursor: not-allowed;
    color: #BFBFBF;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:before, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:after, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:after, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:after, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:after {
    border-color: #c0c4cc;
}

/*筛选*/
.vxe-cell--filter {
    padding: 0 0.1em 0 0.25em;
    text-align: center;
    vertical-align: middle;
    display: inline-block;
    line-height: 0;
}
.vxe-cell--filter.is--active .vxe-filter--btn {
    color: white;
}
.vxe-cell--filter .vxe-filter--btn {
    color: #c0c4cc;
    cursor: pointer;
}
.vxe-cell--filter .vxe-filter--btn:hover {
    color: white;
}

.filter--active .vxe-cell--filter .vxe-filter--btn {
    color: #409eff;
}

/*筛选容器*/
.vxe-table--filter-wrapper {
    display: none;
    position: absolute;
    top: 0;
    min-width: 100px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #DADCE0;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    z-index: 10;
}
.vxe-table--filter-wrapper:not(.is--multiple) {
    text-align: center;
}
.vxe-table--filter-wrapper.filter--active {
    display: block;
}
.vxe-table--filter-wrapper .vxe-table--filter-header > li,
.vxe-table--filter-wrapper .vxe-table--filter-body > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 360px;
    padding: 0.25em 0.8em;
    cursor: pointer;
}
.vxe-table--filter-wrapper .vxe-table--filter-header > li.is--checked,
.vxe-table--filter-wrapper .vxe-table--filter-body > li.is--checked {
    color: #409eff;
}
.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover,
.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover {
    background-color: #374c6b;
}
.vxe-table--filter-wrapper .vxe-table--filter-header {
    padding-top: 0.2em;
}
.vxe-table--filter-wrapper .vxe-table--filter-body {
    max-height: 200px;
    padding-bottom: 0.2em;
}
.vxe-table--filter-wrapper > ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    overflow: auto;
    user-select: none;
}
.vxe-table--filter-wrapper.is--multiple > ul > li {
    padding: 0.25em 0.8em 0.25em 2.3em;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer {
    border-top: 1px solid #DADCE0;
    padding: 0.6em;
    user-select: none;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button {
    background-color: transparent;
    padding: 0 0.4em;
    border: 0;
    color: white;
    cursor: pointer;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button:focus {
    outline: none;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button:hover {
    color: #409eff;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button.is--disabled {
    color: #BFBFBF;
    cursor: not-allowed;
}

.vxe-table--filter-option .vxe-checkbox--icon {
    left: 0.6em;
    top: 0.38em;
}

.vxe-table .vxe-table--filter-option > .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-table.size--medium .vxe-table--filter-option > .vxe-checkbox--icon {
    font-size: 15px;
}
.vxe-table.size--small .vxe-table--filter-option > .vxe-checkbox--icon {
    font-size: 14px;
}
.vxe-table.size--mini .vxe-table--filter-option > .vxe-checkbox--icon {
    font-size: 14px;
}

/**Variable**/
/*快捷菜单*/
.vxe-table--context-menu-wrapper,
.vxe-table--context-menu-clild-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    border: 1px solid #DADCE0;
    box-shadow: 3px 3px 4px -2px rgba(0, 0, 0, 0.6);
    padding: 0 1px;
    user-select: none;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    background-color: #fff;
}

.vxe-context-menu--link {
    display: block;
    padding: 0 2.5em;
    width: 178px;
    line-height: 26px;
    color: white;
    cursor: pointer;
}
.vxe-context-menu--link .vxe-context-menu--link-prefix,
.vxe-context-menu--link .vxe-context-menu--link-suffix {
    position: absolute;
    top: 5px;
    margin-right: 5px;
    font-size: 16px;
}
.vxe-context-menu--link .vxe-context-menu--link-prefix {
    left: 5px;
}
.vxe-context-menu--link .vxe-context-menu--link-suffix {
    right: 5px;
}
.vxe-context-menu--link .vxe-context-menu--link-suffix.suffix--haschild {
    top: 8px;
}
.vxe-context-menu--link .vxe-context-menu--link-suffix.suffix--haschild:before {
    position: absolute;
    content: "";
    border: 4px solid transparent;
    border-left-color: #727272;
}
.vxe-context-menu--link .vxe-context-menu--link-content {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.vxe-table--context-menu-clild-wrapper .vxe-context-menu--link {
    padding: 0 2em 0 2.5em;
}

.vxe-context-menu--option-wrapper,
.vxe-table--context-menu-clild-wrapper {
    margin: 0;
    padding: 0;
    list-style-type: none;
    border-bottom: 1px solid #E8EAED;
}
.vxe-context-menu--option-wrapper li,
.vxe-table--context-menu-clild-wrapper li {
    position: relative;
    margin: 1px 0;
    border: 1px solid transparent;
}
.vxe-context-menu--option-wrapper li:last-child,
.vxe-table--context-menu-clild-wrapper li:last-child {
    border: 0;
}
.vxe-context-menu--option-wrapper li.link--active,
.vxe-table--context-menu-clild-wrapper li.link--active {
    background-color: #C5C5C5;
    border-color: #C5C5C5;
}
.vxe-context-menu--option-wrapper li.link--active > .vxe-context-menu--link,
.vxe-table--context-menu-clild-wrapper li.link--active > .vxe-context-menu--link {
    color: #2B2B2B;
}
.vxe-context-menu--option-wrapper li.link--disabled > .vxe-context-menu--link,
.vxe-table--context-menu-clild-wrapper li.link--disabled > .vxe-context-menu--link {
    color: #BFBFBF;
    cursor: no-drop;
}
.vxe-context-menu--option-wrapper li.link--disabled.link--active,
.vxe-table--context-menu-clild-wrapper li.link--disabled.link--active {
    border-color: #C0C1C2;
    background-color: #EEEEEE;
}
.vxe-context-menu--option-wrapper li.link--disabled.link--active:hover,
.vxe-table--context-menu-clild-wrapper li.link--disabled.link--active:hover {
    background-color: inherit;
}

.vxe-table--context-menu-clild-wrapper {
    display: none;
    top: 0;
    left: 100%;
}
.vxe-table--context-menu-clild-wrapper.is--show {
    display: block;
}

/**Variable**/
.vxe-custom--option, .vxe-export--panel-column-option, .vxe-table--render-default .vxe-cell--radio, .vxe-table--render-default .vxe-cell--checkbox, .vxe-table--filter-option {
    position: relative;
    user-select: none;
    cursor: pointer;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--radio .vxe-radio--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon {
    display: none;
    position: absolute;
    height: 1em;
    width: 1em;
}

.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
    font-size: 16px;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    border: 2px solid #dcdfe6;
    background-color: #fff;
    border-radius: 50%;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--unchecked-icon {
    display: inline-block;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.25em;
    width: 0.25em;
    top: 0.4em;
    left: 0.4em;
    border-radius: 50%;
    background-color: #fff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio {
    color: #409eff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--unchecked-icon {
    display: none;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon {
    display: inline-block;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon:before {
    border-color: #409eff;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio {
    cursor: not-allowed;
    color: #BFBFBF;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:after {
    background-color: #c0c4cc;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-custom--option .vxe-checkbox--icon:before, .vxe-export--panel-column-option .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon:before, .vxe-table--filter-option .vxe-checkbox--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    background-color: #fff;
    border-radius: 2px;
    border: 2px solid #dcdfe6;
}
.vxe-custom--option .vxe-checkbox--unchecked-icon, .vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .vxe-table--filter-option .vxe-checkbox--unchecked-icon {
    display: inline-block;
}
.vxe-custom--option .vxe-checkbox--checked-icon:after, .vxe-export--panel-column-option .vxe-checkbox--checked-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--checked-icon:after, .vxe-table--filter-option .vxe-checkbox--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.64em;
    width: 0.32em;
    top: 50%;
    left: 50%;
    border: 2px solid #fff;
    border-left: 0;
    border-top: 0;
    transform: translate(-50%, -50%) rotate(45deg);
}
.vxe-custom--option .vxe-checkbox--indeterminate-icon:after, .vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--indeterminate-icon:after, .vxe-table--filter-option .vxe-checkbox--indeterminate-icon:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    height: 2px;
    width: 0.6em;
    background-color: #fff;
    transform: translate(-50%, -50%);
}
.is--checked.vxe-custom--option, .is--checked.vxe-export--panel-column-option, .vxe-table--render-default .is--checked.vxe-cell--checkbox, .is--checked.vxe-table--filter-option, .is--indeterminate.vxe-custom--option, .is--indeterminate.vxe-export--panel-column-option, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox, .is--indeterminate.vxe-table--filter-option {
    color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--unchecked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-custom--option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--unchecked-icon {
    display: none;
}
.is--checked.vxe-custom--option .vxe-checkbox--icon:before, .is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--checked.vxe-table--filter-option .vxe-checkbox--icon:before, .is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--checked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--checked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--checked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--checked-icon {
    display: inline-block;
}
.is--indeterminate.vxe-custom--option .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--indeterminate-icon {
    display: inline-block;
}
.vxe-custom--option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon:before {
    border-color: #409eff;
}
.is--disabled.vxe-custom--option, .is--disabled.vxe-export--panel-column-option, .vxe-table--render-default .is--disabled.vxe-cell--checkbox, .is--disabled.vxe-table--filter-option {
    cursor: not-allowed;
    color: #BFBFBF;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:before, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:before, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:after, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:after, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:after, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:after {
    border-color: #c0c4cc;
}

.vxe-export--panel-column > ul {
    list-style-type: none;
    overflow: auto;
    margin: 0;
    padding: 0;
    user-select: none;
}
.vxe-export--panel-column > ul > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
}

.vxe-export--panel > table {
    width: 100%;
    border: 0;
    table-layout: fixed;
}
.vxe-export--panel > table tr td {
    padding: 0 10px;
}
.vxe-export--panel > table tr td:nth-child(1) {
    text-align: right;
    width: 30%;
    font-weight: 700;
    padding: 8px 10px;
}
.vxe-export--panel > table tr td:nth-child(2) {
    width: 70%;
}
.vxe-export--panel > table tr td > .vxe-input, .vxe-export--panel > table tr td > .vxe-select {
    width: 80%;
}
.vxe-export--panel > table tr td > .vxe-export--panel-option-row {
    padding: 0.25em 0;
}
.vxe-export--panel .vxe-export--panel-column {
    width: 80%;
    border: 1px solid #dcdfe6;
    margin: 3px 0;
    border-radius: 4px;
    user-select: none;
}
.vxe-export--panel .vxe-export--panel-column > ul > li {
    padding: 0.2em 1em 0.2em 2.3em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--2 {
    padding-left: 3.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--2 .vxe-checkbox--icon {
    left: 1.8em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--3 {
    padding-left: 4.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--3 .vxe-checkbox--icon {
    left: 2.8em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--4 {
    padding-left: 5.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--4 .vxe-checkbox--icon {
    left: 3.8em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--5 {
    padding-left: 6.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--5 .vxe-checkbox--icon {
    left: 4.8em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--6 {
    padding-left: 7.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--6 .vxe-checkbox--icon {
    left: 5.8em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--7 {
    padding-left: 8.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--7 .vxe-checkbox--icon {
    left: 6.8em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--8 {
    padding-left: 9.5em;
}
.vxe-export--panel .vxe-export--panel-column > ul > li.level--8 .vxe-checkbox--icon {
    left: 7.8em;
}
.vxe-export--panel .vxe-export--panel-column .vxe-export--panel-column-header {
    padding: 0.1em 0;
    background-color: #1f242d;
    font-weight: 700;
    border-bottom: 1px solid black;
}
.vxe-export--panel .vxe-export--panel-column .vxe-export--panel-column-body {
    padding: 0.2em 0;
    min-height: 10em;
    max-height: 17.6em;
}
.vxe-export--panel .vxe-import-selected--file {
    padding-right: 40px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;
}
.vxe-export--panel .vxe-import-selected--file > i {
    display: none;
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    font-size: 16px;
    cursor: pointer;
}
.vxe-export--panel .vxe-import-selected--file:hover {
    color: #409eff;
}
.vxe-export--panel .vxe-import-selected--file:hover > i {
    display: block;
}
.vxe-export--panel .vxe-import-select--file {
    border: 1px dashed #dcdfe6;
    padding: 6px 34px;
    outline: 0;
    border-radius: 4px;
    background-color: #fff;
    user-select: none;
    cursor: pointer;
}
.vxe-export--panel .vxe-import-select--file:focus {
    border-color: #409eff;
    box-shadow: 0 0 0.25em 0 #409eff;
}
.vxe-export--panel .vxe-import-select--file:hover {
    color: #409eff;
    border-color: #409eff;
}
.vxe-export--panel .vxe-export--panel-btns {
    text-align: right;
    padding: 0.25em;
}

.vxe-export--panel-column-option .vxe-checkbox--icon {
    left: 0.6em;
    top: 0.38em;
}
.vxe-export--panel-column-option:hover {
    background-color: #374c6b;
}

.vxe-modal--wrapper .vxe-export--panel-column-option > .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-modal--wrapper.size--medium .vxe-export--panel-column-option > .vxe-checkbox--icon {
    font-size: 15px;
}
.vxe-modal--wrapper.size--small .vxe-export--panel-column-option > .vxe-checkbox--icon {
    font-size: 14px;
}
.vxe-modal--wrapper.size--mini .vxe-export--panel-column-option > .vxe-checkbox--icon {
    font-size: 14px;
}

/**Variable**/
.vxe-grid {
    position: relative;
}
.vxe-grid.is--loading:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
}
.vxe-grid.is--loading > .vxe-table .vxe-loading {
    background-color: transparent;
}
.vxe-grid.is--maximize {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0.5em 1em;
    background-color: #fff;
}
.vxe-grid .vxe-body--row.row--pending {
    color: #f56c6c;
    text-decoration: line-through;
    cursor: no-drop;
}
.vxe-grid .vxe-body--row.row--pending .vxe-body--column {
    position: relative;
}
.vxe-grid .vxe-body--row.row--pending .vxe-body--column:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0;
    border-bottom: 1px solid #f56c6c;
    z-index: 1;
}
.vxe-grid .vxe-grid--form-wrapper,
.vxe-grid .vxe-grid--top-wrapper,
.vxe-grid .vxe-grid--bottom-wrapper {
    position: relative;
}

.vxe-grid {
    font-size: 14px;
}
.vxe-grid.size--medium {
    font-size: 14px;
}
.vxe-grid.size--small {
    font-size: 13px;
}
.vxe-grid.size--mini {
    font-size: 12px;
}

/**Variable**/
.vxe-custom--option, .vxe-table--render-default .vxe-cell--radio, .vxe-table--render-default .vxe-cell--checkbox, .vxe-table--filter-option, .vxe-export--panel-column-option {
    position: relative;
    user-select: none;
    cursor: pointer;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--radio .vxe-radio--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon {
    display: none;
    position: absolute;
    height: 1em;
    width: 1em;
}

.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
    font-size: 16px;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    border: 2px solid #dcdfe6;
    background-color: #fff;
    border-radius: 50%;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--unchecked-icon {
    display: inline-block;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.25em;
    width: 0.25em;
    top: 0.4em;
    left: 0.4em;
    border-radius: 50%;
    background-color: #fff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio {
    color: #409eff;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--unchecked-icon {
    display: none;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon {
    display: inline-block;
}
.vxe-table--render-default .is--checked.vxe-cell--radio .vxe-radio--checked-icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon:before {
    border-color: #409eff;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio {
    cursor: not-allowed;
    color: #BFBFBF;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-table--render-default .is--disabled.vxe-cell--radio .vxe-radio--icon:after {
    background-color: #c0c4cc;
}

.vxe-custom--option .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon, .vxe-table--filter-option .vxe-checkbox--icon, .vxe-export--panel-column-option .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-custom--option .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon:before, .vxe-table--filter-option .vxe-checkbox--icon:before, .vxe-export--panel-column-option .vxe-checkbox--icon:before {
    content: "";
    position: absolute;
    height: 1em;
    width: 1em;
    top: 0;
    left: 0;
    background-color: #fff;
    border-radius: 2px;
    border: 2px solid #dcdfe6;
}
.vxe-custom--option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .vxe-table--filter-option .vxe-checkbox--unchecked-icon, .vxe-export--panel-column-option .vxe-checkbox--unchecked-icon {
    display: inline-block;
}
.vxe-custom--option .vxe-checkbox--checked-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--checked-icon:after, .vxe-table--filter-option .vxe-checkbox--checked-icon:after, .vxe-export--panel-column-option .vxe-checkbox--checked-icon:after {
    content: "";
    position: absolute;
    height: 0.64em;
    width: 0.32em;
    top: 50%;
    left: 50%;
    border: 2px solid #fff;
    border-left: 0;
    border-top: 0;
    transform: translate(-50%, -50%) rotate(45deg);
}
.vxe-custom--option .vxe-checkbox--indeterminate-icon:after, .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--indeterminate-icon:after, .vxe-table--filter-option .vxe-checkbox--indeterminate-icon:after, .vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    height: 2px;
    width: 0.6em;
    background-color: #fff;
    transform: translate(-50%, -50%);
}
.is--checked.vxe-custom--option, .vxe-table--render-default .is--checked.vxe-cell--checkbox, .is--checked.vxe-table--filter-option, .is--checked.vxe-export--panel-column-option, .is--indeterminate.vxe-custom--option, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox, .is--indeterminate.vxe-table--filter-option, .is--indeterminate.vxe-export--panel-column-option {
    color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-custom--option .vxe-checkbox--unchecked-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--unchecked-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--unchecked-icon {
    display: none;
}
.is--checked.vxe-custom--option .vxe-checkbox--icon:before, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--checked.vxe-table--filter-option .vxe-checkbox--icon:before, .is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before, .is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before {
    border-color: #409eff;
    background-color: #409eff;
}
.is--checked.vxe-custom--option .vxe-checkbox--checked-icon, .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--checked-icon, .is--checked.vxe-table--filter-option .vxe-checkbox--checked-icon, .is--checked.vxe-export--panel-column-option .vxe-checkbox--checked-icon {
    display: inline-block;
}
.is--indeterminate.vxe-custom--option .vxe-checkbox--indeterminate-icon, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-table--filter-option .vxe-checkbox--indeterminate-icon, .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--indeterminate-icon {
    display: inline-block;
}
.vxe-custom--option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon:before, .vxe-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon:before {
    border-color: #409eff;
}
.is--disabled.vxe-custom--option, .vxe-table--render-default .is--disabled.vxe-cell--checkbox, .is--disabled.vxe-table--filter-option, .is--disabled.vxe-export--panel-column-option {
    cursor: not-allowed;
    color: #BFBFBF;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:before, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:before, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:before, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:before {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.is--disabled.vxe-custom--option .vxe-checkbox--icon:after, .vxe-table--render-default .is--disabled.vxe-cell--checkbox .vxe-checkbox--icon:after, .is--disabled.vxe-table--filter-option .vxe-checkbox--icon:after, .is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon:after {
    border-color: #c0c4cc;
}

/*toolbar*/
.vxe-toolbar {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    background-color: #fff;
}
.vxe-toolbar:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.vxe-toolbar.is--perfect {
    border: 1px solid black;
    border-bottom-width: 0;
    background-color: #1f242d;
}
.vxe-toolbar.is--loading:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
}
.vxe-toolbar .vxe-buttons--wrapper {
    flex-grow: 1;
}
.vxe-toolbar .vxe-buttons--wrapper > .vxe-button + .vxe-button--item, .vxe-toolbar .vxe-buttons--wrapper > .vxe-button--item + .vxe-button, .vxe-toolbar .vxe-buttons--wrapper > .vxe-button--item + .vxe-button--item {
    margin-left: 0.8em;
}
.vxe-toolbar .vxe-buttons--wrapper > .vxe-button--item {
    display: inline-block;
}
.vxe-toolbar .vxe-tools--wrapper > .vxe-button + .vxe-tool--item, .vxe-toolbar .vxe-tools--wrapper > .vxe-tool--item + .vxe-button, .vxe-toolbar .vxe-tools--wrapper > .vxe-tool--item + .vxe-tool--item {
    margin-left: 0.8em;
}
.vxe-toolbar .vxe-tools--wrapper > .vxe-tool--item {
    display: inline-block;
}
.vxe-toolbar .vxe-tools--wrapper > .vxe-button {
    display: flex;
    align-items: center;
}
.vxe-toolbar .vxe-tools--wrapper,
.vxe-toolbar .vxe-tools--operate {
    display: flex;
    flex-shrink: 0;
    align-items: center;
}
.vxe-toolbar .vxe-custom--wrapper {
    position: relative;
    margin-left: 0.8em;
}
.vxe-toolbar .vxe-custom--wrapper.is--active > .vxe-button {
    background-color: #D9DADB;
    border-radius: 50%;
}
.vxe-toolbar .vxe-custom--wrapper.is--active .vxe-custom--option-wrapper {
    display: block;
}
.vxe-toolbar .vxe-custom--option-wrapper {
    display: none;
    position: absolute;
    right: 2px;
    text-align: left;
    background-color: #fff;
    z-index: 19;
    border: 1px solid black;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header {
    font-weight: 700;
    border-bottom: 1px solid #DADCE0;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body .vxe-custom--option:hover {
    background-color: #374c6b;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body {
    padding: 0.2em 0;
    max-height: 17.6em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li {
    max-width: 16em;
    min-width: 10em;
    padding: 0.2em 1em 0.2em 2.3em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--2,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--2 {
    padding-left: 3.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--2 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--2 .vxe-checkbox--icon {
    left: 1.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--3,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--3 {
    padding-left: 4.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--3 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--3 .vxe-checkbox--icon {
    left: 2.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--4,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--4 {
    padding-left: 5.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--4 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--4 .vxe-checkbox--icon {
    left: 3.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--5,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--5 {
    padding-left: 6.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--5 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--5 .vxe-checkbox--icon {
    left: 4.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--6,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--6 {
    padding-left: 7.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--6 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--6 .vxe-checkbox--icon {
    left: 5.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--7,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--7 {
    padding-left: 8.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--7 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--7 .vxe-checkbox--icon {
    left: 6.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--8,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--8 {
    padding-left: 9.5em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--header > li.level--8 .vxe-checkbox--icon,
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--body > li.level--8 .vxe-checkbox--icon {
    left: 7.8em;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--footer {
    border-top: 1px solid #DADCE0;
    padding: 0.45em 0.4em;
    text-align: right;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--footer button {
    background-color: transparent;
    width: 50%;
    border: 0;
    color: #606266;
    text-align: center;
    cursor: pointer;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--footer button:focus {
    outline: none;
}
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--footer button:hover {
    color: #409eff;
}

.vxe-custom--option-wrapper .vxe-custom--header,
.vxe-custom--option-wrapper .vxe-custom--body {
    list-style-type: none;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    user-select: none;
}
.vxe-custom--option-wrapper .vxe-custom--header > li,
.vxe-custom--option-wrapper .vxe-custom--body > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.vxe-custom--option .vxe-checkbox--icon {
    left: 0.6em;
    top: 0.38em;
}

.vxe-toolbar {
    font-size: 14px;
    height: 52px;
}
.vxe-toolbar .vxe-custom--option > .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-toolbar.size--medium {
    font-size: 14px;
    height: 50px;
}
.vxe-toolbar.size--medium .vxe-custom--option > .vxe-checkbox--icon {
    font-size: 15px;
}
.vxe-toolbar.size--small {
    font-size: 13px;
    height: 48px;
}
.vxe-toolbar.size--small .vxe-custom--option > .vxe-checkbox--icon {
    font-size: 14px;
}
.vxe-toolbar.size--mini {
    font-size: 12px;
    height: 46px;
}
.vxe-toolbar.size--mini .vxe-custom--option > .vxe-checkbox--icon {
    font-size: 14px;
}

/**Variable**/
/*pager*/
.vxe-pager {
    position: relative;
    display: flex;
    align-items: center;
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    text-align: right;
    background-color: #fff;
}
.vxe-pager.is--hidden {
    display: none;
}
.vxe-pager.align--left {
    text-align: left;
}
.vxe-pager.align--center {
    text-align: center;
}
.vxe-pager.is--loading:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
}
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--jump-next {
    color: inherit;
    outline: 0;
    border: 1px solid transparent;
}
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):focus {
    box-shadow: 0 0 0.25em 0 #409eff;
}
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):hover {
    color: #5faeff;
}
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):active {
    background-color: #f7f7f7;
}
.vxe-pager.is--border:not(.is--background) .vxe-pager--prev-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--next-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--num-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--jump-prev,
.vxe-pager.is--border:not(.is--background) .vxe-pager--jump-next, .vxe-pager.is--perfect:not(.is--background) .vxe-pager--prev-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--next-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--num-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--jump-prev,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--jump-next {
    border-color: #dcdfe6;
}
.vxe-pager.is--background .vxe-pager--prev-btn,
.vxe-pager.is--background .vxe-pager--next-btn,
.vxe-pager.is--background .vxe-pager--jump-prev,
.vxe-pager.is--background .vxe-pager--num-btn,
.vxe-pager.is--background .vxe-pager--jump-next, .vxe-pager.is--perfect .vxe-pager--prev-btn,
.vxe-pager.is--perfect .vxe-pager--next-btn,
.vxe-pager.is--perfect .vxe-pager--jump-prev,
.vxe-pager.is--perfect .vxe-pager--num-btn,
.vxe-pager.is--perfect .vxe-pager--jump-next {
    background-color: #f4f4f5;
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active {
    color: #fff;
    background-color: #409eff;
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active:hover,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active:hover,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active:hover, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active:hover,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active:hover,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active:hover {
    background-color: #5faeff;
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active:focus,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active:focus,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active:focus, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active:focus,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active:focus,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active:focus {
    border-color: #409eff;
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active:active,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active:active,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active:active, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active:active,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active:active,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active:active {
    border-color: #3196ff;
    background-color: #3196ff;
}
.vxe-pager.is--perfect {
    border: 1px solid black;
    border-top-width: 0;
    background-color: #1f242d;
}
.vxe-pager.is--perfect .vxe-pager--prev-btn,
.vxe-pager.is--perfect .vxe-pager--next-btn,
.vxe-pager.is--perfect .vxe-pager--jump-prev,
.vxe-pager.is--perfect .vxe-pager--num-btn,
.vxe-pager.is--perfect .vxe-pager--jump-next {
    background-color: #fff;
}
.vxe-pager.is--border .vxe-pager--num-btn.is--active {
    border-color: #409eff;
}
.vxe-pager .vxe-pager--wrapper {
    flex-grow: 1;
}
.vxe-pager .vxe-pager--jump-icon,
.vxe-pager .vxe-pager--btn-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.vxe-pager .vxe-pager--left-wrapper,
.vxe-pager .vxe-pager--right-wrapper,
.vxe-pager .vxe-pager--total,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--count,
.vxe-pager .vxe-pager--sizes {
    margin: 0 0.4em;
    vertical-align: middle;
    display: inline-block;
}
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--next-btn {
    position: relative;
    cursor: pointer;
}
.vxe-pager .vxe-pager--left-wrapper,
.vxe-pager .vxe-pager--right-wrapper,
.vxe-pager .vxe-pager--count,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--jump-next {
    height: 2.15em;
    line-height: 2em;
    display: inline-block;
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto,
.vxe-pager .vxe-pager--sizes > .vxe-input {
    height: 2.15em;
}
.vxe-pager .vxe-pager--sizes > .vxe-select--panel .vxe-select-option {
    text-align: center;
}
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--count {
    min-width: 2.15em;
}
.vxe-pager .vxe-pager--btn-wrapper {
    padding: 0;
    margin: 0;
    display: inline-block;
    text-align: center;
}
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-prev:hover .vxe-pager--jump-more-icon,
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-next:hover .vxe-pager--jump-more-icon {
    display: none;
}
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-prev:hover .vxe-pager--jump-icon,
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-next:hover .vxe-pager--jump-icon {
    display: inline-block;
}
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-icon {
    display: none;
}
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--num-btn {
    text-align: center;
    border-radius: 4px;
    margin: 0 0.25em;
    user-select: none;
    background-color: #fff;
}
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):hover,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):hover,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):hover {
    color: #5faeff;
}
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled).is--active, .vxe-pager .vxe-pager--jump-prev:not(.is--disabled):focus,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):focus,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):focus {
    color: #409eff;
}
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):active {
    color: #3196ff;
}
.vxe-pager .vxe-pager--jump-prev.is--disabled,
.vxe-pager .vxe-pager--prev-btn.is--disabled,
.vxe-pager .vxe-pager--next-btn.is--disabled,
.vxe-pager .vxe-pager--jump-next.is--disabled,
.vxe-pager .vxe-pager--num-btn.is--disabled {
    cursor: no-drop;
    color: #BFBFBF;
}
.vxe-pager .vxe-pager--jump-prev.is--disabled:hover,
.vxe-pager .vxe-pager--prev-btn.is--disabled:hover,
.vxe-pager .vxe-pager--next-btn.is--disabled:hover,
.vxe-pager .vxe-pager--jump-next.is--disabled:hover,
.vxe-pager .vxe-pager--num-btn.is--disabled:hover {
    color: #BFBFBF;
}
.vxe-pager .vxe-pager--num-btn {
    vertical-align: middle;
}
.vxe-pager .vxe-pager--num-btn.is--active {
    font-weight: 700;
}
.vxe-pager .vxe-pager--sizes {
    width: 7em;
    text-align: center;
    cursor: pointer;
}
.vxe-pager .vxe-pager--sizes .vxe-input--inner {
    text-align: center;
}
.vxe-pager .vxe-pager--count {
    text-align: center;
}
.vxe-pager .vxe-pager--count > span {
    vertical-align: middle;
}
.vxe-pager .vxe-pager--count .vxe-pager--separator {
    margin-right: 0.2em;
}
.vxe-pager .vxe-pager--count .vxe-pager--separator:before {
    content: "/";
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    color: #606266;
    transition: border 0.2s ease-in-out;
    padding: 0 0.4em;
    background-color: #fff;
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto:focus {
    border: 1px solid #409eff;
    outline: 0;
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto-text {
    margin-right: 0.25em;
}
.vxe-pager .vxe-pager--jump .vxe-pager--classifier-text {
    margin-left: 0.25em;
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto {
    width: 3.2em;
    text-align: center;
}

.vxe-pager {
    font-size: 14px;
    height: 48px;
}
.vxe-pager.size--medium {
    font-size: 14px;
    height: 44px;
}
.vxe-pager.size--small {
    font-size: 13px;
    height: 40px;
}
.vxe-pager.size--mini {
    font-size: 12px;
    height: 36px;
}

/**Variable**/
[class*=vxe-], [class*=vxe-]:after, [class*=vxe-]:before,
[class*=vxe-] *:after, [class*=vxe-] *:before {
    box-sizing: border-box;
}

.vxe-radio-button .vxe-radio--label, .vxe-radio .vxe-radio--label, .vxe-checkbox .vxe-checkbox--label, .vxe-table--render-default .vxe-header--column.col--ellipsis:not(.col--actived) > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis:not(.col--actived) > .vxe-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

[class*=vxe-] {
    font-variant: tabular-nums;
    font-feature-settings: "tnum";
}
.vxe-primary-color {
    color: #409eff;
}

.vxe-success-color {
    color: #67c23a;
}

.vxe-info-color {
    color: #909399;
}

.vxe-warning-color {
    color: #e6a23c;
}

.vxe-danger-color {
    color: #f56c6c;
}

.vxe-perfect-color {
    color: #1f242d;
}

.vxe-row:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.vxe-row > .vxe-col--1 {
    float: left;
    width: 4.16667%;
}
.vxe-row > .vxe-col--2 {
    float: left;
    width: 8.33333%;
}
.vxe-row > .vxe-col--3 {
    float: left;
    width: 12.5%;
}
.vxe-row > .vxe-col--4 {
    float: left;
    width: 16.66667%;
}
.vxe-row > .vxe-col--5 {
    float: left;
    width: 20.83333%;
}
.vxe-row > .vxe-col--6 {
    float: left;
    width: 25%;
}
.vxe-row > .vxe-col--7 {
    float: left;
    width: 29.16667%;
}
.vxe-row > .vxe-col--8 {
    float: left;
    width: 33.33333%;
}
.vxe-row > .vxe-col--9 {
    float: left;
    width: 37.5%;
}
.vxe-row > .vxe-col--10 {
    float: left;
    width: 41.66667%;
}
.vxe-row > .vxe-col--11 {
    float: left;
    width: 45.83333%;
}
.vxe-row > .vxe-col--12 {
    float: left;
    width: 50%;
}
.vxe-row > .vxe-col--13 {
    float: left;
    width: 54.16667%;
}
.vxe-row > .vxe-col--14 {
    float: left;
    width: 58.33333%;
}
.vxe-row > .vxe-col--15 {
    float: left;
    width: 62.5%;
}
.vxe-row > .vxe-col--16 {
    float: left;
    width: 66.66667%;
}
.vxe-row > .vxe-col--17 {
    float: left;
    width: 70.83333%;
}
.vxe-row > .vxe-col--18 {
    float: left;
    width: 75%;
}
.vxe-row > .vxe-col--19 {
    float: left;
    width: 79.16667%;
}
.vxe-row > .vxe-col--20 {
    float: left;
    width: 83.33333%;
}
.vxe-row > .vxe-col--21 {
    float: left;
    width: 87.5%;
}
.vxe-row > .vxe-col--22 {
    float: left;
    width: 91.66667%;
}
.vxe-row > .vxe-col--23 {
    float: left;
    width: 95.83333%;
}
.vxe-row > .vxe-col--24 {
    float: left;
    width: 100%;
}

/*animat*/
.is--animat .vxe-sort--asc-btn:before, .is--animat .vxe-sort--asc-btn:after,
.is--animat .vxe-sort--desc-btn:before,
.is--animat .vxe-sort--desc-btn:after,
.is--animat .vxe-filter--btn:before,
.is--animat .vxe-filter--btn:after {
    transition: border 0.1s ease-in-out;
}
.is--animat .vxe-input--wrapper .vxe-input {
    transition: border 0.1s ease-in-out;
}
.is--animat .vxe-table--expand-btn,
.is--animat .vxe-tree--node-btn {
    transition: transform 0.1s ease-in-out;
}
.is--animat .vxe-checkbox > input:checked + span,
.is--animat .vxe-radio > input:checked + span {
    transition: background-color 0.1s ease-in-out;
}

/*checkbox-group*/
.vxe-checkbox-group {
    display: inline-block;
    vertical-align: middle;
    line-height: 1;
}

/*checkbox*/
.vxe-checkbox {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    user-select: none;
    line-height: 1;
    cursor: pointer;
}
.vxe-checkbox + .vxe-checkbox {
    margin-left: 10px;
}
.vxe-checkbox > input[type=checkbox] {
    position: absolute;
    width: 0;
    height: 0;
    border: 0;
    appearance: none;
}
.vxe-checkbox > input + .vxe-checkbox--icon {
    position: relative;
    display: inline-block;
    width: 1em;
    height: 1em;
    background-color: #fff;
    vertical-align: middle;
    border-radius: 2px;
    border: 2px solid #dcdfe6;
}
.vxe-checkbox > input + .vxe-checkbox--icon:before {
    content: "";
    position: absolute;
}
.vxe-checkbox > input:checked + .vxe-checkbox--icon {
    background-color: #409eff;
    border-color: #409eff;
}
.vxe-checkbox > input:checked + .vxe-checkbox--icon:before {
    height: 0.64em;
    width: 0.32em;
    left: 50%;
    top: 50%;
    border: 2px solid #fff;
    border-left: 0;
    border-top: 0;
    transform: translate(-50%, -50%) rotate(45deg);
}
.vxe-checkbox > input:checked + .vxe-checkbox--icon + .vxe-checkbox--label {
    color: #409eff;
}
.vxe-checkbox.is--indeterminate > input:not(:checked) + .vxe-checkbox--icon {
    background-color: #409eff;
    border-color: #409eff;
}
.vxe-checkbox.is--indeterminate > input:not(:checked) + .vxe-checkbox--icon:before {
    border: 0;
    left: 50%;
    top: 50%;
    height: 2px;
    width: 0.6em;
    background-color: #fff;
    transform: translate(-50%, -50%);
}
.vxe-checkbox:not(.is--disabled) > input:focus + .vxe-checkbox--icon {
    border-color: #409eff;
    box-shadow: 0 0 0.2em 0 #409eff;
}
.vxe-checkbox:not(.is--disabled):hover > input + .vxe-checkbox--icon {
    border-color: #409eff;
}
.vxe-checkbox.is--disabled {
    cursor: not-allowed;
}
.vxe-checkbox.is--disabled > input + .vxe-checkbox--icon {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-checkbox.is--disabled > input + .vxe-checkbox--icon:before {
    border-color: #c0c4cc;
}
.vxe-checkbox.is--disabled > input + .vxe-checkbox--icon + .vxe-checkbox--label {
    color: #BFBFBF;
}
.vxe-checkbox.is--disabled > input:checked + .vxe-checkbox--icon {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-checkbox .vxe-checkbox--label {
    padding-left: 0.5em;
    vertical-align: middle;
    display: inline-block;
    max-width: 50em;
}

.vxe-checkbox {
    font-size: 14px;
}
.vxe-checkbox .vxe-checkbox--icon {
    font-size: 16px;
}
.vxe-checkbox.size--medium {
    font-size: 14px;
}
.vxe-checkbox.size--medium .vxe-checkbox--icon {
    font-size: 15px;
}
.vxe-checkbox.size--small {
    font-size: 13px;
}
.vxe-checkbox.size--small .vxe-checkbox--icon {
    font-size: 14px;
}
.vxe-checkbox.size--mini {
    font-size: 12px;
}
.vxe-checkbox.size--mini .vxe-checkbox--icon {
    font-size: 14px;
}

/**Variable**/
/**Variable**/
[class*=vxe-], [class*=vxe-]:after, [class*=vxe-]:before,
[class*=vxe-] *:after, [class*=vxe-] *:before {
    box-sizing: border-box;
}

.vxe-radio-button .vxe-radio--label, .vxe-radio .vxe-radio--label, .vxe-table--render-default .vxe-header--column.col--ellipsis:not(.col--actived) > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis:not(.col--actived) > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis:not(.col--actived) > .vxe-cell, .vxe-checkbox .vxe-checkbox--label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

[class*=vxe-] {
    font-variant: tabular-nums;
    font-feature-settings: "tnum";
}
.vxe-primary-color {
    color: #409eff;
}

.vxe-success-color {
    color: #67c23a;
}

.vxe-info-color {
    color: #909399;
}

.vxe-warning-color {
    color: #e6a23c;
}

.vxe-danger-color {
    color: #f56c6c;
}

.vxe-perfect-color {
    color: #1f242d;
}

.vxe-row:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.vxe-row > .vxe-col--1 {
    float: left;
    width: 4.16667%;
}
.vxe-row > .vxe-col--2 {
    float: left;
    width: 8.33333%;
}
.vxe-row > .vxe-col--3 {
    float: left;
    width: 12.5%;
}
.vxe-row > .vxe-col--4 {
    float: left;
    width: 16.66667%;
}
.vxe-row > .vxe-col--5 {
    float: left;
    width: 20.83333%;
}
.vxe-row > .vxe-col--6 {
    float: left;
    width: 25%;
}
.vxe-row > .vxe-col--7 {
    float: left;
    width: 29.16667%;
}
.vxe-row > .vxe-col--8 {
    float: left;
    width: 33.33333%;
}
.vxe-row > .vxe-col--9 {
    float: left;
    width: 37.5%;
}
.vxe-row > .vxe-col--10 {
    float: left;
    width: 41.66667%;
}
.vxe-row > .vxe-col--11 {
    float: left;
    width: 45.83333%;
}
.vxe-row > .vxe-col--12 {
    float: left;
    width: 50%;
}
.vxe-row > .vxe-col--13 {
    float: left;
    width: 54.16667%;
}
.vxe-row > .vxe-col--14 {
    float: left;
    width: 58.33333%;
}
.vxe-row > .vxe-col--15 {
    float: left;
    width: 62.5%;
}
.vxe-row > .vxe-col--16 {
    float: left;
    width: 66.66667%;
}
.vxe-row > .vxe-col--17 {
    float: left;
    width: 70.83333%;
}
.vxe-row > .vxe-col--18 {
    float: left;
    width: 75%;
}
.vxe-row > .vxe-col--19 {
    float: left;
    width: 79.16667%;
}
.vxe-row > .vxe-col--20 {
    float: left;
    width: 83.33333%;
}
.vxe-row > .vxe-col--21 {
    float: left;
    width: 87.5%;
}
.vxe-row > .vxe-col--22 {
    float: left;
    width: 91.66667%;
}
.vxe-row > .vxe-col--23 {
    float: left;
    width: 95.83333%;
}
.vxe-row > .vxe-col--24 {
    float: left;
    width: 100%;
}

/*animat*/
.is--animat .vxe-sort--asc-btn:before, .is--animat .vxe-sort--asc-btn:after,
.is--animat .vxe-sort--desc-btn:before,
.is--animat .vxe-sort--desc-btn:after,
.is--animat .vxe-filter--btn:before,
.is--animat .vxe-filter--btn:after {
    transition: border 0.1s ease-in-out;
}
.is--animat .vxe-input--wrapper .vxe-input {
    transition: border 0.1s ease-in-out;
}
.is--animat .vxe-table--expand-btn,
.is--animat .vxe-tree--node-btn {
    transition: transform 0.1s ease-in-out;
}
.is--animat .vxe-checkbox > input:checked + span,
.is--animat .vxe-radio > input:checked + span {
    transition: background-color 0.1s ease-in-out;
}

/*radio-group*/
.vxe-radio-group {
    display: inline-block;
    vertical-align: middle;
    line-height: 1;
    font-size: 0;
}
.vxe-radio-group + .vxe-radio-group {
    margin-left: 10px;
}

/*radio*/
.vxe-radio {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    user-select: none;
    line-height: 1;
    cursor: pointer;
}
.vxe-radio > input[type=radio] {
    position: absolute;
    width: 0;
    height: 0;
    border: 0;
    appearance: none;
}
.vxe-radio > input + .vxe-radio--icon {
    position: relative;
    display: inline-block;
    width: 1em;
    height: 1em;
    border: 2px solid #dcdfe6;
    background-color: #fff;
    vertical-align: middle;
    border-radius: 50%;
}
.vxe-radio > input:checked + .vxe-radio--icon {
    background-color: #409eff;
    border-color: #409eff;
}
.vxe-radio > input:checked + .vxe-radio--icon:before {
    content: "";
    position: absolute;
    background-color: #fff;
    border-radius: 50%;
    height: 0.25em;
    width: 0.25em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.vxe-radio > input:checked + .vxe-radio--icon + .vxe-radio--label {
    color: #409eff;
}
.vxe-radio:not(.is--disabled) > input:focus + .vxe-radio--icon {
    border-color: #409eff;
    box-shadow: 0 0 0.2em 0 #409eff;
}
.vxe-radio:not(.is--disabled):hover > input + .vxe-radio--icon {
    border-color: #409eff;
}
.vxe-radio.is--disabled {
    cursor: not-allowed;
}
.vxe-radio.is--disabled > input + .vxe-radio--icon {
    border-color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-radio.is--disabled > input + .vxe-radio--icon:before {
    border-color: #c0c4cc;
    background-color: #c0c4cc;
}
.vxe-radio.is--disabled > input + .vxe-radio--icon + .vxe-radio--label {
    color: #BFBFBF;
}
.vxe-radio .vxe-radio--label {
    padding-left: 0.5em;
    vertical-align: middle;
    display: inline-block;
    max-width: 50em;
}
.vxe-radio:not(.vxe-radio-button) + .vxe-radio {
    margin-left: 10px;
}

.vxe-radio-button .vxe-radio--label {
    background-color: #fff;
}
.vxe-radio-button:first-child .vxe-radio--label {
    border-left: 1px solid #dcdfe6;
    border-radius: 4px 0 0 4px;
}
.vxe-radio-button:last-child .vxe-radio--label {
    border-radius: 0 4px 4px 0;
}
.vxe-radio-button > input:checked + .vxe-radio--label {
    color: #fff;
    background-color: #409eff;
    border-color: #409eff;
}
.vxe-radio-button .vxe-radio--label {
    padding: 0 1em;
    line-height: 32px;
    display: inline-block;
    border-style: solid;
    border-color: #dcdfe6;
    border-width: 1px 1px 1px 0;
    max-width: 50em;
}
.vxe-radio-button.is--disabled {
    cursor: not-allowed;
}
.vxe-radio-button.is--disabled > input:not(:checked) + .vxe-radio--label {
    color: #dcdfe6;
}
.vxe-radio-button.is--disabled > input:checked + .vxe-radio--label {
    border-color: #a6d2ff;
    background-color: #a6d2ff;
}
.vxe-radio-button:not(.is--disabled) > input:focus + .vxe-radio--label {
    border-color: #409eff;
    box-shadow: 0 0 0.2em 0 #409eff;
}
.vxe-radio-button:not(.is--disabled):hover > input:not(:checked) + .vxe-radio--label {
    color: #409eff;
}
.vxe-radio-button.size--medium .vxe-radio--label {
    line-height: 30px;
}
.vxe-radio-button.size--small .vxe-radio--label {
    line-height: 28px;
}
.vxe-radio-button.size--mini .vxe-radio--label {
    line-height: 26px;
}

.vxe-radio {
    font-size: 14px;
}
.vxe-radio .vxe-radio--icon {
    font-size: 16px;
}
.vxe-radio.size--medium {
    font-size: 14px;
}
.vxe-radio.size--medium .vxe-radio--icon {
    font-size: 15px;
}
.vxe-radio.size--small {
    font-size: 13px;
}
.vxe-radio.size--small .vxe-radio--icon {
    font-size: 14px;
}
.vxe-radio.size--mini {
    font-size: 12px;
}
.vxe-radio.size--mini .vxe-radio--icon {
    font-size: 14px;
}

/**Variable**/
/**Variable**/
/**Variable**/
.vxe-input--inner {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    color: #606266;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    box-shadow: none;
}
.vxe-input--inner::placeholder {
    color: #C0C4CC;
}
.vxe-input--inner[type=number] {
    appearance: none;
    -moz-appearance: textfield;
}
.vxe-input--inner[type=search], .vxe-input--inner[type=search]::-webkit-search-cancel-button, .vxe-input--inner[type=number]::-webkit-outer-spin-button, .vxe-input--inner[type=number]::-webkit-inner-spin-button {
    appearance: none;
}
.vxe-input--inner[disabled] {
    cursor: not-allowed;
    color: #BFBFBF;
    background-color: #f3f3f3;
}

.vxe-input {
    display: inline-block;
    position: relative;
    width: 180px;
}
.vxe-input.is--disabled .vxe-input--date-picker-suffix,
.vxe-input.is--disabled .vxe-input--search-suffix,
.vxe-input.is--disabled .vxe-input--suffix,
.vxe-input.is--disabled .vxe-input--password-suffix,
.vxe-input.is--disabled .vxe-input--number-suffix {
    cursor: no-drop;
}
.vxe-input:not(.is--disabled) .vxe-input--search-suffix,
.vxe-input:not(.is--disabled) .vxe-input--clear-icon,
.vxe-input:not(.is--disabled) .vxe-input--password-suffix,
.vxe-input:not(.is--disabled) .vxe-input--number-suffix {
    cursor: pointer;
}
.vxe-input:not(.is--disabled).is--active .vxe-input--inner {
    border: 1px solid #409eff;
}
.vxe-input .vxe-input--prefix,
.vxe-input .vxe-input--suffix,
.vxe-input .vxe-input--extra-suffix {
    display: flex;
    position: absolute;
    top: 0;
    width: 1.6em;
    height: 100%;
    user-select: none;
    align-items: center;
    justify-content: center;
    color: #c0c4cc;
}

.vxe-input .vxe-input--prefix {
    left: 0.2em;
}
.vxe-input.is--prefix .vxe-input--inner {
    padding-left: 1.8em;
}

.vxe-input .vxe-input--clear-icon {
    display: none;
}
.vxe-input .vxe-input--suffix,
.vxe-input .vxe-input--extra-suffix {
    right: 0.2em;
}
.vxe-input.is--suffix .vxe-input--inner {
    padding-right: 1.8em;
}
.vxe-input.is--left .vxe-input--inner {
    text-align: left;
}
.vxe-input.is--center .vxe-input--inner {
    text-align: center;
}
.vxe-input.is--right .vxe-input--inner {
    text-align: right;
}
.vxe-input.is--controls.type--search .vxe-input--inner, .vxe-input.is--controls.type--password .vxe-input--inner, .vxe-input.is--controls.type--number .vxe-input--inner, .vxe-input.is--controls.type--integer .vxe-input--inner, .vxe-input.is--controls.type--float .vxe-input--inner, .vxe-input.is--controls.type--date .vxe-input--inner, .vxe-input.is--controls.type--datetime .vxe-input--inner, .vxe-input.is--controls.type--week .vxe-input--inner, .vxe-input.is--controls.type--month .vxe-input--inner, .vxe-input.is--controls.type--year .vxe-input--inner {
    padding-right: 1.8em;
}
.vxe-input.is--controls.type--search .vxe-input--suffix, .vxe-input.is--controls.type--password .vxe-input--suffix, .vxe-input.is--controls.type--number .vxe-input--suffix, .vxe-input.is--controls.type--integer .vxe-input--suffix, .vxe-input.is--controls.type--float .vxe-input--suffix, .vxe-input.is--controls.type--date .vxe-input--suffix, .vxe-input.is--controls.type--datetime .vxe-input--suffix, .vxe-input.is--controls.type--week .vxe-input--suffix, .vxe-input.is--controls.type--month .vxe-input--suffix, .vxe-input.is--controls.type--year .vxe-input--suffix {
    right: 1.6em;
}
.vxe-input.is--suffix.is--controls.type--search .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--password .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--number .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--integer .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--float .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--date .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--datetime .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--week .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--month .vxe-input--inner, .vxe-input.is--suffix.is--controls.type--year .vxe-input--inner {
    padding-right: 3.2em;
}
.vxe-input.is--suffix:hover .vxe-input--suffix.is--clear .vxe-input--suffix-icon {
    display: none;
}
.vxe-input.is--suffix:hover .vxe-input--suffix.is--clear .vxe-input--clear-icon {
    display: inline;
}
.vxe-input:not(.is--disabled) .vxe-input--suffix:hover .vxe-input--clear-icon {
    color: #606266;
}
.vxe-input:not(.is--disabled) .vxe-input--suffix:active .vxe-input--clear-icon {
    color: #409eff;
}
.vxe-input:not(.is--disabled) .vxe-input--extra-suffix:hover .vxe-input--search-suffix,
.vxe-input:not(.is--disabled) .vxe-input--extra-suffix:hover .vxe-input--password-suffix {
    color: #606266;
}
.vxe-input:not(.is--disabled) .vxe-input--extra-suffix:active .vxe-input--search-suffix,
.vxe-input:not(.is--disabled) .vxe-input--extra-suffix:active .vxe-input--password-suffix {
    color: #409eff;
}
.vxe-input:not(.is--disabled) .vxe-input--number-prev:hover,
.vxe-input:not(.is--disabled) .vxe-input--number-next:hover {
    color: #606266;
}
.vxe-input:not(.is--disabled) .vxe-input--number-prev:active,
.vxe-input:not(.is--disabled) .vxe-input--number-next:active {
    color: #409eff;
}

.vxe-input .vxe-input--password-suffix,
.vxe-input .vxe-input--number-suffix,
.vxe-input .vxe-input--date-picker-suffix,
.vxe-input .vxe-input--search-suffix {
    position: relative;
    width: 100%;
    height: 100%;
}
.vxe-input .vxe-input--date-picker-icon,
.vxe-input .vxe-input--search-icon,
.vxe-input .vxe-input--password-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.vxe-input .vxe-input--date-picker-suffix {
    display: flex;
    align-items: center;
    justify-content: center;
}
.vxe-input .vxe-input--date-picker-suffix .vxe-input--panel-icon {
    transition: transform 0.2s ease-in-out;
}
.vxe-input .vxe-input--number-prev,
.vxe-input .vxe-input--number-next {
    position: relative;
    display: block;
    height: 50%;
    width: 100%;
    text-align: center;
}
.vxe-input .vxe-input--number-prev-icon,
.vxe-input .vxe-input--number-next-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}
.vxe-input .vxe-input--number-prev-icon {
    bottom: 0;
}
.vxe-input .vxe-input--number-next-icon {
    top: 0;
}

.vxe-input--panel {
    display: none;
    position: absolute;
    left: 0;
    padding: 4px 0;
    color: #606266;
    font-size: 14px;
    text-align: left;
}
.vxe-input--panel:not(.is--transfer) {
    min-width: 100%;
}
.vxe-input--panel.is--transfer {
    position: fixed;
}
.vxe-input--panel.animat--leave {
    display: block;
    opacity: 0;
    transform: scaleY(0.5);
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: center top;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}
.vxe-input--panel.animat--leave[placement=top] {
    transform-origin: center bottom;
}
.vxe-input--panel.animat--enter {
    opacity: 1;
    transform: scaleY(1);
}

.vxe-input--panel-wrapper,
.vxe-input--panel-layout-wrapper {
    background-color: #fff;
    border: 1px solid #DADCE0;
    box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.vxe-input--panel-wrapper {
    overflow-x: hidden;
    overflow-y: auto;
}

.vxe-input--panel-layout-wrapper {
    display: flex;
    flex-direction: row;
}

.vxe-input--panel.type--date, .vxe-input--panel.type--week, .vxe-input--panel.type--month, .vxe-input--panel.type--year {
    user-select: none;
}
.vxe-input--panel.type--datetime .vxe-input--panel-right-wrapper {
    display: flex;
    flex-direction: column;
    border-left: 1px solid #dcdfe6;
}
.vxe-input--panel.type--date .vxe-input--date-picker-body th, .vxe-input--panel.type--datetime .vxe-input--date-picker-body th {
    width: 14.28571%;
}
.vxe-input--panel.type--week .vxe-input--date-picker-body table th {
    width: 12%;
}
.vxe-input--panel.type--week .vxe-input--date-picker-body table th:first-child {
    width: 14%;
}
.vxe-input--panel.type--month .vxe-input--date-picker-body td, .vxe-input--panel.type--year .vxe-input--date-picker-body td {
    width: 25%;
}

.vxe-input--time-picker-title {
    display: inline-block;
    text-align: center;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

.vxe-input--time-picker-confirm {
    position: absolute;
    right: 0;
    top: 0;
    outline: 0;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    color: #fff;
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-input--time-picker-confirm:hover {
    background-color: #5faeff;
    border-color: #5faeff;
}
.vxe-input--time-picker-confirm:active {
    background-color: #3196ff;
    border-color: #3196ff;
}

.vxe-input--time-picker-header {
    display: flex;
    position: relative;
    flex-shrink: 0;
}

.vxe-input--date-picker-header {
    display: flex;
    flex-direction: row;
    user-select: none;
}
.vxe-input--date-picker-header .vxe-input--date-picker-type-wrapper {
    flex-grow: 1;
}
.vxe-input--date-picker-header .vxe-input--date-picker-btn-wrapper {
    flex-shrink: 0;
    text-align: center;
}

.vxe-input--date-picker-type-wrapper .vxe-input--date-picker-label,
.vxe-input--date-picker-type-wrapper .vxe-input--date-picker-btn {
    display: inline-block;
}

.vxe-input--date-picker-btn-wrapper {
    display: flex;
    flex-direction: row;
}

.vxe-input--date-picker-label,
.vxe-input--date-picker-btn {
    display: inline-block;
    display: flex;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    align-items: center;
    justify-content: center;
    background-color: #fff;
}

.vxe-input--date-picker-btn.is--disabled {
    color: #BFBFBF;
    cursor: no-drop;
}
.vxe-input--date-picker-btn:not(.is--disabled) {
    cursor: pointer;
}
.vxe-input--date-picker-btn:not(.is--disabled):hover {
    background-color: white;
}
.vxe-input--date-picker-btn:not(.is--disabled):active {
    background-color: #f7f7f7;
}

.vxe-input--date-picker-body {
    border-radius: 4px;
    border: 1px solid #DADCE0;
    user-select: none;
}
.vxe-input--date-picker-body table {
    border: 0;
    width: 100%;
    border-spacing: 0;
    border-collapse: separate;
    text-align: center;
    table-layout: fixed;
}
.vxe-input--date-picker-body th,
.vxe-input--date-picker-body td {
    font-weight: normal;
}
.vxe-input--date-picker-body th {
    box-shadow: inset 0 -1px 0 0 #DADCE0;
}
.vxe-input--date-picker-body td.is--prev, .vxe-input--date-picker-body td.is--next {
    color: #BFBFBF;
}
.vxe-input--date-picker-body td.is--prev .vxe-input--date-label,
.vxe-input--date-picker-body td.is--prev .vxe-input--date-festival, .vxe-input--date-picker-body td.is--next .vxe-input--date-label,
.vxe-input--date-picker-body td.is--next .vxe-input--date-festival {
    color: #BFBFBF;
}
.vxe-input--date-picker-body td.is--now {
    box-shadow: inset 0 0 0 1px #DADCE0;
}
.vxe-input--date-picker-body td.is--now:not(.is--selected).is--current {
    color: #409eff;
}
.vxe-input--date-picker-body td.is--now:not(.is--selected).is--current .vxe-input--date-label,
.vxe-input--date-picker-body td.is--now:not(.is--selected).is--current .vxe-input--date-festival {
    color: #409eff;
}
.vxe-input--date-picker-body td.is--hover {
    background-color: #f2f6fc;
}
.vxe-input--date-picker-body td.is--selected {
    color: #fff;
    background-color: #409eff;
}
.vxe-input--date-picker-body td.is--selected .vxe-input--date-label,
.vxe-input--date-picker-body td.is--selected .vxe-input--date-festival {
    color: #fff;
}
.vxe-input--date-picker-body td.is--selected .vxe-input--date-label.is-notice:before {
    background-color: #fff;
}
.vxe-input--date-picker-body td:not(.is--disabled) {
    cursor: pointer;
}
.vxe-input--date-picker-body td.is--disabled {
    cursor: no-drop;
    color: #dcdfe6;
    background-color: #f3f3f3;
}
.vxe-input--date-picker-body td.is--disabled .vxe-input--date-label,
.vxe-input--date-picker-body td.is--disabled .vxe-input--date-festival {
    color: #dcdfe6;
}

.vxe-input--date-week-view th:first-child {
    box-shadow: inset -1px -1px 0 0 #DADCE0;
}
.vxe-input--date-week-view td:first-child {
    box-shadow: inset -1px 0 0 0 #DADCE0;
}

.vxe-input--date-label,
.vxe-input--date-festival {
    display: block;
    overflow: hidden;
}

.vxe-input--date-label {
    position: relative;
    padding-top: 8%;
}
.vxe-input--date-label.is-notice:before {
    content: "";
    position: absolute;
    width: 4px;
    height: 4px;
    left: 0.8em;
    top: 0.3em;
    transform: translateX(-50%);
    border-radius: 100%;
    background-color: #FF0000;
}

.vxe-input--date-label--extra {
    position: absolute;
    right: 0.1em;
    top: 0;
    font-size: 12px;
    line-height: 12px;
    transform: scale(0.7);
    color: #67c23a;
}
.vxe-input--date-label--extra.is-important {
    color: #fd2222;
}

.vxe-input--date-festival {
    color: #999999;
    height: 14px;
    line-height: 1;
    overflow: hidden;
}
.vxe-input--date-festival.is-important {
    color: #409eff;
}

.vxe-input--date-festival--label {
    display: block;
    font-size: 12px;
    transform: scale(0.9);
}

@keyframes festivalOverlap2 {
    0%, 45%, 100% {
        transform: translateY(0);
    }
    50%, 95% {
        transform: translateY(-14px);
    }
}
@keyframes festivalOverlap3 {
    0%, 20%, 100% {
        transform: translateY(0);
    }
    25%, 45%, 75%, 95% {
        transform: translateY(-14px);
    }
    50%, 70% {
        transform: translateY(-28px);
    }
}
.vxe-input--date-festival--overlap {
    display: block;
    font-size: 12px;
}
.vxe-input--date-festival--overlap.overlap--2 {
    animation: festivalOverlap2 6s infinite ease-in-out;
}
.vxe-input--date-festival--overlap.overlap--3 {
    animation: festivalOverlap3 9s infinite ease-in-out;
}
.vxe-input--date-festival--overlap > span {
    height: 14px;
    display: block;
    transform: scale(0.9);
}

.vxe-input--time-picker-body {
    position: relative;
    display: flex;
    flex-direction: row;
    border: 1px solid #DADCE0;
    flex-grow: 1;
    border-radius: 4px;
    user-select: none;
}
.vxe-input--time-picker-body > ul {
    height: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
}
.vxe-input--time-picker-body > ul:before, .vxe-input--time-picker-body > ul:after {
    content: " ";
    display: block;
}
.vxe-input--time-picker-body > ul:hover {
    overflow-y: auto;
}
.vxe-input--time-picker-body > ul > li {
    display: block;
}
.vxe-input--time-picker-body > ul > li:hover {
    background-color: #f2f6fc;
    cursor: pointer;
}
.vxe-input--time-picker-body > ul > li.is--selected {
    font-weight: 700;
    color: #409eff;
}
.vxe-input--time-picker-body .vxe-input--time-picker-minute-list {
    border-left: 1px solid #DADCE0;
}
.vxe-input--time-picker-body .vxe-input--time-picker-second-list {
    border-left: 1px solid #DADCE0;
}

.vxe-input {
    font-size: 14px;
    height: 34px;
}
.vxe-input .vxe-input--inner[type=date]::-webkit-inner-spin-button, .vxe-input .vxe-input--inner[type=month]::-webkit-inner-spin-button, .vxe-input .vxe-input--inner[type=week]::-webkit-inner-spin-button {
    margin-top: 6px;
}
.vxe-input .vxe-input--inner[type=date]::-webkit-inner-spin-button, .vxe-input .vxe-input--inner[type=month]::-webkit-inner-spin-button, .vxe-input .vxe-input--inner[type=week]::-webkit-inner-spin-button, .vxe-input .vxe-input--inner[type=number]::-webkit-inner-spin-button {
    height: 24px;
}
.vxe-input.size--medium {
    font-size: 14px;
    height: 32px;
}
.vxe-input.size--medium .vxe-input--inner[type=date]::-webkit-inner-spin-button, .vxe-input.size--medium .vxe-input--inner[type=month]::-webkit-inner-spin-button, .vxe-input.size--medium .vxe-input--inner[type=week]::-webkit-inner-spin-button {
    margin-top: 4px;
}
.vxe-input.size--small {
    font-size: 13px;
    height: 30px;
}
.vxe-input.size--small .vxe-input--inner[type=date]::-webkit-inner-spin-button, .vxe-input.size--small .vxe-input--inner[type=month]::-webkit-inner-spin-button, .vxe-input.size--small .vxe-input--inner[type=week]::-webkit-inner-spin-button {
    margin-top: 2px;
}
.vxe-input.size--mini {
    font-size: 12px;
    height: 28px;
}
.vxe-input.size--mini .vxe-input--inner[type=date]::-webkit-inner-spin-button, .vxe-input.size--mini .vxe-input--inner[type=month]::-webkit-inner-spin-button, .vxe-input.size--mini .vxe-input--inner[type=week]::-webkit-inner-spin-button {
    margin-top: 0;
}

.vxe-input--panel {
    font-size: 14px;
}
.vxe-input--panel .vxe-input--panel-wrapper {
    max-height: 380px;
}
.vxe-input--panel.type--date .vxe-input--panel-wrapper, .vxe-input--panel.type--time .vxe-input--panel-wrapper, .vxe-input--panel.type--week .vxe-input--panel-wrapper, .vxe-input--panel.type--month .vxe-input--panel-wrapper, .vxe-input--panel.type--year .vxe-input--panel-wrapper {
    padding: 11px;
}
.vxe-input--panel.type--date .vxe-input--panel-wrapper, .vxe-input--panel.type--month .vxe-input--panel-wrapper, .vxe-input--panel.type--year .vxe-input--panel-wrapper {
    width: 336px;
}
.vxe-input--panel.type--week .vxe-input--panel-wrapper {
    width: 380px;
}
.vxe-input--panel.type--time .vxe-input--panel-wrapper {
    width: 170px;
}
.vxe-input--panel.type--datetime .vxe-input--panel-left-wrapper {
    width: 336px;
}
.vxe-input--panel.type--datetime .vxe-input--panel-left-wrapper,
.vxe-input--panel.type--datetime .vxe-input--panel-right-wrapper {
    padding: 11px;
}
.vxe-input--panel .vxe-input--time-picker-title {
    height: 30px;
    line-height: 30px;
    padding: 0 11px;
}
.vxe-input--panel .vxe-input--date-picker-label,
.vxe-input--panel .vxe-input--date-picker-btn {
    height: 30px;
    line-height: 30px;
}
.vxe-input--panel .vxe-input--date-picker-btn-wrapper .vxe-input--date-picker-btn {
    width: 30px;
    margin-left: 8px;
}
.vxe-input--panel .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-label,
.vxe-input--panel .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-btn {
    padding: 0 9px;
}
.vxe-input--panel .vxe-input--time-picker-header,
.vxe-input--panel .vxe-input--date-picker-header {
    padding-bottom: 8px;
}
.vxe-input--panel .vxe-input--date-picker-body table,
.vxe-input--panel .vxe-input--time-picker-body {
    height: 258px;
}
.vxe-input--panel .vxe-input--time-picker-body > ul {
    width: 48px;
}
.vxe-input--panel .vxe-input--time-picker-body > ul:before, .vxe-input--panel .vxe-input--time-picker-body > ul:after {
    height: 120px;
}
.vxe-input--panel .vxe-input--time-picker-body > ul > li {
    height: 26px;
    padding-left: 9px;
}
.vxe-input--panel .vxe-input--time-picker-body .vxe-input--time-picker-minute-list {
    left: 48px;
}
.vxe-input--panel .vxe-input--time-picker-body .vxe-input--time-picker-second-list {
    left: 96px;
}
.vxe-input--panel .vxe-input--date-day-view td,
.vxe-input--panel .vxe-input--date-week-view td {
    height: 38px;
}
.vxe-input--panel .vxe-input--date-month-view td,
.vxe-input--panel .vxe-input--date-year-view td {
    height: 48px;
}
.vxe-input--panel .vxe-input--date-picker-body th {
    height: 30px;
}
.vxe-input--panel .vxe-input--time-picker-confirm {
    height: 30px;
    padding: 0 9px;
}
.vxe-input--panel .vxe-input--date-label {
    line-height: 15px;
}
.vxe-input--panel.size--medium {
    font-size: 14px;
}
.vxe-input--panel.size--medium .vxe-input--panel-wrapper {
    max-height: 360px;
}
.vxe-input--panel.size--medium.type--date .vxe-input--panel-wrapper, .vxe-input--panel.size--medium.type--time .vxe-input--panel-wrapper, .vxe-input--panel.size--medium.type--week .vxe-input--panel-wrapper, .vxe-input--panel.size--medium.type--month .vxe-input--panel-wrapper, .vxe-input--panel.size--medium.type--year .vxe-input--panel-wrapper {
    padding: 10px;
}
.vxe-input--panel.size--medium.type--date .vxe-input--panel-wrapper, .vxe-input--panel.size--medium.type--month .vxe-input--panel-wrapper, .vxe-input--panel.size--medium.type--year .vxe-input--panel-wrapper {
    width: 336px;
}
.vxe-input--panel.size--medium.type--week .vxe-input--panel-wrapper {
    width: 380px;
}
.vxe-input--panel.size--medium.type--time .vxe-input--panel-wrapper {
    width: 168px;
}
.vxe-input--panel.size--medium.type--datetime .vxe-input--panel-left-wrapper {
    width: 336px;
}
.vxe-input--panel.size--medium.type--datetime .vxe-input--panel-left-wrapper,
.vxe-input--panel.size--medium.type--datetime .vxe-input--panel-right-wrapper {
    padding: 10px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-title {
    height: 29px;
    line-height: 29px;
    padding: 0 10px;
}
.vxe-input--panel.size--medium .vxe-input--date-picker-label,
.vxe-input--panel.size--medium .vxe-input--date-picker-btn {
    height: 29px;
    line-height: 29px;
}
.vxe-input--panel.size--medium .vxe-input--date-picker-btn-wrapper .vxe-input--date-picker-btn {
    width: 29px;
    margin-left: 7px;
}
.vxe-input--panel.size--medium .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-label,
.vxe-input--panel.size--medium .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-btn {
    padding: 0 8px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-header,
.vxe-input--panel.size--medium .vxe-input--date-picker-header {
    padding-bottom: 7px;
}
.vxe-input--panel.size--medium .vxe-input--date-picker-body table,
.vxe-input--panel.size--medium .vxe-input--time-picker-body {
    height: 245px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-body > ul {
    width: 48px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-body > ul:before, .vxe-input--panel.size--medium .vxe-input--time-picker-body > ul:after {
    height: 120px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-body > ul > li {
    height: 26px;
    padding-left: 8px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-body .vxe-input--time-picker-minute-list {
    left: 48px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-body .vxe-input--time-picker-second-list {
    left: 96px;
}
.vxe-input--panel.size--medium .vxe-input--date-day-view td,
.vxe-input--panel.size--medium .vxe-input--date-week-view td {
    height: 36px;
}
.vxe-input--panel.size--medium .vxe-input--date-month-view td,
.vxe-input--panel.size--medium .vxe-input--date-year-view td {
    height: 46px;
}
.vxe-input--panel.size--medium .vxe-input--date-picker-body th {
    height: 29px;
}
.vxe-input--panel.size--medium .vxe-input--time-picker-confirm {
    height: 29px;
    padding: 0 8px;
}
.vxe-input--panel.size--medium .vxe-input--date-label {
    line-height: 15px;
}
.vxe-input--panel.size--small {
    font-size: 13px;
}
.vxe-input--panel.size--small .vxe-input--panel-wrapper {
    max-height: 340px;
}
.vxe-input--panel.size--small.type--date .vxe-input--panel-wrapper, .vxe-input--panel.size--small.type--time .vxe-input--panel-wrapper, .vxe-input--panel.size--small.type--week .vxe-input--panel-wrapper, .vxe-input--panel.size--small.type--month .vxe-input--panel-wrapper, .vxe-input--panel.size--small.type--year .vxe-input--panel-wrapper {
    padding: 9px;
}
.vxe-input--panel.size--small.type--date .vxe-input--panel-wrapper, .vxe-input--panel.size--small.type--month .vxe-input--panel-wrapper, .vxe-input--panel.size--small.type--year .vxe-input--panel-wrapper {
    width: 312px;
}
.vxe-input--panel.size--small.type--week .vxe-input--panel-wrapper {
    width: 354px;
}
.vxe-input--panel.size--small.type--time .vxe-input--panel-wrapper {
    width: 154px;
}
.vxe-input--panel.size--small.type--datetime .vxe-input--panel-left-wrapper {
    width: 312px;
}
.vxe-input--panel.size--small.type--datetime .vxe-input--panel-left-wrapper,
.vxe-input--panel.size--small.type--datetime .vxe-input--panel-right-wrapper {
    padding: 9px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-title {
    height: 28px;
    line-height: 28px;
    padding: 0 9px;
}
.vxe-input--panel.size--small .vxe-input--date-picker-label,
.vxe-input--panel.size--small .vxe-input--date-picker-btn {
    height: 28px;
    line-height: 28px;
}
.vxe-input--panel.size--small .vxe-input--date-picker-btn-wrapper .vxe-input--date-picker-btn {
    width: 28px;
    margin-left: 6px;
}
.vxe-input--panel.size--small .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-label,
.vxe-input--panel.size--small .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-btn {
    padding: 0 7px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-header,
.vxe-input--panel.size--small .vxe-input--date-picker-header {
    padding-bottom: 6px;
}
.vxe-input--panel.size--small .vxe-input--date-picker-body table,
.vxe-input--panel.size--small .vxe-input--time-picker-body {
    height: 232px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-body > ul {
    width: 44px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-body > ul:before, .vxe-input--panel.size--small .vxe-input--time-picker-body > ul:after {
    height: 110px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-body > ul > li {
    height: 26px;
    padding-left: 7px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-body .vxe-input--time-picker-minute-list {
    left: 44px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-body .vxe-input--time-picker-second-list {
    left: 88px;
}
.vxe-input--panel.size--small .vxe-input--date-day-view td,
.vxe-input--panel.size--small .vxe-input--date-week-view td {
    height: 34px;
}
.vxe-input--panel.size--small .vxe-input--date-month-view td,
.vxe-input--panel.size--small .vxe-input--date-year-view td {
    height: 44px;
}
.vxe-input--panel.size--small .vxe-input--date-picker-body th {
    height: 28px;
}
.vxe-input--panel.size--small .vxe-input--time-picker-confirm {
    height: 28px;
    padding: 0 7px;
}
.vxe-input--panel.size--small .vxe-input--date-label {
    line-height: 14px;
}
.vxe-input--panel.size--mini {
    font-size: 12px;
}
.vxe-input--panel.size--mini .vxe-input--panel-wrapper {
    max-height: 320px;
}
.vxe-input--panel.size--mini.type--date .vxe-input--panel-wrapper, .vxe-input--panel.size--mini.type--time .vxe-input--panel-wrapper, .vxe-input--panel.size--mini.type--week .vxe-input--panel-wrapper, .vxe-input--panel.size--mini.type--month .vxe-input--panel-wrapper, .vxe-input--panel.size--mini.type--year .vxe-input--panel-wrapper {
    padding: 8px;
}
.vxe-input--panel.size--mini.type--date .vxe-input--panel-wrapper, .vxe-input--panel.size--mini.type--month .vxe-input--panel-wrapper, .vxe-input--panel.size--mini.type--year .vxe-input--panel-wrapper {
    width: 288px;
}
.vxe-input--panel.size--mini.type--week .vxe-input--panel-wrapper {
    width: 326px;
}
.vxe-input--panel.size--mini.type--time .vxe-input--panel-wrapper {
    width: 146px;
}
.vxe-input--panel.size--mini.type--datetime .vxe-input--panel-left-wrapper {
    width: 288px;
}
.vxe-input--panel.size--mini.type--datetime .vxe-input--panel-left-wrapper,
.vxe-input--panel.size--mini.type--datetime .vxe-input--panel-right-wrapper {
    padding: 8px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-title {
    height: 27px;
    line-height: 27px;
    padding: 0 8px;
}
.vxe-input--panel.size--mini .vxe-input--date-picker-label,
.vxe-input--panel.size--mini .vxe-input--date-picker-btn {
    height: 27px;
    line-height: 27px;
}
.vxe-input--panel.size--mini .vxe-input--date-picker-btn-wrapper .vxe-input--date-picker-btn {
    width: 27px;
    margin-left: 5px;
}
.vxe-input--panel.size--mini .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-label,
.vxe-input--panel.size--mini .vxe-input--date-picker-type-wrapper .vxe-input--date-picker-btn {
    padding: 0 6px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-header,
.vxe-input--panel.size--mini .vxe-input--date-picker-header {
    padding-bottom: 5px;
}
.vxe-input--panel.size--mini .vxe-input--date-picker-body table,
.vxe-input--panel.size--mini .vxe-input--time-picker-body {
    height: 218px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-body > ul {
    width: 42px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-body > ul:before, .vxe-input--panel.size--mini .vxe-input--time-picker-body > ul:after {
    height: 100px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-body > ul > li {
    height: 26px;
    padding-left: 6px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-body .vxe-input--time-picker-minute-list {
    left: 42px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-body .vxe-input--time-picker-second-list {
    left: 84px;
}
.vxe-input--panel.size--mini .vxe-input--date-day-view td,
.vxe-input--panel.size--mini .vxe-input--date-week-view td {
    height: 32px;
}
.vxe-input--panel.size--mini .vxe-input--date-month-view td,
.vxe-input--panel.size--mini .vxe-input--date-year-view td {
    height: 42px;
}
.vxe-input--panel.size--mini .vxe-input--date-picker-body th {
    height: 26px;
}
.vxe-input--panel.size--mini .vxe-input--time-picker-confirm {
    height: 27px;
    padding: 0 6px;
}
.vxe-input--panel.size--mini .vxe-input--date-label {
    line-height: 13px;
}

/**Variable**/
.vxe-textarea {
    position: relative;
    display: inline-block;
    width: 100%;
}

.vxe-textarea--inner {
    border-radius: 4px;
    outline: 0;
    font-size: inherit;
    padding: 0 0.6em;
    color: #606266;
    line-height: inherit;
    border: 1px solid #dcdfe6;
    background-color: #fff;
}
.vxe-textarea--inner:focus {
    border: 1px solid #409eff;
}
.vxe-textarea--inner[disabled] {
    cursor: not-allowed;
    background-color: #f3f3f3;
}

.vxe-textarea--inner {
    width: 100%;
    height: 100%;
    display: block;
    padding: 0.3em 0.6em;
}
.vxe-textarea--inner::placeholder {
    color: #C0C4CC;
}

.vxe-textarea--inner,
.vxe-textarea--autosize {
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}

.vxe-textarea--autosize {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 0.3em 0.6em;
    word-wrap: break-word;
    white-space: pre-wrap;
    z-index: -1;
    visibility: hidden;
}

.vxe-textarea--count {
    position: absolute;
    bottom: 0.2em;
    right: 1.4em;
    padding-left: 0.2em;
    color: #999;
    background-color: #fff;
}
.vxe-textarea--count.is--error {
    color: #f56c6c;
}

.vxe-textarea,
.vxe-textarea--autosize {
    font-size: 14px;
}
.vxe-textarea.size--medium,
.vxe-textarea--autosize.size--medium {
    font-size: 14px;
}
.vxe-textarea.size--small,
.vxe-textarea--autosize.size--small {
    font-size: 13px;
}
.vxe-textarea.size--mini,
.vxe-textarea--autosize.size--mini {
    font-size: 12px;
}

.vxe-textarea:not(.is--autosize) {
    min-height: 34px;
}
.vxe-textarea.size--medium {
    font-size: 14px;
}
.vxe-textarea.size--medium:not(.is--autosize) {
    min-height: 32px;
}
.vxe-textarea.size--small:not(.is--autosize) {
    min-height: 30px;
}
.vxe-textarea.size--mini:not(.is--autosize) {
    min-height: 28px;
}

/**Variable**/
.vxe-button {
    position: relative;
    text-align: center;
    background-color: #fff;
    outline: 0;
    font-size: 14px;
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    white-space: nowrap;
    user-select: none;
    appearance: none;
    transition: border 0.2s ease-in-out;
}
.vxe-button.is--disabled {
    color: #BFBFBF;
}
.vxe-button.is--disabled .vxe-button--icon.vxe-icon--zoomin {
    border-color: #BFBFBF;
}
.vxe-button.is--disabled:not(.is--loading) {
    cursor: no-drop;
}
.vxe-button:not(.is--disabled) {
    color: #606266;
    cursor: pointer;
}
.vxe-button:not(.is--disabled) .vxe-button--icon.vxe-icon--zoomin {
    border-color: #606266;
}
.vxe-button.is--loading:before {
    content: "";
    position: absolute;
    left: -1px;
    top: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: inherit;
    background-color: rgba(255, 255, 255, 0.35);
    pointer-events: none;
}
.vxe-button.type--text {
    text-decoration: none;
    border: 0;
    padding: 0.1em 0.5em;
    background-color: transparent;
}
.vxe-button.type--text:not(.is--disabled):focus {
    box-shadow: 0 0 0.25em 0 #409eff;
}
.vxe-button.type--text:not(.is--disabled):hover {
    color: #73b8ff;
}
.vxe-button.type--text.theme--primary {
    color: #409eff;
}
.vxe-button.type--text.theme--primary:not(.is--disabled):hover {
    color: #73b8ff;
}
.vxe-button.type--text.theme--primary.is--disabled {
    color: #a6d2ff;
}
.vxe-button.type--text.theme--success {
    color: #67c23a;
}
.vxe-button.type--text.theme--success:not(.is--disabled):hover {
    color: #85cf60;
}
.vxe-button.type--text.theme--success.is--disabled {
    color: #a3db87;
}
.vxe-button.type--text.theme--info {
    color: #909399;
}
.vxe-button.type--text.theme--info:not(.is--disabled):hover {
    color: #abadb1;
}
.vxe-button.type--text.theme--info.is--disabled {
    color: #c5c7ca;
}
.vxe-button.type--text.theme--warning {
    color: #e6a23c;
}
.vxe-button.type--text.theme--warning:not(.is--disabled):hover {
    color: #ecb869;
}
.vxe-button.type--text.theme--warning.is--disabled {
    color: #f2cd96;
}
.vxe-button.type--text.theme--danger {
    color: #f56c6c;
}
.vxe-button.type--text.theme--danger:not(.is--disabled):hover {
    color: #f89c9c;
}
.vxe-button.type--text.theme--danger.is--disabled {
    color: #fbcccc;
}
.vxe-button.type--text.theme--perfect {
    color: #1f242d;
}
.vxe-button.type--text.theme--perfect:not(.is--disabled):hover {
    color: #343c4b;
}
.vxe-button.type--text.theme--perfect.is--disabled {
    color: #495469;
}
.vxe-button.type--button {
    font-family: inherit;
    line-height: inherit;
    height: 34px;
    line-height: 1;
    border: 1px solid #dcdfe6;
}
.vxe-button.type--button.is--round {
    border-radius: 17px;
}
.vxe-button.type--button:not(.is--round) {
    border-radius: 4px;
}
.vxe-button.type--button.is--circle {
    padding: 0 0.5em;
    min-width: 34px;
    border-radius: 50%;
}
.vxe-button.type--button:not(.is--circle) {
    padding: 0 1em;
}
.vxe-button.type--button:not(.is--disabled):hover {
    color: #5faeff;
}
.vxe-button.type--button:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #5faeff;
}
.vxe-button.type--button:not(.is--disabled):focus {
    border-color: #409eff;
    box-shadow: 0 0 0.25em 0 #409eff;
}
.vxe-button.type--button:not(.is--disabled):active {
    color: #3196ff;
    border-color: #3196ff;
    background-color: #f7f7f7;
}
.vxe-button.type--button:not(.is--disabled):active .vxe-button--icon.vxe-icon--zoomin {
    background-color: #f7f7f7;
}
.vxe-button.type--button:not(.is--disabled):active .vxe-button--icon.vxe-icon--zoomout:after {
    background-color: #f7f7f7;
}
.vxe-button.type--button.theme--primary {
    color: #fff;
}
.vxe-button.type--button.theme--primary .vxe-button--icon.vxe-icon--zoomin {
    border-color: #fff;
}
.vxe-button.type--button.theme--primary:not(.is--disabled) {
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-button.type--button.theme--primary:not(.is--disabled):hover {
    color: #fff;
    background-color: #5faeff;
    border-color: #5faeff;
}
.vxe-button.type--button.theme--primary:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #5faeff;
}
.vxe-button.type--button.theme--primary:not(.is--disabled):active {
    color: #fff;
    background-color: #3196ff;
    border-color: #3196ff;
}
.vxe-button.type--button.theme--primary:not(.is--disabled):active.vxe-icon--zoomin {
    background-color: #3196ff;
}
.vxe-button.type--button.theme--primary:not(.is--disabled):active.vxe-icon--zoomout:after {
    background-color: #3196ff;
}
.vxe-button.type--button.theme--primary.is--disabled {
    border-color: #a6d2ff;
    background-color: #a6d2ff;
}
.vxe-button.type--button.theme--primary.is--loading {
    border-color: #409eff;
    background-color: #409eff;
}
.vxe-button.type--button.theme--success {
    color: #fff;
}
.vxe-button.type--button.theme--success .vxe-button--icon.vxe-icon--zoomin {
    border-color: #fff;
}
.vxe-button.type--button.theme--success:not(.is--disabled) {
    border-color: #67c23a;
    background-color: #67c23a;
}
.vxe-button.type--button.theme--success:not(.is--disabled):hover {
    color: #fff;
    background-color: #79cb50;
    border-color: #79cb50;
}
.vxe-button.type--button.theme--success:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #79cb50;
}
.vxe-button.type--button.theme--success:not(.is--disabled):active {
    color: #fff;
    background-color: #61b636;
    border-color: #61b636;
}
.vxe-button.type--button.theme--success:not(.is--disabled):active.vxe-icon--zoomin {
    background-color: #61b636;
}
.vxe-button.type--button.theme--success:not(.is--disabled):active.vxe-icon--zoomout:after {
    background-color: #61b636;
}
.vxe-button.type--button.theme--success.is--disabled {
    border-color: #a3db87;
    background-color: #a3db87;
}
.vxe-button.type--button.theme--success.is--loading {
    border-color: #67c23a;
    background-color: #67c23a;
}
.vxe-button.type--button.theme--info {
    color: #fff;
}
.vxe-button.type--button.theme--info .vxe-button--icon.vxe-icon--zoomin {
    border-color: #fff;
}
.vxe-button.type--button.theme--info:not(.is--disabled) {
    border-color: #909399;
    background-color: #909399;
}
.vxe-button.type--button.theme--info:not(.is--disabled):hover {
    color: #fff;
    background-color: #a0a3a8;
    border-color: #a0a3a8;
}
.vxe-button.type--button.theme--info:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #a0a3a8;
}
.vxe-button.type--button.theme--info:not(.is--disabled):active {
    color: #fff;
    background-color: #888b92;
    border-color: #888b92;
}
.vxe-button.type--button.theme--info:not(.is--disabled):active.vxe-icon--zoomin {
    background-color: #888b92;
}
.vxe-button.type--button.theme--info:not(.is--disabled):active.vxe-icon--zoomout:after {
    background-color: #888b92;
}
.vxe-button.type--button.theme--info.is--disabled {
    border-color: #c5c7ca;
    background-color: #c5c7ca;
}
.vxe-button.type--button.theme--info.is--loading {
    border-color: #909399;
    background-color: #909399;
}
.vxe-button.type--button.theme--warning {
    color: #fff;
}
.vxe-button.type--button.theme--warning .vxe-button--icon.vxe-icon--zoomin {
    border-color: #fff;
}
.vxe-button.type--button.theme--warning:not(.is--disabled) {
    border-color: #e6a23c;
    background-color: #e6a23c;
}
.vxe-button.type--button.theme--warning:not(.is--disabled):hover {
    color: #fff;
    background-color: #e9af57;
    border-color: #e9af57;
}
.vxe-button.type--button.theme--warning:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #e9af57;
}
.vxe-button.type--button.theme--warning:not(.is--disabled):active {
    color: #fff;
    background-color: #e49c2e;
    border-color: #e49c2e;
}
.vxe-button.type--button.theme--warning:not(.is--disabled):active.vxe-icon--zoomin {
    background-color: #e49c2e;
}
.vxe-button.type--button.theme--warning:not(.is--disabled):active.vxe-icon--zoomout:after {
    background-color: #e49c2e;
}
.vxe-button.type--button.theme--warning.is--disabled {
    border-color: #f2cd96;
    background-color: #f2cd96;
}
.vxe-button.type--button.theme--warning.is--loading {
    border-color: #e6a23c;
    background-color: #e6a23c;
}
.vxe-button.type--button.theme--danger {
    color: #fff;
}
.vxe-button.type--button.theme--danger .vxe-button--icon.vxe-icon--zoomin {
    border-color: #fff;
}
.vxe-button.type--button.theme--danger:not(.is--disabled) {
    border-color: #f56c6c;
    background-color: #f56c6c;
}
.vxe-button.type--button.theme--danger:not(.is--disabled):hover {
    color: #fff;
    background-color: #f78989;
    border-color: #f78989;
}
.vxe-button.type--button.theme--danger:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #f78989;
}
.vxe-button.type--button.theme--danger:not(.is--disabled):active {
    color: #fff;
    background-color: #f45e5e;
    border-color: #f45e5e;
}
.vxe-button.type--button.theme--danger:not(.is--disabled):active.vxe-icon--zoomin {
    background-color: #f45e5e;
}
.vxe-button.type--button.theme--danger:not(.is--disabled):active.vxe-icon--zoomout:after {
    background-color: #f45e5e;
}
.vxe-button.type--button.theme--danger.is--disabled {
    border-color: #fbcccc;
    background-color: #fbcccc;
}
.vxe-button.type--button.theme--danger.is--loading {
    border-color: #f56c6c;
    background-color: #f56c6c;
}
.vxe-button.type--button.theme--perfect {
    color: #606266;
}
.vxe-button.type--button.theme--perfect .vxe-button--icon.vxe-icon--zoomin {
    border-color: #606266;
}
.vxe-button.type--button.theme--perfect:not(.is--disabled) {
    border-color: #1f242d;
    background-color: #1f242d;
}
.vxe-button.type--button.theme--perfect:not(.is--disabled):hover {
    color: #606266;
    background-color: #2b323f;
    border-color: #2b323f;
}
.vxe-button.type--button.theme--perfect:not(.is--disabled):hover .vxe-button--icon.vxe-icon--zoomin {
    border-color: #2b323f;
}
.vxe-button.type--button.theme--perfect:not(.is--disabled):active {
    color: #606266;
    background-color: #191d24;
    border-color: #191d24;
}
.vxe-button.type--button.theme--perfect:not(.is--disabled):active.vxe-icon--zoomin {
    background-color: #191d24;
}
.vxe-button.type--button.theme--perfect:not(.is--disabled):active.vxe-icon--zoomout:after {
    background-color: #191d24;
}
.vxe-button.type--button.theme--perfect.is--disabled {
    border-color: #495469;
    background-color: #495469;
}
.vxe-button.type--button.theme--perfect.is--loading {
    border-color: #1f242d;
    background-color: #1f242d;
}
.vxe-button.size--medium {
    font-size: 14px;
}
.vxe-button.size--medium.type--button {
    height: 32px;
}
.vxe-button.size--medium.type--button.is--circle {
    min-width: 32px;
}
.vxe-button.size--medium.type--button.is--round {
    border-radius: 16px;
}
.vxe-button.size--medium .vxe-button--loading-icon,
.vxe-button.size--medium .vxe-button--icon {
    min-width: 14px;
}
.vxe-button.size--small {
    font-size: 13px;
}
.vxe-button.size--small.type--button {
    height: 30px;
}
.vxe-button.size--small.type--button.is--circle {
    min-width: 30px;
}
.vxe-button.size--small.type--button.is--round {
    border-radius: 15px;
}
.vxe-button.size--small .vxe-button--loading-icon,
.vxe-button.size--small .vxe-button--icon {
    min-width: 13px;
}
.vxe-button.size--mini {
    font-size: 12px;
}
.vxe-button.size--mini.type--button {
    height: 28px;
}
.vxe-button.size--mini.type--button.is--circle {
    min-width: 28px;
}
.vxe-button.size--mini.type--button.is--round {
    border-radius: 14px;
}
.vxe-button.size--mini .vxe-button--loading-icon,
.vxe-button.size--mini .vxe-button--icon {
    min-width: 12px;
}

.vxe-input + .vxe-button, .vxe-input + .vxe-button--dropdown,
.vxe-button + .vxe-button,
.vxe-button + .vxe-button--dropdown {
    margin-left: 12px;
}

.vxe-button--loading-icon,
.vxe-button--icon,
.vxe-button--content {
    vertical-align: middle;
}

.vxe-button--loading-icon,
.vxe-button--icon {
    min-width: 14px;
}
.vxe-button--loading-icon + .vxe-button--content,
.vxe-button--icon + .vxe-button--content {
    margin-left: 4px;
}

.vxe-button--wrapper,
.vxe-button--dropdown {
    display: inline-block;
}

.vxe-button--dropdown {
    position: relative;
}
.vxe-button--dropdown + .vxe-button, .vxe-button--dropdown + .vxe-button--dropdown {
    margin-left: 12px;
}
.vxe-button--dropdown > .vxe-button.type--button.theme--primary {
    color: #fff;
}
.vxe-button--dropdown > .vxe-button.type--button.theme--success {
    color: #fff;
}
.vxe-button--dropdown > .vxe-button.type--button.theme--info {
    color: #fff;
}
.vxe-button--dropdown > .vxe-button.type--button.theme--warning {
    color: #fff;
}
.vxe-button--dropdown > .vxe-button.type--button.theme--danger {
    color: #fff;
}
.vxe-button--dropdown > .vxe-button.type--button.theme--perfect {
    color: #606266;
}
.vxe-button--dropdown.is--active > .vxe-button:not(.is--disabled) {
    color: #5faeff;
}
.vxe-button--dropdown.is--active > .vxe-button.type--text.theme--primary {
    color: #73b8ff;
}
.vxe-button--dropdown.is--active > .vxe-button.type--text.theme--success {
    color: #85cf60;
}
.vxe-button--dropdown.is--active > .vxe-button.type--text.theme--info {
    color: #abadb1;
}
.vxe-button--dropdown.is--active > .vxe-button.type--text.theme--warning {
    color: #ecb869;
}
.vxe-button--dropdown.is--active > .vxe-button.type--text.theme--danger {
    color: #f89c9c;
}
.vxe-button--dropdown.is--active > .vxe-button.type--text.theme--perfect {
    color: #343c4b;
}
.vxe-button--dropdown.is--active > .vxe-button.type--button.theme--primary {
    color: #fff;
    background-color: #5faeff;
    border-color: #5faeff;
}
.vxe-button--dropdown.is--active > .vxe-button.type--button.theme--success {
    color: #fff;
    background-color: #79cb50;
    border-color: #79cb50;
}
.vxe-button--dropdown.is--active > .vxe-button.type--button.theme--info {
    color: #fff;
    background-color: #a0a3a8;
    border-color: #a0a3a8;
}
.vxe-button--dropdown.is--active > .vxe-button.type--button.theme--warning {
    color: #fff;
    background-color: #e9af57;
    border-color: #e9af57;
}
.vxe-button--dropdown.is--active > .vxe-button.type--button.theme--danger {
    color: #fff;
    background-color: #f78989;
    border-color: #f78989;
}
.vxe-button--dropdown.is--active > .vxe-button.type--button.theme--perfect {
    color: #606266;
    background-color: #2b323f;
    border-color: #2b323f;
}
.vxe-button--dropdown.is--active .vxe-button--dropdown-arrow {
    transform: rotate(180deg);
}

.vxe-button--dropdown-arrow {
    font-size: 12px;
    margin-left: 4px;
    transition: transform 0.2s ease-in-out;
}

.vxe-button--dropdown-panel {
    display: none;
    position: absolute;
    right: 0;
    padding: 4px 0;
}
.vxe-button--dropdown-panel.animat--leave {
    display: block;
    opacity: 0;
    transform: scaleY(0.5);
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: center top;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}
.vxe-button--dropdown-panel.animat--leave[placement=top] {
    transform-origin: center bottom;
}
.vxe-button--dropdown-panel.animat--enter {
    opacity: 1;
    transform: scaleY(1);
}

.vxe-button--dropdown-wrapper {
    padding: 5px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}
.vxe-button--dropdown-wrapper > .vxe-button {
    margin: 2px 0;
    display: block;
    width: 100%;
    border: 0;
}
.vxe-button--dropdown-wrapper > .vxe-button.type--text {
    padding: 2px 8px;
}
.vxe-button--dropdown-wrapper > .vxe-button:first-child {
    margin-top: 0;
}
.vxe-button--dropdown-wrapper > .vxe-button:last-child {
    margin-bottom: 0;
}

/**Variable**/
.vxe-modal--wrapper {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    line-height: 1.5;
    width: calc(100% + 18px);
    height: calc(100% + 18px);
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    transition: top 0.4s ease-in-out;
}
.vxe-modal--wrapper.is--active {
    display: block;
}
.vxe-modal--wrapper.is--visible.is--mask:before {
    background-color: rgba(0, 0, 0, 0.5);
}
.vxe-modal--wrapper.is--visible.type--message .vxe-modal--box {
    opacity: 1;
    transform: translateY(0);
}
.vxe-modal--wrapper.is--visible .vxe-modal--box {
    opacity: 1;
    visibility: visible;
}
.vxe-modal--wrapper.is--loading .vxe-modal--header,
.vxe-modal--wrapper.is--loading .vxe-modal--footer {
    position: relative;
    border-bottom-color: rgba(0, 0, 0, 0.2);
}
.vxe-modal--wrapper.is--loading .vxe-modal--header:before,
.vxe-modal--wrapper.is--loading .vxe-modal--footer:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
}
.vxe-modal--wrapper:not(.lock--view) {
    pointer-events: none;
}
.vxe-modal--wrapper:not(.type--message).lock--scroll {
    overflow: hidden;
}
.vxe-modal--wrapper:not(.type--message):not(.lock--scroll) {
    overflow: auto;
}
.vxe-modal--wrapper.lock--view:before, .vxe-modal--wrapper.is--mask:before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: auto;
}
.vxe-modal--wrapper.is--mask:before {
    background-color: rgba(0, 0, 0, 0);
}
.vxe-modal--wrapper.is--animat.is--mask:before {
    transition: background-color 0.2s ease-in-out;
}
.vxe-modal--wrapper.is--animat.type--message .vxe-modal--box:not(.is--drag) {
    transition: all 0.4s ease-out;
}
.vxe-modal--wrapper.type--message .vxe-modal--body, .vxe-modal--wrapper.type--alert .vxe-modal--body, .vxe-modal--wrapper.type--confirm .vxe-modal--body {
    white-space: normal;
    word-break: break-word;
}
.vxe-modal--wrapper.type--message {
    text-align: center;
}
.vxe-modal--wrapper.type--message .vxe-modal--box {
    display: inline-block;
    padding: 2px 0;
    margin-top: 0;
    width: auto;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(-100%);
}
.vxe-modal--wrapper.type--message .vxe-modal--box .vxe-modal--body:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
}
.vxe-modal--wrapper.type--message .vxe-modal--box .vxe-modal--content {
    max-width: 800px;
    float: left;
}
.vxe-modal--wrapper.type--message .vxe-modal--status-wrapper {
    font-size: 1.4em;
    padding-left: 10px;
}
.vxe-modal--wrapper.type--modal .vxe-modal--box, .vxe-modal--wrapper.type--alert .vxe-modal--box, .vxe-modal--wrapper.type--confirm .vxe-modal--box {
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 50%;
    top: 0;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
}
.vxe-modal--wrapper.type--modal .vxe-modal--box .vxe-modal--header, .vxe-modal--wrapper.type--alert .vxe-modal--box .vxe-modal--header, .vxe-modal--wrapper.type--confirm .vxe-modal--box .vxe-modal--header {
    cursor: move;
}
.vxe-modal--wrapper.type--modal .vxe-modal--header {
    padding: 0.6em 4.6em 0.6em 1em;
}
.vxe-modal--wrapper.type--modal .vxe-modal--body {
    overflow: auto;
}
.vxe-modal--wrapper.type--modal .vxe-modal--body .vxe-modal--content {
    overflow: auto;
}
.vxe-modal--wrapper.type--alert .vxe-modal--status-wrapper, .vxe-modal--wrapper.type--confirm .vxe-modal--status-wrapper {
    font-size: 1.6em;
    padding-left: 10px;
}
.vxe-modal--wrapper .vxe-modal--box {
    visibility: hidden;
    width: 420px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    text-align: left;
    pointer-events: auto;
    opacity: 0;
}
.vxe-modal--wrapper .vxe-modal--box.is--drag {
    cursor: move;
}
.vxe-modal--wrapper .vxe-modal--box.is--drag .vxe-modal--body:after,
.vxe-modal--wrapper .vxe-modal--box.is--drag .vxe-modal--footer:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.vxe-modal--wrapper .vxe-modal--box.is--drag .vxe-modal--body {
    overflow: hidden;
}
.vxe-modal--wrapper .vxe-modal--box.is--drag .vxe-modal--body .vxe-modal--content {
    overflow: hidden;
}
.vxe-modal--wrapper.status--info .vxe-modal--status-wrapper {
    color: #909399;
}
.vxe-modal--wrapper.status--warning .vxe-modal--status-wrapper, .vxe-modal--wrapper.status--question .vxe-modal--status-wrapper {
    color: #e6a23c;
}
.vxe-modal--wrapper.status--success .vxe-modal--status-wrapper {
    color: #67c23a;
}
.vxe-modal--wrapper.status--error .vxe-modal--status-wrapper {
    color: #f56c6c;
}
.vxe-modal--wrapper.status--loading .vxe-modal--status-wrapper {
    color: #BFBFBF;
}
.vxe-modal--wrapper .vxe-modal--status-wrapper {
    flex-shrink: 0;
    display: flex;
    align-items: center;
}
.vxe-modal--wrapper .vxe-modal--content {
    flex-grow: 1;
    padding: 0.8em 1em;
}
.vxe-modal--wrapper .vxe-modal--header,
.vxe-modal--wrapper .vxe-modal--body,
.vxe-modal--wrapper .vxe-modal--footer {
    position: relative;
}
.vxe-modal--wrapper .vxe-modal--body {
    display: flex;
    flex-grow: 1;
}
.vxe-modal--wrapper .vxe-modal--header {
    flex-shrink: 0;
    font-size: 1.1em;
    font-weight: 700;
    padding: 0.6em 2.8em 0.6em 1em;
    border-bottom: 1px solid #ebeef5;
    background-color: #F8F8F8;
    border-radius: 4px 4px 0 0;
    user-select: none;
}
.vxe-modal--wrapper .vxe-modal--header.is--ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-modal--wrapper .vxe-modal--zoom-btn,
.vxe-modal--wrapper .vxe-modal--close-btn {
    position: absolute;
    right: 14px;
    top: 0.85em;
    z-index: 1;
    cursor: pointer;
}
.vxe-modal--wrapper .vxe-modal--zoom-btn:hover,
.vxe-modal--wrapper .vxe-modal--close-btn:hover {
    color: #409eff;
}
.vxe-modal--wrapper .vxe-modal--zoom-btn {
    right: 44px;
}
.vxe-modal--wrapper .vxe-modal--footer {
    flex-shrink: 0;
    text-align: right;
    padding: 0.4em 1em 0.8em 1em;
}
.vxe-modal--wrapper.is--maximize .vxe-modal--box .vxe-modal--header {
    cursor: default;
}
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .wl-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .wr-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .st-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .swlb-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .selb-resize,
.vxe-modal--wrapper.is--maximize .vxe-modal--resize .sb-resize {
    display: none;
}
.vxe-modal--wrapper .vxe-modal--resize .wl-resize,
.vxe-modal--wrapper .vxe-modal--resize .wr-resize,
.vxe-modal--wrapper .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper .vxe-modal--resize .st-resize,
.vxe-modal--wrapper .vxe-modal--resize .swlb-resize,
.vxe-modal--wrapper .vxe-modal--resize .selb-resize,
.vxe-modal--wrapper .vxe-modal--resize .sb-resize {
    position: absolute;
    z-index: 100;
}
.vxe-modal--wrapper .vxe-modal--resize .wl-resize,
.vxe-modal--wrapper .vxe-modal--resize .wr-resize {
    width: 8px;
    height: 100%;
    top: 0;
    cursor: w-resize;
}
.vxe-modal--wrapper .vxe-modal--resize .wl-resize {
    left: -5px;
}
.vxe-modal--wrapper .vxe-modal--resize .wr-resize {
    right: -5px;
}
.vxe-modal--wrapper .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper .vxe-modal--resize .swlb-resize,
.vxe-modal--wrapper .vxe-modal--resize .selb-resize {
    width: 10px;
    height: 10px;
    z-index: 101;
}
.vxe-modal--wrapper .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper .vxe-modal--resize .sest-resize {
    top: -8px;
}
.vxe-modal--wrapper .vxe-modal--resize .swlb-resize,
.vxe-modal--wrapper .vxe-modal--resize .selb-resize {
    bottom: -8px;
}
.vxe-modal--wrapper .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper .vxe-modal--resize .swlb-resize {
    cursor: sw-resize;
}
.vxe-modal--wrapper .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper .vxe-modal--resize .selb-resize {
    cursor: se-resize;
}
.vxe-modal--wrapper .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper .vxe-modal--resize .swlb-resize {
    left: -8px;
}
.vxe-modal--wrapper .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper .vxe-modal--resize .selb-resize {
    right: -8px;
}
.vxe-modal--wrapper .vxe-modal--resize .st-resize,
.vxe-modal--wrapper .vxe-modal--resize .sb-resize {
    width: 100%;
    height: 8px;
    left: 0;
    cursor: s-resize;
}
.vxe-modal--wrapper .vxe-modal--resize .st-resize {
    top: -5px;
}
.vxe-modal--wrapper .vxe-modal--resize .sb-resize {
    bottom: -5px;
}

.vxe-modal--wrapper {
    font-size: 14px;
}
.vxe-modal--wrapper.size--medium {
    font-size: 14px;
}
.vxe-modal--wrapper.size--small {
    font-size: 13px;
}
.vxe-modal--wrapper.size--mini {
    font-size: 12px;
}

/**Variable**/
/*tppltip*/
.vxe-table--tooltip-wrapper {
    display: none;
    position: absolute;
    top: -100%;
    left: -100%;
    font-size: 12px;
    max-width: 400px;
    border-radius: 4px;
    padding: 8px 12px;
    white-space: normal;
    word-break: break-word;
    box-shadow: 2px 2px 4px -2px rgba(0, 0, 0, 0.2);
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}
.vxe-table--tooltip-wrapper:not(.is--enterable) {
    pointer-events: none;
}
.vxe-table--tooltip-wrapper.is--visible {
    display: block;
}
.vxe-table--tooltip-wrapper.is--arrow .vxe-table--tooltip-arrow {
    display: block;
}
.vxe-table--tooltip-wrapper.is--enterable:after {
    content: "";
    position: absolute;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: transparent;
}
.vxe-table--tooltip-wrapper .vxe-table--tooltip-content {
    white-space: pre-line;
}
.vxe-table--tooltip-wrapper .vxe-table--tooltip-arrow {
    display: none;
    position: absolute;
    border-color: transparent;
    border-width: 6px;
    border-style: solid;
    left: 50%;
    transform: translateX(-6px);
}
.vxe-table--tooltip-wrapper .vxe-table--tooltip-arrow:before {
    content: "";
    position: absolute;
    border-color: transparent;
    border-width: 5px;
    border-style: solid;
    left: -5px;
}
.vxe-table--tooltip-wrapper.placement--top.is--enterable:after {
    bottom: -6px;
}
.vxe-table--tooltip-wrapper.placement--top .vxe-table--tooltip-arrow {
    bottom: -12px;
}
.vxe-table--tooltip-wrapper.placement--top .vxe-table--tooltip-arrow:before {
    top: -7px;
}
.vxe-table--tooltip-wrapper.placement--bottom.is--enterable:after {
    top: -6px;
}
.vxe-table--tooltip-wrapper.placement--bottom .vxe-table--tooltip-arrow {
    top: -12px;
}
.vxe-table--tooltip-wrapper.placement--bottom .vxe-table--tooltip-arrow:before {
    top: -4px;
}

.vxe-table--tooltip-wrapper.theme--light {
    background-color: #fff;
    border: 1px solid #dcdfe6;
}
.vxe-table--tooltip-wrapper.theme--light.placement--top .vxe-table--tooltip-arrow {
    border-top-color: #dcdfe6;
}
.vxe-table--tooltip-wrapper.theme--light.placement--top .vxe-table--tooltip-arrow:before {
    border-top-color: #fff;
}
.vxe-table--tooltip-wrapper.theme--light.placement--bottom .vxe-table--tooltip-arrow {
    border-bottom-color: #dcdfe6;
}
.vxe-table--tooltip-wrapper.theme--light.placement--bottom .vxe-table--tooltip-arrow:before {
    border-bottom-color: #fff;
}
.vxe-table--tooltip-wrapper.theme--dark {
    background: #303133;
    color: #fff;
}
.vxe-table--tooltip-wrapper.theme--dark.placement--top .vxe-table--tooltip-arrow {
    border-top-color: #303133;
}
.vxe-table--tooltip-wrapper.theme--dark.placement--top .vxe-table--tooltip-arrow:before {
    border-top-color: #303133;
}
.vxe-table--tooltip-wrapper.theme--dark.placement--bottom .vxe-table--tooltip-arrow {
    border-bottom-color: #303133;
}
.vxe-table--tooltip-wrapper.theme--dark.placement--bottom .vxe-table--tooltip-arrow:before {
    border-bottom-color: #303133;
}

/*valid error*/
.vxe-table--tooltip-wrapper.vxe-table--valid-error {
    background-color: #f56c6c;
    color: #fff;
}

/**Variable**/
.vxe-form--item .vxe-default-input[type=submit]:hover,
.vxe-form--item .vxe-default-input[type=reset]:hover {
    color: #5faeff;
    border-color: #73b8ff;
}

.vxe-form {
    position: relative;
    font-size: 14px;
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    background-color: #fff;
    text-align: left;
}
.vxe-form.is--colon .vxe-form--item-title:after {
    content: ":";
    font-size: 1.2em;
    font-family: SimSun, sans-serif;
}

.vxe-form-slots {
    display: none;
}

.vxe-form--item-title,
.vxe-form--item-content,
.vxe-form--item-trigger-node {
    display: inline-block;
    vertical-align: middle;
}

.vxe-form--item-title {
    max-width: 320px;
    padding-right: 0.8em;
}
.vxe-form--item-title.is--ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.vxe-form--item-title .vxe-form--item-title-prefix,
.vxe-form--item-title .vxe-form--item-title-suffix {
    cursor: help;
}
.vxe-form--item-title .vxe-form--item-title-prefix > [class*=vxe-icon--],
.vxe-form--item-title .vxe-form--item-title-suffix > [class*=vxe-icon--] {
    margin-top: -0.2em;
}
.vxe-form--item-title .vxe-form--item-title-prefix {
    margin-right: 0.25em;
}
.vxe-form--item-title .vxe-form--item-title-suffix {
    margin-left: 0.25em;
}

.vxe-form--item-trigger-node {
    font-size: 12px;
    min-width: 100px;
    color: #909399;
    text-align: center;
    user-select: none;
    cursor: pointer;
}
.vxe-form--item-trigger-node .vxe-form--item-trigger-icon {
    margin: 0 0.25em;
    transition: all 0.1s;
}

.vxe-form--item-valid {
    position: absolute;
    width: 100%;
    font-size: 12px;
    line-height: 1.2em;
    color: #f56c6c;
    background-color: inherit;
    z-index: 1;
    opacity: 0;
    transform-origin: center top;
    transform: scaleY(0);
    transition: all 0.2s ease-in-out;
}

.vxe-form .vxe-form--gather {
    display: inline-block;
}
.vxe-form .vxe-form--item {
    display: none;
    padding: 0.5em 0.8em 0.5em 0;
}
.vxe-form .vxe-form--item.is--active:not(.is--hidden) {
    display: inline-block;
}
.vxe-form.is--asterisk .vxe-form--item.is--required .vxe-form--item-title:before {
    content: "*";
    color: #f56c6c;
    margin-right: 0.2em;
    font-family: Verdana, Arial, Tahoma;
    font-weight: normal;
}

.vxe-form--item.is--span .vxe-default-input:not([type=submit]):not([type=reset]),
.vxe-form--item.is--span .vxe-default-textarea,
.vxe-form--item.is--span .vxe-default-select,
.vxe-form--item.is--span .vxe-input,
.vxe-form--item.is--span .vxe-textarea,
.vxe-form--item.is--span .vxe-select {
    width: 100%;
}
.vxe-form--item.is--error .vxe-input > .vxe-input--inner,
.vxe-form--item.is--error .vxe-textarea > .vxe-textarea--inner,
.vxe-form--item.is--error .vxe-select,
.vxe-form--item.is--error .vxe-select.is--active > .vxe-input .vxe-input--inner,
.vxe-form--item.is--error .vxe-default-input,
.vxe-form--item.is--error .vxe-default-textarea,
.vxe-form--item.is--error .vxe-default-select {
    border-color: #f56c6c;
}
.vxe-form--item.is--error .vxe-input > .vxe-input--inner:focus,
.vxe-form--item.is--error .vxe-textarea > .vxe-textarea--inner:focus,
.vxe-form--item.is--error .vxe-default-input[type=text]:focus,
.vxe-form--item.is--error .vxe-default-input[type=search]:focus,
.vxe-form--item.is--error .vxe-default-textarea:focus,
.vxe-form--item.is--error .vxe-default-select:focus {
    border-color: #f56c6c;
}
.vxe-form--item.is--error .vxe-form--item-valid {
    opacity: 1;
    transform: scaleY(1);
}
.vxe-form--item .vxe-form--item-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.vxe-form--item .vxe-form--item-inner .vxe-form--item-title {
    flex-shrink: 0;
}
.vxe-form--item .vxe-form--item-inner .vxe-form--item-content {
    position: relative;
    flex-grow: 1;
}
.vxe-form--item .vxe-default-input,
.vxe-form--item .vxe-default-textarea,
.vxe-form--item .vxe-default-select {
    outline: 0;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}
.vxe-form--item .vxe-default-input,
.vxe-form--item .vxe-default-select {
    height: 34px;
}
.vxe-form--item .vxe-default-input {
    padding: 0 0.8em;
}
.vxe-form--item .vxe-default-textarea {
    padding: 0.3em 0.6em;
}
.vxe-form--item .vxe-default-input[type=number] {
    padding-right: 0.2em;
}
.vxe-form--item .vxe-default-input[type=text],
.vxe-form--item .vxe-default-input[type=search] {
    padding: 0 1em;
}
.vxe-form--item .vxe-default-input[type=text],
.vxe-form--item .vxe-default-input[type=search],
.vxe-form--item .vxe-default-textarea,
.vxe-form--item .vxe-default-select {
    color: #606266;
}
.vxe-form--item .vxe-default-input[type=text]:focus,
.vxe-form--item .vxe-default-input[type=search]:focus,
.vxe-form--item .vxe-default-textarea:focus,
.vxe-form--item .vxe-default-select:focus {
    border: 1px solid #409eff;
}
.vxe-form--item .vxe-default-input[type=text][disabled],
.vxe-form--item .vxe-default-input[type=search][disabled],
.vxe-form--item .vxe-default-textarea[disabled],
.vxe-form--item .vxe-default-select[disabled] {
    cursor: not-allowed;
    background-color: #f3f3f3;
}
.vxe-form--item .vxe-default-input[type=submit],
.vxe-form--item .vxe-default-input[type=reset] {
    line-height: 32px;
    background-color: #fff;
    cursor: pointer;
}
.vxe-form--item .vxe-default-input[type=submit]:active,
.vxe-form--item .vxe-default-input[type=reset]:active {
    color: #3699ff;
    border-color: #3699ff;
}
.vxe-form--item .vxe-default-input[type=date]::-webkit-inner-spin-button {
    margin-top: 6px;
}
.vxe-form--item .vxe-default-input[type=date]::-webkit-inner-spin-button, .vxe-form--item .vxe-default-input[type=number]::-webkit-inner-spin-button {
    height: 24px;
}
.vxe-form--item .vxe-default-input::placeholder {
    color: #C0C4CC;
}
.vxe-form--item .vxe-default-input[type=text],
.vxe-form--item .vxe-default-input[type=search],
.vxe-form--item .vxe-default-textarea,
.vxe-form--item .vxe-default-select {
    width: 180px;
}
.vxe-form--item .vxe-default-textarea {
    resize: none;
    vertical-align: middle;
}
.vxe-form--item .vxe-default-textarea::placeholder {
    color: #C0C4CC;
}

.vxe-form .vxe-form--item-inner {
    min-height: 36px;
}
.vxe-form .vxe-form--item-inner > .align--center {
    text-align: center;
}
.vxe-form .vxe-form--item-inner > .align--left {
    text-align: left;
}
.vxe-form .vxe-form--item-inner > .align--right {
    text-align: right;
}
.vxe-form.size--medium {
    font-size: 14px;
}
.vxe-form.size--medium .vxe-form--item-inner {
    min-height: 34px;
}
.vxe-form.size--medium .vxe-default-input[type=submit],
.vxe-form.size--medium .vxe-default-input[type=reset] {
    line-height: 30px;
}
.vxe-form.size--medium .vxe-default-input,
.vxe-form.size--medium .vxe-default-input,
.vxe-form.size--medium .vxe-default-select {
    height: 32px;
}
.vxe-form.size--small {
    font-size: 13px;
}
.vxe-form.size--small .vxe-form--item-inner {
    min-height: 32px;
}
.vxe-form.size--small .vxe-default-input[type=submit],
.vxe-form.size--small .vxe-default-input[type=reset] {
    line-height: 28px;
}
.vxe-form.size--small .vxe-default-input,
.vxe-form.size--small .vxe-default-input,
.vxe-form.size--small .vxe-default-select {
    height: 30px;
}
.vxe-form.size--mini {
    font-size: 12px;
}
.vxe-form.size--mini .vxe-form--item-inner {
    min-height: 30px;
}
.vxe-form.size--mini .vxe-default-input[type=submit],
.vxe-form.size--mini .vxe-default-input[type=reset] {
    line-height: 26px;
}
.vxe-form.size--mini .vxe-default-input,
.vxe-form.size--mini .vxe-default-input,
.vxe-form.size--mini .vxe-default-select {
    height: 28px;
}

/**Variable**/
/**Variable**/
/*加载中*/
.vxe-loading {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.2);
}
.vxe-loading.is--visible {
    display: block;
}
.vxe-loading .vxe-loading--spinner {
    width: 56px;
    height: 56px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.vxe-loading .vxe-loading--spinner:before, .vxe-loading .vxe-loading--spinner:after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #409eff;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: bounce 2s infinite ease-in-out;
}
.vxe-loading .vxe-loading--spinner:after {
    animation-delay: -1s;
}
@keyframes bounce {
    0%, 100% {
        transform: scale(0);
    }
    50% {
        transform: scale(1);
    }
}

.size--mini .vxe-loading .vxe-loading--spinner {
    width: 38px;
    height: 38px;
}

.size--small .vxe-loading .vxe-loading--spinner {
    width: 44px;
    height: 44px;
}

.size--medium .vxe-loading .vxe-loading--spinner {
    width: 50px;
    height: 50px;
}

.vxe-select {
    position: relative;
    display: inline-block;
    width: 180px;
    color: #606266;
    text-align: left;
}
.vxe-select:not(.is--disabled) > .vxe-input .vxe-input--inner {
    cursor: pointer;
}
.vxe-select > .vxe-input {
    width: 100%;
}
.vxe-select > .vxe-input .vxe-input--suffix-icon {
    transition: transform 0.2s ease-in-out;
}
.vxe-select.is--active > .vxe-input .vxe-input--inner {
    border: 1px solid #409eff;
}

.vxe-select-slots {
    display: none;
}

.vxe-select--panel {
    display: none;
    position: absolute;
    left: 0;
    padding: 4px 0;
    color: #606266;
    text-align: left;
    background-color: #fff;
}
.vxe-select--panel:not(.is--transfer) {
    min-width: 100%;
}
.vxe-select--panel.is--transfer {
    position: fixed;
}
.vxe-select--panel.animat--leave {
    display: block;
    opacity: 0;
    transform: scaleY(0.5);
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: center top;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}
.vxe-select--panel.animat--leave[placement=top] {
    transform-origin: center bottom;
}
.vxe-select--panel.animat--enter {
    opacity: 1;
    transform: scaleY(1);
}

.vxe-select-option--wrapper {
    overflow-x: hidden;
    overflow-y: auto;
    padding: 4px 0;
    max-height: 200px;
    border-radius: 4px;
    border: 1px solid #DADCE0;
    box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}

.vxe-optgroup .vxe-optgroup--title {
    padding: 0 6px;
    color: #909399;
    font-size: 12px;
}

.vxe-optgroup--wrapper .vxe-select-option {
    padding: 0 20px;
}

.vxe-select-option {
    padding: 0 10px;
    max-width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;
}
.vxe-select-option.is--selected {
    font-weight: 700;
    color: #409eff;
}
.vxe-select-option:not(.is--disabled) {
    cursor: pointer;
}
.vxe-select-option:not(.is--disabled).is--hover {
    background-color: #f5f7fa;
}
.vxe-select-option.is--disabled {
    color: #BFBFBF;
    cursor: no-drop;
}

.vxe-select--empty-placeholder {
    padding: 0 10px;
    text-align: center;
}

.vxe-select,
.vxe-select--panel {
    font-size: 14px;
}
.vxe-select.size--medium,
.vxe-select--panel.size--medium {
    font-size: 14px;
}
.vxe-select.size--small,
.vxe-select--panel.size--small {
    font-size: 13px;
}
.vxe-select.size--mini,
.vxe-select--panel.size--mini {
    font-size: 12px;
}

.vxe-select--panel .vxe-optgroup--title,
.vxe-select--panel .vxe-select-option {
    height: 30px;
}
.vxe-select--panel .vxe-optgroup--title,
.vxe-select--panel .vxe-select-option,
.vxe-select--panel .vxe-select--empty-placeholder {
    line-height: 30px;
}
.vxe-select--panel.size--medium .vxe-optgroup--title,
.vxe-select--panel.size--medium .vxe-select-option {
    height: 28px;
}
.vxe-select--panel.size--medium .vxe-optgroup--title,
.vxe-select--panel.size--medium .vxe-select-option,
.vxe-select--panel.size--medium .vxe-select--empty-placeholder {
    line-height: 28px;
}
.vxe-select--panel.size--small .vxe-optgroup--title,
.vxe-select--panel.size--small .vxe-select-option {
    height: 26px;
}
.vxe-select--panel.size--small .vxe-optgroup--title,
.vxe-select--panel.size--small .vxe-select-option,
.vxe-select--panel.size--small .vxe-select--empty-placeholder {
    line-height: 26px;
}
.vxe-select--panel.size--mini .vxe-optgroup--title,
.vxe-select--panel.size--mini .vxe-select-option {
    height: 24px;
}
.vxe-select--panel.size--mini .vxe-optgroup--title,
.vxe-select--panel.size--mini .vxe-select-option,
.vxe-select--panel.size--mini .vxe-select--empty-placeholder {
    line-height: 24px;
}

/**Variable**/
/**Variable**/
/**Variable**/
.vxe-switch {
    display: inline-block;
    color: #606266;
    vertical-align: middle;
    padding: 0.4em;
    user-select: none;
    text-align: center;
}
.vxe-switch.is--animat .vxe-switch--button {
    transition: border-color 0.3s, background-color 0.3s;
}
.vxe-switch.is--animat .vxe-switch--icon {
    transition: all 0.3s;
}
.vxe-switch.is--on .vxe-switch--button {
    padding-right: 1.7em;
    background-color: #409eff;
}
.vxe-switch.is--on .vxe-switch--icon {
    left: 100%;
    transform: translateX(-1.4em);
}
.vxe-switch.is--off .vxe-switch--button {
    padding-left: 1.7em;
    background-color: rgba(0, 0, 0, 0.35);
}
.vxe-switch.is--off .vxe-switch--icon {
    left: 0.2em;
    transform: translateX(0);
}
.vxe-switch.is--on .vxe-switch--label-off, .vxe-switch.is--off .vxe-switch--label-on {
    height: 0;
    visibility: hidden;
    overflow: hidden;
}
.vxe-switch.is--on .vxe-switch--label, .vxe-switch.is--off .vxe-switch--label {
    opacity: 1;
}
.vxe-switch:not(.is--disabled) .vxe-switch--button {
    cursor: pointer;
}
.vxe-switch:not(.is--disabled) .vxe-switch--button:focus {
    box-shadow: 0 0 0.4em 0 #409eff;
}
.vxe-switch.is--disabled .vxe-switch--button {
    cursor: no-drop;
}
.vxe-switch.is--disabled.is--on .vxe-switch--button {
    background-color: #a6d2ff;
}
.vxe-switch.is--disabled.is--off .vxe-switch--button {
    background-color: rgba(0, 0, 0, 0.15);
}
.vxe-switch .vxe-switch--button {
    display: block;
    position: relative;
    height: 1.6em;
    line-height: 1;
    min-width: 3.2em;
    padding: 0 0.6em;
    border-radius: 1em;
    border: 0;
    outline: 0;
}
.vxe-switch .vxe-switch--label {
    opacity: 0;
    display: block;
    color: #fff;
    font-size: 0.8em;
}
.vxe-switch .vxe-switch--icon {
    position: absolute;
    top: 0.2em;
    left: 0;
    width: 1.2em;
    height: 1.2em;
    border-radius: 50%;
    background-color: #fff;
}
.vxe-switch .vxe-switch--label-icon {
    margin-right: 0.25em;
}

.vxe-switch {
    font-size: 14px;
}
.vxe-switch.size--medium {
    font-size: 14px;
}
.vxe-switch.size--small {
    font-size: 13px;
}
.vxe-switch.size--mini {
    font-size: 12px;
}

/**Variable**/
.vxe-list {
    position: relative;
    display: block;
    padding: 0;
}
.vxe-list .vxe-list--virtual-wrapper {
    position: relative;
    overflow: auto;
}
.vxe-list .vxe-list--y-space {
    width: 0;
    float: left;
}
.vxe-list .vxe-list--virtual-wrapper,
.vxe-list .vxe-list--body {
    padding: 0;
    margin: 0;
    border: 0;
    outline: 0;
}

.vxe-list--virtual-wrapper {
    height: 100px;
}

/**Variable**/
/**Variable**/
.vxe-pulldown {
    position: relative;
    display: inline-block;
    color: #606266;
    text-align: left;
}

.vxe-pulldown--panel {
    display: none;
    position: absolute;
    left: 0;
    padding: 4px 0;
    color: #606266;
    text-align: left;
    background-color: #fff;
}
.vxe-pulldown--panel:not(.is--transfer) {
    min-width: 100%;
}
.vxe-pulldown--panel.is--transfer {
    position: fixed;
}
.vxe-pulldown--panel.animat--leave {
    display: block;
    opacity: 0;
    transform: scaleY(0.5);
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: center top;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}
.vxe-pulldown--panel.animat--leave[placement=top] {
    transform-origin: center bottom;
}
.vxe-pulldown--panel.animat--enter {
    opacity: 1;
    transform: scaleY(1);
}

.vxe-pulldown,
.vxe-pulldown--panel {
    font-size: 14px;
}
.vxe-pulldown.size--medium,
.vxe-pulldown--panel.size--medium {
    font-size: 14px;
}
.vxe-pulldown.size--small,
.vxe-pulldown--panel.size--small {
    font-size: 13px;
}
.vxe-pulldown.size--mini,
.vxe-pulldown--panel.size--mini {
    font-size: 12px;
}

/*# sourceMappingURL=table.css.map */
