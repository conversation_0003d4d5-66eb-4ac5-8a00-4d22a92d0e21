<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';
import { type MomTerminal, type FormTerminal, TerminalType } from '../../../../xtrade-sdk/dist';

const { terminal } = defineProps<{
  terminal?: MomTerminal;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [data: MomTerminal];
}>();

// 表单校验规则
const rules = {
  terminalName: [{ required: true, message: '请输入终端名称', trigger: 'blur' }],
  pwd: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  interfaceType: [{ required: true, message: '请选择接口类型', trigger: 'change' }],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormTerminal>({
  terminalName: '',
  pwd: '',
  description: '',
  status: 1,
  interfaceType: 1,
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (terminal) {
      form.value = {
        terminalName: terminal.terminalName,
        pwd: terminal.pwd,
        description: terminal.description || '',
        status: terminal.status,
        interfaceType: terminal.interfaceType,
      };
    } else {
      resetForm();
    }
  }
});

// 重置表单
const resetForm = () => {
  form.value = {
    terminalName: '',
    pwd: '',
    description: '',
    status: 1,
    interfaceType: 1,
  };
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      try {
        if (terminal) {
          const { errorCode, errorMsg, data } = await AdminService.updateTerminal({
            ...terminal,
            ...form.value,
          });
          if (errorCode === 0) {
            emit('success', data!);
            ElMessage.success('修改成功');
            handleClose();
          } else {
            ElMessage.error(errorMsg || '操作失败');
          }
        } else {
          const { errorCode, errorMsg, data } = await AdminService.createTerminal(form.value);
          if (errorCode === 0) {
            emit('success', data!);
            ElMessage.success('添加成功');
            handleClose();
          } else {
            ElMessage.error(errorMsg || '操作失败');
          }
        }
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error('操作失败');
      }
    }
  });
};

// 接口类型选项
const interfaceTypeOptions = Object.values(TerminalType);
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="terminal ? '编辑终端' : '新建终端'"
    width="500px"
    @close="handleClose"
    draggable
    destroy-on-close
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="终端名称" prop="terminalName">
        <el-input :disabled="!!terminal" v-model="form.terminalName" placeholder="请输入终端名称" />
      </el-form-item>

      <el-form-item v-if="!terminal" label="密码" prop="pwd">
        <el-input
          v-model="form.pwd"
          type="password"
          placeholder="请输入密码"
          show-password
          flex-1
        />
      </el-form-item>

      <el-form-item label="接口类型" prop="interfaceType">
        <el-select v-model="form.interfaceType" placeholder="请选择接口类型">
          <el-option
            v-for="option in interfaceTypeOptions"
            :key="option.Value"
            :label="option.Label"
            :value="option.Value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述信息" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述信息"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
