const { TypicalDataView } = require('../../classcial/typical-data-view');
const { AccountDetail } = require('../../../../model/account');
const { repoAccount } = require('../../../../repository/account');

class AccountsView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '账号列表');
    }

    /**
     * @param {AccountDetail} record 
     */
    testRecords(record) {
        return this.tableObj.matchKeywords(record) || this.testPy(record.identityName, this.states.keywords);
    }

    async requestRecords() {

        if (this.helper.isNone(this.identityId)) {

            console.log('no contextual identity has been set');
            return;
        }

        var resp = await repoAccount.getAccountDetailInfo({ user_id: this.userInfo.userId, identity_id: this.identityId });
        if (resp.errorCode != 0) {
            this.interaction.showError(`账号列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var accounts = resp.data.list;
        this.tableObj.refill(accounts.map(x => new AccountDetail(x)));
    }

    handleIdentityChange(identityId) {

        // if (this.identityId == identityId) {
        //     return;
        // }
        
        this.identityId = identityId;
        this.requestRecords();
    }

    /**
     * @param {AccountDetail} record
     */
    formatClosePosition(record) {
        return '<button class="danger" event.onclick="confirmCloseAll">一键平仓</button>';
    }

    /**
     * @param {AccountDetail} record
     */
    confirmCloseAll(record) {

        this.interaction.showConfirm({

            title: '一键平仓确认',
            message: '一键平仓账号：' + record.identityName,
            confirmed: () => {
                console.log({ identityId: record.id }, record);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.closePosition, { identityId: record.id });
                this.interaction.showSuccess(`平仓请求已发出，目标：${record.identityName}`);
            },
        });
    }

    build($container, options) {

        super.build($container, options, { tableName: 'smt-oca', heightOffset: 98 });
        this.registerEvent('set-context-identity', this.handleIdentityChange.bind(this));
    }
}

module.exports = { AccountsView };