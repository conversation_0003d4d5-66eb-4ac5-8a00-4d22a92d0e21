/**
 * 风控指标组件名称定义
 */
export enum IdcComponentNameDef {
  /** 黑名单 */
  BlackList = 'BlankListRisk',
  /** 白名单 */
  WhiteList = 'WhiteListRisk',
  /** 单笔委托最大数量限制 */
  SingleOrderMaxVolume = 'SingleVolumeRisk',
  /** 账号总委托数量限制 */
  AccountOrderMaxVolume = 'OrderCountRisk',
  /** 单笔委托最大金额限制 */
  SingleOrderMaxAmount = 'SingleTradeAmountRisk',
  /** 净买入金额限制 */
  NetBuyAmount = 'NetBuyAmountRisk',
  /** 交易频率限制 */
  TradeFrequency = 'TradeFrequencyRisk',
  /** 撤单率限制 */
  OrderCancelRate = 'CancelRateRisk',
  /** 无效委托率限制 */
  InvalidOrderRate = 'InvalidOrderRisk',
  /** 日内反向 */
  IndayReversedDirection = 'IndayReversedDirectionRisk',
  /** 自成交限制 */
  SelfTrade = 'SelfTradeRisk',
  /** 价格偏离度限制 */
  PriceDeviation = 'PriceDeviationRisk',
  /** 委托价格限制 */
  PriceLimit = 'PriceLimitRisk',
  /** 市值 */
  MarketValue = 'MarketValueRisk',
  /** 市值占比 */
  MarketValueRatio = 'MarketValueRateRisk',
  /** 总股本占比 */
  MarketCapitalRatio = 'TotalShrRateRisk',
  /** 流通股本占比 */
  FlowableMarketCapitalRatio = 'FloatShrRateRisk',
  /** 期货保证金 */
  FuturesMargin = 'FutureMarginRateRisk',
  /** 净值止损 */
  NavStopLoss = 'NavStopLossRisk',
  /** 期货持仓量占比 */
  FuturesPositionRatio = 'FuturePositionRateRisk',
}
