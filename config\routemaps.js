module.exports = {

    100: { path: '@admin/shared/org', icon: 'iconfont icon-tab_jigou' },
    200: { path: '@admin/shared/product', icon: 'iconfont icon-tab_chanpin' },
    300: { path: '@admin/shared/account', icon: 'iconfont icon-tab_zhanghu' },
    303: { path: '@admin/shared/account-monitor', icon: 'iconfont icon-tab_zhanghu', tag: 'org' },
    500: { path: '@admin/role', icon: 'iconfont icon-tab_jiaose' },
    600: { path: '@admin/user', icon: 'iconfont icon-tab_yonghu' },
    700: {},
    800: { path: '@admin/terminal', icon: 'iconfont icon-tab_zhongduan' },
    900: { path: '@admin/trading-day', icon: 'iconfont icon-tab_jiaoyiri' },
    1000: { path: '@admin/risk-control', icon: 'iconfont icon-tab_fengkong' },
    1100: { path: '@admin/broker', icon: 'iconfont icon-tab_jingjishang' },
    1200: { path: '@admin/maintain', icon: 'iconfont icon-tab_yunwei' },
    1300: { path: '@admin/report', icon: 'iconfont icon-tab_baogao' },
    1400: { path: '@admin/app' },
    1600: {},
    1601: { path: '@admin/approve-pending', icon: 'el-icon-phone-outline' },
    1602: { path: '@admin/approve-completed', icon: 'el-icon-circle-check' },
    1603: { path: '@admin/approve-sent', icon: 'el-icon-circle-check' },
    1700: { path: '@admin/workflow' },
    1800: { path: '@report/report', icon: 'iconfont icon-tab_baogao' },
    1900: { path: '@2021/overview/product/index' },
    2100: { path: '@2021/overview/account/index', icon: 'iconfont icon-chanpin' },
    2200: {},
    2201: { path: '@2021/query/asset' },
    2202: { path: '@2021/query/entrust' },
    2203: { path: '@2021/query/position' },
    2204: { path: '@2021/query/trade' },
    2205: { path: '@2021/query/credit' },
    2300: {},
    2301: { path: '@2021/trading/tools/normal' },
    2302: { path: '@2021/trading/tools/batch' },
    2303: { path: '@2021/trading/tools/basket' },
    // 2304: { path: '@2021/trading/tools/algo' },
    2304: { path: '@2023/algot/main' },
    // 2305: { path: '@2021/trading/tools/scan' },
    2306: { path: '@2021/trading/tools/apply' },
    2307: { path: '@2023/t0/main' },
    2308: { path: '@2021/trading/tools/tradional-basket' },
    2350: {},
    2351: { path: '@2021/trading/tools/normal' },
    2352: { path: '@2021/trading/tools/batch' },
    2353: { path: 'https://quant-test.yntrust.com/api/quantify-trade/client/casLogin?role=counselor' },
    2354: { path: '@2025/planed-audit' },
    3001: { path: '@2023/yuliang/entrance' },
    9900: { path: '', icon: '' },
    9902: { path: '@2023/riskc/message' },
    9903: { path: '@2023/homepage' },
    10000: { path: '@2024/trading/black-and-white' },
};