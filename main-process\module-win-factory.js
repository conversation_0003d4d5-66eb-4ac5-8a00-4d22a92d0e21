const path = require('path');
const fs = require('fs');
const electron = require('electron');
const remote = require("@electron/remote/main");
const { BrowserWindow } = electron;
const { MainModule } = require('./main-module');
const { Routing } = require('../model/routing');

class WinFactoryModule extends MainModule {

    constructor(module_name) {

        super(module_name);
        this.viewFolderName = 'view';
        this.viewDir = path.resolve(__dirname, `../${this.viewFolderName}`);
        this.routings = [new Routing()];
        this.routings.pop();

        // 预创建窗口，和窗口个数上限设置
        this.landscapeWins = [];
        this.landscapeWinsNum = 2;
        this.tabViewWins = [];
        this.tabViewWinsNum = 2;
        this.initialWindowCounter = 0;
    }

    setupRouting(location, relative_path) {

        var file_names = fs.readdirSync(location, { encoding: 'utf8' });
        var file_name_map = {};
        var template_postfix = '.html';

        file_names.forEach(fnm => { file_name_map[fnm.toLowerCase()] = true; });
        file_names.forEach(file_name => {

            let full_file_path = path.join(location, file_name);
            let stats = fs.statSync(full_file_path);

            if(stats.isDirectory()) {
                this.setupRouting(full_file_path, path.join(relative_path, file_name));
                return;
            }
            else if(!file_name.endsWith(template_postfix)) {
                return;
            }

            let net_file_name = file_name.replace(template_postfix, '');
            let view_name = path.join(relative_path, net_file_name).replace(/\\/ig,'/');
            let html_file = path.join(location, net_file_name + template_postfix);
            let script_file = net_file_name + '.js';
            let script = file_name_map[script_file.toLowerCase()] ? path.join(location, script_file) : null;

            let css_file = net_file_name + '.css';
            let css_file_lower = css_file.toLowerCase();
            let css = file_name_map[css_file_lower] ? path.join(location, css_file) : null;
            let css_link = file_name_map[css_file_lower] ? path.join('../', this.viewFolderName, relative_path, css_file).replace(/\\/ig,'/') : null;

            this.routings.push(new Routing(view_name, html_file, script, css, css_link));
        });
    }

    createMoreLandscapeWins(is_initial_creation) {

        while (this.landscapeWins.length < this.landscapeWinsNum) {
            this.landscapeWins.push(this.createLandscapeWin(is_initial_creation));
        }
    }

    createLandscapeWin(is_initial_creation) {

        var new_win = new BrowserWindow({

            width: 100, 
            height: 100, 
            frame: false, 
            show: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
            }
        });

        // 设置当前窗口对Render Process可见
        remote.enable(new_win.webContents);
        new_win.loadURL(`file://${path.join(__dirname, '../component/win-landscape.html')}`);
        
        // 窗口创建好，在窗口对象上触发（推送）全局视图配置信息
        new_win.on(this.systemEvent.renderWindowPrepared, () => {
            new_win.emit(this.systemEvent.globalViewRouting, this.routings);
        });

        if(is_initial_creation) {
            new_win.webContents.on('did-finish-load', this.countFirstBatchCreation.bind(this));
        }
        return new_win;
    }

    createMoreTabViewWins() {

        while (this.tabViewWins.length < this.tabViewWinsNum) {
            this.tabViewWins.push(this.createTabViewWin());
        }
    }

    createTabViewWin() {

        var new_win = new BrowserWindow({

            width: 100, 
            height: 100, 
            frame: false, 
            show: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
            }
        });

        // 设置当前窗口对Render Process可见
        remote.enable(new_win.webContents);
        new_win.loadURL(`file://${path.join(__dirname, '../component/win-tab-view.html')}`);
        
        // 窗口创建好，在窗口对象上触发（推送）全局视图配置信息
        new_win.on(this.systemEvent.renderWindowPrepared, () => {
            new_win.emit(this.systemEvent.globalViewRouting, this.routings);
        });
        return new_win;
    }

    countFirstBatchCreation() {

        this.initialWindowCounter += 1;
        
        /**
         * 预创建窗口到达预定数量时，启动第一个窗口
         */
        if (this.initialWindowCounter >= this.landscapeWinsNum) {
            this.mainProcess.emit('to-create-first-window');
        }
    }

    getCustomizedWindow(the_win, win_options) {

        if (!(the_win instanceof BrowserWindow)) {
            throw new Error('the window is not an instance of [BrowserWindow]');
        }
        else if (!this.helper.isJson(win_options)) {
            return the_win;
        }

        if (win_options.width >= 0 && win_options.height >= 0) { the_win.setSize(win_options.width, win_options.height); }
        if (win_options.minWidth >= 0 && win_options.minHeight >= 0) { the_win.setMinimumSize(win_options.minWidth, win_options.minHeight); }
        if (typeof win_options.maximizable == 'boolean') { the_win.setMaximizable(win_options.maximizable); }
        if (typeof win_options.minimizable == 'boolean') { the_win.setMinimizable(win_options.minimizable); }
        if (typeof win_options.resizable == 'boolean') { the_win.setResizable(win_options.resizable); }
        if (typeof win_options.alwaysOnTop == 'boolean') { the_win.setAlwaysOnTop(win_options.alwaysOnTop); }
        if (win_options.x >= 0 && win_options.y >= 0) { the_win.setPosition(win_options.x, win_options.y); }

        if(win_options.close2Hide === true) {
            the_win.on('close', (event) => {
                event.preventDefault();
                the_win.hide();
            });
        }

        if(win_options.highlight === true) {
            the_win.on('focus', (event) => { the_win.emit(this.systemEvent.highlightWindow, true); });
            the_win.on('blur', (event) => { the_win.emit(this.systemEvent.highlightWindow, false); });
        }

        if(win_options.devTools === true) {
            the_win.webContents.openDevTools();
        }

        return the_win;
    }

    grabOneLandscapeWin() {

        var destroyed = this.landscapeWins.filter(x => x.isDestroyed());
        if (destroyed.length > 0) {

            this.landscapeWins.remove(x => destroyed.findIndex(y => y === x) >= 0);
            destroyed.length = 0;
            this.createMoreLandscapeWins();
        }

        return this.landscapeWins.shift();
    }

    /**
     * 来自主进程内部的请求，索取 landscape 窗口，采用emit触发，无event对象，不跨进程，返回结果通过callback方式进行回应
     */
    huntWinLandscape4Main(view_name, window_options, callback) {

        if (this.landscapeWins.length == 0) {
            this.createMoreLandscapeWins();
        }

        var the_win = this.getCustomizedWindow(this.grabOneLandscapeWin(), window_options);
        var routing = this.routings.find(x => x.name == view_name);
        the_win.webContents.send('build-app', routing);
        the_win.show();
        this.reportWindowBorn(the_win);
        typeof callback == 'function' ? callback(the_win) : null;
        this.createMoreLandscapeWins();
    }

    /**
     * 来自渲染进程的请求，索取 landscape 窗口，跨进程，目标窗口ID通过ipc请求通知
     */
    huntWinLandscape4Render(event, view_name, window_options) {
        
        if (this.landscapeWins.length == 0) {
            this.createMoreLandscapeWins();
        }

        var the_win = this.getCustomizedWindow(this.grabOneLandscapeWin(), window_options);
        var routing = this.routings.find(x => x.name == view_name);

        the_win.webContents.send('build-app', routing);
        the_win.show();
        this.reportWindowBorn(the_win);
        event.sender.send(this.systemEvent.huntWinLandscapeFromRender, the_win.id);
        this.createMoreLandscapeWins();
    }

    grabOneTabWin() {

        var destroyed = this.tabViewWins.filter(x => x.isDestroyed());
        if (destroyed.length > 0) {

            this.tabViewWins.remove(x => destroyed.findIndex(y => y === x) >= 0);
            destroyed.length = 0;
            this.createMoreTabViewWins();
        }

        return this.tabViewWins.shift();
    }

    /**
     * 来自主进程内部的请求，索取 tab view 窗口，采用emit触发，无event对象，不跨进程，返回结果通过callback方式进行回应
     */
    huntWinTabView4Main(view_name, title, window_options, callback) {

        if (this.tabViewWins.length == 0) {
            this.createMoreTabViewWins();
        }

        var the_win = this.getCustomizedWindow(this.grabOneTabWin(), window_options);
        var routing = this.routings.find(x => x.name == view_name);

        if(!routing) {
            electron.dialog.showErrorBox(`视图错误', '视图模板未找到，标识：${view_name}`);
            return;
        }
        
        the_win.webContents.send('add-first-tab', routing, title, window_options);
        the_win.show();
        this.reportWindowBorn(the_win);
        typeof callback == 'function' ? callback(the_win) : null;
        this.createMoreTabViewWins();
    }

    /**
     * 来自渲染进程的请求，索取 tab view 窗口，跨进程，目标窗口ID通过ipc请求通知
     */
    huntWinTabView4Render(event, view_name, title, window_options) {
        
        if(this.tabViewWins.length == 0) {
            this.createMoreTabViewWins();
        }

        var the_win = this.getCustomizedWindow(this.grabOneTabWin(), window_options);
        var routing = this.routings.find(x => x.name == view_name);

        if(!routing) {
            electron.dialog.showErrorBox(`视图错误', '视图模板未找到，标识：${view_name}`);
            return;
        }

        the_win.webContents.send('add-first-tab', routing, title, window_options);
        the_win.show();
        this.reportWindowBorn(the_win);
        event.sender.send(this.systemEvent.huntWinTabViewFromRender, the_win.id);
        this.createMoreTabViewWins();
    }

    /**
     * @param { BrowserWindow } the_win 
     */
    reportWindowBorn(the_win) {

        const null_as_event = null;
        const main = this.mainProcess;
        const systemEvent = this.systemEvent;
        const winId = the_win.id;

        main.emit(this.systemEvent.reportWindowStatus, null_as_event, winId, 'created');
        the_win.on('focus', function() { main.emit(systemEvent.reportWindowStatus, null_as_event, winId, 'focused'); });
        the_win.on('blur', function() { main.emit(systemEvent.reportWindowStatus, null_as_event, winId, 'blured'); });
        the_win.on('closed', function() { main.emit(systemEvent.reportWindowStatus, null_as_event, winId, 'closed'); the_win = null; });
    }

    listen2Events() {

        this.app.on('ready', () => {
            
            this.createMoreLandscapeWins(true);
            this.createMoreTabViewWins();
        });

        this.mainProcess.on(this.systemEvent.huntWinLandscapeFromMain, this.huntWinLandscape4Main.bind(this));
        this.mainProcess.on(this.systemEvent.huntWinLandscapeFromRender, this.huntWinLandscape4Render.bind(this));
        this.mainProcess.on(this.systemEvent.huntWinTabViewFromMain, this.huntWinTabView4Main.bind(this));
        this.mainProcess.on(this.systemEvent.huntWinTabViewFromRender, this.huntWinTabView4Render.bind(this));

        /**
         * 注销时，清除已分配的相关未释放资源
         */
        this.mainProcess.on(this.systemEvent.toLogout, event => {

            this.landscapeWins.clear();
            this.tabViewWins.clear();

            /**
             * 注销后关闭了所有的预备窗口，重新创建
             */
            setTimeout(() => {
                this.createMoreLandscapeWins();
                this.createMoreTabViewWins();
            }, 500);
        });
    }

    run() {
        this.listen2Events();
        this.setupRouting(this.viewDir, '');
    }
}

module.exports = { WinFactoryModule };