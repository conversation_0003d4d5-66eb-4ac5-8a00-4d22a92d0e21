<script setup lang="tsx">
import { shallowRef, onMounted, ref, useTemplateRef, reactive, onActivated } from 'vue';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import type { ColumnDefinition, RowAction } from '@/types';
import { deepClone, getUser, isNone, isNotNone, remove } from '@/script';
import { AlgoParamType, AlgoParamTypes } from '@/enum';
import { ElMessage } from 'element-plus';

import {
  type AlgoParam,
  type MomBroker,
  type TradingAlgorithm,
  AlgoStrategyType,
  AlgoStrategyTypes,
  AlgorithmVendors,
  Repos,
} from '../../../../xtrade-sdk/dist';

const usr = getUser()!;
// 控制对话框显示
const dialogVisible = ref(false);
const nullv = null as any;
// 算法表单数据
const formModel = ref<TradingAlgorithm>(createEmpty());

function createEmpty(): TradingAlgorithm {
  return {
    id: null as any,
    name: nullv,
    remark: nullv,
    vendorId: nullv,
    brokerId: nullv,
    externalId: 0,
    params: [],
    strategyType: AlgoStrategyType.SplitOrder.value,
    userId: usr.userId,
  };
}

const selectedProp = ref('');
// 算法某个参数表单数据
const formParam = ref<AlgoParam | null>(null);

// 算法表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入算法名称', trigger: 'blur' }],
  vendorId: [{ required: true, message: '请选择算法开发商', trigger: 'blur' }],
  externalId: [{ required: true, message: '请输入算法外部ID', trigger: 'blur' }],
  brokerId: [{ required: true, message: '请选择支持券商', trigger: 'blur' }],
};

// 算法参数表单验证规则
const formParamRules = {
  prop: [
    { required: true, message: '请输入参数属性名，必须为合法的变量名称', trigger: 'blur' },
    { validator: checkParamProp, trigger: 'blur' },
  ],
  label: [
    { required: true, message: '请输入参数显示名称', trigger: 'blur' },
    // { validator: checkParamLabel, trigger: 'blur' },
  ],
  type: [{ required: true, message: '请选择参数类型', trigger: 'change' }],
  required: [{ required: true, message: '请选择是否必填', trigger: 'change' }],
  defaultValue: [{ required: true, message: '请输入默认值', trigger: 'blur' }],
  optionLabel: [{ required: true, message: '请输入选项含义', trigger: 'blur' }],
  display: [{ required: true, message: '请参数是否展示', trigger: 'blur' }],
  remark: [{ required: false, message: '请输入参数描述', trigger: 'blur' }],
};

function checkParamProp(rule: any, value: any, callback: any) {
  if (!isValidIdentifier(value)) {
    callback(new Error('参数名称不合法'));
  } else {
    callback();
  }

  // const matched = formModel.value.params.find(x => x.prop == value);
  // if (matched) {
  //   callback(new Error('有存在相同变量名称 = ' + value));
  // } else if (!isValidIdentifier(value)) {
  //   callback(new Error('参数名称不合法'));
  // } else {
  //   callback();
  // }
}

// function checkParamLabel(rule: any, value: any, callback: any) {
//   const matched = formModel.value.params.find(x => x.label == value);
//   if (matched) {
//     callback(new Error('有存在相同名称参数 = ' + value));
//   } else {
//     callback();
//   }
// }

// 表单引用
const formRef = useTemplateRef('formRef');
const formParamRef = useTemplateRef('formParamRef');
// 表单标题
const dialogTitle = ref('创建算法');
// // 是否为创建
// const isCreation = computed(() => formModel.value.id == 0 || isNone(formModel.value.id));
// // 是否为编辑现有
// const isEdit = computed(() => !isCreation.value);

// 定义列配置
const columns: ColumnDefinition<TradingAlgorithm> = [
  { key: 'id', title: '算法ID', width: 100, sortable: true },
  { key: 'name', title: '算法名称', width: 150, sortable: true },
  { key: 'remark', title: '备注', width: 200, sortable: true },
  { key: 'vendorId', title: '供应商ID', width: 120, sortable: true },
  { key: 'brokerId', title: '经纪商ID', width: 120, sortable: true },
];

// 行操作
const rowActions: RowAction<TradingAlgorithm>[] = [
  {
    label: '编辑',
    onClick: (row: TradingAlgorithm) => {
      edit(row);
    },
  },
  {
    label: '删除',
    onClick: (row: TradingAlgorithm) => {
      discard(row);
    },
  },
];

const repoInstance = new Repos.TradeManageRepo();
const repoAdmin = new Repos.AdminRepo();
const algoList = shallowRef<TradingAlgorithm[]>([]);
const brokerList = shallowRef<MomBroker[]>([]);

// 加载算法列表
async function request() {
  algoList.value = (await repoInstance.QueryAlgos()).data || [];
}

async function requestBrokers() {
  brokerList.value = (await repoAdmin.QueryBrokers()).data || [];
}

// 创建新算法
function create() {
  dialogTitle.value = '创建算法';
  // 重置表单
  formModel.value = createEmpty();
  dialogVisible.value = true;
}

// 编辑算法
function edit(row: TradingAlgorithm) {
  dialogTitle.value = '编辑算法';
  // 填充表单数据
  formModel.value = deepClone(row);
  dialogVisible.value = true;
}

// 删除算法
async function discard(row: TradingAlgorithm) {
  const resp = await repoInstance.DeleteRule(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    dialogVisible.value = false;
    ElMessage.success('已删除');
    request();
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

function isValidIdentifier(str: string) {
  // 基本字符校验：以字母、_ 或 $ 开头，后面跟字母、数字、_、$
  const basicPattern = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/;

  // 保留关键字列表（简化版，实际更多）
  const keywords = new Set([
    'break',
    'case',
    'catch',
    'class',
    'const',
    'continue',
    'debugger',
    'default',
    'delete',
    'do',
    'else',
    'export',
    'extends',
    'finally',
    'for',
    'function',
    'if',
    'import',
    'in',
    'instanceof',
    'let',
    'new',
    'return',
    'super',
    'switch',
    'this',
    'throw',
    'try',
    'typeof',
    'var',
    'void',
    'while',
    'with',
    'yield',
    'enum',
    'await',
    'implements',
    'interface',
    'package',
    'private',
    'protected',
    'public',
    'static',
    'null',
    'true',
    'false',
  ]);

  // 先检查格式
  if (!basicPattern.test(str)) {
    return false;
  }

  // 再检查是否为关键字
  if (keywords.has(str)) {
    return false;
  }

  // 额外：检查是否为空字符串
  if (str.length === 0) {
    return false;
  }

  return true;
}

function createEmptyParam(): AlgoParam {
  return {
    label: '',
    prop: '',
    type: '',
    required: true,
    defaultValue: null,
    uoptions: [],
    optionLabel: '',
    remark: '',
    display: true,
  };
}

function handlePropChange() {
  const matched = formModel.value.params.find(x => x.prop === selectedProp.value);
  formParam.value = isNotNone(selectedProp.value) ? matched! : null;
}

function addParam() {
  selectedProp.value = '';
  const empty = createEmptyParam();
  formParam.value = empty;
  formParamRef.value?.clearValidate();
}

async function saveParam() {
  if (!formParamRef.value) {
    return;
  }

  await formParamRef.value.validate();
  const fp = formParam.value!;
  const fm = formModel.value;
  const data = deepClone(fp);
  const matched = fm.params.find(x => x.label == fp.label || x.prop == fp.prop);

  if (matched) {
    Object.assign(matched, data);
  } else {
    formModel.value.params.push(data);
  }

  addParam();
}

function resetParam() {
  selectedProp.value = '';
  const empty = createEmptyParam();
  formParam.value = empty;
  formParamRef.value?.clearValidate();
}

function handleParamTypeChange() {
  formParam.value!.defaultValue = null;
}

const optionData = reactive({ visible: false, label: '', value: '' });

function hope2AddOption() {
  const od = optionData;
  od.visible = true;
  od.label = '';
  od.value = '';
}

function deleteParam() {
  const prop = selectedProp.value;
  if (!prop) {
    return;
  }

  const fp = formParam.value!;
  remove(fp.uoptions, x => x.value == prop);

  if (fp.uoptions.length > 0) {
    selectedProp.value = fp.uoptions[0].value;
  } else {
    selectedProp.value = '';
  }
}

function deleteOption() {
  const fp = formParam.value!;
  if (!fp) {
    return;
  }

  remove(fp.uoptions, x => x.value == fp.defaultValue);

  if (fp.uoptions.length > 0) {
    fp.defaultValue = fp.uoptions[0].value;
  } else {
    fp.defaultValue = null;
  }
}

function addOption() {
  const { label, value } = optionData;

  if (!label || !value) {
    ElMessage.error('请填写选项内容');
    return;
  }

  const matched = formParam.value!.uoptions.find(x => x.label == label || x.value == value);
  if (matched) {
    ElMessage.error('选项名称或取值有重复');
    return;
  }

  const { defaultValue, uoptions } = formParam.value!;
  uoptions.push({ label, value });

  if (uoptions.length > 0 && isNone(defaultValue)) {
    formParam.value!.defaultValue = uoptions[0].value;
  }

  const od = optionData;
  od.visible = false;
  od.label = '';
  od.value = '';
}

// 保存算法
async function save() {
  if (!formRef.value) {
    return;
  }

  await formRef.value.validate();
  // if (formModel.value.params.length == 0) {
  //   ElMessage.error('请添加至少一个参数');
  //   return;
  // }

  const cloned = deepClone(formModel.value);
  // cloned.params.forEach(item => {
  //   if (item.type == AlgoParamType.Date.value || item.type == AlgoParamType.Time.value) {
  //     //
  //   }

  //   item.value = item.value.toString();
  // });

  const resp = await repoInstance.CreateRule(cloned);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    dialogVisible.value = false;
    ElMessage.success('已保存');
    request();
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
}

onMounted(() => {
  request();
  requestBrokers();
});

onActivated(() => {
  requestBrokers();
});
</script>

<template>
  <div class="algo-design-view" h-full flex flex-col>
    <div h-400>
      <VirtualizedTable
        :columns="columns"
        :data="algoList"
        :row-actions="rowActions"
        :row-action-width="170"
        fixed
      >
        <template #actions>
          <div flex aic>
            <el-button type="primary" @click="create">
              <i class="iconfont icon-add" mr-5></i>
              <span>新增算法</span>
            </el-button>
          </div>
        </template>
      </VirtualizedTable>
    </div>
    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1000"
      :close-on-click-modal="false"
      append-to-body
      draggable
    >
      <div h-full flex gap-50>
        <div class="left-panel" h-full flex flex-col>
          <div h-36 lh-36 text-center>基础信息</div>
          <div w-full flex-1 of-y-auto>
            <el-form ref="formRef" :model="formModel" :rules="formRules" label-width="100px">
              <el-form-item label="算法名称" prop="name">
                <el-input v-model.trim="formModel.name" clearable />
              </el-form-item>
              <el-form-item label="算法开发商" prop="vendorId">
                <el-select
                  v-model="formModel.vendorId"
                  placeholder="请选择支持券商"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in AlgorithmVendors"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="算法外部ID">
                <el-input v-model.trim="formModel.externalId" clearable />
              </el-form-item>
              <el-form-item label="支持券商" prop="brokerId">
                <el-select
                  v-model="formModel.brokerId"
                  placeholder="请选择支持券商"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in brokerList"
                    :key="item.id"
                    :label="item.brokerName"
                    :value="item.brokerId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="策略类型">
                <el-select
                  v-model="formModel.strategyType"
                  placeholder="请选择算法策略类型"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in AlgoStrategyTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="算法说明">
                <el-input v-model.trim="formModel.remark" type="textarea" clearable />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="right-panel" h-full w-100 of-y-auto flex flex-col>
          <div h-36 lh-36 text-center>算法内参数设置</div>
          <div h-42 lh-36 pb-6 text-center>
            <el-form label-width="100px">
              <el-form-item label="参数项">
                <div h-full flex aic>
                  <el-select
                    v-model="selectedProp"
                    @change="handlePropChange"
                    placeholder="请选择需要配置的参数"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in formModel.params"
                      :key="item.prop"
                      :label="item.label"
                      :value="item.prop"
                    />
                  </el-select>
                  <el-button type="primary" size="small" @click="addParam" ml-10>
                    <span>添加参数</span>
                  </el-button>
                  <el-button
                    v-if="selectedProp"
                    type="success"
                    size="small"
                    @click="deleteParam"
                    ml-10
                  >
                    <span>删除参数</span>
                  </el-button>
                  <template v-if="formParam">
                    <el-button type="success" size="small" @click="saveParam" ml-10>
                      <span>暂存参数</span>
                    </el-button>
                    <el-button type="default" size="small" @click="resetParam">重置</el-button>
                  </template>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div v-if="formParam" class="form-param" h-100 flex-1>
            <el-form
              :model="formParam"
              :rules="formParamRules"
              label-width="100px"
              ref="formParamRef"
            >
              <el-form-item label="显示名称" prop="label">
                <el-input v-model.trim="formParam.label" placeholder="请输入显示名称" />
              </el-form-item>

              <el-form-item label="变量名称" prop="prop">
                <el-input v-model.trim="formParam.prop" placeholder="请输入参数属性变量名" />
              </el-form-item>

              <el-form-item label="参数类型" prop="type">
                <el-select
                  v-model="formParam.type"
                  @change="handleParamTypeChange"
                  placeholder="请选择参数类型"
                >
                  <el-option
                    v-for="item in AlgoParamTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <!-- 根据type类型动态显示默认值输入控件 -->

              <!-- 字符串类型 -->
              <el-form-item
                v-if="formParam.type == AlgoParamType.String.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-input v-model.trim="formParam.defaultValue" placeholder="请输入默认值" />
              </el-form-item>

              <!-- 数字类型 -->
              <el-form-item
                v-else-if="formParam.type == AlgoParamType.Number.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-input-number
                  v-model="formParam.defaultValue"
                  placeholder="请输入默认值"
                  :precision="2"
                  :controls="false"
                  style="width: 100%"
                />
              </el-form-item>

              <!-- 布尔类型 -->
              <el-form-item
                v-else-if="formParam.type == AlgoParamType.Boolean.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-switch v-model="formParam.defaultValue" active-text="是" inactive-text="否" />
              </el-form-item>

              <!-- 时间类型 -->
              <el-form-item
                v-else-if="formParam.type == AlgoParamType.Time.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-time-picker
                  v-model="formParam.defaultValue"
                  placeholder="请选择时间"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>

              <!-- 时间范围类型 -->
              <el-form-item
                v-else-if="formParam.type == AlgoParamType.TimeRange.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-time-picker
                  v-model="formParam.defaultValue"
                  is-range
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>

              <!-- 日期类型 -->
              <el-form-item
                v-else-if="formParam.type == AlgoParamType.Date.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-date-picker
                  v-model="formParam.defaultValue"
                  type="date"
                  placeholder="请选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>

              <!-- 日期范围类型 -->
              <el-form-item
                v-else-if="formParam.type == AlgoParamType.DateRange.value"
                label="默认值"
                prop="defaultValue"
              >
                <el-date-picker
                  v-model="formParam.defaultValue"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>

              <!-- 数组类型 -->
              <template v-else-if="formParam.type == AlgoParamType.Select.value">
                <el-form-item label="默认值" prop="defaultValue">
                  <div h-full flex aic>
                    <el-select
                      v-model="formParam.defaultValue"
                      placeholder="请选择选项默认值"
                      filterable
                      clearable
                    >
                      <el-option
                        v-for="item in formParam.uoptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <el-button type="primary" size="small" @click="hope2AddOption" ml-10>
                      <span>添加选项</span>
                    </el-button>
                    <el-button
                      v-if="formParam.defaultValue"
                      type="primary"
                      size="small"
                      @click="deleteOption"
                      ml-10
                    >
                      <span>删除选项</span>
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label="新选项" v-show="optionData.visible">
                  <div h-full flex aic>
                    <label>选项名称：</label>
                    <div flex-1>
                      <el-input v-model.trim="optionData.label" clearable />
                    </div>
                    <label pl-10>选项取值：</label>
                    <div flex-1>
                      <el-input v-model.trim="optionData.value" clearable />
                    </div>
                    <el-button type="primary" size="small" @click="addOption" ml-10>
                      <span>暂存选项</span>
                    </el-button>
                  </div>
                </el-form-item>
              </template>

              <!-- 默认情况 -->
              <el-form-item v-else label="默认值" prop="defaultValue">
                <el-input v-model.trim="formParam.defaultValue" placeholder="请输入默认值" />
              </el-form-item>

              <el-form-item label="是否必填" prop="required">
                <el-switch v-model="formParam.required" />
              </el-form-item>

              <el-form-item label="参数描述" prop="remark">
                <el-input
                  v-model.trim="formParam.remark"
                  type="textarea"
                  placeholder="请输入参数描述"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.algo-design-view {
  height: 100%;
  padding: 10px;
}

.left-panel {
  width: 40%;
}

.right-panel {
  width: 60%;
}

.form-param {
  .el-input__inner {
    text-align: left;
  }
}
</style>
