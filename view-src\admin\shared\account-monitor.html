<div class="xsplitter">

    <div class="part-upper" style="height: 300px;">

        <div class="toolbar">

            <el-input v-model="searching.keywords" class="input-searching" placeholder="输入关键词过滤" @change="filterRecords" clearable>
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            
            <span>&nbsp;&nbsp;</span>

            <el-select v-model="searching.assetType" @change="filterRecords" style="width: 100px;" clearable>
                <el-option v-for="(item, item_idx) in assetTypes" :key="item_idx" :label="item.mean" :value="item.code"></el-option>
            </el-select>

            <span>&nbsp;&nbsp;</span>

            <el-select v-model="searching.checkType" @change="filterRecords" style="width: 150px;" clearable>
                <el-option v-for="(item, item_idx) in checkTypes" :key="item_idx" :label="item.mean" :value="item.code"></el-option>
            </el-select>

            <span class="pagination-wrapper" style="display: block; float: right;">

                <el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
                    :current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
                    @current-change="handlePageChange"></el-pagination>

            </span>
            <!--
            <el-button size="small" type="primary" @click="doRealtimePosCheck" class="s-mgr-10" style="display: block; float: right;">
                <span class="el-icon-refresh"></span> 实时持仓检查
            </el-button>
            -->

            <!-- 
            <el-button size="small" type="primary" @click="openSanguDialog" class="s-mgr-10" style="display: block; float: right;">
                <span class="el-icon-jiaoyi"></span> 散股处理
            </el-button> 
            -->

        </div>

        <table>
            <tr>
                <th label="账号名称" prop="accountName" watch="accountName" formatter="formatAccountName" min-width="202.0" searchable sortable overflowt></th>
                <th label="ID" prop="accountId" min-width="100" searchable sortable overflowt></th>
                <th label="账号类型" prop="assetType" min-width="70" formatter="formatAssetType" sortable overflowt></th>
                <th class="cell-row-actions" label="操作" fixed-width="60" align="center" fixed="right" exportable="false" formatter="formatActions"></th>
            </tr>
        </table>

    </div>

    <div class="splitter-bar"></div>

    <div class="part-lower">

        <div class="account-summary">
            <div class="summary-tabs">
                <!-- multiple tabs 123 -->
            </div>
            <div class="summary-content">
                <!-- multiple tabs content 123 -->
            </div>
        </div>

    </div>

    <div class="dialog-error-list">
        <template>
            <el-dialog width="1100px" :title="dialog.title" :visible="dialog.visible" :close-on-click-modal="false" :show-close="false" v-drag>
                <div class="sangu-filter-option">
                    <el-checkbox v-model="dialog.onlyReal" class="s-mgr-10" @change="handleRealAccountFilter">仅真实账号</el-checkbox>
                </div>
                <div class="table-box"></div>
                <div slot="footer">
                    <el-button @click="closeDialog" type="primary" size="small">关闭</el-button>
                </div>
            </el-dialog>
        </template>
    </div>

    <div class="dialog-sangu-list">
        <template>
            <el-dialog width="1100px" :title="dialog.title" :visible="dialog.visible" :close-on-click-modal="false" :show-close="false" v-drag>
                <div class="sangu-filter-option">
                    <span v-if="dialog.estimate > 0" class="s-pdr-20">预估金额 = {{ thousandsInt(dialog.estimate) }}</span>
                    <el-button @click="onekeySellSangu" type="primary" size="small">一键卖出</el-button>
                </div>
                <div class="table-box">
                    <el-table ref="$tsangu" max-height="800" :data="dialog.records" @selection-change="handleSelectionChange">
                        <el-table-column label="选择" fixed-width="60" type="selection"></el-table-column>
                        <el-table-column label="账号ID" min-width="120" prop="accountId" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="账号名称" min-width="200" prop="accountName" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="合约代码" min-width="100" prop="instrument" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="合约名称" min-width="100" prop="instrumentName" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="最新价格" min-width="100" prop="price" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="持仓量" min-width="100" prop="totalPosition" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="散股数量" min-width="100" prop="sanguPosition" sortable searchable show-overflow-tooltip></el-table-column>
                        <el-table-column label="处理方式" min-width="100" prop="operateWay" sortable searchable show-overflow-tooltip>
                            <template slot-scope='props'>
                                <a v-html="formatOperWay(props.row)"></a>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div slot="footer">
                    <el-button @click="closeSanguDialog" type="primary" size="small">关闭</el-button>
                </div>
            </el-dialog>
        </template>
    </div>

</div>