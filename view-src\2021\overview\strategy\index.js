const { IView } = require('../../../../component/iview');
const { TabList } = require('../../../../component/tab-list');
const { Tab } = require('../../../../component/tab');
const { Splitter } = require('../../../../component/splitter');
const { StrategiesView } = require('./strategies');
const { SysStrategy } = require('../../../../model/sys-strategy');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '策略概览');
        this.events = {
            contextChange: 'set-context-identity',
        };
    }

    createStrategies() {

        var $root = this.$container.querySelector('.xsplitter > .view-strategies');
        var view = new StrategiesView('@2021/overview/strategy/strategies');
        view.registerEvent('selected-one-strategy', this.handleStrategySelect.bind(this));
        view.loadBuild($root);
        this.strategyView = view;
    }

    /**
     * @param {SysStrategy} strategy 
     */
    handleStrategySelect(strategy) {

        /**
         * 上下文策略对象
         */
        this.strategy = strategy;
        this.tabs.fireEventOnFocusedTab(this.events.contextChange, strategy.id);
    }

    createRecords() {

        var $tab = this.$container.querySelector('.xsplitter > .view-records > .tabs');
        var $content = this.$container.querySelector('.xsplitter > .view-records > .contents');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/overview/components/fund-template', '概览', { is4Strategy: true });
        tabs.openTab(true, '@2021/overview/components/composition', '净值', { is4Strategy: true });
        tabs.openTab(true, '@2021/fragment/regular-orders', '委托', { is4Strategy: true });
        tabs.openTab(true, '@2021/fragment/regular-positions', '持仓', { is4Strategy: true });
        tabs.openTab(true, '@2021/fragment/regular-exchanges', '成交', { is4Strategy: true });
        tabs.openTab(true, '@2021/overview/components/alert', '风控消息', { is4Strategy: true });
        this.tabs = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        if (this.strategy) {
            this.tabs.fireEventOnTab(tab, this.events.contextChange, this.strategy.id);
        }

        this.simulateSplit();
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {
        
        if (this.strategy) {
            this.tabs.fireEventOnTab(tab, this.events.contextChange, this.strategy.id);
        }

        this.simulateSplit();
    }

    createSplitter() {

        var bar_name = 'overview-strategy';
        var $bar = this.$container.querySelector('.xsplitter > .splitter-bar');
        this.splitter = new Splitter(bar_name, $bar, this.handleSpliting.bind(this), {

            previousMinHeight: 145,
            nextMinHeight: 200,
        });

        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    simulateSplit() {
        setTimeout(() => { this.splitter.simulateResize(); }, 100);
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {

        this.strategyView.trigger('table-max-height', previous_height);
        this.tabs.fireEventOnAllTabs('table-max-height', next_height);
    }

    exportSome() {
        this.strategyView.exportSome();
    }

    refresh() {
        this.strategyView.trigger('refresh');
    }

    build($container) {

        super.build($container);
        this.createRecords();
        this.createStrategies();
        this.createSplitter();
    }
}

module.exports = View;