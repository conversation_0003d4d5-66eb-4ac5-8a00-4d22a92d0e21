<div class="trade-form algform s-full-height">
	<div class="trade-form-inner algform-internal themed-box s-scroll-bar s-full-height">
		
		<div class="xtcontainer s-border-box s-full-height">
			<template>
				<div class="xtheader themed-header">
					<span>算法交易</span>
					<el-popover placement="bottom"
								title="导入一篮子算法单（文件请保存为UTF8格式）"
								trigger="hover"
								:append-to-body="false">
						<span class="import-algo-orders-sample-pic"></span>
						<i slot="reference" class="s-pull-right s-mgl-10 s-mgt-5 el-icon-question"></i>
					</el-popover>
					<el-button class="import-button s-pull-right" type="primary" @click="hope2Import">
						<i class="iconfont icon-daoru1"></i> 导入算法篮子</el-button>
				</div>
			</template>

			<template>

				<div class="form-external s-unselectable">
	
					<form class="xtform">
	
						<div class="direction-row">

							<el-radio-group v-model="uistates.direction" 
											@change="handleDirectionChange" 
											class="s-full-width">

								<el-radio-button v-for="(item, item_idx) in directions"
												 :key="item_idx"
												 :label="item.code"
												 :style="{ width: 100 / directions.length + '%' }">{{ item.mean }}</el-radio-button>
							</el-radio-group>
							
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">产品</span>
							<el-select v-model="istates.productId" @change="handleProductChange" filterable clearable>
								<el-option v-for="(item, item_idx) in products"
										   :key="item_idx"
										   :value="item.value"
										   :label="item.label"></el-option>
							</el-select>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">账号</span>
							<el-select v-model="istates.accountId" @change="handleAccountChange" filterable clearable>
								<el-option v-for="(item, item_idx) in accounts"
										   :key="item_idx"
										   :value="item.value"
										   :label="formatSelectAccountName(item)"></el-option>
							</el-select>
						</div>

						<div class="xtinput xtinput-algo">

							<span class="xtlabel themed-color">算法类型</span>

							<el-select v-model="uistates.algoId" @change="handleAlgoChange">								
								<el-option-group 
									v-for="(group, group_idx) in algoGrps" 
									:key="group_idx" 
									:label="group.name">
									<el-option 
										v-for="(item, item_idx) in group.algoes" 
										:key="item_idx" 
										:label="item.name" 
										:value="item.id"></el-option>
								</el-option-group>
							</el-select>

							<el-popover 
								title="算法参数值设置" 
								placement="right"
								popper-class="algo-param-popup"
								trigger="hover"
								:append-to-body="false"
								:disabled="!isParameterApplicable">

								<el-button
									slot="reference" 
									class="algo-param-setting"
									:class="{ 's-color-red': isParameterApplicable && uistates.isParamChanged }" 
									:disabled="!isParameterApplicable">参</el-button>

								<div class="themed-box s-pdt-5 s-pdb-5">
									<div v-for="(item, item_idx) in algoParams" :key="item_idx" class="alog-param-row">
										<span class="xtlabel themed-color">
											<span>{{ shortize(item.label) }}</span>
											<el-tooltip placement="top" :content="item.tooltip">
												<i class="s-mgl-5 el-icon-question"></i>
											</el-tooltip>
										</span>
										<el-input v-if="isNumberParam(item.type)" v-model.number="item.value" @change="handleAlgoParamChange" clearable></el-input>
										<el-select v-else-if="isBooleanParam(item.type)" v-model.trim="item.value" @change="handleAlgoParamChange" clearable>
											<el-option :value="true" label="启用"></el-option>
											<el-option :value="false" label="禁用"></el-option>
										</el-select>
										<el-input v-else v-model.trim="item.value" @change="handleAlgoParamChange" clearable></el-input>
									</div>
									<div v-if="algoParams.length > 0" class="alog-param-row">
										<span class="xtlabel themed-color"></span>
										<el-button type="info" @click="unkeepParam">
											<i class="el-icon-close"></i> <span>清除参数</span></el-button>
									</div>
								</div>

							</el-popover>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">合约</span>
							<el-autocomplete v-model="uistates.keywords"
											 :fetch-suggestions="suggest"
											 @keydown.native="handleUserInput"
											 @clear="handleClearIns"
											 @select="handleSelect" clearable>
	
								<template slot-scope="{ item: ins }">
									<span class="item-code">[{{ ins.instrument }}] </span>
									<span class="item-name">{{ ins.instrumentName }}</span>
								</template>
								
							</el-autocomplete>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">数量</span>
							<el-input-number placeholder="0"
											 v-model="uistates.volume"
											 :min="0" 
											 :max="999999999"
											 :step="uistates.step"
											 @change="handleVolumeChange"></el-input-number>
						</div>

						<div class="xtinput s-mgb-5">
							<span class="xtlabel themed-color">执行时间</span>
							<el-time-picker is-range 
											v-model="uistates.trange"
											range-separator="~"
											start-placeholder="开始时间"
											end-placeholder="结束时间"></el-time-picker>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">备注</span>
							<el-input v-model="uistates.remark" clearable></el-input>
						</div>

						<div class="xtinput xtinput-options">
							<span class="xtlabel themed-color">选项</span>
							<span class="trade-options">
								<el-checkbox v-model="options.onexpired">过期后继续交易</el-checkbox>
								<br>
								<el-checkbox v-model="options.onlimited">涨跌停继续交易</el-checkbox>
							</span>
						</div>
	
						<div class="xtinput button-row basket-button-row">
							<el-button v-if="isBuy" type="danger" @click="hope2Entrust">买入</el-button>
							<el-button v-else type="success" @click="hope2Entrust">卖出</el-button>
						</div>
	
					</form>

				</div>

			</template>
		</div>
		
		<div class="dialog-import">

			<el-dialog width="990px"
					:title="dialog.title"
					:visible="dialog.visible"
					:close-on-click-modal="false"
					:show-close="false">

				<template>

					<div class="data-list">
						<table>
							<tr>

								<th label="所属产品" 
									min-width="100" 
									prop="productName" overflowt sortable searchable></th>

								<th label="所属账号" 
									min-width="100" 
									prop="accountName" overflowt sortable searchable></th>

								<th label="算法类型" 
									min-width="70" 
									prop="algoName" overflowt sortable searchable></th>

								<th label="证券代码" 
									min-width="100" 
									prop="instrument" overflowt sortable searchable></th>
				
								<th label="证券名称" 
									min-width="80" 
									prop="instrumentName" overflowt sortable searchable></th>
							
								<th label="方向" 
									min-width="50"
									prop="directionName" sortable></th>				
				
								<th label="数量" 
									min-width="60" 
									prop="volume" 
									align="right" sortable thousands-int></th>
				
								<th label="开始时间" 
									min-width="70" 
									prop="startTime" sortable></th>
				
								<th label="结束时间" 
									min-width="70"
									prop="endTime" sortable></th>

								<th label="参数" 
									min-width="80" 
									prop="algoParam" overflowt></th>

								<th label="备注" 
									min-width="80" 
									prop="remark" overflowt></th>
							</tr>
						</table>
					</div>
	
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="confirm">下单</el-button>
						<el-button type="default" @click="giveup">放弃</el-button>
					</span>

				</template>

			</el-dialog>

		</div>

	</div>
</div>