<div ref="previewArea" class="preview-area" @scroll="handleScroll">
    <div class="hint" v-if="previewList.length == 0 && designMode">将指标拖至此处</div>
    <div class="drag-area" style="min-height: 300px; max-height: 800px;">
        <transition-group name="list" class="s-scroll-bar">
            <preview-box v-for="(preview, index) in previewList" :key="preview.key" :item="preview" :interval="interval"
                :raw-data="rawData" :design-mode="designMode" :identity-id="identityId" :style="getBoxStyle(preview)"
                :controller="controller" :class="getBoxClass(preview)" :destroy="destroy" @click.native="handleClick"
                @mousedown.native="handleDown">
            </preview-box>
        </transition-group>
    </div>
    <div v-if="designMode" class="hover-box" :style="splitStyle"></div>
    <div v-if="designMode" v-show="dragName" class="drag-box" :style="dragStyle">{{dragName}}</div>
</div>