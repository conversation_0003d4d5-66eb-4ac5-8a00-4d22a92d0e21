
class Routing {

    /**
     * @param {String} name <required> template html file relative path, eg. b/c (postfix '.html' is omitted)
     * @param {String} html <required> full html file path, in file system format, eg. d:/a/b/c.html
     * @param {String} script <required> full script file path, in file system format, eg. d:/a/b/c.js
     * @param {String} css <optional> full css file path, in file system format, eg. d:/a/b/c.css
     * @param {String} css_link <optional> css link address, for css reference by html tag <link>     
     */
    constructor(name, html, script, css, css_link) {

        /**
         * <required> html template file relative path, always starting with '@'
         */
        this.name = '@' + name;

        /**
         * <required> html file full file system path
         */
        this.html = html;

        /**
         * <required> script file full file system path (absent means null)
         */
        this.script = script || null;

        /**
         * <optional> css file full file system path (absent means null)
         */
        this.css = css || null;

        /**
         * <optional> css link address, for css reference by html tag <link> (absent means null)
         */
        this.cssLink = css_link || null;
    }

    /**
     * duplicate an instance
     * @param {*} struc can be instance of [Routing] or not
     */
    static duplicate(struc) {
        
        var copied = new Routing();
        for (let pname in struc) {
            copied[pname] = struc[pname];
        }

        return copied;
    }
}

module.exports = { Routing };