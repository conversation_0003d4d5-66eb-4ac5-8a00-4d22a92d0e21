/**
 * 发往服务器的功能码
 */
export enum ServerFunction {

    /** 心跳 */
    HeartBeat = 1,
    /** 心跳Ping包 */
    Ping = 8,
    /** 用户登录交易服务器/行情服务器 */
    UserLogOn = 10000,
    /** 发送订单 */
    SendOrder = 10001,
    /** 取消订单 */
    CancelOrder = 10002,
    /** 策略调仓 */
    StrategyAdjustPosition = 10003,
    /** 发送篮子订单 */
    SendBasketOrder = 10007,
    /** 暂停篮子 */
    SuspendBasket = 10008,
    /** 重置篮子 */
    ResetBasket = 10009,
    /** 取消篮子订单 */
    CancelBasketOrder = 10010,
    /** 确认篮子 */
    ConfirmBasket = 10011,
    /** 请求批量订单(母单) */
    RequestMotherOrder = 10022,
    /** 请求今日订单 */
    RequestTodayOrder = 10030,
    /** 请求今日交易记录 */
    RequestTodayTradeRecord = 10031,
    /** 请求今日持仓 */
    RequestTodayPosition = 10032,
    /** 请求账号权益数据 */
    RequestAccountEquity = 10033,
    /** 请求账号持仓数据 */
    RequestAccountPosition = 10034,
    /** 发送母单 */
    SendMotherOrder = 10035,
    /** 追单 */
    ReplaceOrder = 10100,
    /** 平仓 */
    ClosePosition = 10101,
    /** 订阅Tick数据 */
    SubscribeTick = 11000,
    /** 取消订阅Tick数据 */
    UnsubscribeTick = 11001,
    /** 刷新令牌 */
    RefreshToken = 11002,
    /** 子账户变更 */
    SubAccountChange = 11005,
    /** 取消子账户变更 */
    UnsubAccountChange = 11006,
    /** 订阅可交易列表 */
    SubscribeTradableList = 11007,
    /** 审核订单 */
    AuditOrder = 11010,
    /** 发送算法单查询请求 */
    RequestAlgoOrder = 13002,
    /** 发送算法单 */
    SendAlgoOrder = 13000,
    /** 撤销算法单 */
    CancelAlgoOrder = 13001,
    /** 用户登出 */
    UserLogout = 19999,
}