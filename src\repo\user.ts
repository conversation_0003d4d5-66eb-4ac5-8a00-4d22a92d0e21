import { BaseRepo } from '../modules/base-repo';
import { SocketDataPackage } from '../types/data-package';
import { GetLogger, SetUserInfo, UpdateUserToken } from '../global-state';
import { MessageDataType, SocketServerType } from '../config/architecture';
import { ServerFunction } from '../config/server-function';
import { ServerEvent } from '../config/server-event';
import { UserLoginResponse } from '../types/table/admin';

const defaultLogger = GetLogger();
const states = {
    tokenUpdateJob: null as any,
};

function UpdateToken(data: SocketDataPackage<string>) {

    const token = data.body!;
    defaultLogger.debug('Token updated', token);
    UpdateUserToken(token);
}

export class UserRepo extends BaseRepo {

    /**
     * 登录交易服务器、行情服务器（行情服务器可选，根据用户角色决定是否需要）
     * @param userName - 用户名
     * @param password - 密码
     * @param mac - MAC地址
     * @param os - 操作系统信息
     * @param quote - 是否登录行情服务器，默认为 true
     */
    async SignIn(userName: string, password: string, mac: string, os: string, quote: boolean = false) {

        const result = await this.ExecSignIn(userName, password, mac, os, quote);

        /**
         * 采用SOCKET通信，交易服务器登录成功时，开启刷新用户令牌
         */
        if (this.isSocket && result.trade && result.trade.errorCode == 0) {

            this.tserver.subscribe(ServerEvent.ForcedKickOut, this.clearTokenUpdateJob.bind(this));
            this.Listen2TokenReply();
            this.ScheduleTokenUpdate();
        }

        return result;
    }

    /**
     * 执行登录
     */
    private async ExecSignIn(userName: string, password: string, mac: string, os: string, quote: boolean = true) {
        
        const sign_in_result = {

            /** 交易服务器登录回执（WEB或TCP方式） */
            trade: null as any as UserLoginResponse,
            /** 行情服务器登录回执（TCP方式） */
            quote: null as any as UserLoginResponse,
        };

        if (this.isSocket) {
            
            const resp = await this.tserver.signin(userName, password, mac, os);
            const { errorCode } = resp;
            
            sign_in_result.trade = resp;
            if (errorCode == 0) {

                SetUserInfo(resp, password);
            }
            else {
                SetUserInfo(null);
            }
            
            defaultLogger.debug('Trade server login reply received', SocketServerType.TradeServer, resp);
        }
        else if (this.isWeb) {

            // 纯WEB登录方式（所有通信均基于HTTP请求，无法支持TICK数据订阅推送等功能）

            const resp = await this.WebSignIn(userName, password, mac, os);
            const { errorCode, errorMsg, data: userInfo } = resp;
            
            sign_in_result.trade = {
                errorCode,
                errorMsg,
                ...userInfo,
            } as any as UserLoginResponse;
            if (errorCode == 0) {

                SetUserInfo(userInfo!, password);
            }
            else {
                SetUserInfo(null);
            }
        }

        /**
         * 交易服务器未登录成功，直接返回
         */
        
        if (!sign_in_result.trade || sign_in_result.trade.errorCode != 0) {
            return sign_in_result;
        }

        /**
         * 交易服务器已登录，且需要登录行情
         */

        if (quote === true) {
            
            const resp = await this.qserver.signin(userName, password, mac, os);
            const { errorCode } = resp;

            if (errorCode == 0) {
                sign_in_result.quote = resp;
            }

            defaultLogger.debug('Quote server login reply received', SocketServerType.QuoteServer, resp);
        }
        
        defaultLogger.debug('SignIn result is', sign_in_result);
        return sign_in_result;
    }

    /**
     * 登录WEB服务器
     * @param username - 用户名
     * @param password - 密码
     * @param mac - MAC地址
     * @param os - 操作系统信息
     */
    private async WebSignIn(username: string, password: string, mac: string, os: string) {

        const body = { user_id: 1, username, password, mac, os, sessionId: '0' };
        return await this.assist.Post<UserLoginResponse>('/user/login', {}, body);
    }

    /**
     * 退出登录
     * @param quote - 是否退出行情服务器登录，默认为 true
     */
    async SignOut() {

        if (this.isSocket) {
            
            this.clearTokenUpdateJob();
            await this.tserver.signout();
            await this.qserver?.signout();
        }
        else if (this.isWeb) {

            const resp = this.WebSignOut();
            SetUserInfo(null);
        }
    }

    /**
     * 退出WEB登录
     */
    private WebSignOut() {

        SetUserInfo(null);
        const resp = { serverType: 'web', errorCode: 0, errorMsg: null };
        return resp;

        // const params = { username };
        // const resp = await this.assist.Put('/user/logout', params);
        // const { errorCode, errorMsg, data } = resp.result;
        
        // /**
        //  * 注销成功后，立即清除用户信息
        //  */
        // if (errorCode == 0) {
        //     SetUserInfo(null);
        // }

        // return resp;
    }

    /**
     * 定期发起刷新用户令牌请求
     */
    private ScheduleTokenUpdate() {

        this.clearTokenUpdateJob();
        const msg: SocketDataPackage = { fc: ServerFunction.RefreshToken, reqId: 0, dataType: MessageDataType.text };
        states.tokenUpdateJob = setInterval(() => { this.tserver.send(msg); }, 1000 * 60 * 30);
    }

    /**
     * 清除定期刷新用户令牌请求
     */
    private clearTokenUpdateJob() {
        clearInterval(states.tokenUpdateJob);
    }

    /**
     * 监听用户令牌刷新回复
     */
    private Listen2TokenReply() {

        this.tserver.unsubscribe(ServerEvent.TokenGenerated, UpdateToken);
        this.tserver.subscribe(ServerEvent.TokenGenerated, UpdateToken);
    }

    /**
     * 监听被强制剔除的消息，收到消息后应用程序应立即：
     * 1）从交易服务器主动退出
     * 2）从行情服务器主动退出
     */
    Listen2KickOut(callback: (data: SocketDataPackage) => void) {

        this.tserver.unsubscribe(ServerEvent.ForcedKickOut, callback);
        this.tserver.subscribe(ServerEvent.ForcedKickOut, callback);
        defaultLogger.trace('To listen to kicking out message');
    }
}