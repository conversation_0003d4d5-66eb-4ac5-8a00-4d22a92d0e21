<div class="trading-day-view-root">
  <div class="s-scroll-bar" style="overflow: auto">
    <div class="s-typical-toolbar">
      <el-button class="s-pull-left" @click="setTradingDay(true)" size="small">
        设置交易日
      </el-button>
      <el-button class="s-pull-left" @click="setTradingDay(false)" size="small">
        设置非交易日
      </el-button>
      <el-popover width="350" v-model="popover.visible">
        <el-row :gutter="20">
          <el-col :span="12">
            <span style="font-size: 16px;line-height: 25px" class="s-pull-left">交易日扩展至：</span>
          </el-col>
          <el-col :span="12">
            <el-date-picker class="s-pull-right" v-model="extendDate" type="year" placeholder="选择扩展日期">
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col class="s-center">
            <el-button size="small" @click="extendTradingDate">确定</el-button>
            <el-button size="small" @click="() => {  popover.visible = false }">取消</el-button>
          </el-col>
        </el-row>
        <el-button class="s-pull-right" slot="reference" size="small">交易日扩展</el-button>
      </el-popover>
    </div>

    <div class="s-typical-toolbar" style="margin-top: 30px">
      <el-button class="s-pull-left" size="small" @click="preYear(-1)">前1年</el-button>
      <el-button class="s-pull-right" size="small" @click="nextYear(1)">后1年</el-button>
    </div>
    <div class="trading-day-container">
      <template v-if="!isEmpty">
        <div class="trading-month" v-for="(item, index) in groupsDate" :class="{'current-month':isCurrentMonth(item) }">
          <div class="month-line">
            <span class="month-title">{{ getCurrentYear(getMonthOffset(item[0].tradingDay)) }}年{{
            getCurrentMonth(getMonthOffset(item[0].tradingDay))
          }}
              月</span>
          </div>
          <div class="week-line">
            <span class="trading-cell title-cell" :class="{ 'weekend-cell': subIndex === 0 || subIndex === 6 }"
              v-for="(week, subIndex) in 7">{{ getLineTitle(subIndex) }}</span>
          </div>
          <div>
            <span class="trading-cell trading-disabled"
              v-for="(pre,subIndex) in getMonthStartPosition(getMonthOffset(item[0].tradingDay))">
              {{
            getMonthDateCounter(getMonthOffset(item[0].tradingDay) - 1) +
              pre -
              getMonthStartPosition(getMonthOffset(item[0].tradingDay))
          }}
            </span>
            <span class="trading-cell" :class="getTradingCellClass(subItem)" v-for="(subItem,subIndex) in item"
              @click="setting(subItem)">{{ subIndex + 1 }}</span>
            <span class="trading-cell trading-disabled"
              v-for="after in (42- getMonthStartPosition(getMonthOffset(item[0].tradingDay)) - getMonthDateCounter(getMonthOffset(item[0].tradingDay)))">{{ after }}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <h2 align="center">暂无数据</h2>
      </template>
    </div>
  </div>
</div>