const IView = require('./iview').IView;

class AppInstance extends IView {

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.webview = null;
        this.view_root = null;
    }

    loadBuild(view_root) {

        this.view_root = view_root;
        let $webview = this.webview = document.createElement('webview');
        $webview.partition = 'persist:xtrade-external-app';
        view_root.appendChild($webview);
        this.onWindowResize();
        let url = this.viewName.slice(1); // viewName = @开头，将首个字符去掉
        let connector = url.indexOf('?') > 0 ? '&' : '?';
        let { userId, token } = this.userInfo;
        let { brokerCode } = this.app.otherSysSettings || {};
        let kvs = [
            { key: 'gaoyu-user-id', value: userId },
            { key: 'gaoyu-token', value: token },
            { key: 'gaoyu-broker-code', value: brokerCode || '' },
            { key: '_ts', value: Date.now() },
        ];
        $webview.src = `${url}${connector}${kvs.map(kv => `${kv.key}=${kv.value}`).join('&')}`;
        setTimeout(() => { this.scheduleTokenUpdate(); }, 1000 * 10);
    }

    setWebviewHeight() {
        this.webview.style.height = `${this.thisWindow.getSize()[1] - 60 - 30}px`;
    }

    onWindowResize() {

        this.setWebviewHeight();
        this.thisWindow.on('resize', () => {
            this.setWebviewHeight();
        });
    }

    scheduleTokenUpdate() {
        
        clearInterval(this.timer);
        this.timer = setInterval(() => { this.updateToken(); }, 1000 * 60 * 2);
    }

    updateToken() {

        const { userId, token } = this.userInfo;
        console.log('to ask webview page to update userid & token', { userId, token });
        this.webview.executeJavaScript(`typeof window.update_gaoyu_token === 'function' && window.update_gaoyu_token(${userId}, '${token}')`);
    }

    refresh() {

        this.interaction.showSuccess('已发送刷新请求');
        console.log('to ask webview page to refresh data');
        this.webview.executeJavaScript(`typeof window.refresh_page_by_gaoyu === 'function' && window.refresh_page_by_gaoyu()`);
    }
}

module.exports = { AppInstance };
