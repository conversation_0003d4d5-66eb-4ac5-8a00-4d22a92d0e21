<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { MomUser, FormUser } from '../../../../xtrade-sdk/dist';

const { user } = defineProps<{
  user?: MomUser;
}>();

interface AddressItem {
  id: number;
  desc: string;
  address: string;
}

const macList = ref<AddressItem[]>([]);
const ipList = ref<AddressItem[]>([]);

// 初始化地址列表
const initAddressList = () => {
  if (user) {
    // 初始化MAC地址列表
    if (user.mac) {
      const macDescs = user.macDesc?.split(',') || [];
      macList.value = user.mac.split(',').map((address, index) => ({
        id: Date.now() + index,
        address,
        desc: macDescs[index] || '',
      }));
    }

    // 初始化IP地址列表
    if (user.ip) {
      const ipDescs = user.ipDesc?.split(',') || [];
      ipList.value = user.ip.split(',').map((address, index) => ({
        id: Date.now() + index + 100,
        address,
        desc: ipDescs[index] || '',
      }));
    }
  }
};

// 监听user变化
watch(
  () => user,
  () => {
    initAddressList();
  },
  { immediate: true },
);

// MAC地址验证
const isMacValid = (mac: string) =>
  /^[A-Fa-f0-9]{2}((-[A-Fa-f0-9]{2}){5})|((:[A-Fa-f0-9]{2}){5})$/.test(mac);

// IP地址验证
const isIpValid = (ip: string) =>
  /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[0-9])\.((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.){2}(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)$/.test(
    ip,
  );

// 添加地址
const handleAddAddress = (type: 'mac' | 'ip') => {
  const list = type === 'mac' ? macList : ipList;
  if (list.value.length >= 5) {
    ElMessage.warning(`最多只能添加5个${type === 'mac' ? 'MAC' : 'IP'}地址`);
    return;
  }

  const newItem: AddressItem = {
    id: Date.now(),
    address: '',
    desc: '',
  };
  list.value.push(newItem);
};

// 删除地址
const handleDeleteAddress = (type: 'mac' | 'ip', id: number) => {
  const list = type === 'mac' ? macList : ipList;
  list.value = list.value.filter(item => item.id !== id);
};

// 地址编辑完成
const handleAddressBlur = (type: 'mac' | 'ip', item: AddressItem) => {
  const validator = type === 'mac' ? isMacValid : isIpValid;

  if (!validator(item.address)) {
    ElMessage.warning(`请输入正确的${type === 'mac' ? 'MAC' : 'IP'}地址格式`);
    return;
  }
};

const validate = () => {
  // 检查所有MAC地址格式
  const isAllMacValid = macList.value.every(item => isMacValid(item.address));
  if (!isAllMacValid) {
    return false;
  }

  // 检查所有IP地址格式
  const isAllIpValid = ipList.value.every(item => isIpValid(item.address));
  if (!isAllIpValid) {
    return false;
  }

  return true;
};

const getForm = () => {
  const macAddresses = macList.value.map(item => item.address).join(',');
  const ipAddresses = ipList.value.map(item => item.address).join(',');
  const macDescs = macList.value.map(item => item.desc).join(',');
  const ipDescs = ipList.value.map(item => item.desc).join(',');

  return {
    mac: macAddresses,
    ip: ipAddresses,
    macDesc: macDescs,
    ipDesc: ipDescs,
  };
};

defineExpose({
  validate,
  getForm,
});
</script>

<template>
  <div p-6>
    <!-- MAC地址绑定 -->
    <div mt-6 mb-20>
      <div flex items-center justify-between mb-4>
        <div>绑定MAC地址({{ macList.length }}/5)</div>
      </div>
      <el-table class="typical-edit-table" :data="macList" stripe>
        <el-table-column label="MAC地址">
          <template #default="{ row }">
            <el-input
              v-model="row.address"
              placeholder="请输入MAC地址"
              @blur="() => handleAddressBlur('mac', row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="MAC描述">
          <template #default="{ row }">
            <el-input v-model="row.desc" placeholder="请输入MAC描述" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button
              class="typical-text-button"
              link
              @click="() => handleDeleteAddress('mac', row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        mt-10
        class="important-c-[--g-block-bg-13] hover:opacity-70"
        link
        @click="() => handleAddAddress('mac')"
      >
        <i class="iconfont icon-add" mr-2 />
        添加MAC地址
      </el-button>
    </div>

    <!-- IP地址绑定 -->
    <div>
      <div flex items-center justify-between mb-4>
        <div>绑定IP地址({{ ipList.length }}/5)</div>
      </div>
      <el-table class="typical-edit-table" :data="ipList" stripe>
        <el-table-column label="IP地址">
          <template #default="{ row }">
            <el-input
              v-model="row.address"
              placeholder="请输入IP地址"
              @blur="() => handleAddressBlur('ip', row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="IP描述">
          <template #default="{ row }">
            <el-input v-model="row.desc" placeholder="请输入IP描述" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button
              class="typical-text-button"
              link
              @click="() => handleDeleteAddress('ip', row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        mt-10
        class="important-c-[--g-block-bg-13] hover:opacity-70"
        link
        @click="() => handleAddAddress('ip')"
      >
        <i class="iconfont icon-add" mr-2 />
        添加IP地址
      </el-button>
    </div>
  </div>
</template>

<style scoped></style>
