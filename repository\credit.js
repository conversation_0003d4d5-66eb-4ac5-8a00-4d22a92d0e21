const http = require('../libs/http').http;

class CreditRepository {

    getTargetInfo(params) {

        return new Promise((resolve, reject) => {

            http.get('../v4/compact/target', { params }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getContractInfo(params) {
        
        return new Promise((resolve, reject) => {

            http.get('../v4/compact', { params }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
}

module.exports = { repoCredit: new CreditRepository() };