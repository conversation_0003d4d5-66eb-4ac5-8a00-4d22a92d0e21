<div class="strategy-list-view-root">
    <div class="view-app-root">
        <template>

            <div class="table-box s-scroll-bar">
                
                <div class="s-typical-toolbar">
    
                    <el-button type="primary" @click="openStrategyCreationDialog" size="mini">
                        <i class="el-icon-plus"></i> 创建策略
                    </el-button>
    
                    <el-input v-model="searching.value" style="width: 160px;" placeholder="请输入关键词" clearable>
                        <i slot="prefix" class="el-input__icon el-icon-search"></i>
                    </el-input>
    
                </div>
    
                <data-tables layout="pagination,table" :filters="filters" table-label="策略管理"
                    :default-sort="{prop: 'id', order: 'descending'}" @row-click="handleRowClick"
                    ref="table" class="s-searchable-table" v-bind:data="strategyList" v-bind:table-props="tableProps"
                    v-bind:pagination-props="{ show: false, layout: 'prev,pager,next,sizes,total' }"
                    v-bind:search-def="searchDef">
                    <el-table-column prop="index" fixed="left" show-overflow-tooltip label="序号" type="index" width="50"
                        align="center">
                    </el-table-column>
                    <el-table-column fixed="left" show-overflow-tooltip label="策略名称" prop="strategyName" min-width="120"
                        sortable="custom">
                        <template slot-scope="scope">
                            <a class="s-underline s-cp" @click.stop="viewReportTemplate(scope.row)" title="查看产品报告">{{ scope.row.strategyName }}</a>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="left" show-overflow-tooltip label="策略ID" prop="id" min-width="150"
                        sortable="custom">
                        <template slot-scope="scope">
                            <span>{{scope.row.id}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :formatter="formatMaxMoney" prop="maxLimitMoney" align="right" show-overflow-tooltip
                        label="最大使用金额" min-width="120" sortable="custom">
                        <template slot-scope="props">
                            <div>{{getPrimaryLimitMoney(props.row) | thousands}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip label="报告模板" min-width="110" prop="reportTemplate">
                        <template slot-scope="scope">
                            <el-tooltip content="绑定报告模板" placement="top" :enterable="false">
                                <a style="margin-right: 5px" class="icon-button s-cp el-icon-edit"
                                    @click.stop="handleBindReportTemplate(scope.row)"></a>
                            </el-tooltip>
                            <span>{{ Array.isArray(scope.row.reportTemplates) && scope.row.reportTemplates.length>0 ?
                        scope.row.reportTemplates.map(x => x.templateName).join('、') : '暂未绑定'}}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="strategyAccounts" label="已绑账号" min-width="200" sortable="custom">
                        <template slot-scope='props'>
                            <span
                                @click.stop="handleOpenAccountBindingDialog(props.row)" 
                                class="anchor-bound-account s-ellipsis"
                                >
                                    <template v-if="props.row.strategyAccounts && props.row.strategyAccounts.length > 0">
                                        {{ props.row.strategyAccounts.map(user => user.accountName).join('、') }}
                                    </template>
                                    <template v-else>未绑定</template>
                            </span>
                        </template>
                    </el-table-column>
    
                    <el-table-column prop="traders" show-overflow-tooltip label="交易员"
                        min-width="140" sortable="custom">
                        <template slot-scope='props'>
                            <span @click.stop="handleOpenTraderBindingDialog(props.row)" class="s-cp s-underline">
                                <a v-if="props.row.traders && props.row.traders.length > 0">
                                    {{props.row.traders.map(user => user.fullName || user.userName).join('、')}}
                                </a>
                                <a v-else>未绑定</a>
                            </span>
                        </template>
                    </el-table-column>
    
                    <el-table-column prop="riskUsers" show-overflow-tooltip label="风控员"
                        min-width="140" sortable="custom">
                        <template slot-scope='props'>
                            <span @click.stop="handleOpenRiskBindingDialog(props.row)" class="s-cp s-underline">
                                <a v-if="props.row.riskUsers && props.row.riskUsers.length > 0">
                                    {{props.row.riskUsers.map(user => user.fullName || user.userName).join('、')}}
                                </a>
                                <a v-else>未绑定</a>
                            </span>
                        </template>
                    </el-table-column>
    
                    <el-table-column align="right" show-overflow-tooltip width="100" label="权益" prop="balance"
                        sortable="custom" :formatter="thousands"></el-table-column>
                    <el-table-column align="right" show-overflow-tooltip width="100" label="市值" prop="marketValue"
                        sortable="custom" :formatter="thousands"></el-table-column>
                    <el-table-column align="right" show-overflow-tooltip width="100" label="收益率" prop="risePercent"
                        sortable="custom" :formatter="percentage">
                        <template slot-scope="scope">
                            <span
                                :class="scope.row.risePercent > 0 ? 's-color-red' : scope.row.risePercent < 0 ? 's-color-green' : null">{{typeof scope.row.risePercent === 'number' ? scope.row.risePercent.toFixed(2) + '%' : (scope.row.risePercent || '0%') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :formatter="formatReduce" align="right" show-overflow-tooltip width="100" label="平仓盈亏"
                        prop="closeProfit" sortable="custom">
                        <template slot-scope="props">
                            <div :class="getColor(summarizeField(props.row.strategyAccounts, 'closeProfit'))">
                                {{ summarizeField(props.row.strategyAccounts, 'closeProfit') | thousands }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column :formatter="formatReduce" align="right" show-overflow-tooltip width="100" label="浮动盈亏"
                        prop="positionProfit" sortable="custom">
                        <template slot-scope="props">
                            <div :class="getColor(summarizeField(props.row.strategyAccounts, 'positionProfit'))">
                                {{ summarizeField(props.row.strategyAccounts, 'positionProfit') | thousands }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="online" label="在线" align="center" width="60">
                        <template slot-scope="scope">
                            <span :class="getConnectClass(scope.row.connectCount)">{{makeOnlineStatus(scope.row)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="实例数" align="center" width="60" prop="connectCount">
                        <template slot-scope="scope">
                            <span @click.stop="viewOnlineInstance(scope.row)"
                                class="s-cp">{{makeOnlineStatistics(scope.row)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="产品名称" prop="fundName" min-width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column label="产品ID" prop="fundId" min-width="130" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="operation" label="操作" width="130" align="center" fixed="right" class-name="s-col-oper">
                        <template slot-scope='props'>
                            <el-tooltip content="风控设置" placement="top" :enterable="false">
                                <a class="s-cp icon-button icon-tianjiafengkong iconfont"
                                    @click.stop="riskConfig(props.row)"></a>
                            </el-tooltip>
                            <!-- 
                            <el-tooltip content="交易行为设置" placement="top" :enterable="false">
                                <a class="s-cp icon-button icon-tab_yunwei iconfont"
                                    @click.stop="behaviorConfig(props.row)"></a>
                            </el-tooltip>
                            -->
                            <!-- <el-tooltip content="权限克隆" placement="top" :enterable="false">
                                <a class="s-cp icon-button el-icon-copy-document"
                                    @click.stop="handlePermissionClone(props.row)"></a>
                            </el-tooltip> -->
                            <el-tooltip content="修改" placement="top" :enterable="false">
                                <a class="s-cp icon-button el-icon-edit" @click.stop="handleEdit(props.row)"></a>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :enterable="false">
                                <a class="s-cp icon-button el-icon-delete s-color-red"
                                    @click.stop="handleDelete(props.row)"></a>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </data-tables>
            </div>
    
            <el-dialog width="400px" 
                       :title="dialog.strategy.title" 
                       :visible="dialog.strategy.visible" 
                       :show-close="false"
                       :close-on-click-modal="false" 
                       :close-on-press-escape="false"
                       v-drag>
    
                <el-form class="dialog-body-form s-pd-10" 
                         ref="strategyForm"
                         @submit.native.prevent 
                         :model="dialog.strategy.form"
                         :rules="dialog.strategy.rules" label-width="80px">
    
                    <el-form-item label="策略名称" prop="strategyName" :maxlength="25">
                        <el-input size="small" v-model.trim="dialog.strategy.form.strategyName"></el-input>
                    </el-form-item>
    
                    <el-form-item label="策略描述" prop="description" :maxlength="50">
                        <el-input type="textarea" v-model.trim="dialog.strategy.form.description"></el-input>
                    </el-form-item>
    
                    <el-form-item label="绑定产品" prop="fundId">
                        <el-select style="width: 100%" size="small" v-model="dialog.strategy.form.fundId">
    
                            <el-option v-for="(item, item_idx) in fundList" 
                                       :key="item_idx" 
                                       :label="item.fundName" 
                                       :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
    
                </el-form>
    
                <span slot="footer">
                    <el-button type="primary" @click="saveStrategy" size="small">确定</el-button>
                    <el-button @click="doClose" size="small">取消</el-button>
                </span>
    
            </el-dialog>
    
            <el-dialog id="strategy-permission" width="800px" title="权限克隆"
                       :show-close="false"
                       :visible="dialog.permission.visible" 
                       :close-on-click-modal="false"
                       :close-on-press-escape="false"
                       v-drag>
    
                <div v-if="dialog.permission.strategyPermissionList.length <= 0">
                    <h2 align="center">无策略信息</h2>
                </div>
                <div style="width: 100%;height: 30px;line-height: 30px;font-size: 14px;">
                    <el-checkbox style="margin:  0 30px;" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                    <el-checkbox style="margin:  0 30px;" :indeterminate="isIndeterminateTrader" v-model="checkTradersAll" @change="checkTradersAllChange">交易员权限</el-checkbox>
                    <el-checkbox style="margin:  0 30px;" :indeterminate="isIndeterminateRisk" v-model="checkRiskUsersAll" @change="checkRiskUsersAllChange">风控员权限</el-checkbox>
                </div>
                <div>
                    <el-checkbox-group v-model="checkedPermissionStrategy" @change="handleCheckedPermissionStrategyChange">
                        <data-tables layout="pagination,table" table-label="权限克隆":default-sort="{prop: 'id', order: 'descending'}"
                        ref="table" class="s-searchable-table" v-bind:data="dialog.permission.strategyPermissionList" v-bind:table-props="tableProps"
                        v-bind:pagination-props="{ show: false, layout: 'prev,pager,next,sizes,total' }">
                            <el-table-column prop="index" fixed="left" show-overflow-tooltip label="序号" type="index" width="50"
                                align="center">
                            </el-table-column>
                            <el-table-column fixed="left" show-overflow-tooltip label="策略ID" prop="id" min-width="150"
                                sortable="custom">
                                <template slot-scope="scope">
                                    <span style="font-size: 12px">{{scope.row.id}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column fixed="left" show-overflow-tooltip label="策略名称" prop="strategyName" min-width="120"
                                sortable="custom">
                                <template slot-scope="scope">
                                    <span style="font-size: 12px">{{scope.row.strategyName}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="traders" show-overflow-tooltip label="交易员权限克隆"
                                min-width="140" sortable="custom">
                                <template slot-scope='props'>
                                    <el-checkbox :key="props.row.id" :label="props.row.id">
                                        <span v-if="props.row.traders && props.row.traders.length > 0" 
                                            style="font-size: 12px">{{props.row.traders.map(user => user.fullName || user.userName).join('、')}}</span>
                                        <span v-else style="font-size: 12px">未绑定</span>
                                    </el-checkbox>
                                </template>
                            </el-table-column>
                            <el-table-column prop="riskUsers" show-overflow-tooltip label="风控员权限克隆"
                                min-width="140" sortable="custom">
                                <template slot-scope='props'>
                                    <el-checkbox :key='props.row.id.concat("_") + props.row.strategyName' :label='props.row.id.concat("_") + props.row.strategyName'>
                                        <span v-if="props.row.riskUsers && props.row.riskUsers.length > 0"
                                            style="font-size: 12px">{{props.row.riskUsers.map(user => user.fullName || user.userName).join('、')}}</span>
                                        <span v-else style="font-size: 12px">未绑定</span>   
                                    </el-checkbox>
                                </template>
                            </el-table-column>           
                        </data-tables>
                    </el-checkbox-group>
                </div>
                <span slot="footer">
                    <el-button @click="saveStrategyPermissionClone" type="primary" size="small">确定</el-button>
                    <el-button @click="closeStrategyPermissionCloneDialog" size="small">取消</el-button>
                </span>
            </el-dialog>
    
            <el-dialog width="600px" title="配置报告模板" 
                       :visible="dialog.report.visible" 
                       :show-close="false" 
                       :close-on-click-modal="false" 
                       :close-on-press-escape="false"
                       v-drag>
    
                <el-form label-width="70px" class="s-pd-10">
    
                    <el-form-item label="报告模板">
                        <el-select v-model="dialog.report.model" class="s-full-width" placeholder="选择报告模板" multiple>
                            <el-option v-for="item in dialog.report.data" :key="item.id" :label="item.label" :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
    
                    <el-form-item label="默认模板">
                        <span style="margin-left: 5px" 
                              v-if="Array.isArray(dialog.report.model) && dialog.report.model.length <= 0">暂未选择任何模板</span>
                        <template v-else>
                            <el-radio v-for="(item, item_idx) in dialog.report.defaultOptions" :key="item_idx"
                                v-model="dialog.report.defaultTemplateId" :label="item.id">{{item.label}}</el-radio>
                        </template>
                    </el-form-item>
    
                </el-form>
    
                <span slot="footer">
                    <el-button @click="handleSaveReport" type="primary" size="small">确定</el-button>
                    <el-button @click="handleCloseReport" size="small">取消</el-button>
                </span>
    
            </el-dialog>
    
            <el-dialog 
                width="540px"
                title="绑定策略账号"
                :visible="dialog.account.visible" 
                :show-close="false"
                :close-on-click-modal="false" 
                :close-on-press-escape="false"
                v-drag
                >
                <div class="s-pd-10">
                    <el-transfer 
                        filter-placeholder="关键字搜索"
                        :titles="['可选策略账号', '已选策略账号']"
                        v-model="dialog.account.selected"
                        :data="dialog.account.data"
                        :props="{ key: 'detailId', label: 'accountName' }" filterable></el-transfer>
                                    
                </div>
                <span slot="footer">
                    <span class="s-color-red">* 已绑子账号，如果解除绑定，将不可再见，请谨慎操作！</span>
                    <el-button @click="handleSaveAccount" type="primary" size="small">确定</el-button>
                    <el-button @click="() => dialog.account.visible = false" size="small">取消</el-button>
                </span>
            </el-dialog>
    
            <el-dialog width="520px" title="绑定交易员"
                       :visible="dialog.trader.visible" 
                       :show-close="false"
                       :close-on-click-modal="false" 
                       :close-on-press-escape="false"
                       v-drag>
    
                <div class="s-pd-10">
    
                    <el-transfer filter-placeholder="关键字搜索"
                                    v-model="dialog.trader.model"
                                    :titles="['可选交易员', '已选交易员']"
                                    :data="dialog.trader.data"
                                    :props="{ key: 'id', label: 'fullName' }" filterable></el-transfer>
                                    
                </div>
    
                <span slot="footer">
                    <el-button @click="handleSaveTrader" type="primary" size="small">确定</el-button>
                    <el-button @click="() => dialog.trader.visible = false" size="small">取消</el-button>
                </span>
    
            </el-dialog>
    
            <el-dialog width="520px" title="绑定风控员"
                       :visible="dialog.riskUser.visible" 
                       :show-close="false"
                       :close-on-click-modal="false" 
                       :close-on-press-escape="false"
                       v-drag>
    
                <div class="s-pd-10">
    
                    <el-transfer filter-placeholder="关键字搜索"
                                    v-model="dialog.riskUser.model"
                                    :titles="['可选风控员', '已选风控员']"
                                    :data="dialog.riskUser.data"
                                    :props="{ key: 'id', label: 'fullName' }" filterable></el-transfer>
    
                </div>
    
                <span slot="footer">
                    <el-button @click="handleSaveRiskUser" type="primary" size="small">确定</el-button>
                    <el-button @click="() => dialog.riskUser.visible = false" size="small">取消</el-button>
                </span>
    
            </el-dialog>
    
            <el-dialog title="在线列表"
                       :visible="dialog.online.visible" 
                       :close-on-click-modal="false"
                       :before-close="() => { dialog.online.visible = false }"
                       v-drag>
    
                <div class="s-pd-10">
                    <data-tables :data="strategyOnline.onlineList"
                                 :table-props="strategyOnline.common.tableProps" 
                                 :search-def="strategyOnline.common.searchDef" 
                                 :pagination-props="strategyOnline.common.paginationDef"
                                 class="s-searchable-table" stripe>
    
                        <el-table-column type="index" align="center" label="序号"></el-table-column>
                        <el-table-column label="IP" align="center" prop="ip"></el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <span @click="forceOffline(scope.row)">强制下线</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-for="item in strategyOnline.tabStructure" :key="item" :prop="item" :label="item">
                            <template slot-scope="scope">
                                <span>{{getConvertProperty(scope.row, item)}}</span>
                            </template>
                        </el-table-column>
                    </data-tables>
                </div>
    
            </el-dialog>
    
        </template>
    </div>
    <div class="table-all-strategies" style="display: none;">
        <table>
            <tr>
                <th label="策略名称" prop="strategyName"></th>
                <th label="策略ID" prop="id"></th>
                <th label="最大使用金额" prop="maxLimitMoney" formatter="getPrimaryLimitMoney"></th>
                <th label="已绑账号" prop="strategyAccounts" formatter="formatBoundAccounts"></th>
                <th label="交易员" prop="traders" formatter="formatBoundTraders"></th>
                <th label="风控员" prop="riskUsers" formatter="formatBoundRiskUsers"></th>
                <th label="权益" prop="balance" thousands></th>
                <th label="市值" prop="marketValue" thousands></th>
                <th label="收益率" prop="risePercent" percentage></th>
                <th label="平仓盈亏" prop="closeProfit" formatter="formatCloseProfit"></th>
                <th label="浮动盈亏" prop="positionProfit" formatter="formatPositionProfit"></th>
                <th label="在线" prop="online" formatter="makeOnlineStatus"></th>
                <th label="实例数" prop="connectCount" formatter="makeOnlineStatistics"></th>
                <th label="产品名称" prop="fundName"></th>
                <th label="产品ID" prop="fundId"></th>
            </tr>
        </table>
    </div>
</div>