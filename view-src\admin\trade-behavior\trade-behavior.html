<div class="trade-behavior-root">
    <el-form label-width="280px">
        <div class="block-title">设置对象</div>
        <el-form-item label="设置对象">
            <el-input v-model="context.name" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="是否启用:">
            <el-switch v-model="behavior.riskEnable"></el-switch>
        </el-form-item>
        <!-- <template v-if="is4Account">
            <el-form-item label="撤单率(%):">
                <el-input-number v-model="account.cancelRate" placeholder="撤单率(%)" :min="0" :max="100" :step="0.01" :controls="false"></el-input-number>
            </el-form-item>
            <el-form-item label="下单速度:">
                <el-input-number v-model.number="account.orderSpeed" placeholder="下单速度" :min="0" :max="10000" :step="1" :controls="false"></el-input-number>
            </el-form-item>
            <el-form-item label="错单数:">
                <el-input-number v-model.number="account.errorCount" placeholder="错单数" :min="0" :max="10000" :step="1" :controls="false"></el-input-number>
            </el-form-item>
            <el-form-item label="资金检查:">
                <el-checkbox v-model="account.availableCheck">资金检查</el-checkbox>
            </el-form-item>
            <el-form-item label="持仓检查:">
                <el-checkbox v-model="account.positionCheck">持仓检查</el-checkbox>
            </el-form-item>
            <el-form-item label="比对覆盖:">
                <el-checkbox v-model="account.overlapBalance">比对覆盖</el-checkbox>
            </el-form-item>
        </template> -->
        <el-form-item label="订单自成交:">
            <el-checkbox v-model="behavior.orderSelfTransaction">订单自成交检查</el-checkbox>
        </el-form-item>
        <!-- <el-form-item label="母单自成交:">
            <el-checkbox v-model="behavior.algoOrderSelfTransaction">母单自成交检查</el-checkbox>
        </el-form-item> -->
        <div class="block-title">流控设置</div>
        <el-form-item label="沪深各市场流速（笔/秒）:">
            <el-input-number v-model="behavior.flowSpeed" :precision="0" :min="0" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="账户总委托数（笔）:">
            <el-input-number v-model="behavior.totalEntrust" :precision="0" :min="0" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="净买入金额（万）:">
            <el-input-number v-model="behavior.netBuyAmount" :precision="0" :min="0" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="单笔委托金额（万）:">
            <el-input-number v-model="behavior.singleOrderAmount" :precision="2" :min="0" :max="999999999" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="单笔委托数量（手）:">
            <el-input-number v-model="behavior.singleOrderVolume" :precision="0" :min="0" :max="999999999" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="比例规则启用阈值:">
            <el-input-number v-model="behavior.ratioEnableThreshold" :precision="0" :min="0" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="撤单比(0 ~ 100%):">
            <el-input-number v-model="behavior.cancelRatio" :precision="2" :min="0" :max="100" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="废单比(0 ~ 100%):">
            <el-input-number v-model="behavior.invalidRatio" :precision="2" :min="0" :max="100" :step="1" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="委托价格偏离度(仅股票有效 0 ~ 2%):">
            <el-input-number v-model="behavior.cagePriceLimit" :precision="2" :min="0" :max="2" :step="0.1" :controls="false"></el-input-number>
        </el-form-item>
    </el-form>
    
    <template>
        <div class="toolbar-row">
            <el-button type="primary" @click="save">保存交易行为限制</el-button>
        </div>
    </template>
</div>