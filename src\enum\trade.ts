import { enumToArray } from '../script';
import { type TradeChannel } from '../types/trade';

/** 资产类型 */
export enum AssetTypeEnum {
  期货 = 1,
  股票 = 2,
  期权 = 3,
  债券 = 4,
  基金 = 5,
  现货 = 6,
  回购 = 7,
}

/** 资产类型 */
export const ASSET_TYPES = enumToArray(AssetTypeEnum);

/** 交易渠道 */
export const TRADE_CHANNELS: TradeChannel[] = [
  {
    label: '现货竞价',
    name: 'spot',
    assetTypes: [AssetTypeEnum.股票, AssetTypeEnum.基金, AssetTypeEnum.债券],
  },
  {
    label: '融资融券',
    name: 'credit',
    assetTypes: [AssetTypeEnum.股票, AssetTypeEnum.基金, AssetTypeEnum.债券],
    credit: true,
  },
  // {
  //   label: '期货',
  //   name: 'future',
  //   assetTypes: [AssetTypeEnum.期货],
  // },
];

/** 交易方向 */
export enum TradeDirectionEnum {
  买入 = 1,
  卖出 = -1,
}

/** 交易方向 */
export const TRADE_DIRECTIONS = enumToArray(TradeDirectionEnum);

/** 篮子交易方向 */
export const ABSKET_TRADE_DIRECTIONS = [
  ...enumToArray(TradeDirectionEnum),
  { label: '调仓', value: 0 },
];

/** 价格类型 */
export enum OrderPriceTypeEnum {
  限价 = 1,
  市价 = 2,
  模拟 = 3,
}

/** 对冲标志 */
export enum HedgeFlagEnum {
  Start = 0,
  投机 = 1,
  套利 = 2,
  强平 = 3,
  End = 4,
}

/**
 * 算法执行时间
 */
export enum AlgorithmExecuteTimeEnum {
  立即执行 = 1,
  指定时间 = 2,
  当日有效 = 3,
}

/**
 * 算法执行时间
 */
export const AlgorithmExecuteTimes = enumToArray(AlgorithmExecuteTimeEnum);

/**
 * 到期未成处理
 */
export enum ExpirationUnfinishedTreatmentEnum {
  自动撤销 = 1,
  转为限价 = 2,
}

/**
 * 到期未成处理
 */
export const ExpirationUnfinishedTreatments = enumToArray(ExpirationUnfinishedTreatmentEnum);

/**
 * 交易风格
 */
export enum TradeStyleEnum {
  激进型 = 1,
  稳健型 = 2,
  保守型 = 3,
}

/**
 * 交易风格
 */
export const TradeStyles = enumToArray(TradeStyleEnum);

/**
 * 篮子交易模式
 */
export enum BasketTradeModeEnum {
  按数量 = 1,
  按权重 = 2,
}

/**
 * 篮子交易模式
 */
export const BasketTradeModes = enumToArray(BasketTradeModeEnum);

/**
 * 算法参数项数据类型
 */
export const AlgoParamType = {
  String: { label: '字符串', value: 'string' },
  Number: { label: '数字', value: 'number' },
  Boolean: { label: '布尔', value: 'bool' },
  Time: { label: '时间', value: 'time' },
  TimeRange: { label: '时间区间', value: 'time_range' },
  Date: { label: '日期', value: 'date' },
  DateRange: { label: '日期区间', value: 'date_range' },
  Select: { label: '下拉选项', value: 'select' },
};

/**
 * 算法参数项数据类型
 */
export const AlgoParamTypes = Object.values(AlgoParamType);
